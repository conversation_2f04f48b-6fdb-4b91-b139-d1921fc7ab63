# OpenVPN Client Setup Guide

## Overview
This document provides the complete setup instructions for configuring OpenVPN client on Ubuntu servers. The current working configuration connects to VPN server `************:1194` with proper routing to maintain SSH connectivity.

## Current Working Configuration

### Server Information
- **Server IP**: **************
- **VPN Server**: ************:1194
- **VPN Interface**: tun1
- **VPN IP Range**: 10.8.0.x
- **Service**: <EMAIL>

### Complete Configuration File (/etc/openvpn/server.conf)

```bash
client
dev tun
proto udp
remote ************ 1194
resolv-retry infinite
nobind
persist-key
persist-tun

remote-cert-tls server
verify-x509-name server_6PT5wbs4pEo2jQHD name

auth SHA256
auth-nocache
cipher AES-128-GCM

tls-client
tls-version-min 1.2
tls-cipher TLS-ECDHE-ECDSA-WITH-AES-128-GCM-SHA256

verb 3

<ca>
-----BEGIN CERTIFICATE-----

-----END CERTIFICATE-----
</ca>

<cert>
-----BEGIN CERTIFICATE-----

-----END CERTIFICATE-----
</cert>

<key>
-----BEGIN PRIVATE KEY-----

-----END PRIVATE KEY-----
</key>

<tls-crypt>
-----BEGIN OpenVPN Static key V1-----

-----END OpenVPN Static key V1-----
</tls-crypt>

# Critical Routing Configuration
route-nopull
route ************ *************** net_gateway
route ************** *************** net_gateway
```

## Setup Instructions

### Prerequisites
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install OpenVPN
sudo apt install -y openvpn

# Enable IP forwarding (if needed)
sudo sed -i 's/#net.ipv4.ip_forward=1/net.ipv4.ip_forward=1/' /etc/sysctl.conf
sudo sysctl -p
```

### Configuration Steps

1. **Create Configuration Directory**
   ```bash
   sudo mkdir -p /etc/openvpn
   ```

2. **Copy Configuration File**
   ```bash
   # Copy the above configuration to /etc/openvpn/server.conf
   sudo cp server.conf /etc/openvpn/server.conf
   sudo chmod 600 /etc/openvpn/server.conf
   sudo chown root:root /etc/openvpn/server.conf
   ```

3. **Test Configuration**
   ```bash
   # Test syntax and connectivity
   sudo openvpn --config /etc/openvpn/server.conf --mode p2p

   # Stop test with Ctrl+C after verifying connection
   ```

4. **Enable and Start Service**
   ```bash
   # Enable service to start on boot
   sudo <NAME_EMAIL>

   # Start the service
   sudo <NAME_EMAIL>

   # Check status
   sudo <NAME_EMAIL>
   ```

## Service Management

### Check Status
```bash
sudo <NAME_EMAIL>
```

### Start Service
```bash
sudo <NAME_EMAIL>
```

### Stop Service
```bash
sudo <NAME_EMAIL>
```

### Restart Service
```bash
sudo <NAME_EMAIL>
```

### View Logs
```bash
# Recent logs
sudo journalctl -u <EMAIL> -n 20

# Follow logs in real-time
sudo journalctl -u <EMAIL> -f
```

## Troubleshooting

### Common Issues

#### 1. SSH Connection Lost After VPN Start
**Problem**: VPN redirects all traffic including SSH back to server
**Solution**: The routing configuration above prevents this

**Check Routes**:
```bash
ip route show | grep **************
```

#### 2. Service Fails to Start
**Problem**: Configuration file not found or syntax error
**Solution**:
```bash
# Check configuration syntax
sudo openvpn --config /etc/openvpn/server.conf --test-crypto

# Check file permissions
sudo ls -la /etc/openvpn/server.conf
```

#### 3. VPN Disconnects Frequently
**Problem**: Network issues or server problems
**Solution**:
```bash
# Check service logs
sudo journalctl -u <EMAIL> -n 50

# Test manual connection
sudo openvpn --config /etc/openvpn/server.conf --verb 4
```

### Debug Commands

#### Check VPN Interface
```bash
ip addr show tun1
```

#### Check Routing Table
```bash
ip route show
```

#### Check OpenVPN Processes
```bash
ps aux | grep openvpn
```

#### Test Network Connectivity
```bash
# Test VPN server connectivity
ping -c 3 ************

# Test local connectivity
ping -c 3 **************
```

## Important Notes

### Routing Configuration Explained
- `route-nopull`: Prevents VPN server from pushing default routes
- `route ************ *************** net_gateway`: Ensures VPN server is reachable
- `route ************** *************** net_gateway`: Preserves SSH connectivity

### Security Considerations
- Configuration file contains private keys - restrict permissions to `600`
- Only root should have access to `/etc/openvpn/`
- Regularly update certificates before expiration

### Backup Configuration
```bash
# Create backup
sudo cp /etc/openvpn/server.conf /etc/openvpn/server.conf.backup.$(date +%Y%m%d)

# Restore from backup
sudo cp /etc/openvpn/server.conf.backup.YYYYMMDD /etc/openvpn/server.conf
```

## Verification

After setup, verify the VPN is working:

1. **Check Service Status**:
   ```bash
   sudo <NAME_EMAIL>
   # Should show: "Active: active (running)"
   ```

2. **Check VPN Interface**:
   ```bash
   ip addr show tun1
   # Should show VPN IP (10.8.0.x)
   ```

3. **Check Routing**:
   ```bash
   ip route show | grep ************
   # Should show route to VPN server
   ```

4. **Test Connectivity**:
   ```bash
   # Should work (SSH to server itself)
   ssh root@************** "echo 'SSH working'"

   # Should work (VPN server reachable)
   ping -c 3 ************
   ```

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review service logs: `sudo journalctl -u <EMAIL> -n 50`
3. Verify configuration file syntax
4. Test network connectivity to VPN server

---

**Last Updated**: October 4, 2025
**Configuration Status**: ✅ Working
**VPN Server**: ************:1194
**Client IP**: **************