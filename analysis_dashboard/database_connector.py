import sqlite3
import json
from typing import Dict, Any, List, Optional, Tuple

class DatabaseConnector:
    def __init__(self, db_path: str = 'trading_bot/data/analysis_results.db'):
        self.db_path = db_path

    def get_connection(self) -> sqlite3.Connection:
        """Get direct connection to trading bot database"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row # This allows accessing columns by name
        return conn

    def row_to_dict(self, cursor, row) -> Dict[str, Any]:
        """Convert SQLite row to dictionary with JSON parsing"""
        columns = [description[0] for description in cursor.description]
        result = dict(zip(columns, row))
        
        # Parse JSON fields
        if 'risk_factors' in result and result['risk_factors']:
            try:
                result['risk_factors'] = json.loads(result['risk_factors'])
            except (json.JSONDecodeError, TypeError):
                result['risk_factors'] = {} # or some other default
        if 'analysis_data' in result and result['analysis_data']:
            try:
                result['analysis_data'] = json.loads(result['analysis_data'])
            except (json.JSONDecodeError, TypeError):
                result['analysis_data'] = {} # or some other default
        
        # Explicitly convert pnl to float if it exists
        if 'pnl' in result and result['pnl'] is not None:
            try:
                result['pnl'] = float(result['pnl'])
            except (ValueError, TypeError):
                result['pnl'] = 0.0 # Default to 0.0 if conversion fails
        
        return result

    def get_analysis_data_for_dashboard(self, limit: int = 1000, symbol: str = None, timeframe: str = None) -> List[Dict[str, Any]]:
        """Retrieve analysis_data records for dashboard analysis"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT * FROM analysis_results WHERE 1=1"
        params = []
        
        if symbol is not None:
            query += " AND symbol = ?"
            params.append(symbol)
        
        if timeframe is not None:
            query += " AND timeframe = ?"
            params.append(timeframe)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        conn.close()
        
        return [self.row_to_dict(cursor, row) for row in rows]

    def get_trades(self, limit: int = 1000, symbol: str = None, status: str = None) -> List[Dict[str, Any]]:
        """Retrieve trade records for dashboard analysis"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT * FROM trades WHERE 1=1"
        params = []
        
        if symbol is not None:
            query += " AND symbol = ?"
            params.append(symbol)
        
        if status is not None:
            query += " AND status = ?"
            params.append(status)
            
        query += " ORDER BY created_at DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        conn.close()
        
        return [self.row_to_dict(cursor, row) for row in rows]

    def get_recommendations_with_outcomes(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """Get recommendations joined with their trade outcomes"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = """
        SELECT
            ar.*,
            t.pnl, t.status AS trade_status, t.entry_price AS trade_entry_price,
            t.avg_exit_price AS trade_exit_price, -- Corrected to avg_exit_price
            t.take_profit AS trade_take_profit, t.stop_loss AS trade_stop_loss,
            t.side AS trade_side, t.quantity AS trade_quantity, t.created_at AS trade_created_at,
            t.updated_at AS trade_updated_at -- Added updated_at
        FROM
            analysis_results ar
        LEFT JOIN
            trades t ON ar.id = t.recommendation_id
            AND t.status = 'closed'
        ORDER BY
            ar.timestamp DESC
        LIMIT ?
        """
        
        cursor.execute(query, (limit,))
        rows = cursor.fetchall()
        conn.close()
        
        return [self.row_to_dict(cursor, row) for row in rows]

    def get_success_rate_by_confidence(self, confidence_ranges: List[Tuple[float, float]]) -> Dict[str, Dict]:
        """Calculate success rates grouped by confidence ranges"""
        # This will be implemented in outcome_tracker.py, but the data will come from here.
        return {}

    def get_chart_path_for_recommendation(self, recommendation_id: str, symbol: str, timeframe: str, timestamp: str) -> Optional[str]:
        """Find the corresponding chart screenshot path for a recommendation"""
        # This will be implemented in chart_viewer.py, but the data will come from here.
        return None
