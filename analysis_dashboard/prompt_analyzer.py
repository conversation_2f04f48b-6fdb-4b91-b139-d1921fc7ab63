import json
from typing import Dict, Any, List
from dataclasses import dataclass
from analysis_dashboard.data_parser import AnalysisDataParser, AnalysisInsight
from analysis_dashboard.outcome_tracker import RecommendationOutcome, ConfidenceCalibrationAnalyzer

@dataclass
class PromptImprovementSuggestion:
    category: str
    priority: str
    description: str
    affected_fields: List[str]
    sample_improvements: List[str]

class PromptEffectivenessAnalyzer:
    """Analyzer for prompt effectiveness and improvement opportunities"""
    
    def __init__(self, analysis_parser: AnalysisDataParser):
        self.parser = analysis_parser
        self.improvement_categories = self._load_improvement_categories()
    
    def _load_improvement_categories(self):
        # Placeholder for loading improvement categories from config or a file
        return {
            "completeness": "Focus on ensuring all required fields are consistently present.",
            "consistency": "Improve logical consistency of generated trade parameters.",
            "actionability": "Ensure recommendations are actionable with clear trade parameters.",
            "confidence_calibration": "Align AI confidence with actual trade outcomes."
        }

    def analyze_field_completion_rates(self, insights: List[RecommendationOutcome]) -> Dict[str, float]:
        """Analyze completion rates for different analysis fields based on outcomes"""
        field_counts = {}
        total_analyses = len(insights)
        
        if total_analyses == 0:
            return {}

        # Initialize counts for all possible issues
        all_possible_issues = set()
        for insight in insights:
            # Ensure analysis_data is a dictionary
            analysis_data = insight.analysis_data
            if isinstance(analysis_data, str):
                try:
                    analysis_data = json.loads(analysis_data)
                except json.JSONDecodeError:
                    analysis_data = {}
            
            issues = self.parser.extract_prompt_issues(analysis_data)
            for issue in issues:
                all_possible_issues.add(issue)
        
        for issue in all_possible_issues:
            field_counts[issue] = 0

        # Count occurrences of each issue
        for insight in insights:
            analysis_data = insight.analysis_data
            if isinstance(analysis_data, str):
                try:
                    analysis_data = json.loads(analysis_data)
                except json.JSONDecodeError:
                    analysis_data = {}
            
            issues = self.parser.extract_prompt_issues(analysis_data)
            for issue in issues:
                field_counts[issue] += 1
            
        return {issue: count / total_analyses for issue, count in field_counts.items()}
    
    def identify_prompt_gaps(self, insights: List[RecommendationOutcome]) -> List[PromptImprovementSuggestion]:
        """Identify gaps in prompt that lead to incomplete analyses"""
        completion_rates = self.analyze_field_completion_rates(insights)
        gaps = []
        
        for issue, rate in completion_rates.items():
            if rate > 0.2: # If more than 20% of analyses have this issue
                description = self.improvement_categories.get(issue.replace("Missing ", "").lower(), f"Address {issue} in prompt.")
                gaps.append(PromptImprovementSuggestion(
                    category="Completeness/Consistency",
                    priority="High" if rate > 0.5 else "Medium",
                    description=f"AI frequently misses: {issue}. {description}",
                    affected_fields=[issue.replace("Missing ", "").lower()],
                    sample_improvements=[f"Explicitly instruct AI to include '{issue.replace('Missing ', '')}' in its response."]
                ))
        return gaps
    
    def generate_prompt_template_suggestions(self, insights: List[RecommendationOutcome]) -> Dict[str, str]:
        """Generate improved prompt template suggestions"""
        # This is a placeholder. Real template generation would be complex.
        # For now, it suggests based on identified gaps.
        gaps = self.identify_prompt_gaps(insights)
        suggestions = {}
        
        if gaps:
            suggestions["general_improvement"] = "Consider adding more explicit instructions and examples for fields that are frequently missed."
            for gap in gaps:
                suggestions[f"improve_{'_'.join(gap.affected_fields)}"] = f"To address '{gap.description}', ensure your prompt clearly defines the requirement for these fields and provides examples of expected output."
        else:
            suggestions["status"] = "Current prompt seems effective. Focus on fine-tuning."
            
        return suggestions

    def generate_prompt_improvement_suggestions(self, analysis_insights: List[RecommendationOutcome]) -> List[PromptImprovementSuggestion]:
        """Generate specific suggestions for improving the analyzer prompt"""
        suggestions = []
        
        # Gaps in completeness/consistency
        suggestions.extend(self.identify_prompt_gaps(analysis_insights))
        
        # Confidence calibration issues (requires ConfidenceCalibrationAnalyzer)
        # Assuming ConfidenceCalibrationAnalyzer is available and initialized
        # For now, this is a placeholder.
        # confidence_analyzer = ConfidenceCalibrationAnalyzer(self.db) # Needs db connector
        # suggestions.extend(confidence_analyzer.suggest_confidence_improvements(analysis_insights))
        
        # Add other types of suggestions based on analysis
        
        return suggestions

    def analyze_prompt_effectiveness_by_timeframe(self, insights: List[RecommendationOutcome]) -> Dict[str, float]:
        """Analyze how prompt performs across different timeframes"""
        timeframe_effectiveness = {}
        for insight in insights:
            tf = insight.analysis_data.get('timeframe', 'unknown')
            # Use the quality score from the parsed analysis data
            # Assuming analysis_data is already parsed into a dict by data_parser
            parsed_analysis_insight = self.parser.parse_single_analysis({"analysis_data": insight.analysis_data})
            quality = parsed_analysis_insight.quality_indicators.get('overall_quality_score', 0)
            
            if tf not in timeframe_effectiveness:
                timeframe_effectiveness[tf] = {'total_quality': 0, 'count': 0}
            
            timeframe_effectiveness[tf]['total_quality'] += quality
            timeframe_effectiveness[tf]['count'] += 1
            
        return {tf: data['total_quality'] / data['count'] for tf, data in timeframe_effectiveness.items() if data['count'] > 0}

    def identify_weak_analysis_areas(self, insights: List[RecommendationOutcome]) -> Dict[str, List[str]]:
        """Identify specific areas where analysis quality is consistently low"""
        weak_areas = {}
        for insight in insights:
            parsed_analysis_insight = self.parser.parse_single_analysis({"analysis_data": insight.analysis_data})
            if parsed_analysis_insight.quality_indicators.get('overall_quality_score', 0) < 0.5: # Low quality threshold
                issues = self.parser.extract_prompt_issues(insight.analysis_data)
                for issue in issues:
                    if issue not in weak_areas:
                        weak_areas[issue] = []
                    weak_areas[issue].append(f"{parsed_analysis_insight.symbol}-{parsed_analysis_insight.timeframe} ({parsed_analysis_insight.timestamp})")
        return weak_areas

    def generate_prompt_optimization_report(self, insights: List[RecommendationOutcome]) -> Dict[str, Any]:
        """Generate comprehensive report on prompt optimization opportunities"""
        report = {
            "field_completion_rates": self.analyze_field_completion_rates(insights),
            "identified_gaps": [s.__dict__ for s in self.identify_prompt_gaps(insights)],
            "template_suggestions": self.generate_prompt_template_suggestions(insights),
            "effectiveness_by_timeframe": self.analyze_prompt_effectiveness_by_timeframe(insights),
            "weak_analysis_areas": self.identify_weak_analysis_areas(insights),
            # "confidence_calibration_analysis": ConfidenceCalibrationAnalyzer(self.db).analyze_confidence_calibration(insights) # Needs db connector
        }
        return report

class QualityMetricsCalculator:
    """Calculator for various quality metrics and scores"""
    
    def calculate_analysis_completeness(self, analysis: Dict) -> float:
        """Calculate completeness score for analysis"""
        # This is a placeholder, actual implementation is in AnalysisDataParser
        return 0.0
    
    def calculate_logical_consistency(self, analysis: Dict) -> float:
        """Calculate logical consistency score"""
        # This is a placeholder, actual implementation is in AnalysisDataParser
        return 0.0
    
    def calculate_actionability_score(self, analysis: Dict) -> float:
        """Calculate how actionable the analysis is"""
        # This is a placeholder, actual implementation is in AnalysisDataParser
        return 0.0
