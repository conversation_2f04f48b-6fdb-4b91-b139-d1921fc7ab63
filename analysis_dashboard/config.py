# analysis_dashboard/config.py

# Configuration for the AI Analyzer Dashboard

# Database path for the trading bot's analysis results
# This should point to the SQLite database file generated by the trading bot.
DATABASE_PATH = 'trading_bot/data/analysis_results.db'

# Path to the directory containing backed-up chart screenshots
# These are the original charts used for analysis, typically with a .png.backup extension.
CHARTS_BACKUP_PATH = 'trading_bot/data/charts/.backup/'

# Thresholds for determining trade profitability/loss
# Used in outcome_tracker.py to categorize trade outcomes.
PROFITABLE_PNL_THRESHOLD = 0.01  # Minimum PnL to be considered profitable
LOSS_PNL_THRESHOLD = -0.01       # Maximum PnL to be considered a loss (negative value)

# Quality thresholds for analysis completeness, consistency, and actionability
# Used in data_parser.py to calculate overall analysis quality scores.
ANALYSIS_QUALITY_THRESHOLDS = {
    "completeness": 0.8,
    "consistency": 0.7,
    "actionability": 0.7
}

# Improvement categories for prompt analysis suggestions
# Used in prompt_analyzer.py to categorize and describe prompt improvement suggestions.
PROMPT_IMPROVEMENT_CATEGORIES = {
    "completeness": "Focus on ensuring all required fields are consistently present.",
    "consistency": "Improve logical consistency of generated trade parameters.",
    "actionability": "Ensure recommendations are actionable with clear trade parameters.",
    "confidence_calibration": "Align AI confidence with actual trade outcomes."
}

# Cache TTL for Streamlit data loading (in seconds)
# Determines how long cached data remains valid.
STREAMLIT_CACHE_TTL = 3600 # 1 hour

# Example: You can add more configuration parameters here as needed,
# such as default date ranges, specific symbols to highlight, etc.
