import os # Import os module
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import List, Dict, Any, Optional
from analysis_dashboard.outcome_tracker import RecommendationOutcome
from analysis_dashboard.chart_viewer import ChartViewer # Import ChartViewer

def display_metrics_card(title: str, value: Any, delta: Optional[Any] = None, help_text: Optional[str] = None):
    """Displays a metric card with optional delta and help text."""
    st.metric(label=title, value=value, delta=delta, help=help_text)

def create_bar_chart(df: pd.DataFrame, x_col: str, y_col: str, title: str, x_axis_title: str, y_axis_title: str, color_col: Optional[str] = None):
    """Creates a Plotly bar chart."""
    fig = px.bar(df, x=x_col, y=y_col, title=title, color=color_col,
                 labels={x_col: x_axis_title, y_col: y_axis_title})
    st.plotly_chart(fig, use_container_width=True)

def create_line_chart(df: pd.DataFrame, x_col: str, y_col: str, title: str, x_axis_title: str, y_axis_title: str, color_col: Optional[str] = None):
    """Creates a Plotly line chart."""
    fig = px.line(df, x=x_col, y=y_col, title=title, color=color_col,
                  labels={x_col: x_axis_title, y_col: y_axis_title})
    st.plotly_chart(fig, use_container_width=True)

def create_scatter_chart(df: pd.DataFrame, x_col: str, y_col: str, title: str, x_axis_title: str, y_axis_title: str, color_col: Optional[str] = None, size_col: Optional[str] = None):
    """Creates a Plotly scatter chart."""
    fig = px.scatter(df, x=x_col, y=y_col, title=title, color=color_col, size=size_col,
                     labels={x_col: x_axis_title, y_col: y_axis_title})
    st.plotly_chart(fig, use_container_width=True)

def display_dataframe(df: pd.DataFrame, caption: Optional[str] = None):
    """Displays a DataFrame with optional caption."""
    st.dataframe(df, use_container_width=True)
    if caption:
        st.caption(caption)

def filter_dataframe(df: pd.DataFrame, column: str, options: List[str], default_options: List[str], multiselect_label: str) -> pd.DataFrame:
    """Provides a multiselect filter for a DataFrame column."""
    selected_options = st.multiselect(multiselect_label, options=options, default=default_options)
    if selected_options:
        return df[df[column].isin(selected_options)]
    return df

def display_json(data: Optional[Dict[str, Any]], title: Optional[str] = None):
    """Displays JSON data in an expandable expander."""
    if title:
        st.subheader(title)
    with st.expander("View Raw Data"):
        st.json(data)

def create_pie_chart(df: pd.DataFrame, names_col: str, values_col: str, title: str):
    """Creates a Plotly pie chart."""
    fig = px.pie(df, names=names_col, values=values_col, title=title)
    st.plotly_chart(fig, use_container_width=True)

def display_enhanced_quick_summary(outcome: RecommendationOutcome, chart_viewer: ChartViewer):
    """
    Displays an enhanced, more visual quick summary of the recommendation outcome.
    Includes chart thumbnail, key metrics, and correlation analysis.
    """
    st.markdown("---")
    st.subheader("Quick Summary")

    col_chart, col_data = st.columns([1, 2]) # 1/3 for chart, 2/3 for data

    with col_chart:
        st.markdown("##### Chart")
        chart_filename = chart_viewer.find_chart_for_analysis(outcome.symbol, outcome.timeframe, outcome.timestamp)
        if chart_filename:
            chart_image = chart_viewer.load_chart(chart_filename)
            if chart_image:
                st.image(chart_image, caption=f"{outcome.symbol} {outcome.timeframe}", use_column_width=True)
            else:
                st.info("Chart not found.")
        else:
            st.info("No chart image path.")

        # Interactive Analysis Overlay
        st.markdown("---")
        st.markdown("##### Analysis Details")

        analysis_data_full = outcome.analysis_data.get('analysis', {}) # Get the nested 'analysis' dict

        # Define fields to display and their display names
        display_fields = {
            "summary": "Summary",
            "key_levels": "Key Levels",
            "risk_factors": "Risk Factors",
            "market_condition": "Market Condition",
            "market_direction": "Market Direction",
            "evidence": "Evidence",
            "entry_price": "Entry Price",
            "stop_loss": "Stop Loss",
            "take_profit": "Take Profit",
            "direction": "Direction",
            "entry_explenation": "Entry Explanation",
            "take_profit_explenation": "Take Profit Explanation",
            "stop_loss_explenation": "Stop Loss Explanation",
            "secret_info": "Secret Info"
        }

        # Filter out fields that are not present in the analysis data
        available_fields = {k: v for k, v in display_fields.items() if analysis_data_full.get(k) is not None}

        # Create a list of display names for the selectbox
        field_options = list(available_fields.values())

        if field_options:
            selected_field_name = st.selectbox("Select Analysis Detail", field_options, key=f"analysis_detail_{outcome.recommendation_id}")

            # Find the original key from the display name
            selected_field_key = next(key for key, value in available_fields.items() if value == selected_field_name)

            selected_content = analysis_data_full.get(selected_field_key)

            if selected_content is not None:
                # Display the selected field with consistent styling
                col_detail_1, col_detail_2 = st.columns([1, 3])

                with col_detail_1:
                    st.metric(label="Field", value=selected_field_name)

                with col_detail_2:
                    if selected_field_key == "key_levels":
                        st.metric(label="Support", value=f"{selected_content.get('support', 'N/A')}")
                        st.metric(label="Resistance", value=f"{selected_content.get('resistance', 'N/A')}")
                    elif isinstance(selected_content, list):
                        for i, item in enumerate(selected_content):
                            st.metric(label=f"Item {i+1}", value=str(item))
                    elif isinstance(selected_content, (int, float)):
                        st.metric(label="Value", value=f"{selected_content:.2f}")
                    else:
                        st.metric(label="Content", value=str(selected_content))
            else:
                st.info("No data available for this field.")
        else:
            st.info("No detailed analysis data available for selection.")


    with col_data:
        st.markdown("##### Analysis & Outcome")
        
        # Row 1: Symbol, Timeframe, Timestamp
        col1_1, col1_2, col1_3 = st.columns(3)
        with col1_1:
            st.metric(label="Symbol", value=outcome.symbol)
        with col1_2:
            st.metric(label="Timeframe", value=outcome.timeframe)
        with col1_3:
            timestamp_str = outcome.timestamp
            if timestamp_str and timestamp_str != 'N/A':
                timestamp_dt = pd.to_datetime(timestamp_str, errors='coerce')
                if pd.notna(timestamp_dt):
                    st.metric(label="Timestamp", value=timestamp_dt.strftime('%Y-%m-%d %H:%M:%S'))
                else:
                    st.metric(label="Timestamp", value="Invalid Format")
            else:
                st.metric(label="Timestamp", value="N/A")

        st.markdown("---")

        # Row 2: Key Metrics (Recommendation, Confidence, Outcome, PnL)
        col2_1, col2_2, col2_3, col2_4 = st.columns(4)
        
        # Access the nested 'analysis' dictionary for core AI analysis details
        analysis_core_data = outcome.analysis_data.get('analysis', {})

        with col2_1:
            st.metric(label="Recommendation", value=analysis_core_data.get('recommendation', 'N/A').upper())
        
        with col2_2:
            confidence = analysis_core_data.get('confidence') # Access from nested analysis
            if confidence is not None:
                st.metric(label="Confidence", value=f"{confidence:.2f}")
                # Optional: Add a progress bar for confidence
                st.progress(confidence)
            else:
                st.metric(label="Confidence", value="N/A")

        with col2_3:
            outcome_category = outcome.outcome_category.replace('_', ' ').title()
            delta_color = "off"
            if outcome_category == "Profitable":
                delta_color = "inverse" # Green
            elif outcome_category == "Loss":
                delta_color = "normal" # Red
            st.metric(label="Outcome", value=outcome_category, delta_color=delta_color)

        with col2_4:
            pnl = outcome.success_metrics.get('pnl')
            if pnl is not None:
                pnl_delta = f"{pnl:.2f}"
                pnl_color = "off"
                if pnl > 0:
                    pnl_color = "inverse"
                elif pnl < 0:
                    pnl_color = "normal"
                st.metric(label="PnL", value=f"${pnl:.2f}", delta=pnl_delta, delta_color=pnl_color)
            else:
                st.metric(label="PnL", value="N/A")
        
        st.markdown("---")

        # Row 3: Correlation Analysis
        st.markdown("##### Correlation: AI Prediction vs. Actual Outcome")
        # Access the nested 'analysis' dictionary for core AI analysis details
        analysis_core_data = outcome.analysis_data.get('analysis', {})
        prediction = analysis_core_data.get('recommendation', 'N/A').upper() # Access from nested analysis
        actual_outcome = outcome.outcome_category.replace('_', ' ').title()
        
        correlation_status = "N/A"
        correlation_emoji = "⚪"
        
        if prediction != 'N/A' and actual_outcome != 'N/A':
            if outcome.success_metrics.get('is_profitable'): # Access from success_metrics
                if prediction == 'BUY' or prediction == 'LONG':
                    correlation_status = "Aligned (Profitable Buy/Long)"
                    correlation_emoji = "✅"
                elif prediction == 'SELL' or prediction == 'SHORT':
                    correlation_status = "Misaligned (Profitable Sell/Short)"
                    correlation_emoji = "❌"
                else: # HOLD or other
                    correlation_status = "Partial (Profitable Hold)"
                    correlation_emoji = "⚠️"
            elif outcome.success_metrics.get('is_loss'): # Access from success_metrics
                if prediction == 'BUY' or prediction == 'LONG':
                    correlation_status = "Misaligned (Loss Buy/Long)"
                    correlation_emoji = "❌"
                elif prediction == 'SELL' or prediction == 'SHORT':
                    correlation_status = "Aligned (Loss Sell/Short)"
                    correlation_emoji = "✅"
                else: # HOLD or other
                    correlation_status = "Partial (Loss Hold)"
                    correlation_emoji = "⚠️"
            elif actual_outcome == "No Trade":
                if prediction == 'HOLD':
                    correlation_status = "Aligned (No Trade Hold)"
                    correlation_emoji = "✅"
                else:
                    correlation_status = "Partial (No Trade, but Rec)"
                    correlation_emoji = "⚠️"
            else:
                correlation_status = "Undetermined"
                correlation_emoji = "❓"

        st.markdown(f"**Prediction:** `{prediction}` (Confidence: `{analysis_core_data.get('confidence', 'N/A'):.2f}`)") # Access from nested analysis
        st.markdown(f"**Actual Outcome:** `{actual_outcome}` (PnL: `{pnl:.2f}`)")
        st.markdown(f"**Correlation Status:** {correlation_emoji} {correlation_status}")

        st.markdown("---")

        # Row 4: Trade Performance Summary (if trade data exists)
        st.markdown("##### Trade Performance Summary")
        if outcome.trade_data:
            trade_data = outcome.trade_data
            entry_price = trade_data.get('entry_price')
            exit_price = trade_data.get('exit_price')
            
            st.markdown(f"**Trade Status:** `{trade_data.get('status', 'N/A').replace('_', ' ').title()}`")

            if isinstance(entry_price, (int, float)) and isinstance(exit_price, (int, float)) and entry_price != 0:
                price_change_percent = ((exit_price - entry_price) / entry_price) * 100
                st.markdown(f"**Entry:** `{entry_price:.2f}` → **Exit:** `{exit_price:.2f}` (`{price_change_percent:.2f}%`)")
            else:
                st.markdown(f"**Entry:** `{entry_price}` → **Exit:** `{exit_price}`")

            # Calculate duration from created_at and updated_at
            created_at_str = trade_data.get('created_at')
            updated_at_str = trade_data.get('updated_at')

            if created_at_str and updated_at_str:
                try:
                    # Convert to datetime objects
                    created_at_dt = pd.to_datetime(created_at_str)
                    updated_at_dt = pd.to_datetime(updated_at_str)
                    
                    duration = updated_at_dt - created_at_dt
                    total_seconds = duration.total_seconds()

                    hours, remainder = divmod(total_seconds, 3600)
                    minutes, seconds = divmod(remainder, 60)
                    st.markdown(f"**Duration:** `{int(hours)}h {int(minutes)}m {int(seconds)}s`")
                except Exception as e:
                    st.markdown(f"**Duration:** Error calculating ({e})")
            else:
                st.markdown(f"**Duration:** N/A")
            
            # Calculate Risk/Reward Ratio (requires SL, TP, Entry from nested analysis_data)
            analysis_core_data = outcome.analysis_data.get('analysis', {}) # Get the nested 'analysis' dict
            analysis_entry = analysis_core_data.get('entry_price')
            analysis_sl = analysis_core_data.get('stop_loss')
            analysis_tp = analysis_core_data.get('take_profit')
            analysis_direction = analysis_core_data.get('direction', '').upper()

            # Ensure all required values are not None before calculation
            if all(x is not None and isinstance(x, (int, float)) for x in [analysis_entry, analysis_sl, analysis_tp]):
                if analysis_direction == 'LONG':
                    risk = abs(analysis_entry - analysis_sl)
                    reward = abs(analysis_tp - analysis_entry)
                elif analysis_direction == 'SHORT':
                    risk = abs(analysis_sl - analysis_entry)
                    reward = abs(analysis_entry - analysis_tp)
                else:
                    risk, reward = 0, 0 # For HOLD or unknown direction

                if risk > 0:
                    rr_ratio = reward / risk
                    st.markdown(f"**Risk/Reward (AI):** `1:{rr_ratio:.2f}`")
                else:
                    st.markdown("**Risk/Reward (AI):** N/A (Risk is zero)")
            else:
                st.markdown("**Risk/Reward (AI):** N/A (Missing AI parameters)") # Removed the '1:'

        else:
            st.info("No trade data available for this recommendation.")

def display_trade_correlation(outcome: RecommendationOutcome):
    """
    Displays trade correlation details for a given recommendation outcome.
    This function is now largely superseded by display_enhanced_quick_summary's
    "Trade Performance Summary" section, but kept for compatibility if needed elsewhere.
    """
    st.markdown("---")
    st.subheader("Trade Details")

    if outcome.trade_data:
        trade_data = outcome.trade_data
        st.markdown(f"**Trade ID:** {trade_data.get('trade_id', 'N/A')}")
        entry_price = trade_data.get('entry_price')
        st.markdown(f"**Entry Price:** {entry_price:.2f}" if isinstance(entry_price, (int, float)) else f"**Entry Price:** {entry_price}")
        
        exit_price = trade_data.get('exit_price')
        st.markdown(f"**Exit Price:** {exit_price:.2f}" if isinstance(exit_price, (int, float)) else f"**Exit Price:** {exit_price}")
        
        trade_pnl = trade_data.get('pnl')
        if isinstance(trade_pnl, (int, float)):
            bg_color = "lightgreen" if trade_pnl > 0 else "lightcoral" if trade_pnl < 0 else "lightgray"
            text_color = "darkgreen" if trade_pnl > 0 else "darkred" if trade_pnl < 0 else "black"
            st.markdown(f"**Trade PnL:** <span style='background-color:{bg_color}; color:{text_color}; padding: 2px 6px; border-radius: 4px; font-weight:bold;'>${trade_pnl:.2f}</span>", unsafe_allow_html=True)
        else:
            st.markdown(f"**Trade PnL:** {trade_pnl}")
        st.markdown(f"**Trade Duration (seconds):** {trade_data.get('duration_seconds', 'N/A')}")
        st.markdown(f"**Trade Status:** {trade_data.get('status', 'N/A').replace('_', ' ').title()}")
        st.markdown(f"**Trade Type:** {trade_data.get('trade_type', 'N/A').replace('_', ' ').title()}")
    else:
        st.info("No trade data available for this recommendation.")
