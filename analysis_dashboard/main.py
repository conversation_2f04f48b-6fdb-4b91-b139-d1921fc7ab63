import streamlit as st
import pandas as pd
import os # Import os module
import datetime # Import datetime module
import json # Import json module
import plotly.express as px # Import plotly.express
from typing import List, Dict, Any, Optional
from analysis_dashboard.database_connector import DatabaseConnector
from analysis_dashboard.data_parser import AnalysisDataParser, AnalysisInsight
from analysis_dashboard.outcome_tracker import OutcomeTracker, RecommendationOutcome, ConfidenceCalibrationAnalyzer
from analysis_dashboard.chart_viewer import <PERSON><PERSON>ie<PERSON>
from analysis_dashboard.prompt_analyzer import PromptEffectivenessAnalyzer
from analysis_dashboard.dashboard_utils import (
    display_metrics_card, create_bar_chart, create_line_chart,
    create_scatter_chart, display_dataframe, filter_dataframe, display_json, create_pie_chart,
    display_enhanced_quick_summary # Updated import
)
from analysis_dashboard.backtest_runner import DashboardBacktestRunner

# Initialize components
db_connector = DatabaseConnector()
data_parser = AnalysisDataParser()
outcome_tracker = OutcomeTracker(db_connector)
chart_viewer = ChartViewer()
prompt_analyzer = PromptEffectivenessAnalyzer(data_parser)
confidence_analyzer = ConfidenceCalibrationAnalyzer(db_connector)
backtest_runner = DashboardBacktestRunner()

def main():
    st.set_page_config(page_title="AI Analyzer Dashboard", layout="wide")
    st.title("🤖 AI Analyzer Performance Dashboard")

    # Load data
    # @st.cache_data(ttl=3600) # Temporarily disable cache to ensure class definition reload
    def load_all_data():
        return outcome_tracker.get_recommendations_with_outcomes()

    all_recommendation_outcomes = load_all_data()

    if not all_recommendation_outcomes:
        st.warning("No analysis data found in the database. Please ensure the trading bot has generated analyses.")
        return

    # Convert to DataFrame for easier filtering and manipulation
    # This requires flattening the nested dictionaries
    df_data = []
    for outcome in all_recommendation_outcomes:
        # Access nested analysis data correctly
        analysis_core_data = outcome.analysis_data.get('analysis', {})
        row = {
            "recommendation_id": outcome.recommendation_id,
            "symbol": outcome.symbol,
            "timeframe": outcome.timeframe,
            "timestamp": outcome.timestamp,
            "confidence_score": analysis_core_data.get('confidence', 0.0),
            "recommendation": analysis_core_data.get('recommendation', 'N/A'),
            "outcome_category": outcome.outcome_category,
            "pnl": outcome.success_metrics.get('pnl'),
            "is_profitable": outcome.success_metrics.get('is_profitable'),
            "is_loss": outcome.success_metrics.get('is_loss'),
            "is_traded": outcome.success_metrics.get('is_traded'),
            "trade_status": outcome.trade_data.get('status') if outcome.trade_data else None, # Add trade_status
            "chart_path_db": outcome.chart_path, # Path as stored in DB
            "analysis_data_raw": outcome.analysis_data,
            "trade_data_raw": outcome.trade_data
        }
        df_data.append(row)
    
    df = pd.DataFrame(df_data)
    df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce') # Use errors='coerce' to turn invalid parsing into NaT
    # Ensure timestamps are timezone-naive for consistent comparison with date inputs
    try:
        if df['timestamp'].dt.tz is not None:
            df['timestamp'] = df['timestamp'].dt.tz_localize(None)
    except (AttributeError, TypeError):
        # Already timezone-naive or not a datetime column
        pass
    df['confidence_score'] = df['confidence_score'].fillna(0.0) # Fill NaN confidence scores with 0.0
    df = df.sort_values(by='timestamp', ascending=False)

    # Calculate min_date and max_date safely for date inputs
    # Drop NaT values from timestamp column before calculating min/max
    valid_timestamps = df['timestamp'].dropna()

    if not valid_timestamps.empty:
        min_date_val = valid_timestamps.min().date()
        max_date_val = valid_timestamps.max().date()
    else:
        min_date_val = datetime.date(2023, 1, 1) # Default start date
        max_date_val = datetime.date.today() # Default end date

    # Sidebar filters
    st.sidebar.header("Filters")
    
    # Prepare unique lists for filters, handling None values and ensuring non-empty options
    all_symbols = sorted(df['symbol'].dropna().unique().tolist())
    if df['symbol'].isnull().any():
        all_symbols.insert(0, None) # Add None as an option if present in data
    if not all_symbols: # Ensure it's never empty for multiselect options
        all_symbols = ["No Symbols Available"]

    all_timeframes = sorted(df['timeframe'].dropna().unique().tolist())
    if df['timeframe'].isnull().any():
        all_timeframes.insert(0, None)
    if not all_timeframes:
        all_timeframes = ["No Timeframes Available"]

    all_outcomes = sorted(df['outcome_category'].dropna().unique().tolist())
    if df['outcome_category'].isnull().any():
        all_outcomes.insert(0, None)
    if not all_outcomes:
        all_outcomes = ["No Outcomes Available"]

    all_recommendations = sorted(df['recommendation'].dropna().unique().tolist())
    if df['recommendation'].isnull().any():
        all_recommendations.insert(0, None)
    if not all_recommendations:
        all_recommendations = ["No Recommendations Available"]

    selected_symbols = st.sidebar.multiselect("Select Symbols", options=all_symbols, default=all_symbols, key="sidebar_symbols")
    selected_timeframes = st.sidebar.multiselect("Select Timeframes", options=all_timeframes, default=all_timeframes, key="sidebar_timeframes")
    selected_outcomes = st.sidebar.multiselect("Select Outcomes", options=all_outcomes, default=all_outcomes, key="sidebar_outcomes")
    selected_recommendations = st.sidebar.multiselect("Select Recommendations", options=all_recommendations, default=all_recommendations, key="sidebar_recommendations")

    # Confidence range filter
    confidence_range = st.sidebar.slider("Confidence Score Range", 0.0, 1.0, (0.0, 1.0), 0.01, key="sidebar_confidence")

    # Date range filters
    st.sidebar.subheader("Date Range")
    start_date = st.sidebar.date_input("Start Date", value=min_date_val, min_value=min_date_val, max_value=max_date_val, key="sidebar_start_date")
    end_date = st.sidebar.date_input("End Date", value=max_date_val, min_value=min_date_val, max_value=max_date_val, key="sidebar_end_date")
    show_all_dates = st.sidebar.checkbox("Show All Dates", value=False, key="sidebar_show_all_dates")

    # Reset all filters button
    if st.sidebar.button("🔄 Reset All Filters", type="primary"):
        # Clear session state for filter keys to reset them to defaults
        filter_keys = ["sidebar_symbols", "sidebar_timeframes", "sidebar_outcomes", "sidebar_recommendations", "sidebar_confidence", "sidebar_start_date", "sidebar_end_date", "sidebar_show_all_dates"]
        for key in filter_keys:
            if key in st.session_state:
                del st.session_state[key]
        st.rerun()

    # Apply filters to DataFrame
    filtered_df = df[
        df['symbol'].isin(selected_symbols) &
        df['timeframe'].isin(selected_timeframes) &
        df['outcome_category'].isin(selected_outcomes) &
        df['recommendation'].isin(selected_recommendations) &
        (df['confidence_score'] >= confidence_range[0]) &
        (df['confidence_score'] <= confidence_range[1])
    ]

    # Apply date filters conditionally
    if not show_all_dates:
        filtered_df = filtered_df[
            filtered_df['timestamp'].notna() &
            (filtered_df['timestamp'] >= pd.Timestamp(start_date)) &
            (filtered_df['timestamp'] <= pd.Timestamp(end_date) + pd.Timedelta(days=1) - pd.Timedelta(microseconds=1))
        ]

    # Apply filters to outcomes list
    filtered_outcomes = [
        o for o in all_recommendation_outcomes
        if o.symbol in selected_symbols and
           o.timeframe in selected_timeframes and
           o.outcome_category in selected_outcomes and
           o.analysis_data.get('analysis', {}).get('recommendation', 'N/A') in selected_recommendations and
           o.analysis_data.get('analysis', {}).get('confidence', 0.0) >= confidence_range[0] and
           o.analysis_data.get('analysis', {}).get('confidence', 0.0) <= confidence_range[1]
    ]

    # Apply date filters to outcomes conditionally
    if not show_all_dates:
        filtered_outcomes = [
            o for o in filtered_outcomes
            if o.timestamp and pd.to_datetime(o.timestamp, errors='coerce') and
               pd.to_datetime(o.timestamp) >= pd.Timestamp(start_date) and
               pd.to_datetime(o.timestamp) <= pd.Timestamp(end_date) + pd.Timedelta(days=1) - pd.Timedelta(microseconds=1)
        ]

    st.sidebar.info(f"Showing {len(filtered_df)} of {len(df)} recommendations.")

    # --- Dashboard Tabs ---
    tab1, tab2, tab3 = st.tabs(["📊 Performance Overview", "🔍 Individual Analysis Explorer", "📈 Backtest Analysis"])

    with tab1:
        st.header("📊 Aggregated Performance Overview")

        # Overall Trade Statistics
        st.subheader("Overall Trade Statistics")
        # Calculate metrics
        total_recs = len(filtered_df)
        # Filter for closed trades only for accurate win rate calculation
        traded_recs_df = filtered_df[filtered_df['trade_status'] == 'closed']
        traded_recs = len(traded_recs_df)
        profitable_recs = traded_recs_df['is_profitable'].sum()
        loss_recs = traded_recs_df['is_loss'].sum()
        total_pnl = traded_recs_df['pnl'].sum()
        win_rate = (profitable_recs / traded_recs * 100) if traded_recs > 0 else 0
        avg_pnl_per_trade = (total_pnl / traded_recs) if traded_recs > 0 else 0

        # Display key metrics using st.metric for a cleaner look
        col1, col2, col3, col4, col5, col6, col7 = st.columns(7)
        with col1:
            st.metric("Total Analyses", total_recs)
        with col2:
            st.metric("Traded Recs", traded_recs)
        with col3:
            st.markdown(f"**Profitable:** <span style='color:green; font-size: 2rem;'>{profitable_recs}</span>", unsafe_allow_html=True)
        with col4:
            st.markdown(f"**Losses:** <span style='color:red; font-size: 2rem;'>{loss_recs}</span>", unsafe_allow_html=True)
        with col5:
            st.metric("Win Rate", f"{win_rate:.2f}%")
        with col6:
            st.metric("Total PnL", f"${total_pnl:,.2f}")
        with col7:
            st.metric("Avg PnL/Trade", f"${avg_pnl_per_trade:,.2f}")
        
        st.markdown("---") # Separator for clarity

        # AI Trading Recommendations - Data-Driven Insights
        st.subheader("🤖 AI Trading Recommendations")

        if not traded_recs_df.empty:
            # Filter out HOLD recommendations for performance analysis
            trade_recs_only = traded_recs_df[traded_recs_df['recommendation'].str.upper() != 'HOLD']

            if trade_recs_only.empty:
                st.warning("⚠️ **No Trading Recommendations Found**: All recommendations are HOLD. " +
                          "Need actual BUY/SELL recommendations to analyze trading performance.")
            else:
                # Check if we have sufficient data for meaningful analysis
                total_trades = len(trade_recs_only)
                min_trades_for_analysis = 5

                if total_trades < min_trades_for_analysis:
                    st.warning(f"⚠️ **Insufficient Trading Data**: Only {total_trades} actual trades (excluding HOLD). " +
                              f"Need at least {min_trades_for_analysis} trades for meaningful analysis.")

                    # Show basic stats
                    basic_pnl = trade_recs_only['pnl'].sum()
                    basic_win_rate = trade_recs_only['is_profitable'].mean() * 100
                    st.info(f"Total PnL: ${basic_pnl:.2f} | Win Rate: {basic_win_rate:.1f}% | Sample Size: {total_trades} trades")
                else:
                    # Calculate optimal parameter combinations
                    st.write("**Optimal Trading Parameters Based on Historical Performance:**")
                    st.info("📊 *Analysis excludes HOLD recommendations to focus on actual trading performance*")

                    # 1. Best performing recommendation type (minimum 3 trades per type)
                    rec_performance = trade_recs_only.groupby('recommendation').agg({
                        'pnl': ['sum', 'mean', 'count'],
                        'is_profitable': 'sum'
                    }).round(2)

                    rec_performance.columns = ['Total PnL', 'Avg PnL', 'Trades', 'Wins']
                    rec_performance['Win Rate'] = (rec_performance['Wins'] / rec_performance['Trades'] * 100).round(1)
                    rec_performance['Avg PnL'] = rec_performance['Avg PnL'].round(2)

                    # Filter for recommendation types with sufficient sample size
                    rec_performance_filtered = rec_performance[rec_performance['Trades'] >= 3]
                    if not rec_performance_filtered.empty:
                        best_rec = str(rec_performance_filtered['Total PnL'].idxmax())
                        best_rec_stats = rec_performance_filtered.loc[best_rec]
                    else:
                        best_rec = str(rec_performance['Total PnL'].idxmax())
                        best_rec_stats = rec_performance.loc[best_rec]

                    # 2. Best confidence range (minimum 3 trades per range)
                    confidence_ranges = pd.cut(trade_recs_only['confidence_score'],
                                              bins=[0, 0.3, 0.5, 0.7, 0.9, 1.0],
                                              labels=['Low (0-0.3)', 'Medium-Low (0.3-0.5)', 'Medium (0.5-0.7)', 'Medium-High (0.7-0.9)', 'High (0.9-1.0)'])

                    conf_performance = trade_recs_only.assign(confidence_range=confidence_ranges).groupby('confidence_range').agg({
                        'pnl': ['sum', 'mean', 'count'],
                        'is_profitable': 'sum'
                    }).round(2)

                    conf_performance.columns = ['Total PnL', 'Avg PnL', 'Trades', 'Wins']
                    conf_performance['Win Rate'] = (conf_performance['Wins'] / conf_performance['Trades'] * 100).round(1)

                    # Filter for confidence ranges with sufficient sample size
                    conf_performance_filtered = conf_performance[conf_performance['Trades'] >= 3]
                    if not conf_performance_filtered.empty:
                        best_conf_range = str(conf_performance_filtered['Total PnL'].idxmax())
                        best_conf_stats = conf_performance_filtered.loc[best_conf_range]
                    else:
                        best_conf_range = str(conf_performance['Total PnL'].idxmax())
                        best_conf_stats = conf_performance.loc[best_conf_range]

                    # 3. Best timeframe (minimum 3 trades per timeframe)
                    tf_performance = trade_recs_only.groupby('timeframe').agg({
                        'pnl': ['sum', 'mean', 'count'],
                        'is_profitable': 'sum'
                    }).round(2)

                    tf_performance.columns = ['Total PnL', 'Avg PnL', 'Trades', 'Wins']
                    tf_performance['Win Rate'] = (tf_performance['Wins'] / tf_performance['Trades'] * 100).round(1)

                    # Filter for timeframes with sufficient sample size
                    tf_performance_filtered = tf_performance[tf_performance['Trades'] >= 3]
                    if not tf_performance_filtered.empty:
                        best_tf = str(tf_performance_filtered['Total PnL'].idxmax())
                        best_tf_stats = tf_performance_filtered.loc[best_tf]
                    else:
                        best_tf = str(tf_performance['Total PnL'].idxmax())
                        best_tf_stats = tf_performance.loc[best_tf]

            # Display recommendations in columns
            col_rec1, col_rec2, col_rec3 = st.columns(3)

            with col_rec1:
                st.info(f"**🎯 Best Recommendation Type:** {best_rec.upper()}\n\n"
                        f"• Total PnL: ${best_rec_stats['Total PnL']:.2f}\n"
                        f"• Win Rate: {best_rec_stats['Win Rate']:.1f}%\n"
                        f"• Avg Trade: ${best_rec_stats['Avg PnL']:.2f}\n"
                        f"• Sample Size: {best_rec_stats['Trades']} trades")

            with col_rec2:
                st.success(f"**📊 Best Confidence Range:** {best_conf_range}\n\n"
                           f"• Total PnL: ${best_conf_stats['Total PnL']:.2f}\n"
                           f"• Win Rate: {best_conf_stats['Win Rate']:.1f}%\n"
                           f"• Avg Trade: ${best_conf_stats['Avg PnL']:.2f}\n"
                           f"• Sample Size: {best_conf_stats['Trades']} trades")

            with col_rec3:
                st.warning(f"**⏱️ Best Timeframe:** {best_tf}\n\n"
                           f"• Total PnL: ${best_tf_stats['Total PnL']:.2f}\n"
                           f"• Win Rate: {best_tf_stats['Win Rate']:.1f}%\n"
                           f"• Avg Trade: ${best_tf_stats['Avg PnL']:.2f}\n"
                           f"• Sample Size: {best_tf_stats['Trades']} trades")

            # Combined analysis - Best combination
            st.write("**🔥 Optimal Trading Strategy:**")

            # Find the combination with highest win rate and positive PnL
            combined_analysis = traded_recs_df.groupby(['recommendation', 'timeframe']).agg({
                'pnl': ['sum', 'mean', 'count'],
                'is_profitable': 'sum',
                'confidence_score': 'mean'
            }).round(2)

            combined_analysis.columns = ['Total PnL', 'Avg PnL', 'Trades', 'Wins', 'Avg Confidence']
            combined_analysis['Win Rate'] = (combined_analysis['Wins'] / combined_analysis['Trades'] * 100).round(1)

            # Filter for combinations with positive PnL and at least 3 trades
            profitable_combinations = combined_analysis[
                (combined_analysis['Total PnL'] > 0) &
                (combined_analysis['Trades'] >= 3)
            ].sort_values('Total PnL', ascending=False)

            if not profitable_combinations.empty:
                best_combo = profitable_combinations.iloc[0]
                combo_name = f"{profitable_combinations.index[0][0].upper()} on {profitable_combinations.index[0][1]}"

                st.success(f"**Most Profitable Combination:** {combo_name}\n\n"
                          f"• Total PnL: ${best_combo['Total PnL']:.2f}\n"
                          f"• Win Rate: {best_combo['Win Rate']}%\n"
                          f"• Avg Trade: ${best_combo['Avg PnL']:.2f}\n"
                          f"• Avg Confidence: {best_combo['Avg Confidence']:.2f}\n"
                          f"• Sample Size: {int(best_combo['Trades'])} trades")

                # Show top 3 profitable combinations
                st.write("**📈 Top 3 Most Profitable Combinations:**")
                top_combos = profitable_combinations.head(3)
                for i, (combo, stats) in enumerate(top_combos.iterrows(), 1):
                    combo_str = str(combo)
                    combo_name = combo_str.replace("(", "").replace(")", "").replace("'", "").upper()
                    st.write(f"**{i}. {combo_name}**\n"
                            f"• PnL: ${stats['Total PnL']:.2f} | Win Rate: {stats['Win Rate']:.1f}% | Avg Confidence: {stats['Avg Confidence']:.2f}")
            else:
                st.info("No profitable combinations found with sufficient sample size (minimum 3 trades).")

        st.markdown("---") # Separator for clarity

        # Confidence-PnL Correlation metrics
        st.subheader("Confidence Calibration")
        confidence_pnl_correlation = confidence_analyzer.analyze_confidence_calibration(filtered_outcomes)
        col_conf1, col_conf2, col_conf3 = st.columns(3)
        with col_conf1:
            st.metric("Confidence-PnL Correlation", f"{confidence_pnl_correlation.get('confidence_pnl_correlation', 0):.2f}")
        with col_conf2:
            st.metric("Avg Conf (Profitable)", f"{confidence_pnl_correlation.get('avg_confidence_profitable_trades', 0):.2f}")
        with col_conf3:
            st.metric("Avg Conf (Losing)", f"{confidence_pnl_correlation.get('avg_confidence_loss_trades', 0):.2f}")

        # Advanced Trading Analytics
        st.subheader("Advanced Trading Analytics")

        if not traded_recs_df.empty:
            # Calculate advanced metrics
            max_profit = traded_recs_df['pnl'].max()
            max_loss = traded_recs_df['pnl'].min()
            avg_win = traded_recs_df[traded_recs_df['pnl'] > 0]['pnl'].mean()
            avg_loss = traded_recs_df[traded_recs_df['pnl'] < 0]['pnl'].mean()
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')

            # Risk-Reward Analysis
            col_rr1, col_rr2, col_rr3, col_rr4 = st.columns(4)
            with col_rr1:
                st.metric("Max Profit", f"${max_profit:.2f}", delta_color="inverse" if max_profit > 0 else "normal")
            with col_rr2:
                st.metric("Max Loss", f"${max_loss:.2f}", delta_color="normal" if max_loss < 0 else "off")
            with col_rr3:
                st.metric("Avg Win", f"${avg_win:.2f}", delta_color="inverse")
            with col_rr4:
                st.metric("Avg Loss", f"${avg_loss:.2f}", delta_color="normal")

            # Additional Metrics
            col_add1, col_add2, col_add3, col_add4 = st.columns(4)
            with col_add1:
                st.metric("Profit Factor", f"{profit_factor:.2f}", delta_color="inverse" if profit_factor > 1 else "normal")
            with col_add2:
                # Calculate win/loss streaks
                pnl_series = (traded_recs_df['pnl'] > 0).astype(int)
                current_streak = 0
                max_win_streak = 0
                max_loss_streak = 0
                current_win_streak = 0
                current_loss_streak = 0

                for pnl in pnl_series:
                    if pnl == 1:  # Win
                        current_win_streak += 1
                        current_loss_streak = 0
                        max_win_streak = max(max_win_streak, current_win_streak)
                    else:  # Loss
                        current_loss_streak += 1
                        current_win_streak = 0
                        max_loss_streak = max(max_loss_streak, current_loss_streak)

                st.metric("Max Win Streak", f"{max_win_streak}", delta_color="inverse")
            with col_add3:
                st.metric("Max Loss Streak", f"{max_loss_streak}", delta_color="normal")
            with col_add4:
                # Calculate recommendation distribution
                rec_counts = traded_recs_df['recommendation'].value_counts()
                most_common_rec = str(rec_counts.index[0]) if not rec_counts.empty else "N/A"
                st.metric("Most Common Rec", most_common_rec.upper())

            # Confidence vs Outcome Analysis
            st.subheader("Confidence vs Outcome Analysis")

            # Create confidence bins
            confidence_bins = pd.cut(traded_recs_df['confidence_score'],
                                   bins=[0, 0.2, 0.4, 0.6, 0.8, 1.0],
                                   labels=['0-20%', '20-40%', '40-60%', '60-80%', '80-100%'])

            confidence_analysis = traded_recs_df.groupby(confidence_bins).agg({
                'pnl': ['count', 'sum', 'mean'],
                'is_profitable': 'sum'
            }).round(2)

            confidence_analysis.columns = ['Trades', 'Total PnL', 'Avg PnL', 'Wins']
            confidence_analysis['Win Rate'] = (confidence_analysis['Wins'] / confidence_analysis['Trades'] * 100).round(1)
            confidence_analysis['Win Rate'] = confidence_analysis['Win Rate'].fillna(0)

            # Display confidence analysis
            st.write("**Performance by Confidence Level:**")
            display_dataframe(confidence_analysis, caption="Trading performance across different confidence levels")

            # Recommendation Type Analysis
            st.subheader("Recommendation Type Analysis")

            if not traded_recs_df.empty:
                rec_analysis = traded_recs_df.groupby('recommendation').agg({
                    'pnl': ['count', 'sum', 'mean'],
                    'is_profitable': 'sum'
                }).round(2)

                rec_analysis.columns = ['Trades', 'Total PnL', 'Avg PnL', 'Wins']
                rec_analysis['Win Rate'] = (rec_analysis['Wins'] / rec_analysis['Trades'] * 100).round(1)
                rec_analysis['Win Rate'] = rec_analysis['Win Rate'].fillna(0)

                st.write("**Performance by Recommendation Type:**")
                display_dataframe(rec_analysis, caption="Trading performance by recommendation type (BUY/SELL/HOLD)")

                # Create recommendation comparison chart
                fig_rec = px.bar(rec_analysis.reset_index(),
                               x='recommendation',
                               y='Win Rate',
                               title='Win Rate by Recommendation Type',
                               color='Win Rate',
                               color_continuous_scale='RdYlGn')
                st.plotly_chart(fig_rec, use_container_width=True)

        # Recommendation Distribution
        st.subheader("Recommendation Distribution")
        recommendation_counts = filtered_df['recommendation'].value_counts().reset_index()
        recommendation_counts.columns = ['Recommendation', 'Count']
        create_pie_chart(recommendation_counts, names_col='Recommendation', values_col='Count', title='Distribution of Recommendations')

        # Outcome Distribution by Recommendation Type
        st.subheader("Outcome Distribution by Recommendation Type")
        if not traded_recs_df.empty:
            outcome_by_rec = traded_recs_df.groupby(['recommendation', 'outcome_category']).size().unstack(fill_value=0)
            st.bar_chart(outcome_by_rec)
        else:
            st.info("No traded recommendations to display outcome distribution.")

        # Performance by Symbol
        st.subheader("Performance by Symbol")
        if not traded_recs_df.empty:
            symbol_performance = traded_recs_df.groupby('symbol').agg(
                Total_PnL=('pnl', 'sum'),
                Total_Closed_Trades=('recommendation_id', 'count'), # Renamed for clarity
                Profitable_Count=('is_profitable', 'sum')
            ).reset_index()
            symbol_performance['Win_Rate'] = (symbol_performance['Profitable_Count'] / symbol_performance['Total_Closed_Trades'] * 100).fillna(0)
            display_dataframe(symbol_performance[['symbol', 'Total_PnL', 'Total_Closed_Trades', 'Win_Rate']].sort_values(by='Total_PnL', ascending=False), caption="Performance by Symbol")
        else:
            st.info("No traded recommendations to display performance by symbol.")

        # Performance by Timeframe
        st.subheader("Performance by Timeframe")
        if not traded_recs_df.empty:
            timeframe_performance = traded_recs_df.groupby('timeframe').agg(
                Total_PnL=('pnl', 'sum'),
                Total_Closed_Trades=('recommendation_id', 'count'), # Renamed for clarity
                Profitable_Count=('is_profitable', 'sum')
            ).reset_index()
            timeframe_performance['Win_Rate'] = (timeframe_performance['Profitable_Count'] / timeframe_performance['Total_Closed_Trades'] * 100).fillna(0)
            display_dataframe(timeframe_performance[['timeframe', 'Total_PnL', 'Total_Closed_Trades', 'Win_Rate']].sort_values(by='Total_PnL', ascending=False), caption="Performance by Timeframe")
        else:
            st.info("No traded recommendations to display performance by timeframe.")

        # Confidence Score Analysis - Distribution
        st.subheader("Confidence Score Distribution")
        if not traded_recs_df.empty:
            st.write("Distribution of Actual Confidence Scores for Traded Recommendations:")

            # Get unique confidence scores and their counts
            confidence_counts = traded_recs_df['confidence_score'].value_counts().sort_index().reset_index()
            confidence_counts.columns = ['Confidence_Score', 'Count']

            # Create full-width histogram with actual confidence values
            fig_conf = px.histogram(traded_recs_df,
                                  x='confidence_score',
                                  nbins=20,
                                  title='Confidence Score Distribution',
                                  labels={'confidence_score': 'Confidence Score', 'count': 'Number of Trades'},
                                  color_discrete_sequence=['lightblue'])
            fig_conf.add_vline(x=traded_recs_df['confidence_score'].mean(),
                             line_dash="dash",
                             line_color="red",
                             annotation_text=f"Mean: {traded_recs_df['confidence_score'].mean():.2f}")
            st.plotly_chart(fig_conf, use_container_width=True)

            # Show confidence statistics below the chart in horizontal layout
            st.write("**Confidence Statistics:**")
            col_stats1, col_stats2, col_stats3, col_stats4, col_stats5 = st.columns(5)
            with col_stats1:
                st.metric("Mean Confidence", f"{traded_recs_df['confidence_score'].mean():.2f}")
            with col_stats2:
                st.metric("Median Confidence", f"{traded_recs_df['confidence_score'].median():.2f}")
            with col_stats3:
                st.metric("Min Confidence", f"{traded_recs_df['confidence_score'].min():.2f}")
            with col_stats4:
                st.metric("Max Confidence", f"{traded_recs_df['confidence_score'].max():.2f}")
            with col_stats5:
                st.metric("Unique Values", f"{traded_recs_df['confidence_score'].nunique()}")

            # Show detailed breakdown table
            st.write("**Detailed Confidence Score Breakdown:**")
            display_dataframe(confidence_counts, caption="Count of trades for each unique confidence score")
        else:
            st.info("No traded recommendations to display confidence score distribution.")

        # Confidence vs. Outcome Scatter Plot
        st.subheader("Confidence vs. PnL by Outcome")
        if not traded_recs_df.empty:
            fig = px.scatter(traded_recs_df, 
                             x="confidence_score", 
                             y="pnl", 
                             color="outcome_category",
                             hover_data=['symbol', 'timeframe', 'recommendation_id'],
                             title="Confidence Score vs. PnL by Outcome Category",
                             labels={"confidence_score": "Confidence Score", "pnl": "Profit/Loss"})
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No traded recommendations to display confidence vs. PnL scatter plot.")

    with tab2:
        st.header("🔍 Individual Analysis Explorer")

        # Note: All filters are now consolidated in the sidebar for better user experience

        # Use the already filtered data from sidebar filters
        tab2_filtered_df = filtered_df.copy()
        tab2_filtered_outcomes = filtered_outcomes.copy()

        st.info(f"Showing {len(tab2_filtered_df)} of {len(filtered_df)} recommendations in this tab (from sidebar filters).")

        if not tab2_filtered_df.empty:
            selected_rec_id = st.selectbox("Select a Recommendation to View Details", tab2_filtered_df['recommendation_id'].tolist(), key="individual_explorer_select")
            
            selected_outcome = next((o for o in tab2_filtered_outcomes if o.recommendation_id == selected_rec_id), None)
            
            if selected_outcome:
                st.subheader(f"Details for Recommendation ID: {selected_rec_id}")
                
                # Display enhanced quick summary
                display_enhanced_quick_summary(selected_outcome, chart_viewer)

                # Re-introduce the original chart and analysis details sections
                col_chart, col_analysis = st.columns(2)
                
                with col_chart:
                    st.subheader("Original Chart")
                    db_image_path = selected_outcome.chart_path
                    if db_image_path:
                        if db_image_path.startswith('./'):
                            db_image_path = db_image_path[2:]
                        chart_filename = os.path.basename(db_image_path) + ".backup"
                        chart_viewer.display_chart_with_analysis(chart_filename, selected_outcome.analysis_data, selected_outcome.trade_data if selected_outcome.trade_data else None)
                    else:
                        st.info("No chart image path available for this recommendation.")
                
                with col_analysis:
                    st.subheader("AI Analysis Details")
                    # Access the nested 'analysis' dictionary
                    analysis_data = selected_outcome.analysis_data.get('analysis', {})
                    
                    st.markdown(f"**Recommendation:** {analysis_data.get('recommendation', 'N/A')}")
                    st.markdown(f"**Confidence:** {analysis_data.get('confidence', 'N/A'):.2f}")
                    st.markdown(f"**Summary:** {analysis_data.get('summary', 'N/A')}")
                    
                    st.markdown("---")
                    st.markdown("**Key Levels:**")
                    key_levels = analysis_data.get('key_levels', {})
                    st.markdown(f"  - Support: {key_levels.get('support', 'N/A')}")
                    st.markdown(f"  - Resistance: {key_levels.get('resistance', 'N/A')}")

                    st.markdown("---")
                    st.markdown("**Trade Parameters:**")
                    st.markdown(f"  - Entry Price: {analysis_data.get('entry_price', 'N/A')}")
                    st.markdown(f"  - Stop Loss: {analysis_data.get('stop_loss', 'N/A')}")
                    st.markdown(f"  - Take Profit: {analysis_data.get('take_profit', 'N/A')}")
                    st.markdown(f"  - Direction: {analysis_data.get('direction', 'N/A')}")

                    st.markdown("---")
                    st.markdown("**Risk Factors:**")
                    risk_factors = analysis_data.get('risk_factors', [])
                    if risk_factors:
                        for factor in risk_factors:
                            st.markdown(f"  - {factor}")
                    else:
                        st.markdown("  - N/A")

                    st.markdown("---")
                    st.markdown("**Market Conditions:**")
                    st.markdown(f"  - Condition: {analysis_data.get('market_condition', 'N/A')}")
                    st.markdown(f"  - Direction: {analysis_data.get('market_direction', 'N/A')}")
                    st.markdown(f"  - Evidence: {analysis_data.get('evidence', 'N/A')}")

                    # Inlined Trade Details from display_trade_correlation
                    st.markdown("---")
                    st.subheader("Trade Details")
                    if selected_outcome.trade_data:
                        trade_data = selected_outcome.trade_data
                        st.markdown(f"**Trade ID:** {trade_data.get('trade_id', 'N/A')}")
                        entry_price = trade_data.get('entry_price')
                        st.markdown(f"**Entry Price:** {entry_price:.2f}" if isinstance(entry_price, (int, float)) else f"**Entry Price:** {entry_price}")
                        
                        exit_price = trade_data.get('exit_price')
                        st.markdown(f"**Exit Price:** {exit_price:.2f}" if isinstance(exit_price, (int, float)) else f"**Exit Price:** {exit_price}")
                        
                        trade_pnl = trade_data.get('pnl')
                        st.markdown(f"**Trade PnL:** {trade_pnl:.2f}" if isinstance(trade_pnl, (int, float)) else f"**Trade PnL:** {trade_pnl}")
                        st.markdown(f"**Trade Duration (seconds):** {trade_data.get('duration_seconds', 'N/A')}")
                        st.markdown(f"**Trade Status:** {trade_data.get('status', 'N/A').replace('_', ' ').title()}")
                        st.markdown(f"**Trade Type:** {trade_data.get('trade_type', 'N/A').replace('_', ' ').title()}")
                    else:
                        st.info("No trade data available for this recommendation.")

                    st.markdown("---")
                    st.subheader("Raw Analysis Data (Full)")
                    display_json(selected_outcome.analysis_data) # Display the full raw data
                    
                    st.subheader("Trade Outcome Data")
                    if selected_outcome.trade_data:
                        display_json(selected_outcome.trade_data)
                    else:
                        st.info("No trade data for this recommendation.")
            else:
                st.info("Select a recommendation from the dropdown to view its details.")
        else:
            st.info("No recommendations available for detailed view based on current filters.")

    with tab3:
        st.header("📈 Backtest Analysis")

        st.markdown("""
        **Comprehensive Backtesting of All Analyzer Results**

        This section runs advanced backtests on all analyzer results stored in the database,
        using the enhanced backtest engine to compare performance across different prompt characteristics.
        """)

        # Backtest configuration
        st.subheader("Backtest Configuration")

        col_config1, col_config2, col_config3 = st.columns(3)

        with col_config1:
            days_back = st.slider("Days of Historical Data", min_value=7, max_value=365, value=30,
                                help="Number of days of analyzer results to include in the backtest")

        with col_config2:
            min_records = st.slider("Minimum Records per Group", min_value=5, max_value=50, value=10,
                                  help="Minimum number of records required for each prompt group")

        with col_config3:
            max_workers = st.slider("Parallel Workers", min_value=1, max_value=8, value=4,
                                  help="Number of parallel processes for running backtests")

        # Signal type filter
        signal_options = ['buy', 'sell', 'hold']
        selected_signals = st.multiselect(
            "Signal Types to Include",
            options=signal_options,
            default=['buy', 'sell'],
            help="Select which signal types to include in the backtest"
        )

        # Run backtest button
        if st.button("🚀 Run Comprehensive Backtest", type="primary", use_container_width=True):
            with st.spinner("Running backtest analysis... This may take several minutes."):
                progress_bar = st.progress(0)
                status_text = st.empty()

                # Run the backtest
                results = backtest_runner.run_full_backtest(
                    days_back=days_back,
                    min_records_per_group=min_records,
                    max_workers=max_workers,
                    signal_filter=selected_signals
                )

                if 'error' in results:
                    st.error(f"Backtest failed: {results['error']}")
                else:
                    st.success("✓ Backtest completed successfully!")

                    # Display summary metrics
                    st.subheader("📊 Backtest Summary")

                    summary = results.get('summary', {})
                    col_sum1, col_sum2, col_sum3, col_sum4 = st.columns(4)

                    with col_sum1:
                        st.metric("Groups Tested", summary.get('total_groups_tested', 0))
                    with col_sum2:
                        st.metric("Simulated Trades", summary.get('total_simulated_trades', 0))
                    with col_sum3:
                        st.metric("Analysis Period", f"{summary.get('analysis_period_days', 0)} days")
                    with col_sum4:
                        st.metric("Data Quality", f"{summary.get('data_quality_score', 0):.1f}")

                    # Best performing group
                    if summary.get('best_performing_group'):
                        st.info(f"🏆 **Best Performing Group:** {summary['best_performing_group']}")

                    # Key insights
                    if summary.get('key_insights'):
                        st.subheader("🔑 Key Insights")
                        for insight in summary['key_insights']:
                            st.write(f"• {insight}")

                    # Performance analysis
                    st.subheader("📈 Performance Rankings")

                    performance_analysis = results.get('performance_analysis', {})

                    # Top performers by win rate
                    if 'best_performers' in performance_analysis and 'win_rate' in performance_analysis['best_performers']:
                        st.write("**Top Groups by Win Rate:**")
                        for i, performer in enumerate(performance_analysis['best_performers']['win_rate'][:5], 1):
                            st.write(f"{i}. **{performer['full_name']}**: {performer['win_rate']:.1f}%")

                    # Top performers by PnL
                    if 'best_performers' in performance_analysis and 'total_pnl_percent' in performance_analysis['best_performers']:
                        st.write("**Top Groups by Total PnL:**")
                        for i, performer in enumerate(performance_analysis['best_performers']['total_pnl_percent'][:5], 1):
                            st.write(f"{i}. **{performer['full_name']}**: {performer['total_pnl_percent']:.2f}%")

                    # Detailed results table
                    st.subheader("📋 Detailed Performance Table")

                    performance_table = backtest_runner.get_performance_summary_table(results)
                    if not performance_table.empty:
                        display_dataframe(performance_table, caption="Performance metrics for all prompt groups")

                        # Prompt Viewer Section
                        st.markdown("---")
                        st.subheader("🔍 Prompt Viewer")

                        st.info("💡 **Select a prompt signature below to view its content**")
                    else:
                        st.info("No performance data available. Click '🚀 Run Comprehensive Backtest' above to generate results and view prompts.")

                        # Show basic prompt viewer even without backtest results
                        st.markdown("---")
                        st.subheader("🔍 Available Prompts in Database")

                        try:
                            # Try to load some prompts directly from database
                            conn = db_connector.get_connection()
                            cursor = conn.cursor()

                            # Get recent analysis records with prompts
                            cursor.execute('''
                                SELECT analysis_prompt, COUNT(*) as record_count
                                FROM analysis_results
                                WHERE analysis_prompt IS NOT NULL
                                AND analysis_prompt != ''
                                GROUP BY analysis_prompt
                                ORDER BY record_count DESC
                                LIMIT 10
                            ''')

                            prompt_records = cursor.fetchall()
                            conn.close()

                            if prompt_records:
                                st.info(f"Found {len(prompt_records)} different prompts in the database:")

                                # Create dropdown for database prompts
                                prompt_options = []
                                prompt_data = []

                                for i, (prompt_text, count) in enumerate(prompt_records):
                                    preview = prompt_text[:50] + "..." if len(prompt_text) > 50 else prompt_text
                                    prompt_options.append(f"Prompt {i+1} ({count} records) - {preview}")
                                    prompt_data.append({
                                        'text': prompt_text,
                                        'count': count,
                                        'index': i
                                    })

                                selected_db_prompt = st.selectbox(
                                    "Select a prompt from database:",
                                    options=list(range(len(prompt_options))),
                                    format_func=lambda x: prompt_options[x],
                                    key="db_prompt_selector"
                                )

                                if selected_db_prompt is not None:
                                    selected_data = prompt_data[selected_db_prompt]

                                    st.metric("Records Using This Prompt", selected_data['count'])

                                    # Display prompt
                                    prompt_text = selected_data['text']
                                    try:
                                        if prompt_text.strip().startswith('{') or prompt_text.strip().startswith('['):
                                            parsed_json = json.loads(prompt_text)
                                            st.json(parsed_json)
                                        else:
                                            st.code(prompt_text, language='text')
                                    except (json.JSONDecodeError, TypeError):
                                        st.code(prompt_text, language='text')

                                    # Copy section
                                    with st.expander("📋 Copy Full Prompt"):
                                        st.text_area("Full prompt text:", prompt_text, height=200, key="db_prompt_copy")
                            else:
                                st.warning("No prompts found in the database.")

                        except Exception as e:
                            st.error(f"Error loading prompts from database: {e}")

                        # Don't show the backtest-based prompt viewer if no results
                        st.stop()

                    # Continue with backtest-based prompt viewer
                    st.info("💡 **Select a prompt signature below to view its content**")

                    # Create dropdown with all available prompts
                    # Cache prompts in session state to avoid reloading
                    cache_key = "cached_available_prompts"
                    if cache_key not in st.session_state or not st.session_state[cache_key]:
                        available_prompts = []
                    else:
                        available_prompts = st.session_state[cache_key]
                        st.info("💾 Using cached prompts for faster loading")

                    # Only rebuild cache if we don't have prompts
                    if not available_prompts:
                        available_prompts = []

                    # Debug: Show available data structure
                    with st.expander("🔍 Debug Info (Click to expand)"):
                        st.write(f"Results keys: {list(results.keys()) if results else 'No results'}")

                        # Try multiple locations for prompt data
                        prompt_sources = [
                            ('extraction_result', lambda r: r.get('extraction_result', {}).get('grouped_data', {})),
                            ('backtest_results', lambda r: r.get('backtest_results', {})),
                            ('raw_results', lambda r: r.get('raw_results', {}))
                        ]

                        for source_name, get_data in prompt_sources:
                            if source_name in results:
                                st.write(f"Found {source_name} in results")
                                data = get_data(results)
                                if data:
                                    st.write(f"{source_name} structure: {list(data.keys())[:5]}...")  # Show first 5 keys

                    # Get ALL groups from the performance table first
                    all_groups_in_table = set()
                    if not performance_table.empty:
                        for _, row in performance_table.iterrows():
                            group_key = f"{row['Group Type']}.{row['Group Name']}"
                            all_groups_in_table.add((row['Group Type'], row['Group Name'], group_key))

                    st.write(f"**Found {len(all_groups_in_table)} groups in performance table**")

                    # Now try to find prompts for ALL groups in the performance table
                    found_prompts = False

                    # Method 1: extraction_result -> grouped_data (for all groups in table)
                    if 'extraction_result' in results and 'grouped_data' in results['extraction_result']:
                        grouped_data = results['extraction_result']['grouped_data']
                        for group_type, group_name, group_key in all_groups_in_table:
                            if group_type in grouped_data and group_name in grouped_data[group_type]:
                                records = grouped_data[group_type][group_name]
                                if records and isinstance(records, list):
                                    sample_record = records[0]
                                    prompt_text = sample_record.get('analysis_prompt', '')
                                    if prompt_text:
                                        available_prompts.append({
                                            'label': f"{group_type}.{group_name} ({len(records)} records)",
                                            'value': f"{group_type}.{group_name}",
                                            'prompt': prompt_text,
                                            'record_count': len(records),
                                            'group_type': group_type,
                                            'group_name': group_name
                                        })
                                        found_prompts = True
                                    else:
                                        # Add entry even if no prompt found
                                        available_prompts.append({
                                            'label': f"{group_type}.{group_name} (No prompt available)",
                                            'value': f"{group_type}.{group_name}",
                                            'prompt': "No prompt data available for this group",
                                            'record_count': len(records) if records else 0,
                                            'group_type': group_type,
                                            'group_name': group_name
                                        })
                                        found_prompts = True

                    # Method 2: Try backtest_results structure for remaining groups
                    if 'backtest_results' in results:
                        backtest_data = results['backtest_results']
                        for group_type, group_name, group_key in all_groups_in_table:
                            # Check if we already have this group
                            if not any(p['value'] == f"{group_type}.{group_name}" for p in available_prompts):
                                if group_type in backtest_data and group_name in backtest_data[group_type]:
                                    group_info = backtest_data[group_type][group_name]
                                    # Look for trades or records in group_info
                                    if 'trades' in group_info and group_info['trades']:
                                        sample_trade = group_info['trades'][0]
                                        prompt_text = sample_trade.get('analysis_prompt', '')
                                        if prompt_text:
                                            available_prompts.append({
                                                'label': f"{group_type}.{group_name} ({len(group_info['trades'])} trades)",
                                                'value': f"{group_type}.{group_name}",
                                                'prompt': prompt_text,
                                                'record_count': len(group_info['trades']),
                                                'group_type': group_type,
                                                'group_name': group_name
                                            })
                                            found_prompts = True
                                        else:
                                            available_prompts.append({
                                                'label': f"{group_type}.{group_name} (No prompt in trades)",
                                                'value': f"{group_type}.{group_name}",
                                                'prompt': "No prompt data available in trade records",
                                                'record_count': len(group_info['trades']),
                                                'group_type': group_type,
                                                'group_name': group_name
                                            })
                                            found_prompts = True

                    # Method 3: For any remaining groups, try database lookup
                    for group_type, group_name, group_key in all_groups_in_table:
                        if not any(p['value'] == f"{group_type}.{group_name}" for p in available_prompts):
                            try:
                                conn = db_connector.get_connection()
                                cursor = conn.cursor()
                                # Try to find prompts that might be associated with this group
                                cursor.execute('''
                                    SELECT analysis_prompt, COUNT(*) as count
                                    FROM analysis_results
                                    WHERE analysis_prompt IS NOT NULL AND analysis_prompt != ''
                                    GROUP BY analysis_prompt
                                    ORDER BY count DESC
                                    LIMIT 1
                                ''')
                                db_result = cursor.fetchone()
                                conn.close()

                                if db_result:
                                    prompt_text, count = db_result
                                    available_prompts.append({
                                        'label': f"{group_type}.{group_name} (DB lookup - {count} records)",
                                        'value': f"{group_type}.{group_name}",
                                        'prompt': prompt_text,
                                        'record_count': count,
                                        'group_type': group_type,
                                        'group_name': group_name
                                    })
                                    found_prompts = True
                                else:
                                    # Add placeholder entry
                                    available_prompts.append({
                                        'label': f"{group_type}.{group_name} (No prompt found)",
                                        'value': f"{group_type}.{group_name}",
                                        'prompt': "Unable to locate prompt data for this group",
                                        'record_count': 0,
                                        'group_type': group_type,
                                        'group_name': group_name
                                    })
                                    found_prompts = True
                            except Exception as e:
                                st.error(f"Database lookup error for {group_key}: {e}")

                    st.write(f"**Total prompts available for viewing: {len(available_prompts)}**")

                    # Cache the prompts for faster subsequent loads
                    if available_prompts:
                        st.session_state[cache_key] = available_prompts

                    if available_prompts:
                        # Create options for selectbox
                        prompt_options = [f"{p['label']} - {p['prompt'][:50]}..." for p in available_prompts]
                        prompt_values = [p['value'] for p in available_prompts]

                        # Use session state for dynamic updates without full page reload
                        if 'selected_prompt_idx' not in st.session_state:
                            st.session_state.selected_prompt_idx = 0

                        # Create a container for dynamic content
                        prompt_display_container = st.empty()

                        # Selection controls in columns
                        col_select, col_nav = st.columns([3, 1])

                        with col_select:
                            selected_prompt_idx = st.selectbox(
                                "Select a prompt signature to view:",
                                options=list(range(len(prompt_options))),
                                format_func=lambda x: prompt_options[x],
                                key="prompt_selector",
                                index=st.session_state.selected_prompt_idx
                            )

                            # Update session state when selection changes
                            if selected_prompt_idx != st.session_state.selected_prompt_idx:
                                st.session_state.selected_prompt_idx = selected_prompt_idx
                                st.rerun()  # Only rerun when selection actually changes

                        with col_nav:
                            # Quick navigation buttons
                            if st.button("⬅️ Previous", key="prev_prompt", disabled=selected_prompt_idx <= 0):
                                st.session_state.selected_prompt_idx = max(0, selected_prompt_idx - 1)
                                st.rerun()

                            if st.button("Next ➡️", key="next_prompt", disabled=selected_prompt_idx >= len(available_prompts) - 1):
                                st.session_state.selected_prompt_idx = min(len(available_prompts) - 1, selected_prompt_idx + 1)
                                st.rerun()

                        # Display selected prompt content dynamically
                        if selected_prompt_idx is not None and selected_prompt_idx < len(available_prompts):
                            selected_prompt_data = available_prompts[selected_prompt_idx]

                            # Loading state for smooth transitions
                            with st.spinner("Loading prompt details..."):
                                import time
                                time.sleep(0.1)  # Brief pause for smooth transition

                            # Display prompt details in a clean layout
                            st.markdown("---")

                            # Header with key info
                            col_info1, col_info2, col_info3, col_info4 = st.columns(4)
                            with col_info1:
                                st.metric("Group Type", selected_prompt_data['group_type'])
                            with col_info2:
                                st.metric("Group Name", selected_prompt_data['group_name'])
                            with col_info3:
                                st.metric("Records", selected_prompt_data['record_count'])
                            with col_info4:
                                st.metric("Prompt Length", len(selected_prompt_data['prompt']))

                            # Display prompt content
                            st.subheader("📝 Prompt Content")

                            prompt_text = selected_prompt_data['prompt']

                            # Create a scrollable container for the prompt
                            with st.container():
                                # Try to parse as JSON for better formatting
                                try:
                                    # Check if it looks like JSON
                                    if prompt_text.strip().startswith('{') or prompt_text.strip().startswith('['):
                                        parsed_json = json.loads(prompt_text)
                                        st.json(parsed_json)
                                    else:
                                        st.code(prompt_text, language='text')
                                except (json.JSONDecodeError, TypeError):
                                    # If not JSON, display as text
                                    st.code(prompt_text, language='text')

                            # Metadata and actions
                            col_meta1, col_meta2 = st.columns([2, 1])

                            with col_meta1:
                                st.caption(f"**Full Signature:** {selected_prompt_data['value']} | **Total Records:** {selected_prompt_data['record_count']}")

                            with col_meta2:
                                # Quick stats
                                has_json = prompt_text.strip().startswith('{') or prompt_text.strip().startswith('[')
                                st.caption(f"**Format:** {'JSON' if has_json else 'Text'} | **Lines:** {len(prompt_text.splitlines())}")

                            # Copy functionality
                            with st.expander("📋 Copy Full Prompt"):
                                st.text_area(
                                    "Full prompt text (for copying):",
                                    prompt_text,
                                    height=min(300, len(prompt_text.splitlines()) * 20 + 50),  # Dynamic height
                                    key=f"dropdown_prompt_copy_{selected_prompt_data['value']}"
                                )

                            # Quick actions
                            col_actions1, col_actions2, col_actions3 = st.columns(3)
                            with col_actions1:
                                if st.button("📋 Copy to Clipboard", key=f"copy_{selected_prompt_idx}"):
                                    # Using JavaScript-like functionality through Streamlit
                                    st.code(prompt_text, language="text")
                                    st.success("Prompt displayed above - you can copy it manually")

                            with col_actions2:
                                if st.button("🔍 View Raw", key=f"raw_{selected_prompt_idx}"):
                                    st.text(prompt_text)

                            with col_actions3:
                                # Show prompt statistics
                                word_count = len(prompt_text.split())
                                char_count = len(prompt_text)
                                st.metric("Words", word_count)
                                st.metric("Characters", char_count)
                    else:
                        st.info("No prompt data available for display.")
                    # else:
                    #     st.info("No performance data available to display.")

                    # Group characteristics
                    st.subheader("🎯 Group Characteristics")

                    backtest_results = results.get('backtest_results', {})
                    if backtest_results:
                        # Show details for top 3 groups
                        top_groups = backtest_runner.get_top_performers(results, 'win_rate', 3)

                        if not top_groups.empty:
                            for _, group in top_groups.iterrows():
                                with st.expander(f"📊 {group['Group Type']}.{group['Group Name']} Details"):
                                    group_key = f"{group['Group Type']}.{group['Group Name']}"
                                    group_data = backtest_results.get(group['Group Type'], {}).get(group['Group Name'], {})

                                    if 'group_characteristics' in group_data:
                                        chars = group_data['group_characteristics']
                                        col_char1, col_char2, col_char3 = st.columns(3)

                                        with col_char1:
                                            st.metric("Avg Confidence", f"{chars.get('avg_confidence', 0):.2f}")
                                        with col_char2:
                                            st.metric("Avg RR Ratio", f"{chars.get('avg_rr_ratio', 0):.2f}")
                                        with col_char3:
                                            st.metric("Unique Symbols", chars.get('unique_symbols', 0))

                                        st.write(f"**Date Range:** {chars.get('date_range', {}).get('start', 'N/A')} to {chars.get('date_range', {}).get('end', 'N/A')}")

                                    # NEW: Display the actual prompt text for this group
                                    st.markdown("---")
                                    st.subheader("🔍 Prompt Used for This Group")

                                    # Get prompt from multiple possible locations
                                    prompt_text = "No prompt available"
                                    record_count = 0

                                    # Method 1: extraction_result -> grouped_data
                                    if 'extraction_result' in results and 'grouped_data' in results['extraction_result']:
                                        grouped_data = results['extraction_result']['grouped_data']
                                        if group['Group Type'] in grouped_data and group['Group Name'] in grouped_data[group['Group Type']]:
                                            records = grouped_data[group['Group Type']][group['Group Name']]
                                            if records and isinstance(records, list):
                                                sample_record = records[0]  # Get first record as sample
                                                prompt_text = sample_record.get('analysis_prompt', 'No prompt available')
                                                record_count = len(records)

                                    # Method 2: Try backtest_results structure
                                    if prompt_text == "No prompt available" and 'backtest_results' in results:
                                        backtest_data = results['backtest_results']
                                        if (group['Group Type'] in backtest_data and
                                            group['Group Name'] in backtest_data[group['Group Type']]):
                                            group_info = backtest_data[group['Group Type']][group['Group Name']]
                                            if 'trades' in group_info and group_info['trades']:
                                                sample_trade = group_info['trades'][0]
                                                prompt_text = sample_trade.get('analysis_prompt', 'No prompt available')
                                                record_count = len(group_info['trades'])

                                    # Method 3: Try raw database lookup as fallback
                                    if prompt_text == "No prompt available":
                                        try:
                                            conn = db_connector.get_connection()
                                            cursor = conn.cursor()
                                            cursor.execute('''
                                                SELECT analysis_prompt, COUNT(*) as count
                                                FROM analysis_results
                                                WHERE analysis_prompt IS NOT NULL AND analysis_prompt != ''
                                                GROUP BY analysis_prompt
                                                ORDER BY count DESC
                                                LIMIT 1
                                            ''')
                                            db_result = cursor.fetchone()
                                            conn.close()
                                            if db_result:
                                                prompt_text = db_result[0]
                                                record_count = db_result[1]
                                        except Exception as e:
                                            st.warning(f"Could not retrieve prompt from database: {e}")

                                    # Display prompt in a code block for readability
                                    if prompt_text != "No prompt available":
                                        st.code(prompt_text, language='text')
                                        st.caption(f"Prompt length: {len(prompt_text)} characters | Based on {record_count} analysis records")

                                        # Option to show full prompt if truncated
                                        if len(prompt_text) > 1000:
                                            with st.expander("Show Full Prompt"):
                                                st.text_area("Full Prompt Text", prompt_text, height=300, disabled=True, key=f"expander_full_prompt_{group_key}")
                                    else:
                                        st.warning("No prompt data available for this group")

                    # Data quality validation
                    st.subheader("🔍 Data Quality Validation")

                    validation_results = results.get('validation_results', {})
                    if validation_results:
                        col_qual1, col_qual2, col_qual3 = st.columns(3)

                        with col_qual1:
                            st.metric("Total Groups", validation_results.get('total_groups', 0))
                        with col_qual2:
                            st.metric("Total Records", validation_results.get('total_records', 0))
                        with col_qual3:
                            issues = validation_results.get('quality_issues', [])
                            st.metric("Quality Issues", len(issues))

                        if issues:
                            with st.expander("View Quality Issues"):
                                for issue in issues[:10]:  # Show first 10 issues
                                    st.write(f"• {issue}")
                                if len(issues) > 10:
                                    st.write(f"... and {len(issues) - 10} more issues")

                    # Extraction summary
                    st.subheader("📊 Data Extraction Summary")

                    extraction_summary = results.get('extraction_summary', {})
                    if extraction_summary:
                        col_ext1, col_ext2, col_ext3 = st.columns(3)

                        with col_ext1:
                            st.metric("Total Prompts", extraction_summary.get('total_prompt_signatures', 0))
                        with col_ext2:
                            groups_created = extraction_summary.get('groups_created', {})
                            st.metric("Groups Created", sum(groups_created.values()))
                        with col_ext3:
                            records_per_group = extraction_summary.get('records_per_group', {})
                            avg_records = sum(sum(g.values()) for g in records_per_group.values()) / max(1, sum(len(g) for g in records_per_group.values()))
                            st.metric("Avg Records/Group", f"{avg_records:.1f}")

        else:
            st.info("👆 Configure your backtest parameters above and click 'Run Comprehensive Backtest' to start the analysis.")

            # Show some sample statistics from the database
            st.subheader("📊 Database Overview")

            try:
                total_records = backtest_runner._get_total_analysis_records(30)
                st.metric("Total Analysis Records (30 days)", total_records)

                if total_records > 0:
                    st.success("✓ Database contains analyzer results ready for backtesting!")
                else:
                    st.warning("⚠️ No recent analyzer results found. The trading bot needs to generate analyses first.")

            except Exception as e:
                st.error(f"Error accessing database: {e}")

if __name__ == "__main__":
    main()
