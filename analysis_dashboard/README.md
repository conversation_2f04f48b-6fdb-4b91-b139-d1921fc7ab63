PYTHONPATH=$PYTHONPATH:. streamlit run analysis_dashboard/main.py


# AI Analyzer Performance Dashboard

This is a standalone Streamlit dashboard designed to analyze the performance of the AI chart analyzer and provide insights for improving its prompt. It connects directly to the trading bot's SQLite database to retrieve analysis results and trade outcomes, and visualizes the original chart screenshots.

## Features

- **Overall Performance Metrics**: View total analyses, traded recommendations, profitable trades, and total PnL.
- **Recommendation Outcome Distribution**: See a breakdown of profitable, losing, no-trade, and pending recommendations.
- **Confidence vs. Outcome Analysis**: Analyze the correlation between AI confidence scores and actual trade outcomes.
- **Analysis Quality & Prompt Effectiveness**:
    - Identify frequently missing fields in AI analyses.
    - Generate specific suggestions for improving the AI analyzer prompt.
    - Analyze analysis quality across different timeframes.
- **Chart Viewer & Trade Details**:
    - Select individual recommendations to view detailed AI analysis.
    - Display the original chart screenshot that the AI analyzed.
    - View associated trade data (PnL, status, entry/exit prices) for each recommendation.

## Setup and Installation

1.  **Clone the Repository**: If you haven't already, clone the main trading bot repository. This dashboard is designed to run alongside it, accessing its database and chart files.

    ```bash
    git clone [repository_url]
    cd [repository_name]
    ```

2.  **Navigate to the Dashboard Directory**:

    ```bash
    cd analysis_dashboard
    ```

3.  **Create a Virtual Environment (Recommended)**:

    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows: .\venv\Scripts\activate
    ```

4.  **Install Dependencies**:

    ```bash
    pip install -r requirements.txt
    ```

5.  **Configure Database and Chart Paths**:
    The dashboard expects the trading bot's database and chart backup files to be in specific relative locations. Verify and, if necessary, adjust the paths in `config.py`:

    ```python
    # analysis_dashboard/config.py
    DATABASE_PATH = 'trading_bot/data/analysis_results.db'
    CHARTS_BACKUP_PATH = 'trading_bot/data/charts/.backup/'
    ```
    These paths are relative to the root of the main trading bot repository (where `analysis_dashboard` folder is located).

## Running the Dashboard

From the `analysis_dashboard` directory (after activating your virtual environment and installing dependencies):

```bash
streamlit run main.py
```

This will open the dashboard in your web browser.

## Project Structure

```
analysis_dashboard/
├── main.py                    # Main Streamlit dashboard application
├── data_parser.py            # Analysis data parsing and insight extraction utilities
├── outcome_tracker.py        # Trade outcome tracking and success rate analysis
├── chart_viewer.py           # Chart screenshot display and management utilities
├── prompt_analyzer.py        # Prompt effectiveness analysis and improvement suggestions
├── dashboard_utils.py        # Shared utilities for dashboard components
├── database_connector.py     # Direct SQLite database connection utilities
├── requirements.txt          # Dashboard-specific dependencies
├── config.py                 # Dashboard configuration settings
└── README.md                 # This file
```

## Contributing

Contributions are welcome! Please follow the standard fork-and-pull-request workflow.

## License

[Specify your license here, e.g., MIT, Apache 2.0]
