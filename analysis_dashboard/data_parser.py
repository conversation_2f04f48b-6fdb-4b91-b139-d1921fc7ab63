import json
from typing import Dict, Any, List
from dataclasses import dataclass

@dataclass
class AnalysisInsight:
    symbol: str
    timeframe: str
    timestamp: str
    confidence_score: float
    recommendation: str
    quality_indicators: Dict[str, Any]
    prompt_effectiveness: float

class AnalysisDataParser:
    """Main parser for analysis_data JSON field"""
    
    def __init__(self):
        self.quality_thresholds = self._load_quality_thresholds()
    
    def _load_quality_thresholds(self):
        # Placeholder for loading quality thresholds from config or a file
        return {
            "completeness": 0.8,
            "consistency": 0.7,
            "actionability": 0.7
        }

    def parse_single_analysis(self, analysis_record: Dict) -> AnalysisInsight:
        """Parse single analysis record into structured insight"""
        analysis_data_raw = analysis_record.get('analysis_data', {})
        
        # Ensure analysis_data_raw is a dictionary, not a JSON string
        if isinstance(analysis_data_raw, str):
            try:
                analysis_data_raw = json.loads(analysis_data_raw)
            except json.JSONDecodeError:
                analysis_data_raw = {} # Fallback to empty dict if parsing fails

        # Check if the actual analysis is nested under an 'analysis' key
        # This handles cases where the LLM output is wrapped
        parsed_analysis = analysis_data_raw.get('analysis', analysis_data_raw)

        confidence = parsed_analysis.get('confidence', 0.0)
        recommendation = parsed_analysis.get('recommendation', 'hold')
        
        quality_score = self.calculate_quality_score(parsed_analysis)
        
        return AnalysisInsight(
            symbol=analysis_record.get('symbol', 'UNKNOWN'),
            timeframe=analysis_record.get('timeframe', 'UNKNOWN'),
            timestamp=analysis_record.get('timestamp', 'UNKNOWN'),
            confidence_score=confidence,
            recommendation=recommendation,
            quality_indicators={
                "completeness_score": self.calculate_analysis_completeness(parsed_analysis),
                "consistency_score": self.calculate_logical_consistency(parsed_analysis),
                "actionability_score": self.calculate_actionability_score(parsed_analysis),
                "overall_quality_score": quality_score
            },
            prompt_effectiveness=quality_score # Initial simple mapping
        )
    
    def calculate_quality_score(self, analysis: Dict) -> float:
        """Calculate overall quality score for analysis"""
        completeness = self.calculate_analysis_completeness(analysis)
        consistency = self.calculate_logical_consistency(analysis)
        actionability = self.calculate_actionability_score(analysis)
        
        # Simple weighted average for now
        return (completeness * 0.4) + (consistency * 0.3) + (actionability * 0.3)
    
    def extract_prompt_issues(self, analysis: Dict) -> List[str]:
        """Identify specific issues with prompt effectiveness"""
        issues = []
        if not analysis.get('summary'):
            issues.append("Missing summary")
        if not analysis.get('key_levels', {}).get('support') or not analysis.get('key_levels', {}).get('resistance'):
            issues.append("Missing key levels")
        if not analysis.get('entry_price') or not analysis.get('stop_loss') or not analysis.get('take_profit'):
            issues.append("Missing trade parameters")
        if not analysis.get('risk_factors'):
            issues.append("Missing risk factors")
        return issues

    def calculate_analysis_completeness(self, analysis: Dict) -> float:
        """Calculate completeness score for analysis"""
        required_fields = ['summary', 'key_levels', 'entry_price', 'stop_loss', 'take_profit', 'risk_factors', 'confidence', 'recommendation']
        present_fields = 0
        for field in required_fields:
            if field == 'key_levels':
                if analysis.get('key_levels', {}).get('support') is not None and analysis.get('key_levels', {}).get('resistance') is not None:
                    present_fields += 1
            elif analysis.get(field) is not None and analysis.get(field) != '':
                present_fields += 1
        return present_fields / len(required_fields)
    
    def calculate_logical_consistency(self, analysis: Dict) -> float:
        """Calculate logical consistency score"""
        # This is a placeholder. Real consistency would require deeper NLP or domain logic.
        # For now, check basic consistency like SL/TP relative to entry and direction.
        entry = analysis.get('entry_price')
        sl = analysis.get('stop_loss')
        tp = analysis.get('take_profit')
        direction = (analysis.get('direction') or '').upper()
        
        if entry is not None and sl is not None and tp is not None:
            if direction == 'LONG':
                if sl >= entry or tp <= entry: # SL should be below entry, TP above
                    return 0.0
            elif direction == 'SHORT':
                if sl <= entry or tp >= entry: # SL should be above entry, TP below
                    return 0.0
        return 1.0 # Assume consistent if no obvious issues or data missing
    
    def calculate_actionability_score(self, analysis: Dict) -> float:
        """Calculate how actionable the analysis is"""
        # Actionability depends on presence of key trade parameters
        if analysis.get('entry_price') is not None and analysis.get('stop_loss') is not None and analysis.get('take_profit') is not None and analysis.get('recommendation') != 'hold':
            return 1.0
        return 0.0

class ConfidenceAnalyzer:
    """Analyzer for confidence-related metrics and patterns"""
    
    def analyze_confidence_calibration(self, insights: List[AnalysisInsight]) -> Dict[str, float]:
        """Analyze how well confidence scores correlate with actual outcomes"""
        # This will require linking with trade outcomes, implemented in outcome_tracker
        return {"correlation": 0.0, "bias": 0.0}
    
    def identify_overconfident_patterns(self, insights: List[AnalysisInsight]) -> List[Dict]:
        """Identify patterns where AI is overconfident"""
        # This will require linking with trade outcomes
        return []
    
    def suggest_confidence_improvements(self, insights: List[AnalysisInsight]) -> List[str]:
        """Suggest improvements for confidence calibration"""
        # This will require linking with trade outcomes
        return []

def parse_analysis_data_batch(analysis_records: List[Dict]) -> List[AnalysisInsight]:
    """Parse multiple analysis records for batch processing"""
    parser = AnalysisDataParser()
    return [parser.parse_single_analysis(record) for record in analysis_records]

def calculate_confidence_trends(insights: List[AnalysisInsight]) -> Dict[str, List[float]]:
    """Calculate confidence trends over time by symbol/timeframe"""
    # This is a placeholder. Requires more complex time-series analysis.
    trends = {}
    for insight in insights:
        key = f"{insight.symbol}-{insight.timeframe}"
        if key not in trends:
            trends[key] = []
        trends[key].append(insight.confidence_score)
    return trends

def identify_low_quality_analyses(insights: List[AnalysisInsight], threshold: float = 0.6) -> List[AnalysisInsight]:
    """Identify analyses with quality issues for prompt improvement"""
    return [insight for insight in insights if insight.quality_indicators.get("overall_quality_score", 0) < threshold]

def extract_missing_fields_pattern(analysis_records: List[Dict]) -> Dict[str, float]:
    """Identify patterns in missing or incomplete analysis fields"""
    parser = AnalysisDataParser()
    field_counts = {}
    total_analyses = len(analysis_records)
    
    for record in analysis_records:
        analysis_data = record.get('analysis_data', {})
        if isinstance(analysis_data, str):
            try:
                analysis_data = json.loads(analysis_data)
            except json.JSONDecodeError:
                analysis_data = {}
        
        issues = parser.extract_prompt_issues(analysis_data)
        for issue in issues:
            field_counts[issue] = field_counts.get(issue, 0) + 1
            
    return {issue: count / total_analyses for issue, count in field_counts.items()}

def analyze_recommendation_consistency(insights: List[AnalysisInsight]) -> Dict[str, Any]:
    """Analyze consistency of recommendations across similar market conditions"""
    # This is a placeholder. Requires market condition data and more complex logic.
    return {"consistency_score": 0.0}
