import os
from typing import Dict, Any, List, Optional
from PIL import Image
import streamlit as st
from datetime import datetime
import pandas as pd
from analysis_dashboard.outcome_tracker import RecommendationOutcome

class ChartViewer:
    """Manages chart screenshot display and analysis overlay"""
    
    def __init__(self, charts_backup_path: str = "trading_bot/data/charts/.backup/"):
        self.charts_path = charts_backup_path
        self.chart_cache = {}
    
    def load_chart(self, chart_filename: str) -> Optional[Image.Image]:
        """Load chart from .png.backup file with caching"""
        full_path = os.path.join(self.charts_path, chart_filename)
        
        if full_path in self.chart_cache:
            return self.chart_cache[full_path]
            
        if not os.path.exists(full_path):
            st.warning(f"Chart file not found: {full_path}")
            return None
            
        try:
            img = Image.open(full_path)
            self.chart_cache[full_path] = img
            return img
        except Exception as e:
            st.error(f"Error loading chart {full_path}: {e}")
            return None
    
    def find_chart_for_analysis(self, symbol: str, timeframe: str, timestamp: str) -> Optional[str]:
        """Find chart file matching analysis parameters"""
        # The timestamp in the DB is typically ISO format, but the filename uses a specific format.
        # Need to convert timestamp to match filename format: YYYYMMDD_HHMMSS
        
        # Example filename: 1000PEPEUSDT.P_15m_20250808_164741.png.backup
        
        # Extract date and time from timestamp (using pandas for flexible parsing)
        try:
            dt_object = pd.to_datetime(timestamp, errors='coerce')
            if pd.isna(dt_object):
                st.warning(f"Could not parse timestamp for chart lookup: {timestamp}")
                return None
            formatted_timestamp = dt_object.strftime('%Y%m%d_%H%M%S')
        except Exception as e:
            st.warning(f"Error parsing timestamp for chart lookup: {timestamp}, {e}")
            return None
            
        # Construct potential filename pattern
        # Note: The '.P_' in the filename seems to be a constant.
        # If symbol already ends with .P, don't add another .P
        if symbol.endswith('.P'):
            prefix = f"{symbol}_{timeframe}_"
        else:
            prefix = f"{symbol}.P_{timeframe}_"

        # List files in the backup directory and find a match
        for filename in os.listdir(self.charts_path):
            if filename.startswith(prefix) and (filename.endswith(".png") or filename.endswith(".png.backup")):
                if formatted_timestamp in filename:
                    return filename
        
        st.info(f"No matching chart found for {symbol}, {timeframe}, {timestamp}")
        return None
    
    def display_chart_with_analysis(self, chart_path: str, analysis_data: Dict, trade_outcome: Optional[Dict[str, Any]] = None) -> None:
        """Display chart with overlay of analysis data and trade outcome"""
        chart_image = self.load_chart(chart_path)
        if chart_image:
            st.image(chart_image, caption=f"Chart for {analysis_data.get('symbol')} {analysis_data.get('timeframe')}", use_column_width=True)
            
            st.subheader("AI Analysis Overlay")
            st.json(analysis_data)
            
            if trade_outcome:
                st.subheader("Trade Outcome Overlay")
                st.json(trade_outcome)
        else:
            st.warning("Could not display chart.")

    def create_chart_gallery(self, recommendations: List[Dict], filter_criteria: Optional[Dict] = None) -> List[str]:
        """Create gallery of charts based on filter criteria (successful/failed trades, etc.)"""
        # This will be implemented later, likely in main.py using this class's methods
        return []

class ChartAnalysisCorrelator:
    """Correlates chart patterns with analysis quality and outcomes"""
    
    def analyze_chart_complexity_vs_accuracy(self, outcomes: List[RecommendationOutcome]) -> Dict[str, Any]:
        """Analyze if chart complexity affects analysis accuracy"""
        # This is a placeholder. Requires image analysis or more complex data.
        return {}
    
    def identify_chart_patterns_in_failures(self, failed_outcomes: List[RecommendationOutcome]) -> List[str]:
        """Identify common chart patterns in failed analyses"""
        # This is a placeholder. Requires image analysis or manual tagging.
        return []
