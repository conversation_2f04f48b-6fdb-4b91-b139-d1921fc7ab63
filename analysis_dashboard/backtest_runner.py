"""
Backtest Runner for Analysis Dashboard
Runs comprehensive backtests on all analyzer results from the database.
"""

import sys
import os
from typing import Dict, List, Any, Optional
from pathlib import Path
import pandas as pd

# Optional streamlit import (only needed for dashboard)
try:
    import streamlit as st
    STREAMLIT_AVAILABLE = True
except ImportError:
    STREAMLIT_AVAILABLE = False
    st = None

# Add the backtest directory to the path
sys.path.append(str(Path(__file__).parent.parent / 'backtest'))

from backtest.core.multi_prompt_backtest_engine import MultiPromptBacktestEngine
from analysis_dashboard.database_connector import DatabaseConnector


class DashboardBacktestRunner:
    """Runs backtests for the analysis dashboard."""

    def __init__(self, db_path: str = 'trading_bot/data/analysis_results.db'):
        """Initialize the backtest runner.

        Args:
            db_path: Path to the analysis results database
        """
        self.db_path = db_path
        self.backtest_engine = MultiPromptBacktestEngine(db_path)
        self.db_connector = DatabaseConnector(db_path)

    def run_full_backtest(self, days_back: int = 30,
                         min_records_per_group: int = 10,
                         max_workers: int = 4,
                         signal_filter: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run a comprehensive backtest on all analyzer results.

        Args:
            days_back: Number of days of historical data to analyze
            min_records_per_group: Minimum records required per group
            max_workers: Maximum number of parallel workers

        Returns:
            Dictionary containing backtest results
        """
        try:
            # Check if we have sufficient data
            total_records = self._get_total_analysis_records(days_back)
            if total_records < min_records_per_group:
                return {
                    'error': f'Insufficient data: Only {total_records} records found, '
                           f'need at least {min_records_per_group} for meaningful analysis'
                }

            # Set default signal filter if not provided
            if signal_filter is None:
                signal_filter = ['buy', 'sell']

            # Run the multi-prompt backtest
            session_id = self.backtest_engine.run_multi_prompt_backtest(
                days_back=days_back,
                min_records_per_group=min_records_per_group,
                max_workers=max_workers,
                signal_filter=signal_filter
            )

            # Wait for completion (in a real implementation, this would be async)
            import time
            max_wait_time = 300  # 5 minutes
            wait_time = 0

            while self.backtest_engine.is_running and wait_time < max_wait_time:
                time.sleep(1)
                wait_time += 1

            if self.backtest_engine.is_running:
                return {'error': 'Backtest timed out after 5 minutes'}

            # Get results
            results = self.backtest_engine.get_results(session_id)

            if not results:
                return {'error': 'No backtest results available'}

            # Format results for dashboard display
            return self._format_results_for_dashboard(results)

        except Exception as e:
            return {'error': f'Backtest failed: {str(e)}'}

    def _get_total_analysis_records(self, days_back: int) -> int:
        """Get total number of analysis records for the specified period."""
        try:
            conn = self.db_connector.get_connection()
            cursor = conn.cursor()

            from datetime import datetime, timedelta
            cutoff_date = datetime.now() - timedelta(days=days_back)

            cursor.execute('''
                SELECT COUNT(*) FROM analysis_results
                WHERE timestamp >= ?
                AND recommendation IN ('buy', 'sell')
                AND entry_price IS NOT NULL
                AND stop_loss IS NOT NULL
                AND take_profit IS NOT NULL
            ''', (cutoff_date.isoformat(),))

            count = cursor.fetchone()[0]
            conn.close()
            return count

        except Exception as e:
            print(f"Error getting record count: {e}")
            return 0

    def _format_results_for_dashboard(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Format backtest results for dashboard display."""
        formatted = {
            'summary': {
                'total_groups_tested': results.get('final_report', {}).get('executive_summary', {}).get('total_prompt_groups_tested', 0),
                'total_simulated_trades': results.get('final_report', {}).get('executive_summary', {}).get('total_simulated_trades', 0),
                'analysis_period_days': results.get('parameters', {}).get('days_back', 30),
                'data_quality_score': results.get('final_report', {}).get('executive_summary', {}).get('data_quality_score', 0),
                'best_performing_group': results.get('final_report', {}).get('executive_summary', {}).get('best_performing_group', 'N/A'),
                'key_insights': results.get('final_report', {}).get('executive_summary', {}).get('key_insights', [])
            },
            'performance_analysis': results.get('performance_analysis', {}),
            'backtest_results': results.get('backtest_results', {}),
            'extraction_summary': results.get('extraction_result', {}).get('summary', {}),
            'validation_results': results.get('validation_results', {}),
            'raw_results': results
        }

        return formatted

    def get_backtest_progress(self) -> Dict[str, Any]:
        """Get current backtest progress."""
        return self.backtest_engine.get_progress()

    def stop_backtest(self):
        """Stop the current backtest."""
        self.backtest_engine.stop_backtest()

    def get_performance_summary_table(self, results: Dict[str, Any]) -> pd.DataFrame:
        """Create a summary table of backtest performance."""
        if 'backtest_results' not in results:
            return pd.DataFrame()

        summary_data = []

        for group_type, groups in results['backtest_results'].items():
            for group_name, result in groups.items():
                if 'summary' in result:
                    summary = result['summary']
                    summary_data.append({
                        'Group Type': group_type,
                        'Group Name': group_name,
                        'Total Trades': summary.get('total_trades', 0),
                        'Win Rate': f"{summary.get('win_rate', 0):.1%}",
                        'Total PnL %': f"{summary.get('total_pnl_percent', 0):.2f}%",
                        'Avg PnL/Trade': f"{summary.get('avg_pnl_per_trade', 0):.2f}%",
                        'Profit Factor': f"{summary.get('profit_factor', 0):.2f}",
                        'Sharpe Ratio': f"{summary.get('sharpe_ratio', 0):.2f}",
                        'Max Drawdown': f"{summary.get('max_drawdown', 0):.2f}%"
                    })

        return pd.DataFrame(summary_data)

    def get_top_performers(self, results: Dict[str, Any], metric: str = 'win_rate', top_n: int = 5) -> pd.DataFrame:
        """Get top performing groups by a specific metric."""
        summary_table = self.get_performance_summary_table(results)

        if summary_table.empty:
            return pd.DataFrame()

        # Convert percentage strings back to floats for sorting
        if metric == 'win_rate':
            summary_table['win_rate_numeric'] = summary_table['Win Rate'].str.rstrip('%').astype(float) / 100
            sort_column = 'win_rate_numeric'
        elif metric == 'total_pnl_percent':
            summary_table['pnl_numeric'] = summary_table['Total PnL %'].str.rstrip('%').astype(float)
            sort_column = 'pnl_numeric'
        elif metric == 'profit_factor':
            summary_table['pf_numeric'] = summary_table['Profit Factor'].astype(float)
            sort_column = 'pf_numeric'
        else:
            return summary_table.head(top_n)

        return summary_table.nlargest(top_n, sort_column)[['Group Type', 'Group Name', 'Total Trades', 'Win Rate', 'Total PnL %', 'Profit Factor']]