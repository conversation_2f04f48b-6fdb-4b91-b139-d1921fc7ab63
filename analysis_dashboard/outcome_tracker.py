from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import pandas as pd
from analysis_dashboard.data_parser import AnalysisDataParser
# Force reload

@dataclass
class RecommendationOutcome:
    recommendation_id: str
    symbol: str
    timeframe: str
    timestamp: str
    analysis_data: Dict[str, Any]
    trade_data: Optional[Dict[str, Any]]
    chart_path: Optional[str]
    success_metrics: Dict[str, float]
    outcome_category: str  # 'profitable', 'loss', 'no_trade', 'pending'

@dataclass
class SuccessRateMetrics:
    total_recommendations: int
    traded_recommendations: int
    profitable_trades: int
    loss_trades: int
    success_rate: float
    avg_pnl: float
    confidence_correlation: float

class OutcomeTracker:
    """Tracks and analyzes recommendation outcomes through trade data"""
    
    def __init__(self, database_connector):
        self.db = database_connector
        self.success_thresholds = self._load_success_thresholds()
    
    def _load_success_thresholds(self):
        # Placeholder for loading success thresholds from config or a file
        return {
            "profitable_pnl_threshold": 0.01, # Minimum PnL to be considered profitable
            "loss_pnl_threshold": -0.01      # Maximum PnL to be considered a loss
        }

    def get_recommendations_with_outcomes(self, date_range: Optional[Tuple[str, str]] = None) -> List[RecommendationOutcome]:
        """Get recommendations linked with their trade outcomes"""
        # Fetch raw data from database
        raw_data = self.db.get_recommendations_with_outcomes(limit=10000) # Fetch more data for comprehensive analysis
        
        outcomes = []
        for record in raw_data:
            analysis_data = record.get('analysis_data', {})
            
            # Extract trade data if available
            trade_data = None
            if record.get('trade_status'): # Check if trade data exists in the joined record
                trade_data = {
                    'pnl': record.get('pnl'),
                    'status': record.get('trade_status'),
                    'entry_price': record.get('trade_entry_price'),
                    'exit_price': record.get('trade_exit_price'), # Added exit_price
                    'take_profit': record.get('trade_take_profit'),
                    'stop_loss': record.get('trade_stop_loss'),
                    'side': record.get('trade_side'),
                    'quantity': record.get('trade_quantity'),
                    'created_at': record.get('trade_created_at'),
                    'updated_at': record.get('trade_updated_at') # Added updated_at
                }
            
            # Determine outcome category and success metrics
            outcome_category = 'no_trade'
            pnl = trade_data.get('pnl', 0.0) if trade_data else 0.0
            
            if trade_data:
                if trade_data['status'] == 'closed':
                    if pnl > self.success_thresholds['profitable_pnl_threshold']:
                        outcome_category = 'profitable'
                    elif pnl < self.success_thresholds['loss_pnl_threshold']:
                        outcome_category = 'loss'
                    else:
                        outcome_category = 'breakeven'
                elif trade_data['status'] == 'open':
                    outcome_category = 'pending'
                elif trade_data['status'] == 'cancelled':
                    outcome_category = 'cancelled'
            
            success_metrics = {
                "pnl": pnl,
                "is_profitable": 1.0 if outcome_category == 'profitable' else 0.0,
                "is_loss": 1.0 if outcome_category == 'loss' else 0.0,
                "is_traded": 1.0 if trade_data else 0.0
            }
            
            outcomes.append(RecommendationOutcome(
                recommendation_id=record.get('id'),
                symbol=record.get('symbol', 'N/A'),
                timeframe=record.get('timeframe', 'N/A'),
                timestamp=record.get('timestamp', 'N/A'),
                analysis_data=analysis_data,
                trade_data=trade_data,
                chart_path=record.get('image_path'), # This is the path stored in DB, needs conversion for actual file
                success_metrics=success_metrics,
                outcome_category=outcome_category
            ))
        return outcomes
    
    def calculate_success_metrics(self, outcomes: List[RecommendationOutcome]) -> SuccessRateMetrics:
        """Calculate comprehensive success metrics"""
        total_recommendations = len(outcomes)
        traded_recommendations = sum(1 for o in outcomes if o.trade_data is not None)
        profitable_trades = sum(1 for o in outcomes if o.outcome_category == 'profitable')
        loss_trades = sum(1 for o in outcomes if o.outcome_category == 'loss')
        
        success_rate = profitable_trades / traded_recommendations if traded_recommendations > 0 else 0.0
        avg_pnl = sum(o.success_metrics['pnl'] for o in outcomes if o.trade_data) / traded_recommendations if traded_recommendations > 0 else 0.0
        
        # Confidence correlation will be calculated separately in ConfidenceCalibrationAnalyzer
        confidence_correlation = 0.0 
        
        return SuccessRateMetrics(
            total_recommendations=total_recommendations,
            traded_recommendations=traded_recommendations,
            profitable_trades=profitable_trades,
            loss_trades=loss_trades,
            success_rate=success_rate,
            avg_pnl=avg_pnl,
            confidence_correlation=confidence_correlation
        )
    
    def analyze_failure_patterns(self, outcomes: List[RecommendationOutcome]) -> Dict[str, Any]:
        """Analyze patterns in failed recommendations for prompt improvement"""
        failed_outcomes = [o for o in outcomes if o.outcome_category == 'loss']
        
        # Example: Analyze common missing fields in failed analyses
        missing_fields_counts = {}
        for outcome in failed_outcomes:
            parser = AnalysisDataParser() # Need to import or pass parser
            issues = parser.extract_prompt_issues(outcome.analysis_data)
            for issue in issues:
                missing_fields_counts[issue] = missing_fields_counts.get(issue, 0) + 1
        
        # Example: Analyze common recommendations in failed trades
        failed_recommendations_counts = {}
        for outcome in failed_outcomes:
            rec = outcome.analysis_data.get('recommendation', 'unknown')
            failed_recommendations_counts[rec] = failed_recommendations_counts.get(rec, 0) + 1
            
        return {
            "missing_fields_in_failures": missing_fields_counts,
            "failed_recommendation_types": failed_recommendations_counts
        }

class ConfidenceCalibrationAnalyzer:
    """Analyzes how well AI confidence correlates with actual outcomes"""
    
    def __init__(self, database_connector):
        self.db = database_connector

    def analyze_confidence_calibration(self, outcomes: List[RecommendationOutcome]) -> Dict[str, Any]:
        """Analyze confidence calibration against actual results"""
        # Filter for traded recommendations
        traded_outcomes = [o for o in outcomes if o.trade_data and o.trade_data['status'] == 'closed']
        
        if not traded_outcomes:
            return {"message": "No closed trades to analyze confidence calibration."}
        
        # Prepare data for correlation
        confidence_scores = [o.analysis_data.get('analysis', {}).get('confidence', 0.0) for o in traded_outcomes]
        pnl_outcomes = [o.success_metrics['pnl'] for o in traded_outcomes]
        
        # Convert to pandas Series for correlation calculation
        conf_series = pd.Series(confidence_scores)
        pnl_series = pd.Series(pnl_outcomes)
        
        correlation = conf_series.corr(pnl_series)
        
        # Analyze bias: e.g., average confidence for profitable vs. losing trades
        profitable_conf = [o.analysis_data.get('analysis', {}).get('confidence', 0.0) for o in traded_outcomes if o.outcome_category == 'profitable']
        loss_conf = [o.analysis_data.get('analysis', {}).get('confidence', 0.0) for o in traded_outcomes if o.outcome_category == 'loss']
        
        avg_profitable_conf = sum(profitable_conf) / len(profitable_conf) if profitable_conf else 0
        avg_loss_conf = sum(loss_conf) / len(loss_conf) if loss_conf else 0
        
        return {
            "confidence_pnl_correlation": correlation,
            "avg_confidence_profitable_trades": avg_profitable_conf,
            "avg_confidence_loss_trades": avg_loss_conf,
            "calibration_bias": avg_profitable_conf - avg_loss_conf # Positive means overconfident in profitable, negative in loss
        }
    
    def identify_overconfident_recommendations(self, outcomes: List[RecommendationOutcome]) -> List[RecommendationOutcome]:
        """Identify recommendations where AI was overconfident relative to outcome"""
        overconfident_recs = []
        for outcome in outcomes:
            # Access confidence from the nested 'analysis' dictionary
            confidence = outcome.analysis_data.get('analysis', {}).get('confidence', 0.0)
            if outcome.outcome_category == 'loss' and confidence > 0.7: # Example threshold
                overconfident_recs.append(outcome)
            elif outcome.outcome_category == 'profitable' and confidence < 0.3: # Example: underconfident
                pass # Could track underconfident too
        return overconfident_recs
    
    def suggest_confidence_improvements(self, outcomes: List[RecommendationOutcome]) -> List[str]:
        """Suggest improvements for confidence calibration"""
        suggestions = []
        calibration_analysis = self.analyze_confidence_calibration(outcomes)
        
        if calibration_analysis.get("calibration_bias", 0) > 0.1:
            suggestions.append("AI tends to be overconfident in profitable trades. Review prompt to encourage more conservative confidence scoring.")
        elif calibration_analysis.get("calibration_bias", 0) < -0.1:
            suggestions.append("AI tends to be underconfident in profitable trades. Review prompt to encourage more assertive confidence scoring when conditions are strong.")
        
        if calibration_analysis.get("confidence_pnl_correlation", 0) < 0.5:
            suggestions.append("Low correlation between confidence and PnL. Refine prompt to ensure confidence scores accurately reflect trade potential.")
            
        return suggestions
