#!/bin/bash

# setup-playwright-vnc.sh
# Minimal VNC setup for Playwright browser viewing — NO desktop environment needed.
# Author: Assistant
# Usage: chmod +x setup-playwright-vnc.sh && ./setup-playwright-vnc.sh

set -e  # Exit on error

# ===== CONFIG =====
VNC_DISPLAY=":99"
VNC_PORT="5999"  # = 5900 + 99
VNC_PASSWORD="playwr2398ehriusbfkjshsdASDaf234ight"  # Change this!
SCREEN_RES="1920x1080x24"
LOG_FILE="/tmp/playwright-vnc.log"
# ==================

echo "🚀 Setting up minimal VNC for Playwright..."

# Check if running as root for system-wide installation
if [[ $EUID -eq 0 ]]; then
    echo "⚠️  Running as root - installing system-wide packages..."
    INSTALL_CMD="apt-get"
    SUDO=""
else
    echo "ℹ️  Running as user - using sudo for package installation..."
    INSTALL_CMD="sudo apt-get"
    SUDO="sudo"
fi

# Detect OS and install dependencies
if [ -f /etc/debian_version ]; then
    echo "📦 Installing packages on Debian/Ubuntu..."
    $INSTALL_CMD update
    $INSTALL_CMD install -y xvfb x11vnc fluxbox xterm

    # Install Chromium browser for Playwright
    echo "🌐 Installing Chromium browser..."
    $INSTALL_CMD install -y chromium-browser

    # Alternative: Install Google Chrome (more stable for complex sites)
    echo "🌐 Installing Google Chrome (alternative browser)..."
    wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | $SUDO apt-key add -
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | $SUDO tee /etc/apt/sources.list.d/google-chrome.list
    $INSTALL_CMD update
    $INSTALL_CMD install -y google-chrome-stable

elif [ -f /etc/centos-release ] || [ -f /etc/redhat-release ] || grep -qi "fedora" /etc/os-release; then
    echo "📦 Installing packages on RHEL/CentOS/Fedora..."
    if command -v dnf >/dev/null 2>&1; then
        $SUDO dnf install -y xorg-x11-server-Xvfb x11vnc fluxbox xterm
        # Install Chromium on Fedora/RHEL
        $SUDO dnf install -y chromium
    else
        $SUDO yum install -y xorg-x11-server-Xvfb x11vnc fluxbox xterm
        # Install Chromium on CentOS/RHEL
        $SUDO yum install -y chromium
    fi
else
    echo "⚠️  Unsupported OS. Please install these manually:"
    echo "   - VNC packages: xvfb, x11vnc, fluxbox, xterm"
    echo "   - Browser: chromium-browser or google-chrome-stable"
    exit 1
fi

# Kill existing processes if any
echo "🧹 Cleaning up any existing processes..."
pkill -f "Xvfb ${VNC_DISPLAY}" 2>/dev/null || true
pkill -f "x11vnc.*${VNC_DISPLAY}" 2>/dev/null || true
pkill fluxbox 2>/dev/null || true

# Start Xvfb virtual display
echo "🖥️  Starting Xvfb on display ${VNC_DISPLAY}..."
Xvfb ${VNC_DISPLAY} -screen 0 ${SCREEN_RES} -ac +extension GLX +render -noreset > $LOG_FILE 2>&1 &
XVFB_PID=$!
sleep 2

# Export display for Playwright
export DISPLAY=${VNC_DISPLAY}
echo "✅ DISPLAY=${DISPLAY} set for Playwright"

# Start fluxbox (optional but recommended for browser compatibility)
echo "🪟 Starting fluxbox window manager..."
fluxbox > $LOG_FILE 2>&1 &
FLUXBOX_PID=$!
sleep 1

# Start x11vnc
echo "📡 Starting x11vnc on port ${VNC_PORT}..."
x11vnc -display ${VNC_DISPLAY} -forever -shared -passwd "${VNC_PASSWORD}" -localhost -rfbport 5999 -o $LOG_FILE > $LOG_FILE 2>&1 &
VNCSERVER_PID=$!
sleep 2

# Verify everything is running
if ! ps -p $XVFB_PID > /dev/null; then
    echo "❌ Xvfb failed to start. Check $LOG_FILE"
    exit 1
fi

if ! ps -p $VNCSERVER_PID > /dev/null; then
    echo "❌ x11vnc failed to start. Check $LOG_FILE"
    exit 1
fi

echo ""
echo "🎉 SUCCESS! Playwright VNC environment is ready."
echo ""
echo "👉 Run your Playwright script with headless: false:"
echo "   export DISPLAY=${VNC_DISPLAY}"
echo "   node your-script.js   # or python, etc."
echo ""
echo "🔐 To connect securely from your local machine:"
echo "   ssh -L 5900:localhost:${VNC_PORT} user@$(hostname -I | awk '{print $1}')"
echo ""
echo "🖼️  Then open your VNC viewer (e.g., RealVNC, TigerVNC) and connect to:"
echo "   localhost:5900"
echo "   Password: ${VNC_PASSWORD}"
echo ""
echo "📌 Log file: $LOG_FILE"
echo "📌 To stop: ./setup-playwright-vnc.sh --stop"
echo ""

# Optional: Create stop alias/script
cat > /tmp/stop-playwright-vnc.sh <<EOF
#!/bin/bash
echo "🛑 Stopping Playwright VNC services..."
pkill -f "Xvfb ${VNC_DISPLAY}" 2>/dev/null || echo "Xvfb not running."
pkill -f "x11vnc.*${VNC_DISPLAY}" 2>/dev/null || echo "x11vnc not running."
pkill fluxbox 2>/dev/null || echo "fluxbox not running."
echo "✅ All services stopped."
EOF

chmod +x /tmp/stop-playwright-vnc.sh
echo "💡 Created stop script: /tmp/stop-playwright-vnc.sh"

# Handle --stop flag
if [[ "$1" == "--stop" ]]; then
    /tmp/stop-playwright-vnc.sh
    exit 0
fi

# Keep script alive (optional — comment out if running in background)
# wait

