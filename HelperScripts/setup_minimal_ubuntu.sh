#!/bin/bash

# Minimal Ubuntu setup for trading bot (core components only)
# Installs only essential dependencies needed to run the bot

set -e

echo "🚀 Minimal Ubuntu setup for Trading Bot..."
echo "========================================="

# Update system
echo "📦 Updating system..."
sudo apt-get update -y

# Install Python and essential packages
echo "🐍 Installing Python and essentials..."
sudo apt-get install -y python3 python3-pip python3-venv git sqlite3

# Install Playwright system dependencies (minimal)
echo "🎭 Installing Playwright dependencies..."
sudo apt-get install -y \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    libgtk-3-0 \
    libx11-xcb1 \
    xvfb

# Create bot user
echo "👤 Creating bot user..."
sudo useradd -m -s /bin/bash botuser 2>/dev/null || echo "User exists"

# Setup bot directory and clone repo
echo "📥 Setting up bot directory..."
sudo mkdir -p /home/<USER>/trading_bot
sudo chown -R botuser:botuser /home/<USER>

# Switch to botuser and setup Python environment
sudo -u botuser bash << 'EOF'
cd /home/<USER>

# Clone repository
echo "📥 Cloning trading bot..."
git clone https://github.com/macd2/Image_analyser_trading_bot.git trading_bot 2>/dev/null || echo "Repository exists"

cd trading_bot

# Setup virtual environment
echo "🐍 Setting up Python environment..."
python3 -m venv venv
source venv/bin/activate

# Install core Python packages
echo "📦 Installing core Python packages..."
pip install --upgrade pip
pip install -r requirements.txt

# Install Playwright browser
echo "🎭 Installing Playwright browser..."
playwright install chromium

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p trading_bot/data trading_bot/logs logs

echo "✅ Core setup complete"
EOF

# Clean up
echo "🧹 Cleaning up..."
sudo apt-get autoremove -y
sudo apt-get clean

echo ""
echo "🎉 Minimal setup complete!"
echo "========================"
echo ""
echo "📋 Next steps:"
echo "1. Configure API keys in /home/<USER>/trading_bot/config.yaml"
echo "2. Run: sudo -u botuser /home/<USER>/run_bot.sh"
echo ""
echo "📁 Bot location: /home/<USER>/trading_bot"
echo "👤 Bot user: botuser"
echo ""
echo "✅ SSH enabled for remote access"
