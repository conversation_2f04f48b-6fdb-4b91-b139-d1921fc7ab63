#!/bin/bash

# Script to set up swap space and optimize system for low memory
# This script creates a 2GB swap file and configures memory management settings

set -e  # Exit on any error

echo "Setting up swap space and memory optimization..."

# Check if swapfile already exists
if [ -f /swapfile ]; then
    echo "Swap file already exists. Skipping swap setup."
else
    echo "Creating 2GB swap file..."
    sudo fallocate -l 2G /swapfile
    sudo chmod 600 /swapfile
    sudo mkswap /swapfile
    sudo swapon /swapfile
    echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
    echo "Swap space setup complete."
fi

# Optimize system for low memory
echo "Configuring memory optimization settings..."
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

echo "Memory optimization complete."
echo "System is now configured with swap space and optimized memory settings."
