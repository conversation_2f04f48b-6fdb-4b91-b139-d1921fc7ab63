#!/bin/bash

# stop-playwright-vnc.sh
# Stop all Playwright VNC services
# Author: Assistant
# Usage: chmod +x stop-playwright-vnc.sh && ./stop-playwright-vnc.sh

# ===== CONFIG =====
VNC_DISPLAY=":99"
LOG_FILE="/tmp/playwright-vnc.log"
# ==================

echo "🛑 Stopping Playwright VNC services..."

# Kill all related processes
echo "🔄 Stopping Xvfb virtual display..."
pkill -f "Xvfb ${VNC_DISPLAY}" 2>/dev/null || echo "ℹ️  Xvfb not running."

echo "🔄 Stopping x11vnc server..."
pkill -f "x11vnc.*${VNC_DISPLAY}" 2>/dev/null || echo "ℹ️  x11vnc not running."

echo "🔄 Stopping fluxbox window manager..."
pkill fluxbox 2>/dev/null || echo "ℹ️  fluxbox not running."

# Clean up log file (optional)
if [ -f "$LOG_FILE" ]; then
    echo "🧹 Cleaning up log file..."
    rm -f "$LOG_FILE"
fi

echo ""
echo "✅ All Playwright VNC services stopped."
echo "💡 To restart: ./launch-playwright-vnc.sh"
