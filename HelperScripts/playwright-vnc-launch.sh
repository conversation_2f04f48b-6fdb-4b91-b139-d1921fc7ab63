#!/bin/bash

# launch-playwright-vnc.sh
# Launch VNC environment for Playwright browser viewing (no installation)
# Author: Assistant
# Usage: chmod +x launch-playwright-vnc.sh && ./launch-playwright-vnc.sh

set -e  # Exit on error

# ===== CONFIG =====
VNC_DISPLAY=":99"
VNC_PORT="5999"  # = 5900 + 99
VNC_PASSWORD="playwr2398ehriusbfkjshsdASDaf234ight"  # Change this!
SCREEN_RES="1920x1080x24"
LOG_FILE="/tmp/playwright-vnc.log"
# ==================

echo "🚀 Launching Playwright VNC environment (no installation)..."

# Kill existing processes if any
echo "🧹 Cleaning up any existing processes..."
pkill -f "Xvfb ${VNC_DISPLAY}" 2>/dev/null || true
pkill -f "x11vnc.*${VNC_DISPLAY}" 2>/dev/null || true
pkill fluxbox 2>/dev/null || true

# Start Xvfb virtual display
echo "🖥️  Starting Xvfb on display ${VNC_DISPLAY}..."
Xvfb ${VNC_DISPLAY} -screen 0 ${SCREEN_RES} -ac +extension GLX +render -noreset > $LOG_FILE 2>&1 &
XVFB_PID=$!
sleep 2

# Export display for Playwright
export DISPLAY=${VNC_DISPLAY}
echo "✅ DISPLAY=${DISPLAY} set for Playwright"

# Start fluxbox (optional but recommended for browser compatibility)
echo "🪟 Starting fluxbox window manager..."
fluxbox > $LOG_FILE 2>&1 &
FLUXBOX_PID=$!
sleep 1

# Start x11vnc
echo "📡 Starting x11vnc on port ${VNC_PORT}..."
x11vnc -display ${VNC_DISPLAY} -forever -shared -passwd "${VNC_PASSWORD}" -localhost -rfbport 5999 -o $LOG_FILE > $LOG_FILE 2>&1 &
VNCSERVER_PID=$!
sleep 2

# Verify everything is running
if ! ps -p $XVFB_PID > /dev/null; then
    echo "❌ Xvfb failed to start. Check $LOG_FILE"
    exit 1
fi

if ! ps -p $VNCSERVER_PID > /dev/null; then
    echo "❌ x11vnc failed to start. Check $LOG_FILE"
    exit 1
fi

echo ""
echo "🎉 SUCCESS! Playwright VNC environment is running."
echo ""
echo "👉 Run your Playwright script with headless: false:"
echo "   export DISPLAY=${VNC_DISPLAY}"
echo "   node your-script.js   # or python, etc."
echo ""
echo "🔐 To connect securely from your local machine:"
echo "   ssh -L 5900:localhost:${VNC_PORT} user@$(hostname -I | awk '{print $1}')"
echo ""
echo "🖼️  Then open your VNC viewer (e.g., RealVNC, TigerVNC) and connect to:"
echo "   localhost:5900"
echo "   Password: ${VNC_PASSWORD}"
echo ""
echo "📌 Log file: $LOG_FILE"
echo "📌 To stop: ./stop-playwright-vnc.sh"
echo ""

# Keep script alive (optional — comment out if running in background)
# wait
