"""Configuration settings for the trading bot."""
import os
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import the new secrets manager
from trading_bot.core.secrets_manager import get_openai_api_key, SecretsManager

@dataclass
class PathsConfig:
    """File paths configuration."""
    database: str
    charts: str
    logs: str
    session_file: str  # Base path for session files (filename is dynamic)

@dataclass
class ChartCleaningConfig:
    """Chart cleaning configuration."""
    enable_age_based_cleaning: bool  # Enable cleaning files older than age threshold
    max_file_age_hours: int  # Maximum age in hours for chart files
    enable_cycle_based_cleaning: bool  # Keep existing cycle-based cleaning

@dataclass
class FileManagementConfig:
    """File management configuration."""
    enable_backup: bool  # Enable/disable backup creation for file operations
    chart_cleaning: Optional[ChartCleaningConfig] = None  # Chart cleaning configuration

@dataclass
class AssistantConfig:
    """OpenAI Assistant configuration."""
    default_timeout: int
    poll_interval: float
    max_retries: int
    assistants: Optional[Dict[str, str]] = None  # Dict of assistant names to IDs

    def __post_init__(self):
        if self.assistants is None:
            self.assistants = {}

@dataclass
class OpenAIConfig:
    """OpenAI API configuration."""
    api_key: str
    model: str
    max_tokens: int
    temperature: float
    assistant: Optional[AssistantConfig] = None

@dataclass
class CircuitBreakerConfig:
    """Circuit Breaker configuration."""
    error_threshold: int
    recovery_timeout: int
    max_recv_window: int
    backoff_multiplier: float
    jitter_range: float

@dataclass
class BybitConfig:
    """Bybit API configuration."""
    recv_window: int  # Receive window in milliseconds
    use_testnet: bool  # Use testnet for trading
    max_retries: int  # Max retries for API calls
    circuit_breaker: Optional[CircuitBreakerConfig] = None

@dataclass
class OrderReplacementConfig:
    """Order replacement system configuration."""
    enable_intelligent_replacement: bool
    min_score_improvement_threshold: float
    partially_filled_protection_threshold: float
    batch_evaluation_timeout_seconds: int
    max_replacements_per_batch: int
    replacement_cooldown_minutes: int
    enable_cross_symbol_replacement: bool
    max_age_bars: int  # Orders older than this many timeframe bars are considered expired
    age_penalty_factor: float  # Minimum score multiplier for expired orders

@dataclass
class ExchangeSyncConfig:
    """Exchange synchronization configuration."""
    enabled: bool
    sync_interval_seconds: int
    max_retries: int
    rate_limit_requests_per_second: int
    batch_size: int
    error_recovery_delay_seconds: int
    incremental_sync_enabled: bool
    full_sync_interval_minutes: int

@dataclass
class RRWeightingConfig:
    """Risk-reward weighting configuration."""
    enabled: bool
    min_weight: float
    max_weight: float

@dataclass
class ConfidenceWeightingConfig:
    """Confidence weighting configuration."""
    enabled: bool
    low_confidence_threshold: float
    high_confidence_threshold: float
    low_confidence_weight: float
    neutral_confidence_weight: float
    high_confidence_weight: float

@dataclass
class VolatilityWeightingConfig:
    """Volatility weighting configuration."""
    enabled: bool
    use_atr_based: bool
    low_volatility_weight: float
    high_volatility_weight: float
    atr_low_threshold: float
    atr_high_threshold: float

@dataclass
class LoggingRotationConfig:
    """Log rotation configuration."""
    max_file_size_mb: int  # Maximum size per log file in MB
    backup_count: int       # Number of backup files to keep

@dataclass
class LoggingConfig:
    """Main logging configuration."""
    rotation: Optional[LoggingRotationConfig]
    style: str  # For position sizing logging
    show_icons: bool

@dataclass
class PositionSizingConfig:
    """Enhanced position sizing configuration."""
    # Position sizing method selection (hierarchical priority)
    use_enhanced_position_sizing: bool  # Use confidence & volatility weighting (highest priority)
    use_slot_based_position_sizing: bool  # Use equal risk allocation across slots (medium priority)

    equal_allocation_base: bool

    # Minimum thresholds to prevent tiny positions
    min_position_value_usd: float
    min_balance_threshold: float

    confidence_weighting: Optional[ConfidenceWeightingConfig]
    rr_weighting: Optional[RRWeightingConfig]
    volatility_weighting: Optional[VolatilityWeightingConfig]
    max_combined_weight: float
    min_combined_weight: float
    logging: Optional[LoggingConfig]

@dataclass
class StateMachineConfig:
    """State machine configuration."""
    enabled: bool
    parallel_mode: bool
    log_comparison: bool
    fail_safely: bool

@dataclass
class RRTighteningStepConfig:
    """Configuration for a single RR-based tightening step."""
    profit_threshold: float
    sl_position: float
    description: str

@dataclass
class RRTighteningConfig:
    """RR-based tightening steps configuration."""
    steps: Optional[Dict[str, RRTighteningStepConfig]] = None

    def __post_init__(self):
        if self.steps is None:
            self.steps = {}

@dataclass
class AgeTighteningConfig:
    """Age-based tightening configuration."""
    enabled: bool
    max_tightening_pct: float
    min_profit_threshold: float
    age_tightening_bars: Optional[Dict[str, int]] = None

@dataclass
class TradingConfig:
    """Trading configuration."""
    risk_tolerance: float
    min_confidence_threshold: float
    paper_trading: bool
    risk_percentage: float
    max_loss_usd: float
    min_rr: float
    leverage: int
    balance_safety_margin: float
    max_concurrent_trades: int
    auto_approve_trades: bool
    enable_dynamic_risk_allocation: bool
    enable_position_tightening: bool  # Master switch for all position tightening features
    enable_sl_tightening: bool
    enable_adx_tightening: bool
    adx_period: int
    atr_period: int
    adx_strength_threshold: float
    base_atr_multiplier: float
    adx_target_profit_usd: float
    enable_tp_proximity_trailing: bool
    tp_proximity_threshold_pct: float
    tp_proximity_trailing_pct: float
    rr_tightening_steps: Optional[RRTighteningConfig] = None
    age_tightening: Optional[AgeTighteningConfig] = None
    state_machine: Optional[StateMachineConfig] = None
    position_sizing: Optional[PositionSizingConfig] = None
    order_replacement: Optional[OrderReplacementConfig] = None
    exchange_sync: Optional[ExchangeSyncConfig] = None

@dataclass
class AgentConfig:
    """Agent-specific configuration."""
    model: str
    max_tokens: int
    temperature: float

@dataclass
class TradingViewBrowserConfig:
    """TradingView browser configuration."""
    headless: bool
    timeout: int
    viewport_width: int
    viewport_height: int
    user_agent: str

    # VNC Integration Settings
    use_vnc: bool
    vnc_display: str
    vnc_port: int
    vnc_window_size: str

@dataclass
class TradingViewAuthConfig:
    """TradingView authentication configuration."""
    session_file: str
    session_timeout: int
    max_login_attempts: int
    login_retry_delay: int
    js_render_wait: int

@dataclass
class TradingViewScreenshotConfig:
    """TradingView screenshot configuration."""
    chart_selector: str
    wait_for_load: int
    hide_elements: list
    quality: int

@dataclass
class TradingViewRateLimitConfig:
    """TradingView rate limiting configuration."""
    requests_per_minute: int
    delay_between_requests: int
    respect_rate_limits: bool

@dataclass
class TradingViewRetryConfig:
    """TradingView retry configuration."""
    max_attempts: int
    backoff_factor: int
    base_delay: int

@dataclass
class TradingViewConfig:
    """TradingView configuration."""
    enabled: bool
    base_url: str
    login_url: str
    chart_url_template: str
    browser: TradingViewBrowserConfig
    auth: TradingViewAuthConfig
    screenshot: TradingViewScreenshotConfig
    rate_limit: TradingViewRateLimitConfig
    retry: TradingViewRetryConfig
    target_chart: Optional[str] = None

@dataclass
class Config:
    """Main configuration class."""
    paths: PathsConfig
    openai: OpenAIConfig
    bybit: BybitConfig
    trading: TradingConfig
    agents: Dict[str, AgentConfig]
    tradingview: TradingViewConfig
    file_management: FileManagementConfig
    logging: Optional[LoggingConfig] = None
    
    @classmethod
    def from_yaml(cls, config_path: str = "config.yaml") -> 'Config':
        """Load configuration from YAML file with strict validation - all values must be provided."""
        with open(config_path, 'r') as f:
            data = yaml.safe_load(f)

        # Load API key using the new secrets manager
        api_key = get_openai_api_key()

        # Handle assistant configuration - required if present
        assistant_config = None
        if 'assistant' in data['openai']:
            assistant_data = data['openai']['assistant']
            assistant_config = AssistantConfig(
                default_timeout=assistant_data['default_timeout'],
                poll_interval=assistant_data['poll_interval'],
                max_retries=assistant_data['max_retries'],
                assistants=assistant_data.get('assistants', {})
            )

        # Handle paths configuration - all required
        paths_data = data['paths']
        paths_config = PathsConfig(
            database=paths_data['database'],
            charts=paths_data['charts'],
            logs=paths_data['logs'],
            session_file=paths_data['session_file']
        )

        # Handle file management configuration - all required
        file_mgmt_data = data['file_management']

        # Handle chart cleaning configuration - all required
        chart_cleaning_config = None
        if 'chart_cleaning' in file_mgmt_data:
            chart_cleaning_data = file_mgmt_data['chart_cleaning']
            chart_cleaning_config = ChartCleaningConfig(
                enable_age_based_cleaning=chart_cleaning_data['enable_age_based_cleaning'],
                max_file_age_hours=chart_cleaning_data['max_file_age_hours'],
                enable_cycle_based_cleaning=chart_cleaning_data['enable_cycle_based_cleaning']
            )

        file_management_config = FileManagementConfig(
            enable_backup=file_mgmt_data['enable_backup'],
            chart_cleaning=chart_cleaning_config
        )
        
        # Handle Bybit configuration - all required
        bybit_data = data['bybit']
        bybit_config = BybitConfig(
            recv_window=bybit_data['recv_window'],
            use_testnet=bybit_data['use_testnet'],
            max_retries=bybit_data['max_retries'],
            circuit_breaker=cls._load_circuit_breaker_config(bybit_data.get('circuit_breaker'))
        )

        # Handle trading configuration - all required
        trading_data = data['trading']
        trading_config = TradingConfig(
            risk_tolerance=trading_data['risk_tolerance'],
            min_confidence_threshold=trading_data['min_confidence_threshold'],
            paper_trading=trading_data['paper_trading'],
            risk_percentage=trading_data['risk_percentage'],
            max_loss_usd=trading_data['max_loss_usd'],
            min_rr=trading_data['min_rr'],
            leverage=trading_data['leverage'],
            balance_safety_margin=trading_data['balance_safety_margin'],
            max_concurrent_trades=trading_data['max_concurrent_trades'],
            auto_approve_trades=trading_data['auto_approve_trades'],
            enable_dynamic_risk_allocation=trading_data['enable_dynamic_risk_allocation'],
            enable_position_tightening=trading_data['enable_position_tightening'],
            enable_sl_tightening=trading_data['enable_sl_tightening'],
            enable_adx_tightening=trading_data['enable_adx_tightening'],
            adx_period=trading_data['adx_period'],
            atr_period=trading_data['atr_period'],
            adx_strength_threshold=trading_data['adx_strength_threshold'],
            base_atr_multiplier=trading_data['base_atr_multiplier'],
            adx_target_profit_usd=trading_data['adx_target_profit_usd'],
            enable_tp_proximity_trailing=trading_data['enable_tp_proximity_trailing'],
            tp_proximity_threshold_pct=trading_data['tp_proximity_threshold_pct'],
            tp_proximity_trailing_pct=trading_data['tp_proximity_trailing_pct'],
            rr_tightening_steps=cls._load_rr_tightening_config(trading_data.get('rr_tightening_steps')),
            age_tightening=cls._load_age_tightening_config(trading_data.get('age_tightening')),
            state_machine=cls._load_state_machine_config(trading_data.get('state_machine')),
            position_sizing=cls._load_position_sizing_config(trading_data.get('position_sizing')),
            order_replacement=cls._load_order_replacement_config(trading_data.get('order_replacement')),
            exchange_sync=cls._load_exchange_sync_config(trading_data.get('exchange_sync'))
        )

        # Handle agents configuration - all required
        agents_config = {
            agent_name: AgentConfig(
                model=agent_data['model'],
                max_tokens=agent_data['max_tokens'],
                temperature=agent_data['temperature']
            )
            for agent_name, agent_data in data['agents'].items()
        }

        # Handle TradingView configuration - all required
        tv_data = data['tradingview']
        tradingview_config = TradingViewConfig(
            enabled=tv_data['enabled'],
            base_url=tv_data['base_url'],
            login_url=tv_data['login_url'],
            chart_url_template=tv_data['chart_url_template'],
            browser=TradingViewBrowserConfig(
                headless=tv_data['browser']['headless'],
                timeout=tv_data['browser']['timeout'],
                viewport_width=tv_data['browser']['viewport_width'],
                viewport_height=tv_data['browser']['viewport_height'],
                user_agent=tv_data['browser']['user_agent'],
                use_vnc=tv_data['browser']['use_vnc'],
                vnc_display=tv_data['browser']['vnc_display'],
                vnc_port=tv_data['browser']['vnc_port'],
                vnc_window_size=tv_data['browser']['vnc_window_size']
            ),
            auth=TradingViewAuthConfig(
                session_file=paths_config.session_file + ".tradingview_session",
                session_timeout=tv_data['auth']['session_timeout'],
                max_login_attempts=tv_data['auth']['max_login_attempts'],
                login_retry_delay=tv_data['auth']['login_retry_delay'],
                js_render_wait=tv_data['auth']['js_render_wait']
            ),
            screenshot=TradingViewScreenshotConfig(
                chart_selector=tv_data['screenshot']['chart_selector'],
                wait_for_load=tv_data['screenshot']['wait_for_load'],
                hide_elements=tv_data['screenshot']['hide_elements'],
                quality=tv_data['screenshot']['quality']
            ),
            rate_limit=TradingViewRateLimitConfig(
                requests_per_minute=tv_data['rate_limit']['requests_per_minute'],
                delay_between_requests=tv_data['rate_limit']['delay_between_requests'],
                respect_rate_limits=tv_data['rate_limit']['respect_rate_limits']
            ),
            retry=TradingViewRetryConfig(
                max_attempts=tv_data['retry']['max_attempts'],
                backoff_factor=tv_data['retry']['backoff_factor'],
                base_delay=tv_data['retry']['base_delay']
            ),
            target_chart=SecretsManager.get_secret('TRADINGVIEW_TARGET_CHART', required=False) or tv_data.get('target_chart')
        )

        return cls(
            paths=paths_config,
            openai=OpenAIConfig(
                api_key=api_key,
                model=data['openai']['model'],
                max_tokens=data['openai']['max_tokens'],
                temperature=data['openai']['temperature'],
                assistant=assistant_config
            ),
            bybit=bybit_config,
            trading=trading_config,
            agents=agents_config,
            tradingview=tradingview_config,
            file_management=file_management_config,
            logging=cls._load_logging_config(data.get('logging'))
        )

    @classmethod
    def _load_circuit_breaker_config(cls, cb_data: Dict[str, Any]) -> Optional[CircuitBreakerConfig]:
        """Load circuit breaker configuration from YAML data."""
        if not cb_data:
            return None
        return CircuitBreakerConfig(
            error_threshold=cb_data['error_threshold'],
            recovery_timeout=cb_data['recovery_timeout'],
            max_recv_window=cb_data['max_recv_window'],
            backoff_multiplier=cb_data['backoff_multiplier'],
            jitter_range=cb_data['jitter_range']
        )

    @classmethod
    def _load_order_replacement_config(cls, order_replacement_data: Dict[str, Any]) -> Optional[OrderReplacementConfig]:
        """Load order replacement configuration from YAML data."""
        if not order_replacement_data:
            return None

        return OrderReplacementConfig(
            enable_intelligent_replacement=order_replacement_data['enable_intelligent_replacement'],
            min_score_improvement_threshold=order_replacement_data['min_score_improvement_threshold'],
            partially_filled_protection_threshold=order_replacement_data['partially_filled_protection_threshold'],
            batch_evaluation_timeout_seconds=order_replacement_data['batch_evaluation_timeout_seconds'],
            max_replacements_per_batch=order_replacement_data['max_replacements_per_batch'],
            replacement_cooldown_minutes=order_replacement_data['replacement_cooldown_minutes'],
            enable_cross_symbol_replacement=order_replacement_data['enable_cross_symbol_replacement'],
            max_age_bars=order_replacement_data['max_age_bars'],
            age_penalty_factor=order_replacement_data['age_penalty_factor']
        )

    @classmethod
    def _load_exchange_sync_config(cls, exchange_sync_data: Dict[str, Any]) -> Optional[ExchangeSyncConfig]:
        """Load exchange sync configuration from YAML data."""
        if not exchange_sync_data:
            return None

        return ExchangeSyncConfig(
            enabled=exchange_sync_data['enabled'],
            sync_interval_seconds=exchange_sync_data['sync_interval_seconds'],
            max_retries=exchange_sync_data['max_retries'],
            rate_limit_requests_per_second=exchange_sync_data['rate_limit_requests_per_second'],
            batch_size=exchange_sync_data['batch_size'],
            error_recovery_delay_seconds=exchange_sync_data['error_recovery_delay_seconds'],
            incremental_sync_enabled=exchange_sync_data['incremental_sync_enabled'],
            full_sync_interval_minutes=exchange_sync_data['full_sync_interval_minutes']
        )

    @classmethod
    def _load_position_sizing_config(cls, position_sizing_data: Dict[str, Any]) -> Optional[PositionSizingConfig]:
        """Load position sizing configuration from YAML data."""
        if not position_sizing_data:
            return None

        # Load RR weighting config
        rr_weighting_data = position_sizing_data.get('rr_weighting')
        rr_weighting_config = None
        if rr_weighting_data:
            rr_weighting_config = RRWeightingConfig(
                enabled=rr_weighting_data['enabled'],
                min_weight=rr_weighting_data['min_weight'],
                max_weight=rr_weighting_data['max_weight']
            )

        # Load confidence weighting config
        confidence_weighting_data = position_sizing_data.get('confidence_weighting')
        confidence_weighting_config = None
        if confidence_weighting_data:
            confidence_weighting_config = ConfidenceWeightingConfig(
                enabled=confidence_weighting_data['enabled'],
                low_confidence_threshold=confidence_weighting_data['low_confidence_threshold'],
                high_confidence_threshold=confidence_weighting_data['high_confidence_threshold'],
                low_confidence_weight=confidence_weighting_data['low_confidence_weight'],
                neutral_confidence_weight=confidence_weighting_data['neutral_confidence_weight'],
                high_confidence_weight=confidence_weighting_data['high_confidence_weight']
            )

        # Load volatility weighting config
        volatility_weighting_data = position_sizing_data.get('volatility_weighting')
        volatility_weighting_config = None
        if volatility_weighting_data:
            volatility_weighting_config = VolatilityWeightingConfig(
                enabled=volatility_weighting_data['enabled'],
                use_atr_based=volatility_weighting_data['use_atr_based'],
                low_volatility_weight=volatility_weighting_data['low_volatility_weight'],
                high_volatility_weight=volatility_weighting_data['high_volatility_weight'],
                atr_low_threshold=volatility_weighting_data['atr_low_threshold'],
                atr_high_threshold=volatility_weighting_data['atr_high_threshold']
            )

        # Load logging config
        logging_data = position_sizing_data.get('logging')
        logging_config = None
        if logging_data:
            rotation_data = logging_data.get('rotation')
            rotation_config = None
            if rotation_data:
                rotation_config = LoggingRotationConfig(
                    max_file_size_mb=rotation_data['max_file_size_mb'],
                    backup_count=rotation_data['backup_count']
                )

            logging_config = LoggingConfig(
                rotation=rotation_config,
                style=logging_data['style'],
                show_icons=logging_data['show_icons']
            )

        return PositionSizingConfig(
            use_enhanced_position_sizing=position_sizing_data['use_enhanced_position_sizing'],
            use_slot_based_position_sizing=position_sizing_data['use_slot_based_position_sizing'],
            equal_allocation_base=position_sizing_data['equal_allocation_base'],
            min_position_value_usd=position_sizing_data['min_position_value_usd'],
            min_balance_threshold=position_sizing_data['min_balance_threshold'],
            confidence_weighting=confidence_weighting_config,
            rr_weighting=rr_weighting_config,
            volatility_weighting=volatility_weighting_config,
            max_combined_weight=position_sizing_data['max_combined_weight'],
            min_combined_weight=position_sizing_data['min_combined_weight'],
            logging=logging_config
        )

    @classmethod
    def _load_state_machine_config(cls, state_machine_data: Dict[str, Any]) -> Optional[StateMachineConfig]:
        """Load state machine configuration from YAML data."""
        if not state_machine_data:
            return None

        return StateMachineConfig(
            enabled=state_machine_data['enabled'],
            parallel_mode=state_machine_data['parallel_mode'],
            log_comparison=state_machine_data['log_comparison'],
            fail_safely=state_machine_data['fail_safely']
        )

    @classmethod
    def _load_rr_tightening_config(cls, rr_tightening_data: Dict[str, Any]) -> Optional[RRTighteningConfig]:
        """Load RR tightening configuration from YAML data."""
        if not rr_tightening_data:
            return None

        # Load individual RR tightening steps
        steps_config = {}
        for step_name, step_data in rr_tightening_data.items():
            steps_config[step_name] = RRTighteningStepConfig(
                profit_threshold=step_data['profit_threshold'],
                sl_position=step_data['sl_position'],
                description=step_data['description']
            )

        return RRTighteningConfig(steps=steps_config)

    @classmethod
    def _load_age_tightening_config(cls, age_tightening_data: Dict[str, Any]) -> Optional[AgeTighteningConfig]:
        """Load age-based tightening configuration from YAML data."""
        if not age_tightening_data:
            return None

        return AgeTighteningConfig(
            enabled=age_tightening_data['enabled'],
            max_tightening_pct=age_tightening_data['max_tightening_pct'],
            min_profit_threshold=age_tightening_data['min_profit_threshold'],
            age_tightening_bars=age_tightening_data.get('age_tightening_bars')
        )

    @classmethod
    def _load_logging_config(cls, logging_data: Dict[str, Any]) -> Optional[LoggingConfig]:
        """Load logging configuration from YAML data."""
        if not logging_data:
            return None

        # Load rotation config
        rotation_data = logging_data.get('rotation')
        rotation_config = None
        if rotation_data:
            rotation_config = LoggingRotationConfig(
                max_file_size_mb=rotation_data['max_file_size_mb'],
                backup_count=rotation_data['backup_count']
            )

        return LoggingConfig(
            rotation=rotation_config,
            style=logging_data['style'],
            show_icons=logging_data['show_icons']
        )
    
    def get_agent_config(self, agent_name: str) -> AgentConfig:
        """Get configuration for specific agent."""
        return self.agents[agent_name]
    
    def validate_slot_based_config(self) -> Dict[str, Any]:
        """
        Validate slot-based risk management configuration.
        
        Returns:
            Dict with validation results and any errors found
        """
        errors = []
        warnings = []
        
        # Validate max_concurrent_trades
        max_trades = getattr(self.trading, 'max_concurrent_trades', 5)
        if not isinstance(max_trades, int) or max_trades < 1:
            errors.append("max_concurrent_trades must be a positive integer")
        elif max_trades > 20:
            warnings.append("max_concurrent_trades > 20 may lead to over-diversification")
        
        # Validate risk_percentage with slot context
        risk_pct = getattr(self.trading, 'risk_percentage', 0.01)
        if not isinstance(risk_pct, (int, float)) or risk_pct <= 0:
            errors.append("risk_percentage must be a positive number")
        elif risk_pct > 0.1:  # 10%
            warnings.append("risk_percentage > 10% is very aggressive")
        
        # Calculate risk per slot
        risk_per_slot = risk_pct / max_trades
        if risk_per_slot < 0.001:  # 0.1%
            warnings.append(f"Risk per slot ({risk_per_slot*100:.3f}%) may be too small for effective trading")
        
        # Validate max_loss_usd
        max_loss = getattr(self.trading, 'max_loss_usd', 200.0)
        if not isinstance(max_loss, (int, float)) or max_loss <= 0:
            errors.append("max_loss_usd must be a positive number")
        
        # Check balance_safety_margin
        safety_margin = getattr(self.trading, 'balance_safety_margin', 0.9)
        if not isinstance(safety_margin, (int, float)) or safety_margin <= 0 or safety_margin > 1:
            errors.append("balance_safety_margin must be between 0 and 1")
        
        # Check leverage
        leverage = getattr(self.trading, 'leverage', 1)
        if not isinstance(leverage, int) or leverage < 1 or leverage > 100:
            errors.append("leverage must be an integer between 1 and 100")
        
        # Validate min_rr
        min_rr = getattr(self.trading, 'min_rr', 1.2)
        if not isinstance(min_rr, (int, float)) or min_rr <= 0:
            errors.append("min_rr must be a positive number")
        elif min_rr < 1.0:
            warnings.append("min_rr < 1.0 means accepting negative expected value trades")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "config_summary": {
                "max_concurrent_trades": max_trades,
                "risk_percentage": risk_pct * 100,
                "risk_per_slot_percentage": risk_per_slot * 100,
                "max_loss_usd": max_loss,
                "balance_safety_margin": safety_margin * 100,
                "leverage": leverage,
                "min_rr": min_rr
            }
        }
