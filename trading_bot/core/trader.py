"""Trading execution module with Bybit integration."""
import logging
import threading
import time
import uuid
from datetime import datetime, timezone
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Any, Optional

# Import utilities from utils to avoid code duplication
from trading_bot.core.utils import normalize_symbol_for_bybit, smart_format_price
from trading_bot.config.settings import Config # Import Config
from trading_bot.core.bybit_api_manager import BybitAPIManager # Import BybitAPIManager

# Import real-time trade tracking
from trading_bot.core.realtime_trade_tracker import RealTimeTradeTracker

# Import TradeState for parallel operation
try:
    from trading_bot.core.trade_state_machine import TradeState
except ImportError:
    TradeState = None

class TradeExecutor:
    """Executes trades based on validated signals."""

    def __init__(self, config: Config, use_testnet: bool = False, position_manager: Optional[Any] = None, risk_manager: Optional[Any] = None, api_manager: Optional[BybitAPIManager] = None):
        self.config = config
        self.use_testnet = use_testnet
        self.position_manager = position_manager
        self.risk_manager = risk_manager
        self.logger = logging.getLogger(__name__)

        # Use provided API manager or create new one
        if api_manager:
            self.api_manager = api_manager
        else:
            # Initialize Bybit API Manager
            self.api_manager = BybitAPIManager(config, use_testnet)

        # Initialize real-time trade tracker (will be set by position manager)
        self.trade_tracker: Optional[RealTimeTradeTracker] = None
        self.trade_monitor = None  # Will be set by position manager

        # Symbol-level locking to prevent race conditions
        self._symbol_locks = {}  # Dict[str, threading.Lock] for per-symbol synchronization
        self._lock_timeout = 30.0  # 30 second timeout for lock acquisition

        # State Machine Configuration (Parallel Operation)
        state_machine_config = getattr(config.trading, 'state_machine', None)
        if state_machine_config:
            self.state_machine_enabled = getattr(state_machine_config, 'enabled')
            self.parallel_mode = getattr(state_machine_config, 'parallel_mode')
            self.log_comparison = getattr(state_machine_config, 'log_comparison')

        # Initialize State Machine if enabled
        self.state_machine = None
        if self.state_machine_enabled:
            try:
                from trading_bot.core.trade_state_machine import TradeStateMachine, TradeState
                self.state_machine = TradeStateMachine()
                logging.info("✅ Trade State Machine initialized (Parallel Mode)")
            except Exception as e:
                logging.error(f"❌ Failed to initialize state machine: {e}")
                self.state_machine = None
                self.state_machine_enabled = False

        # Exchange sync manager for position tracking
        self._exchange_sync_manager = None

    def cleanup_symbol_locks(self) -> None:
        """Clean up unused symbol locks to prevent memory leaks."""
        # Remove locks for symbols that haven't been used recently
        # This is a simple cleanup - in production you might want more sophisticated tracking
        current_symbols = set(self._symbol_locks.keys())
        if len(current_symbols) > 100:  # Arbitrary threshold
            logging.info(f"Cleaning up {len(current_symbols)} symbol locks")
            # For now, just log - in a real implementation you might remove old locks
            self._symbol_locks.clear()  # Uncomment if you want to clear all locks
    
    def get_exchange_sync_manager(self):
        """Get or create the exchange sync manager."""
        if self._exchange_sync_manager is None:
            try:
                from trading_bot.core.data_agent import DataAgent # Import DataAgent
                from trading_bot.core.rebuild_sync_manager import RebuildSyncManager, SyncConfig

                # Use the already loaded config object
                exchange_sync_config_data = self.config.trading.exchange_sync

                if exchange_sync_config_data is None:
                    logging.warning("Exchange sync configuration is not available in settings. Skipping initialization.")
                    return None

                # Create sync config from settings
                sync_config = SyncConfig(
                    enabled=getattr(exchange_sync_config_data, 'enabled'),
                    sync_interval_seconds=getattr(exchange_sync_config_data, 'sync_interval_seconds'),
                    max_retries=getattr(exchange_sync_config_data, 'max_retries'),
                    rate_limit_requests_per_second=getattr(exchange_sync_config_data, 'rate_limit_requests_per_second'),
                    batch_size=getattr(exchange_sync_config_data, 'batch_size'),
                    error_recovery_delay_seconds=getattr(exchange_sync_config_data, 'error_recovery_delay_seconds'),
                    incremental_sync_enabled=getattr(exchange_sync_config_data, 'incremental_sync_enabled'),
                    full_sync_interval_minutes=getattr(exchange_sync_config_data, 'full_sync_interval_minutes')
                )

                data_agent_instance: Optional[DataAgent] = None
                if self.position_manager and hasattr(self.position_manager, 'data_agent'):
                    data_agent_instance = self.position_manager.data_agent

                if data_agent_instance:
                    self._exchange_sync_manager = RebuildSyncManager(
                        data_agent=data_agent_instance,
                        trader=self,
                        config=sync_config
                    )
                    logging.info("Exchange sync manager initialized successfully")
                else:
                    logging.warning("No DataAgent available for exchange sync manager initialization")

            except Exception as e:
                logging.error(f"Failed to initialize exchange sync manager: {e}")
                return None
        return self._exchange_sync_manager
    
    def calculate_risk_reward_ratio(self, entry_price: float, tp_price: float, sl_price: float,
                                  direction: str = "LONG", symbol: Optional[str] = None, include_fees: bool = True) -> float:
        """Calculate risk-reward ratio with direction-specific logic and optional trading fees."""
        if entry_price <= 0 or tp_price <= 0 or sl_price <= 0:
            return 0.0
        
        direction = direction.upper()
        
        # Get trading fees if requested and symbol provided
        taker_fee_rate = 0.0
        if include_fees and symbol: # Removed self.session check
            fee_response = self.api_manager.get_fee_rates(symbol=symbol) # Use api_manager
            if "error" not in fee_response:
                try:
                    fee_list = fee_response.get("result", {}).get("list", [])
                    if fee_list:
                        taker_fee_rate = float(fee_list[0].get("takerFeeRate", 0))
                        logging.info(f"Using taker fee rate {taker_fee_rate:.4f} for {symbol}")
                except (ValueError, KeyError, IndexError):
                    logging.warning(f"Could not parse fee rate for {symbol}, using 0")
        
        if direction == "LONG":
            # For long positions: risk is entry to SL (downward), reward is entry to TP (upward)
            risk = entry_price - sl_price  # Should be positive (SL below entry)
            reward = tp_price - entry_price  # Should be positive (TP above entry)
            
            # Validate LONG position logic
            if risk <= 0:
                logging.warning(f"LONG position has invalid SL: entry={entry_price}, SL={sl_price}. SL should be below entry.")
            if reward <= 0:
                logging.warning(f"LONG position has invalid TP: entry={entry_price}, TP={tp_price}. TP should be above entry.")
                
        elif direction == "SHORT":
            # For short positions: risk is SL to entry (upward), reward is entry to TP (downward)
            risk = sl_price - entry_price  # Should be positive (SL above entry)
            reward = entry_price - tp_price  # Should be positive (TP below entry)
            
            # Validate SHORT position logic and auto-correct if needed
            if risk <= 0:
                logging.warning(f"SHORT position has invalid SL: entry={entry_price}, SL={sl_price}. SL should be above entry.")
                logging.info(f"Auto-correcting SHORT position: swapping SL and TP values")
                # Swap SL and TP for SHORT positions if they're inverted
                sl_price, tp_price = tp_price, sl_price
                risk = sl_price - entry_price
                reward = entry_price - tp_price
                logging.info(f"Corrected SHORT: entry={entry_price}, SL={sl_price}, TP={tp_price}")
                
            if reward <= 0:
                logging.warning(f"SHORT position has invalid TP: entry={entry_price}, TP={tp_price}. TP should be below entry.")
                if risk > 0:  # Only swap if we haven't already swapped
                    logging.info(f"Auto-correcting SHORT position: swapping SL and TP values")
                    sl_price, tp_price = tp_price, sl_price
                    risk = sl_price - entry_price
                    reward = entry_price - tp_price
                    logging.info(f"Corrected SHORT: entry={entry_price}, SL={sl_price}, TP={tp_price}")
        elif direction == "NEUTRAL":
            # For neutral positions, use absolute values (no directional bias)
            risk = abs(entry_price - sl_price)
            reward = abs(tp_price - entry_price)
        else:
            # Fallback for legacy values like BUY/SELL
            if direction in ["BUY"]:
                risk = entry_price - sl_price
                reward = tp_price - entry_price
            elif direction in ["SELL"]:
                risk = sl_price - entry_price
                reward = entry_price - tp_price
            else:
                # Unknown direction, use absolute values
                risk = abs(entry_price - sl_price)
                reward = abs(tp_price - entry_price)
        
        # Apply trading fees to both risk and reward
        if include_fees and taker_fee_rate > 0:
            # Entry fee (reduces reward, increases risk)
            entry_fee = entry_price * taker_fee_rate
            
            # Exit fees
            tp_fee = tp_price * taker_fee_rate
            sl_fee = sl_price * taker_fee_rate
            
            # Adjust risk and reward for fees
            risk = risk + entry_fee + sl_fee  # Risk increases due to fees
            reward = reward - entry_fee - tp_fee  # Reward decreases due to fees
            
            logging.info(f"Fee-adjusted RR for {symbol}: Entry fee: ${entry_fee:.4f}, "
                        f"TP fee: ${tp_fee:.4f}, SL fee: ${sl_fee:.4f}")
        
        if risk <= 0 or reward <= 0:
            logging.warning(f"Invalid RR calculation: risk={risk:.4f}, reward={reward:.4f}, direction={direction}")
            return 0.0
        
        return reward / risk
    
    def calculate_trade_score(self, confidence: float, rr_ratio: float,
                            time_elapsed_hours: float, current_price: float,
                            intended_entry_price: float) -> float:
        """
        Calculate trade score based on confidence, risk-reward ratio, recency, and distance factors.
        
        Args:
            confidence: Trade confidence (0.0 to 1.0)
            rr_ratio: Risk-reward ratio
            time_elapsed_hours: Hours since trade signal was generated
            current_price: Current market price
            intended_entry_price: Intended entry price from signal
            
        Returns:
            Trade score (higher is better)
        """
        # Recency Factor - trades lose value over time
        recency_factor = 1 / (1 + time_elapsed_hours)
        
        # Distance Factor - configurable impact multiplier
        distance_multiplier = getattr(self.config.trading, 'trade_scoring', {}).get('distance_factor_multiplier', 0.1)
        distance_from_entry = abs(current_price - intended_entry_price) / intended_entry_price
        distance_factor = 1 / (1 + distance_from_entry * distance_multiplier)
        
        # Calculate final score
        score = confidence * rr_ratio * recency_factor * distance_factor
        
        logging.info(f"Trade score calculation: confidence={confidence:.3f}, rr={rr_ratio:.2f}, "
                    f"recency={recency_factor:.3f}, distance={distance_factor:.3f}, "
                    f"final_score={score:.3f}")
        
        return score
    
    def compare_trades(self, existing_trade: Dict[str, Any], new_signal: Dict[str, Any],
                      symbol: str, current_price: Optional[float] = None) -> Dict[str, Any]:
        """
        Compare existing trade with new signal to determine which one to keep.
        
        Args:
            existing_trade: Current active trade data
            new_signal: New trade signal data
            symbol: Trading symbol
            current_price: Current market price (will fetch if not provided)
            
        Returns:
            Dict with comparison result and recommendation
        """
        try:
            # Get current price if not provided
            if current_price is None:
                current_price = self.get_last_close_price(symbol, "1m")
                if current_price is None:
                    return {"error": "Could not fetch current price for comparison"}
            
            # Calculate time elapsed for existing trade
            existing_timestamp = existing_trade.get("timestamp")
            existing_time = datetime.now(timezone.utc)  # Default fallback
            
            if isinstance(existing_timestamp, str):
                try:
                    existing_time = datetime.fromisoformat(existing_timestamp.replace('Z', '+00:00'))
                except ValueError:
                    existing_time = datetime.now(timezone.utc)
            elif hasattr(existing_timestamp, 'timestamp'):
                existing_time = existing_timestamp
            
            # Ensure timezone awareness
            if existing_time and existing_time.tzinfo is None:
                existing_time = existing_time.replace(tzinfo=timezone.utc)
            
            # Calculate time elapsed, handle None case
            if existing_time is not None:
                time_elapsed = (datetime.now(timezone.utc) - existing_time).total_seconds() / 3600
            else:
                time_elapsed = 0  # Default to 0 hours if no existing time
            
            # Calculate scores
            existing_score = self.calculate_trade_score(
                confidence=existing_trade.get("confidence", 0.5),
                rr_ratio=existing_trade.get("risk_reward_ratio", 1.0),
                time_elapsed_hours=time_elapsed,
                current_price=current_price,
                intended_entry_price=existing_trade.get("entry_price", current_price)
            )
            
            new_score = self.calculate_trade_score(
                confidence=new_signal.get("confidence", 0.5),
                rr_ratio=self.calculate_risk_reward_ratio(
                    new_signal.get("entry_price", current_price),
                    new_signal.get("take_profit", current_price * 1.02),
                    new_signal.get("stop_loss", current_price * 0.98),
                    new_signal.get("direction", "LONG"),
                    symbol
                ),
                time_elapsed_hours=0.0,  # New signal has no time elapsed
                current_price=current_price,
                intended_entry_price=new_signal.get("entry_price", current_price)
            )
            
            keep_new = new_score > existing_score
            score_difference = new_score - existing_score
            
            # Enhanced replacement decision logging
            if keep_new:
                improvement_percentage = (score_difference / existing_score * 100) if existing_score > 0 else 0
                existing_entry = existing_trade.get("entry_price", "N/A")
                new_entry = new_signal.get("entry_price", "N/A")
                
                logging.info(f"🔄 REPLACEMENT DECISION: {symbol}")
                logging.info(f"   EXISTING: Entry=${existing_entry}, Score={existing_score:.3f}")
                logging.info(f"   NEW SIGNAL: Entry=${new_entry}, Score={new_score:.3f}")
                logging.info(f"   DECISION: Replace (improvement: +{score_difference:.3f}, {improvement_percentage:+.1f}%)")
            else:
                logging.info(f"📊 TRADE COMPARISON: {symbol} - Keeping existing trade (score: {existing_score:.3f} vs {new_score:.3f})")
            
            return {
                "existing_score": existing_score,
                "new_score": new_score,
                "keep_new": keep_new,
                "score_difference": score_difference,
                "recommendation": "replace" if keep_new else "keep_existing"
            }
            
        except Exception as e:
            logging.error(f"Error comparing trades: {e}")
            return {"error": str(e)}
    
    def validate_trade_parameters(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Validate trade parameters including TP/SL and risk-reward ratio."""
        required_fields = ["recommendation", "entry_price", "take_profit", "stop_loss"]
        
        # Check required fields
        missing_fields = [field for field in required_fields if field not in signal]
        if missing_fields:
            return {"valid": False, "error": f"Missing required fields: {missing_fields}"}
        
        # Extract prices
        entry_price = float(signal["entry_price"])
        tp_price = float(signal["take_profit"])
        sl_price = float(signal["stop_loss"])
        
        # Validate prices
        if entry_price <= 0 or tp_price <= 0 or sl_price <= 0:
            return {"valid": False, "error": "All prices must be positive"}
        
        # Calculate risk-reward ratio with direction and fees
        # Check if direction is provided separately, otherwise derive from recommendation
        if "direction" in signal:
            direction = signal["direction"].upper()
        else:
            # Fallback: derive direction from recommendation
            recommendation = signal["recommendation"].upper()
            if recommendation in ["BUY", "LONG"]:
                direction = "LONG"
            elif recommendation in ["SELL", "SHORT"]:
                direction = "SHORT"
            else:
                direction = "NEUTRAL"
        
        # Get symbol from signal if available for fee calculation
        symbol = signal.get("symbol", None)
        symbol = normalize_symbol_for_bybit(str(symbol))
        rr_ratio = self.calculate_risk_reward_ratio(entry_price, tp_price, sl_price, direction, symbol)
        
        # Check minimum risk-reward ratio
        if rr_ratio < self.config.trading.min_rr:
            return {
                "valid": False,
                "error": f"Risk-reward ratio {rr_ratio:.2f} is below minimum {self.config.trading.min_rr}"
            }
        
        # Validate TP/SL logic based on trade direction
        recommendation = signal["recommendation"].upper()
        
        if recommendation in ["BUY", "LONG"]:
            if tp_price <= entry_price:
                return {"valid": False, "error": "Take profit must be above entry price for long positions"}
            if sl_price >= entry_price:
                return {"valid": False, "error": "Stop loss must be below entry price for long positions"}
        elif recommendation in ["SELL", "SHORT"]:
            if tp_price >= entry_price:
                return {"valid": False, "error": "Take profit must be below entry price for short positions"}
            if sl_price <= entry_price:
                return {"valid": False, "error": "Stop loss must be above entry price for short positions"}
        else:
            return {"valid": False, "error": f"Invalid recommendation: {recommendation}"}
        
        return {
            "valid": True,
            "rr_ratio": rr_ratio,
            "entry_price": entry_price,
            "take_profit": tp_price,
            "stop_loss": sl_price
        }
    
    def get_positions(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get current positions from Bybit."""
        try:
            params: Dict[str, Any] = {
                "category": "linear",
                "settleCoin": "USDT"
            }
            if symbol:
                params["symbol"] = normalize_symbol_for_bybit(symbol)
            
            response = self.api_manager.get_positions(**params) # Use api_manager
            if isinstance(response, dict):
                return response
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            return {"error": str(e)}
    
    def get_open_orders(self, symbol: Optional[str] = None, openOnly: int = 1, limit: int = 50) -> Dict[str, Any]:
        """Get open orders from Bybit."""
        try:
            params: Dict[str, Any] = {
                "category": "linear",
                "openOnly": openOnly,  # Allow custom openOnly parameter
                "limit": limit
            }

            if symbol:
                params["symbol"] = normalize_symbol_for_bybit(symbol)
            else:
                # When no specific symbol is provided, use settleCoin for linear contracts
                # This is required by Bybit API - must provide symbol, settleCoin, or baseCoin
                params["settleCoin"] = "USDT"

            logging.info(f"Getting open orders with params: {params}")
            response = self.api_manager.get_open_orders(**params) # Use api_manager

            if isinstance(response, dict):
                if response.get("retCode") == 0:
                    logging.info(f"Successfully retrieved {len(response.get('result', {}).get('list', []))} open orders")
                else:
                    logging.warning(f"Bybit API returned non-zero retCode: {response}")
                return response
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            logging.error(f"Exception in get_open_orders: {str(e)}")
            return {"error": str(e)}
    

    
    def get_last_close_price(self, symbol: str, interval: str) -> Optional[float]:
        """Get the last close price for a given symbol and interval."""
        # Map common intervals to Bybit kline intervals
        interval_map = {
            "1m": "1", "5m": "5", "15m": "15", "30m": "30",
            "1h": "60", "2h": "120", "4h": "240", "6h": "360",
            "12h": "720", "1d": "D", "1w": "W", "1M": "M"
        }
        bybit_interval = interval_map.get(interval.lower())
        
        if not bybit_interval:
            logging.warning(f"Unsupported interval for kline: {interval}. Using default '60' (1h).")
            bybit_interval = "5" # Default to 1 hour
            
        try:
            # Fetch only the latest kline to get the last close price
            response = self.api_manager.get_kline( # Use api_manager
                category="linear", # Assuming linear for trading bot
                symbol=normalize_symbol_for_bybit(symbol),
                interval=bybit_interval,
                limit=1, # Get only the latest candle
            )
            
            if response and response.get("retCode") == 0 and response.get("result") and response["result"].get("list"):
                # kline data is [timestamp, open, high, low, close, volume, turnover]
                last_candle = response["result"]["list"][0]
                return float(last_candle[4]) # Close price is at index 4
            else:
                logging.warning(f"❌ Could not retrieve kline for {symbol} {interval}: retCode={response.get('retCode', 'unknown')}, retMsg={response.get('retMsg', 'unknown')}")
                return None
        except Exception as e:
            logging.error(f"❌ Error fetching kline for {symbol} {interval}: {e}")
            logging.error(f"❌ Exception type: {type(e).__name__}")
            import traceback
            logging.error(f"❌ Kline fetch traceback: {traceback.format_exc()}")
            return None

    def get_instruments_info(self, symbol: str) -> Dict[str, Any]:
        """Get instrument information for a specific symbol."""
        # Normalize symbol for Bybit API
        normalized_symbol = normalize_symbol_for_bybit(symbol)
        
        # Try the normalized symbol first
        try:
            response = self.api_manager.get_instruments_info( # Use api_manager
                category="linear",
                symbol=normalized_symbol
            )
            if isinstance(response, dict):
                # Check if the response indicates success
                if response.get("retCode") == 0:
                    return response
                # If symbol invalid error, try with 1000 prefix
                elif response.get("retCode") == 10001 and "symbol invalid" in str(response.get("retMsg", "")):
                    return self._try_with_1000_prefix(normalized_symbol)
                else:
                    return response
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            # If exception contains symbol invalid, try with 1000 prefix
            if "symbol invalid" in str(e) or "10001" in str(e):
                return self._try_with_1000_prefix(normalized_symbol)
            return {"error": str(e)}

    def _try_with_1000_prefix(self, symbol: str) -> Dict[str, Any]:
        """Try to get instrument info with 1000 prefix for certain tokens like PEPE."""
        try:
            # Add 1000 prefix if not already present
            if not symbol.startswith("1000"):
                prefixed_symbol = "1000" + symbol
                
                response = self.api_manager.get_instruments_info( # Use api_manager
                    category="linear",
                    symbol=prefixed_symbol
                )
                
                if isinstance(response, dict):
                    if response.get("retCode") == 0:
                        logging.info(f"Successfully found instrument with 1000 prefix: {prefixed_symbol}")
                        return response
                    else:
                        return {"error": f"Failed to get instrument info for both {symbol} and {prefixed_symbol}"}
                
            return {"error": f"Failed to get instrument info for {symbol}"}
            
        except Exception as e:
            return {"error": f"Failed to get instrument info for {symbol}: {str(e)}"}
    
    def get_fee_rates(self, symbol: str) -> Dict[str, Any]:
        """Get trading fee rates for a specific symbol."""
        # Normalize symbol for Bybit API
        normalized_symbol = normalize_symbol_for_bybit(symbol)
        
        try:
            response = self.api_manager.get_fee_rates( # Use api_manager
                category="linear",
                symbol=normalized_symbol
            )
            if isinstance(response, dict):
                if response.get("retCode") == 0:
                    return response
                else:
                    return {"error": response.get("retMsg", "Failed to get fee rates")}
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            return {"error": str(e)}

    def set_leverage(self, symbol: str, leverage: int) -> Dict[str, Any]:
        """Set leverage for a specific symbol."""
        # Normalize symbol for Bybit API
        normalized_symbol = normalize_symbol_for_bybit(symbol)
        
        try:
            response = self.api_manager.set_leverage( # Use api_manager
                category="linear",
                symbol=normalized_symbol,
                buyLeverage=str(leverage),
                sellLeverage=str(leverage)
            )
            if isinstance(response, dict):
                if response.get("retCode") == 0:
                    logging.info(f"Leverage set to {leverage}x for {symbol}")
                    return {"success": True, "leverage": leverage}
                else:
                    return {"error": response.get("retMsg", "Failed to set leverage")}
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            return {"error": str(e)}

    def test_connection(self) -> Dict[str, Any]:
        """Test Bybit connection by fetching account info."""
        try:
            # Test with a simple API call
            response = self.api_manager.get_wallet_balance(accountType="UNIFIED") # Use api_manager
            if isinstance(response, dict):
                return {
                    "connected": True,
                    "account_info": response.get("result", {})
                }
            return {"connected": False, "error": "Invalid response from Bybit API"}
        except Exception as e:
            return {"connected": False, "error": str(e)}
    
    def get_wallet_balance(self, coin: str = "USDT") -> Dict[str, Any]:
        """Get wallet balance for a specific coin."""
        try:
            response = self.api_manager.get_wallet_balance( # Use api_manager
                accountType="UNIFIED",
                coin=coin
            )
            if isinstance(response, dict):
                return response
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            return {"error": str(e)}
    
    def calculate_position_size(self, entry_price: float, stop_loss: float,
                              symbol: str, risk_percentage: Optional[float] = None) -> Dict[str, Any]:
        """Calculate position size based on risk management with instrument validation and USD hard cap."""
        if risk_percentage is None:
            risk_percentage = getattr(self.config.trading, 'risk_percentage')
        
        # Ensure risk_percentage is a float (it should not be None at this point)
        if risk_percentage is None:
            risk_percentage = 0.03
        
        # Get instrument info for validation
        instrument_info = self.get_instruments_info(symbol)
        if "error" in instrument_info:
            return {"error": f"Failed to get instrument info: {instrument_info['error']}"}
        
        # Extract instrument limits
        try:
            instrument_data = instrument_info.get("result", {}).get("list", [])
            if not instrument_data:
                return {"error": f"No instrument data found for {symbol}"}
            
            instrument = instrument_data[0]
            lot_size_filter = instrument.get("lotSizeFilter", {})
            
            min_order_qty = float(lot_size_filter.get("minOrderQty", 0))
            max_order_qty = float(lot_size_filter.get("maxOrderQty", float('inf')))
            qty_step = float(lot_size_filter.get("qtyStep", 0.000001))
            
        except (ValueError, KeyError, IndexError) as e:
            return {"error": f"Failed to parse instrument data: {str(e)}"}
        
        # Get USDT balance
        balance_response = self.get_wallet_balance("USDT")
        if "error" in balance_response:
            return {"error": f"Failed to get wallet balance: {balance_response['error']}"}
        
        try:
            # Extract USDT balance from response
            result = balance_response.get("result", {})
            coin_list = result.get("list", [])
            
            usdt_balance = 0.0
            wallet_balance = 0.0
            coin_available_balance = 0.0
            total_available_balance = 0.0
            
            for account in coin_list:
                # Get total available balance for margin trading (this is the key!)
                total_available_balance = float(account.get("totalAvailableBalance", 0))
                
                # Also get USDT-specific balances for detailed logging
                coins = account.get("coin", [])
                for coin in coins:
                    if coin.get("coin") == "USDT":
                        coin_available_balance = float(coin.get("availableBalance", 0))
                        wallet_balance = float(coin.get("walletBalance", 0))
                        break
                
                # For margin trading, use totalAvailableBalance as it represents
                # the actual buying power available across all collateral
                if total_available_balance > 0:
                    usdt_balance = total_available_balance
                    break
            
            logging.info(f"Balance Details: Total Available=${total_available_balance:.2f}, "
                        f"USDT Wallet=${wallet_balance:.2f}, USDT Available=${coin_available_balance:.2f}")
            
            if usdt_balance <= 0:
                # Provide detailed error information
                if wallet_balance > 0:
                    return {
                        "error": f"No available balance for trading. Total available balance: ${total_available_balance:.2f}, "
                                f"USDT wallet balance: ${wallet_balance:.2f}. "
                                f"This usually means funds are tied up in open orders or positions. "
                                f"Please close some positions or cancel orders to free up balance."
                    }
                else:
                    return {"error": "No USDT balance found in wallet"}
            
            # Calculate risk amount (percentage of total capital)
            risk_amount = usdt_balance * risk_percentage
            
            # Apply USD hard cap from config
            max_loss_usd = getattr(self.config.trading, 'max_loss_usd')
            if risk_amount > max_loss_usd:
                risk_amount = max_loss_usd
                logging.warning(f"Risk amount capped at ${max_loss_usd} USD (was ${smart_format_price(usdt_balance * risk_percentage)})")
            
            # Log balance and leverage info for debugging
            leverage = getattr(self.config.trading, 'leverage', 1)
            logging.info(f"POSITION CALC for {symbol}: Balance=${usdt_balance:.2f}, Leverage={leverage}x, Risk=${risk_amount:.2f}")
            
            # Calculate price difference for risk calculation
            price_diff = abs(entry_price - stop_loss)
            if price_diff <= 0:
                return {"error": "Invalid entry price or stop loss"}
            
            # Get leverage for calculations
            leverage = getattr(self.config.trading, 'leverage', 1)
            
            # Get trading fees for more accurate position sizing
            taker_fee_rate = 0.0
            fee_response = self.get_fee_rates(symbol)
            if "error" not in fee_response:
                try:
                    fee_list = fee_response.get("result", {}).get("list", [])
                    if fee_list:
                        taker_fee_rate = float(fee_list[0].get("takerFeeRate", 0))
                        logging.info(f"Using taker fee rate {taker_fee_rate:.4f} for position sizing")
                except (ValueError, KeyError, IndexError):
                    logging.warning(f"Could not parse fee rate for {symbol}, using 0")
            
            # Calculate risk-based position size accounting for fees
            # We need to account for entry fee + stop loss fee in the risk calculation
            if taker_fee_rate > 0:
                # Adjusted price difference includes fees
                entry_fee_cost = entry_price * taker_fee_rate
                sl_fee_cost = stop_loss * taker_fee_rate
                total_fee_cost = entry_fee_cost + sl_fee_cost
                
                # Effective risk includes price movement + fees
                effective_risk_per_unit = price_diff + total_fee_cost
                risk_based_position_size = risk_amount / effective_risk_per_unit
                
                logging.info(f"Fee-adjusted position sizing: Entry fee=${entry_fee_cost:.4f}, "
                           f"SL fee=${sl_fee_cost:.4f}, Effective risk per unit=${effective_risk_per_unit:.4f}")
            else:
                # No fees, use original calculation
                risk_based_position_size = risk_amount / price_diff
            
            # Calculate balance-based position size (respecting available balance)
            safety_margin = self.config.trading.balance_safety_margin
            max_usable_balance = usdt_balance * safety_margin
            max_position_value = max_usable_balance * leverage
            balance_based_position_size = max_position_value / entry_price
            
            # Use the SMALLER of risk-based or balance-based position size
            position_size = min(risk_based_position_size, balance_based_position_size)
            
            # Log which constraint is limiting the position size
            if position_size == risk_based_position_size:
                self.logger.info(f"Position size limited by risk management: {position_size:.6f} {symbol}")
            else:
                self.logger.info(f"Position size limited by available balance: {position_size:.6f} {symbol} (risk-based would be {risk_based_position_size:.6f})")
            
            # Use Decimal for precise quantity calculation to avoid floating point errors
            # Convert to Decimal for precise arithmetic
            position_size_decimal = Decimal(str(position_size))
            qty_step_decimal = Decimal(str(qty_step))
            
            # Calculate the number of steps and round down to ensure we don't exceed limits
            steps = (position_size_decimal / qty_step_decimal).quantize(Decimal('1'), rounding=ROUND_DOWN)
            
            # Calculate final position size with exact precision
            position_size_precise = steps * qty_step_decimal
            position_size = float(position_size_precise)
            
            logging.info(f"Precision fix: Original={position_size_decimal}, Steps={steps}, "
                        f"Final={position_size_precise}, qty_step={qty_step_decimal}")
            
            # Validate against exchange limits
            if position_size < min_order_qty:
                # Calculate what balance would be needed for minimum position
                min_position_value = min_order_qty * entry_price
                min_risk_amount = min_order_qty * price_diff
                min_balance_needed = min_risk_amount / risk_percentage
                
                return {
                    "error": f"Insufficient balance for {symbol}. Need ${min_balance_needed:.0f}+ USDT "
                            f"(minimum order: {min_order_qty} {symbol.replace('USDT', '')}, "
                            f"worth ${min_position_value:.0f})"
                }
            
            if position_size > max_order_qty:
                return {"error": f"Position size {position_size:.8f} exceeds maximum {max_order_qty}"}
            
            # Recalculate actual risk with rounded position size
            actual_risk = position_size * price_diff
            position_value = position_size * entry_price
            
            reason_for_adjustment = "No adjustment needed."
            if position_size == risk_based_position_size:
                reason_for_adjustment = "Position size limited by risk management."
            elif position_size == balance_based_position_size:
                reason_for_adjustment = "Position size limited by available balance."
            
            return {
                "success": True,
                "usdt_balance": usdt_balance,
                "risk_amount": risk_amount,
                "actual_risk": actual_risk,
                "risk_percentage": risk_percentage * 100,
                "position_size": position_size,
                "position_value": position_value,
                "price_diff": price_diff,
                "entry_price": entry_price,
                "stop_loss": stop_loss,
                "min_order_qty": min_order_qty,
                "max_order_qty": max_order_qty,
                "qty_step": qty_step,
                "max_loss_usd": max_loss_usd,
                "risk_capped": risk_amount < (usdt_balance * risk_percentage),
                "reason_for_adjustment": reason_for_adjustment
            }
            
        except Exception as e:
            return {"error": f"Error calculating position size: {str(e)}"}
    
    def place_market_order_with_tp_sl(self, symbol: str, side: str, qty: float,
                                     tp_price: float, sl_price: float,
                                     order_link_id: Optional[str] = None) -> Dict[str, Any]:
        """Place a market order with take profit and stop loss."""
        try:
            # Normalize symbol and adjust prices if needed
            normalized_symbol = normalize_symbol_for_bybit(symbol)
            
            # Prices should already be correctly scaled for the normalized symbol
            # by the calling function or the signal generation process.
            # No additional scaling needed here.
            adjusted_tp_price = tp_price
            adjusted_sl_price = sl_price
            
            params: Dict[str, Any] = {
                "category": "linear",
                "symbol": normalized_symbol,
                "side": side.capitalize(),
                "orderType": "Market",
                "qty": str(qty),
                "timeInForce": "IOC",
                "takeProfit": str(adjusted_tp_price),
                "stopLoss": str(adjusted_sl_price)
            }
            
            if order_link_id:
                params["orderLinkId"] = order_link_id
            
            response = self.api_manager.place_order(**params) # Use api_manager
            
            # Debug logging to capture raw API response structure
            logging.debug(f"Raw Bybit API response: {response}")
            
            if isinstance(response, dict):
                # Check if response has error and log the structure
                if response.get("retCode") != 0:
                    logging.debug(f"Bybit API error - retCode: {response.get('retCode')}, retMsg: {response.get('retMsg')}")
                    # Return error in expected format
                    return {"error": response.get("retMsg", "Unknown Bybit API error")}
                return response
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            return {"error": str(e)}
    
    def place_limit_order_with_tp_sl(self, symbol: str, side: str, qty: float, 
                                    price: float, tp_price: float, sl_price: float,
                                    time_in_force: str = "GTC", order_link_id: Optional[str] = None) -> Dict[str, Any]:
        """Place a limit order with take profit and stop loss."""
        try:
            # Normalize symbol and adjust prices if needed
            normalized_symbol = normalize_symbol_for_bybit(symbol)
            
            # Prices should already be correctly scaled for the normalized symbol
            # by the calling function or the signal generation process.
            # No additional scaling needed here.
            adjusted_price = price
            adjusted_tp_price = tp_price
            adjusted_sl_price = sl_price
            
            params: Dict[str, Any] = {
                "category": "linear",
                "symbol": normalized_symbol,
                "side": side.capitalize(),
                "orderType": "Limit",
                "qty": str(qty),
                "price": str(adjusted_price),
                "timeInForce": time_in_force,
                "takeProfit": str(adjusted_tp_price),
                "stopLoss": str(adjusted_sl_price)
            }
            
            if order_link_id:
                params["orderLinkId"] = order_link_id
            
            response = self.api_manager.place_order(**params) # Use api_manager
            
            # Debug logging to capture raw API response structure
            logging.debug(f"Raw Bybit API response: {response}")
            
            if isinstance(response, dict):
                # Check if response has error and log the structure
                if response.get("retCode") != 0:
                    logging.debug(f"Bybit API error - retCode: {response.get('retCode')}, retMsg: {response.get('retMsg')}")
                    # Return error in expected format
                    return {"error": response.get("retMsg", "Unknown Bybit API error")}
                return response
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            return {"error": str(e)}
    
    def cancel_order(self, symbol: str, order_id: Optional[str] = None,
                    order_link_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Cancel an order using the centralized utils function for consistency.

        This method now delegates to the centralized cancel_order_with_verification
        function to ensure all order cancellations follow the same verification
        and error handling logic.

        Args:
            symbol: Trading symbol
            order_id: Bybit order ID (optional if order_link_id provided)
            order_link_id: Client-generated order link ID (optional if order_id provided)

        Returns:
            Dict with cancellation result
        """
        # Import the centralized function
        from trading_bot.core.utils import cancel_order_with_verification

        # Use the centralized cancellation function
        return cancel_order_with_verification(
            trader=self,
            symbol=symbol,
            order_id=order_id,
            order_link_id=order_link_id,
            max_retries=3,
            retry_delay=0.5
        )
    
    def cancel_all_orders(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Cancel all open orders."""
        try:
            params: Dict[str, Any] = {"category": "linear"}
            if symbol:
                params["symbol"] = symbol

            response = self.api_manager.cancel_all_orders(**params) # Use api_manager
            if isinstance(response, dict):
                return response
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            return {"error": str(e)}

    def cleanup_exchange_duplicates(self) -> Dict[str, Any]:
        """
        SINGLE SOURCE OF TRUTH: Clean up duplicate orders at exchange level.

        SAFETY CONSTRAINTS:
        - ONLY cancels NEW entry orders (never PartiallyFilled)
        - NEVER touches TP/SL orders (stopOrderType not empty)
        - Uses exchange as single source of truth

        DUPLICATE SCENARIOS HANDLED:
        1. Multiple NEW entry orders for same symbol → cancel older ones
        2. NEW entry order + position for same symbol → cancel entry order
        """
        try:
            cancelled_orders = []
            errors = []

            # Get fresh exchange state - SINGLE SOURCE OF TRUTH
            positions_response = self.get_positions()
            orders_response = self.get_open_orders()

            if "error" in positions_response:
                return {"error": f"Failed to get positions: {positions_response['error']}"}

            if "error" in orders_response:
                return {"error": f"Failed to get orders: {orders_response['error']}"}

            # Extract data
            positions_data = positions_response.get("result", {}).get("list", [])
            orders_data = orders_response.get("result", {}).get("list", [])

            # Create positions lookup (symbol -> position)
            open_positions = {}
            for pos in positions_data:
                if float(pos.get('size', 0)) > 0:  # Only actual positions
                    symbol = pos.get('symbol')
                    if symbol:
                        open_positions[symbol] = pos

            # SAFETY FILTER: Only NEW entry orders (never PartiallyFilled, never TP/SL)
            safe_entry_orders = [
                order for order in orders_data
                if (order.get('orderStatus') == 'New'  # Only completely unfilled orders
                and not order.get('stopOrderType'))     # Never TP/SL orders
            ]

            logging.info(f"🧹 Exchange cleanup: Found {len(safe_entry_orders)} safe entry orders, "
                        f"{len(open_positions)} open positions")

            # Group orders by symbol
            orders_by_symbol = {}
            for order in safe_entry_orders:
                symbol = order.get('symbol')
                if symbol:
                    if symbol not in orders_by_symbol:
                        orders_by_symbol[symbol] = []
                    orders_by_symbol[symbol].append(order)

            # Process each symbol for duplicates
            for symbol, orders in orders_by_symbol.items():
                try:
                    # SCENARIO 1: Multiple NEW entry orders for same symbol
                    if len(orders) > 1:
                        logging.info(f"🔄 Multiple entry orders for {symbol}: {len(orders)} orders")

                        # Sort by creation time (newest first) - use createdTime if available
                        sorted_orders = sorted(
                            orders,
                            key=lambda o: o.get('createdTime', 0),
                            reverse=True  # Newest first
                        )

                        # Cancel older orders, keep the newest
                        orders_to_cancel = sorted_orders[1:]  # All except newest

                        for order in orders_to_cancel:
                            order_id = order.get('orderId')
                            order_link_id = order.get('orderLinkId')

                            logging.info(f"🗑️ Cancelling duplicate entry order: {symbol} {order_id}")

                            cancel_result = self.cancel_order(
                                symbol=symbol,
                                order_id=order_id,
                                order_link_id=order_link_id
                            )

                            if "error" in cancel_result:
                                error_msg = f"Failed to cancel {symbol} order {order_id}: {cancel_result['error']}"
                                logging.error(f"❌ {error_msg}")
                                errors.append(error_msg)
                            else:
                                logging.info(f"✅ Successfully cancelled duplicate: {symbol} {order_id}")
                                cancelled_orders.append({
                                    "symbol": symbol,
                                    "order_id": order_id,
                                    "reason": "duplicate_entry_order",
                                    "type": "multiple_entry_orders"
                                })

                    # SCENARIO 2: NEW entry order + position for same symbol
                    elif len(orders) == 1 and symbol in open_positions:
                        order = orders[0]
                        order_id = order.get('orderId')
                        order_link_id = order.get('orderLinkId')

                        logging.info(f"🔄 Entry order + position conflict for {symbol}")

                        # Cancel the entry order since we already have a position
                        cancel_result = self.cancel_order(
                            symbol=symbol,
                            order_id=order_id,
                            order_link_id=order_link_id
                        )

                        if "error" in cancel_result:
                            error_msg = f"Failed to cancel {symbol} entry order {order_id}: {cancel_result['error']}"
                            logging.error(f"❌ {error_msg}")
                            errors.append(error_msg)
                        else:
                            logging.info(f"✅ Successfully cancelled entry order (position exists): {symbol} {order_id}")
                            cancelled_orders.append({
                                "symbol": symbol,
                                "order_id": order_id,
                                "reason": "entry_order_with_position",
                                "type": "entry_order_plus_position"
                            })

                except Exception as e:
                    error_msg = f"Error processing {symbol}: {str(e)}"
                    logging.error(f"❌ {error_msg}")
                    errors.append(error_msg)

            # Summary
            result = {
                "success": True,
                "cancelled_orders": cancelled_orders,
                "errors": errors,
                "summary": {
                    "total_cancelled": len(cancelled_orders),
                    "total_errors": len(errors),
                    "symbols_processed": len(orders_by_symbol),
                    "open_positions": len(open_positions)
                }
            }

            logging.info(f"🧹 Duplicate cleanup complete: {len(cancelled_orders)} cancelled, {len(errors)} errors")
            return result

        except Exception as e:
            error_msg = f"Critical error in duplicate cleanup: {str(e)}"
            logging.error(f"❌ {error_msg}")
            return {"error": error_msg, "success": False}
    
    def get_order_history(self, symbol: Optional[str] = None, limit: int = 20) -> Dict[str, Any]:
        """Get order history."""
        try:
            params: Dict[str, Any] = {
                "category": "linear",
                "limit": limit
            }
            if symbol:
                params["symbol"] = symbol
            
            response = self.api_manager.get_order_history(**params) # Use api_manager
            if isinstance(response, dict):
                return response
            return {"error": "Invalid response from Bybit API"}
        except Exception as e:
            return {"error": str(e)}
    
    def get_all_open_positions(self) -> Dict[str, bool]:
        """Get all open positions at once for batch checking."""
        try:
            positions = self.api_manager.get_positions(category="linear", settleCoin="USDT") # Use api_manager
            
            if isinstance(positions, dict) and positions.get("retCode") == 0:
                position_list = positions.get("result", {}).get("list", [])
                open_positions = {}
                actual_positions = 0
                
                for position in position_list:
                    symbol = position.get("symbol", "")
                    size = float(position.get("size", 0))
                    # Only count actual positions (size > 0), not TP/SL orders
                    if size > 0:
                        open_positions[symbol] = True
                        # REMOVED: Artificial .P suffix addition for compatibility
                        # if not symbol.endswith('.P'):
                        #     open_positions[f"{symbol}.P"] = True
                        actual_positions += 1
                
                logging.info(f"Batch position check: Found {actual_positions} actual open positions (filtered out TP/SL orders)")
                return open_positions
            else:
                logging.warning("Failed to get batch positions, falling back to individual checks")
                return {}
                
        except Exception as e:
            logging.warning(f"Exception in batch position check: {e}, falling back to individual checks")
            return {}

    def has_duplicate_order(self, symbol: str, side: str, entry_price: float, tolerance: float = 0.001) -> Dict[str, Any]:
        """
        Check if there's already an existing order for the same symbol, side, and similar entry price.
        
        Args:
            symbol: Trading symbol
            side: Order side (Buy/Sell)
            entry_price: Entry price to check
            tolerance: Price tolerance as a percentage (default 0.1%)
            
        Returns:
            Dict with 'has_duplicate' boolean and 'existing_order' details if found
        """
        try:
            # Get open orders from exchange
            orders_response = self.get_open_orders(symbol)
            if "error" in orders_response:
                logging.warning(f"Could not check for duplicate orders: {orders_response['error']}")
                return {"has_duplicate": False, "error": orders_response["error"]}
            
            if orders_response.get("retCode") != 0:
                logging.warning(f"Failed to get open orders: {orders_response.get('retMsg', 'Unknown error')}")
                return {"has_duplicate": False, "error": orders_response.get('retMsg', 'Unknown error')}
            
            orders = orders_response.get("result", {}).get("list", [])
            
            # Check each order for duplicates
            for order in orders:
                order_symbol = order.get("symbol", "")
                order_side = order.get("side", "")
                order_price = float(order.get("price", 0))
                order_status = order.get("orderStatus", "")
                
                # Skip cancelled or filled orders
                if order_status in ["Cancelled", "Filled", "Rejected"]:
                    continue
                
                # Check if symbol and side match
                if order_symbol == normalize_symbol_for_bybit(symbol) and order_side == side:
                    # Check if price is within tolerance
                    price_diff = abs(order_price - entry_price) / entry_price
                    if price_diff <= tolerance:
                        logging.info(f"Duplicate order found: {order_symbol} {order_side} @ {order_price} "
                                   f"(new: {entry_price}, diff: {price_diff:.4f})")
                        return {
                            "has_duplicate": True,
                            "existing_order": {
                                "orderId": order.get("orderId"),
                                "symbol": order_symbol,
                                "side": order_side,
                                "price": order_price,
                                "qty": order.get("qty"),
                                "orderStatus": order_status,
                                "price_difference": price_diff
                            }
                        }
            
            return {"has_duplicate": False}
            
        except Exception as e:
            logging.error(f"Error checking for duplicate orders: {e}")
            return {"has_duplicate": False, "error": str(e)}

    def has_open_position(self, symbol: str) -> bool:
        """Check if there's an open position for the given symbol."""
        try:
            positions = self.api_manager.get_positions( # Use api_manager
                category="linear",
                symbol=normalize_symbol_for_bybit(symbol)
            )
            
            # The pybit library might raise an exception or return a dict with an error.
            # We need to handle both cases.
            
            if isinstance(positions, dict) and positions.get("retCode") == 0:
                position_list = positions.get("result", {}).get("list", [])
                for position in position_list:
                    if float(position.get("size", 0)) > 0:
                        logging.info(f"Open position found for {symbol}.")
                        return True
                logging.info(f"No open position found for {symbol}.")
                return False
            
            # Handle non-zero return codes that are not exceptions
            # Ensure positions is a dict before calling .get()
            ret_msg = positions.get('retMsg', 'Unknown error') if isinstance(positions, dict) else 'Invalid response'
            if "symbol not exist" in ret_msg:
                logging.info(f"No open position check for {symbol}: symbol not found on exchange.")
                return False
            else:
                logging.error(f"Error checking open positions for {symbol}: {ret_msg}")
                return False

        except Exception as e:
            # Handle exceptions raised by pybit
            if "symbol not exist" in str(e):
                logging.info(f"No open position check for {symbol}: symbol not found on exchange (exception).")
                return False
            
            logging.error(f"Exception while checking open positions for {symbol}: {e}")
            return False
    
    def execute_trade(self, signal: Dict[str, Any], symbol: str,
                     order_type: str = "Limit", qty: Optional[float] = None, dry_run: bool = False,
                     alteration_details: Optional[str] = None) -> Dict[str, Any]:
        """Execute a trade based on signal with TP/SL and RR validation (Parallel Operation)."""
        trade_id = str(uuid.uuid4())[:8]

        # Log parallel operation start
        if self.log_comparison and self.parallel_mode and self.state_machine_enabled:
            logging.info(f"📊 PARALLEL OPERATION: Starting trade execution for {symbol} (ID: {trade_id})")

        # Validate trade parameters
        validation = self.validate_trade_parameters(signal)
        if not validation["valid"]:
            return {
                "id": trade_id,
                "status": "rejected",
                "error": validation["error"],
                "timestamp": datetime.now(timezone.utc)
            }

        # CRITICAL: Check slot availability before proceeding with any trade
        if hasattr(self, 'risk_manager') and self.risk_manager:
            slot_check = self.risk_manager.can_execute_trade(signal)
            if not slot_check.get("can_execute", False):
                logging.warning(f"🚫 SLOT LIMIT ENFORCED: {symbol} - {slot_check.get('reason', 'Unknown reason')}")
                return {
                    "id": trade_id,
                    "status": "rejected",
                    "error": f"Slot validation failed: {slot_check.get('reason', 'No slots available')}",
                    "slot_info": slot_check,
                    "timestamp": datetime.now(timezone.utc)
                }
            else:
                logging.info(f"✅ SLOT VALIDATION PASSED: {symbol} - {slot_check.get('reason', 'Slot available')}")
        else:
            logging.warning(f"⚠️ RISK MANAGER UNAVAILABLE: {symbol} - Proceeding without slot validation")

        # SYMBOL-LEVEL LOCKING: Prevent race conditions for the same symbol
        # Get or create lock for this symbol
        if symbol not in self._symbol_locks:
            self._symbol_locks[symbol] = threading.Lock()

        symbol_lock = self._symbol_locks[symbol]
        lock_acquired = False

        try:
            # Try to acquire lock with timeout
            lock_acquired = symbol_lock.acquire(timeout=self._lock_timeout)
            if not lock_acquired:
                logging.warning(f"⏰ LOCK TIMEOUT: Could not acquire lock for {symbol} within {self._lock_timeout}s")
                return {
                    "id": trade_id,
                    "status": "rejected",
                    "error": f"Lock acquisition timeout for {symbol}",
                    "timestamp": datetime.now(timezone.utc)
                }

            logging.info(f"🔒 LOCK ACQUIRED: {symbol} (Trade ID: {trade_id})")

            # CRITICAL SECTION: All operations that need to be atomic for this symbol
            # Final validation: Check for valid exchange data before proceeding
            try:
                # Quick validation of current positions and orders
                positions_response = self.get_positions()
                orders_response = self.get_open_orders()

                if positions_response.get("error") or orders_response.get("error"):
                    return {
                        "id": trade_id,
                        "status": "rejected",
                        "error": f"Cannot validate exchange data: positions={positions_response.get('error', 'OK')}, orders={orders_response.get('error', 'OK')}",
                        "timestamp": datetime.now(timezone.utc)
                    }

                # Check if we already have a position for this symbol (final safety check)
                positions_list = positions_response.get("result", {}).get("list", [])
                for position in positions_list:
                    if position.get('symbol') == symbol and float(position.get('size', 0)) > 0:
                        return {
                            "id": trade_id,
                            "status": "rejected",
                            "error": f"Position already exists for {symbol} (final validation)",
                            "timestamp": datetime.now(timezone.utc)
                        }

            except Exception as e:
                return {
                    "id": trade_id,
                    "status": "rejected",
                    "error": f"Exchange validation failed: {str(e)}",
                    "timestamp": datetime.now(timezone.utc)
                }

            # NOTE: Duplicate prevention is now handled by cleanup_exchange_duplicates()
            # which runs at the end of each autotrader cycle (STEP 7)

            # Simplified position check - just check if position exists (WITHIN LOCK)
            if self.has_open_position(symbol):
                logging.warning(f"Open position exists for {symbol} - rejecting new trade")
                return {
                    "id": trade_id,
                    "status": "rejected",
                    "error": f"Open position exists for {symbol}",
                    "timestamp": datetime.now(timezone.utc)
                }

        finally:
            # ALWAYS RELEASE THE LOCK
            if lock_acquired:
                symbol_lock.release()
                logging.info(f"🔓 LOCK RELEASED: {symbol} (Trade ID: {trade_id})")
        
        # Determine quantity if not provided
        if qty is None:
            # Get position sizing preferences
            use_enhanced = getattr(self.config.trading.position_sizing, 'use_enhanced_position_sizing')
            use_slot_based = getattr(self.config.trading.position_sizing, 'use_slot_based_position_sizing')

            # PRIORITY 1: ENHANCED POSITION SIZING (highest priority)
            if use_enhanced and hasattr(self, 'risk_manager') and self.risk_manager:
                logging.info(f"🎯 ENHANCED POSITION SIZING: Using confidence & volatility weighting for {symbol}")
                position_calc = self.risk_manager.calculate_enhanced_position_size_with_safety(
                    validation["entry_price"], validation["stop_loss"], symbol, signal
                )

            # PRIORITY 2: SLOT-BASED EQUAL RISK ALLOCATION (only if enhanced not used)
            elif use_slot_based and hasattr(self, 'risk_manager') and self.risk_manager:
                logging.info(f"🎯 SLOT-BASED EQUAL RISK: Using equal allocation across slots for {symbol}")
                position_calc = self.risk_manager.calculate_slot_based_position_size(
                    validation["entry_price"], validation["stop_loss"], symbol, signal
                )

            # PRIORITY 3: BASIC POSITION SIZING (fallback)
            else:
                logging.info(f"🎯 BASIC POSITION SIZING: Using simple risk percentage for {symbol}")
                position_calc = self.calculate_position_size(
                    validation["entry_price"], validation["stop_loss"], symbol
                )

            if "error" in position_calc:
                return {
                    "id": trade_id,
                    "status": "rejected",
                    "error": f"Position sizing failed: {position_calc['error']}",
                    "timestamp": datetime.now(timezone.utc)
                }

            qty = position_calc["position_size"]
        
        # Ensure qty is not None
        if qty is None:
            return {
                "id": trade_id,
                "status": "rejected",
                "error": "Failed to determine position size",
                "timestamp": datetime.now(timezone.utc)
            }
        
        trade = {
            "id": trade_id,
            "symbol": symbol,
            "recommendation": signal["recommendation"],
            "confidence": signal["confidence"],
            "entry_price": validation["entry_price"],
            "take_profit": validation["take_profit"],
            "stop_loss": validation["stop_loss"],
            "risk_reward_ratio": validation["rr_ratio"],
            "qty": qty,
            "order_type": order_type,
            "timestamp": datetime.now(timezone.utc),
            "status": "pending",
            "recommendation_id": signal.get("recommendation_id", "")
        }

        # 🚀 REAL-TIME TRADE TRACKING: Record trade immediately upon placement
        if self.trade_tracker and not dry_run:
            # Prepare comprehensive trade signal for tracking
            tracking_signal = {
                "recommendation": signal["recommendation"],
                "direction": signal["recommendation"],
                "quantity": qty,
                "entry_price": validation["entry_price"],
                "take_profit": validation["take_profit"],
                "stop_loss": validation["stop_loss"],
                "confidence": signal.get("confidence"),
                "timeframe": signal.get("timeframe"),
                "risk_reward_ratio": validation["rr_ratio"],
                "prompt_name": signal.get("prompt_name")
            }

            # Record trade placement immediately
            recorded_trade_id = self.trade_tracker.record_trade_placement(
                trade_signal=tracking_signal,
                symbol=symbol,
                trade_id=trade_id,
                recommendation_id=signal.get("recommendation_id", "")
            )

            if recorded_trade_id:
                logging.info(f"✅ Trade recorded in real-time tracking: {symbol} (ID: {trade_id})")
            else:
                logging.warning(f"⚠️ Failed to record trade in real-time tracking: {symbol}")
        elif not dry_run:
            logging.warning("⚠️ Real-time trade tracker not available - trade will not be tracked immediately")
        
        if dry_run:
            trade["status"] = "dry_run"
            trade["type"] = "dry_run"
            
            # Fetch last close price for dry run output
            # Use normalized symbol to ensure consistency with trading operations
            normalized_symbol = normalize_symbol_for_bybit(symbol)
            last_close_price = self.get_last_close_price(normalized_symbol, signal.get("timeframe", "1h"))
            
            # If symbol was normalized with 1000 prefix, adjust the last price to match recommendation scale
            if normalized_symbol != symbol and normalized_symbol.startswith('1000'):
                # The recommendation prices are for the original symbol (e.g., PEPEUSDT)
                # But we're fetching price for normalized symbol (e.g., 1000PEPEUSDT)
                # Since 1000PEPEUSDT price = PEPEUSDT price, but recommendation expects PEPEUSDT scale
                # We need to divide by 1000 to match the recommendation price scale
                if last_close_price:
                    last_close_price = last_close_price / 1000
            
            trade["last_close_price"] = last_close_price
            
            logging.info(f"Dry run trade: {trade}")
            return trade
        
        # Set leverage before executing trade
        leverage = getattr(self.config.trading, 'leverage', 1)
        leverage_result = self.set_leverage(symbol, leverage)
        if "error" in leverage_result:
            # Check if this is the expected "leverage not modified" error
            error_msg = leverage_result.get('error', '')
            if "leverage not modified" in error_msg or "110043" in error_msg:
                logging.info(f"✅ Leverage already set correctly for {symbol} (leverage: {leverage}x)")
            else:
                logging.warning(f"⚠️ Failed to set leverage for {symbol}: {error_msg}")
                # Don't fail the trade for leverage issues - continue execution
        else:
            logging.info(f"✅ Leverage set successfully for {symbol} (leverage: {leverage}x)")
        
        # Execute real trade with TP/SL (PRIMARY SYSTEM)
        side = "Buy" if signal["recommendation"].upper() in ["BUY", "LONG"] else "Sell"

        # Initialize state machine tracking BEFORE trade execution (Parallel Operation)
        trade_state_id = None
        if (self.parallel_mode and self.state_machine_enabled and
            self.state_machine and not dry_run):

            try:
                # Extract order information for state machine
                main_order_id = trade_id  # Use trade_id as main_order_id initially
                order_link_id = trade_id

                # Initialize state machine tracking
                trade_state_id = self.state_machine.initialize_trade_state(
                    trade_id=trade_id,
                    symbol=symbol,
                    main_order_id=main_order_id,
                    tp_order_id=f"tp_{main_order_id}",
                    sl_order_id=f"sl_{main_order_id}",
                    order_link_id=order_link_id
                )

                if trade_state_id:
                    # Store state ID for future reference
                    trade["trade_state_id"] = trade_state_id

                    if self.log_comparison:
                        logging.info(f"📊 STATE MACHINE: Trade {trade_id} initialized with state_id {trade_state_id}")
                else:
                    logging.warning(f"⚠️ STATE MACHINE: Failed to initialize tracking for trade {trade_id}")

            except Exception as e:
                # State machine failure - log but don't break existing system
                logging.error(f"❌ STATE MACHINE INIT ERROR: {e}")
                if self.log_comparison:
                    logging.info(f"📊 STATE MACHINE: Initialization failed, continuing with existing system only")

        try:
            if order_type == "Market":
                logging.warning(f"⚠️ MARKET ORDER PLACED: {symbol} {side} {qty} - Consider using Limit orders for better price control")
                result = self.place_market_order_with_tp_sl(
                    symbol, side, qty,
                    validation["take_profit"],
                    validation["stop_loss"],
                    trade_id
                )
            elif order_type == "Limit":
                result = self.place_limit_order_with_tp_sl(
                    symbol, side, qty,
                    validation["entry_price"],
                    validation["take_profit"],
                    validation["stop_loss"],
                    order_link_id=trade_id
                )
            else:
                result = {"error": f"Unsupported order type: {order_type}"}

            if "error" in result:
                trade["status"] = "failed"
                trade["error"] = result["error"]
                logging.error(f"Trade failed: {result['error']}")

                # Update state machine on failure (Parallel Operation)
                if (self.parallel_mode and self.state_machine_enabled and
                    self.state_machine and trade_state_id and TradeState):
                    try:
                        self.state_machine.transition_state(
                            trade_state_id=trade_state_id,
                            new_state=TradeState.ERROR_SUBMISSION,
                            reason=f"Order failed: {result['error']}",
                            changed_by="SYSTEM"
                        )
                        if self.log_comparison:
                            logging.info(f"📊 STATE MACHINE: Order failure recorded for {trade_id}")
                    except Exception as e:
                        logging.error(f"❌ STATE MACHINE FAILURE UPDATE ERROR: {e}")

            else:
                trade["status"] = "executed_live"
                trade["type"] = "live_trade"
                # Extract order ID from successful response
                if isinstance(result, dict) and "result" in result:
                    order_result = result.get("result", {})
                    if isinstance(order_result, dict):
                        trade["order_id"] = order_result.get("orderId")
                        trade["orderLinkId"] = order_result.get("orderLinkId") # Store orderLinkId
                trade["order_link_id"] = trade_id # This is the client-generated UUID, not Bybit's orderLinkId
                logging.info(f"Live trade executed: {trade}")

                # 🚀 REAL-TIME TRADE TRACKING: Update trade with order ID from broker response
                if self.trade_tracker and trade.get("order_id"):
                    update_success = self.trade_tracker.update_trade_with_order_id(trade_id, result)
                    if update_success:
                        logging.info(f"✅ Trade updated with order ID in real-time: {trade_id} -> {trade['order_id']}")

                        # Add order to real-time monitoring
                        if self.trade_monitor:
                            self.trade_monitor.add_order_to_monitoring(trade["order_id"])
                    else:
                        logging.warning(f"⚠️ Failed to update trade with order ID: {trade_id}")
                elif self.trade_tracker and not trade.get("order_id"):
                    logging.warning(f"⚠️ No order ID received from broker for trade: {trade_id}")

                # Update state machine on success (Parallel Operation)
                if (self.parallel_mode and self.state_machine_enabled and
                    self.state_machine and trade_state_id and TradeState):
                    try:
                        # Update with actual order IDs from exchange
                        actual_order_id = trade.get("order_id")
                        if actual_order_id:
                            # Update the state machine with actual order IDs
                            self.state_machine.update_trade_state(
                                trade_state_id=trade_state_id,
                                updates={
                                    "main_order_id": actual_order_id,
                                    "tp_order_id": f"tp_{actual_order_id}",
                                    "sl_order_id": f"sl_{actual_order_id}"
                                }
                            )

                        # Transition to order submitted state
                        self.state_machine.transition_state(
                            trade_state_id=trade_state_id,
                            new_state=TradeState.ORDER_SUBMITTED,
                            reason="Order submitted to exchange",
                            changed_by="SYSTEM"
                        )

                        if self.log_comparison:
                            logging.info(f"📊 STATE MACHINE: Order submitted recorded for {trade_id}")

                    except Exception as e:
                        logging.error(f"❌ STATE MACHINE SUCCESS UPDATE ERROR: {e}")
                
                # Note: Trade recording is now handled by real-time trade tracker
                # The old position_manager.ensure_trade_record() call has been removed
                # as it's redundant with the new real-time tracking system
                
        except Exception as e:
            trade["status"] = "failed"
            trade["error"] = str(e)
            logging.error(f"Trade execution error: {e}")
        
        return trade
    
if __name__ == "__main__":
    # Test the Bybit connection and trading functions
    import json
    
    try:
        # Use TradeExecutor for testing
        from trading_bot.config.settings import Config
        config = Config.from_yaml()
        trader = TradeExecutor(config)
        
        print("Testing Bybit connection...")
        
        # Test connection
        balance = trader.api_manager.get_wallet_balance()
        print("✓ Connection successful")
        print("Wallet balance:", json.dumps(balance.get("result", {}), indent=2))
        
        # Test positions
        positions = trader.api_manager.get_positions()
        print("\n✓ Positions retrieved")
        print("Positions:", json.dumps(positions.get("result", {}), indent=2))
        
        # Test open orders
        orders = trader.api_manager.get_open_orders()
        print("\n✓ Open orders retrieved")
        print("Open orders:", json.dumps(orders.get("result", {}), indent=2))
        
    except Exception as e:
        print(f"✗ Error: {e}")
