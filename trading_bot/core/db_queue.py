import queue
import threading
from typing import Callable, Any

class DatabaseQueue:
    def __init__(self, data_agent):
        self.data_agent = data_agent
        self.queue = queue.Queue()
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.stop_event = threading.Event()

    def _worker(self):
        while not self.stop_event.is_set() or not self.queue.empty():
            try:
                operation, args, kwargs = self.queue.get(timeout=1)
                operation(*args, **kwargs)
                self.queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Error in database worker: {e}")

    def start(self):
        self.worker_thread.start()

    def stop(self):
        self.stop_event.set()
        self.queue.join()
        self.worker_thread.join()

    def enqueue(self, operation: Callable, *args: Any, **kwargs: Any):
        self.queue.put((operation, args, kwargs))
