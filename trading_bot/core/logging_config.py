"""
Centralized logging configuration for the trading bot.
Allows configurable log levels for different components.
Captures all console output to log files while filtering telegram content.
"""
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Optional

# Default log levels for each component
DEFAULT_LOG_LEVELS = {
    'trading_bot': logging.INFO, # General bot operations
    'trading_bot.core': logging.INFO, # Core functionality
    'trading_bot.core.analyzer': logging.INFO, # Chart analysis details
    'trading_bot.core.cleaner': logging.INFO, # File cleanup operations
    'trading_bot.core.data_agent': logging.INFO, # Database operations
    'trading_bot.core.position_manager': logging.INFO, # Position management
    'trading_bot.core.position_monitor': logging.DEBUG, # Detailed position monitoring
    'trading_bot.core.sourcer': logging.INFO, # Chart sourcing
    'trading_bot.core.trader': logging.DEBUG, # Trade execution details
    'trading_bot.core.utils': logging.WARNING, # Utility functions
    'trading_bot.core.timestamp_extractor': logging.INFO, # Timestamp extraction
    'trading_bot.core.timestamp_validator': logging.INFO, # Timestamp validation
    'trading_bot.core.simple_openai_handler': logging.INFO, # OpenAI API calls
    'trading_bot.core.secrets_manager': logging.WARNING, # Security-related
    'trading_bot.core.shutdown_handler': logging.INFO, # Shutdown operations
    'trading_bot.core.telegram_controller': logging.INFO, # Telegram control
    'trading_bot.core.adx_stop_tightener': logging.DEBUG, # Stop loss tightening
    'trading_bot.core.file_validator': logging.INFO, # File validation
    'trading_bot.core.recommender': logging.INFO, # Recommendation engine
    'trading_bot.core.risk_manager': logging.DEBUG, # Risk management decisions
    'trading_bot.core.timeframe_extractor': logging.INFO, # Timeframe extraction
    'trading_bot.core.position_analyzer': logging.INFO, # Position analysis
    'trading_bot.core.telegram_bot': logging.INFO, # Telegram bot operations
    'trading_bot.core.intelligent_order_replacement': logging.DEBUG, # Order replacement logic
    'trading_bot.core.pnl_corrector': logging.INFO, # PnL corrections
    'trading_bot.core.db_queue': logging.INFO, # Database queue operations
    'trading_bot.core.validator': logging.INFO, # Signal validation
    '__main__': logging.INFO, # Main application
}

def get_log_level_from_env(logger_name: str, default_level: int) -> int:
    """Get log level from environment variable or use default."""
    env_var_name = f"LOG_LEVEL_{logger_name.replace('.', '_').upper()}"
    level_str = os.environ.get(env_var_name)
    
    if level_str:
        level_dict = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        return level_dict.get(level_str.upper(), default_level)
    
    return default_level

def configure_logging(log_levels: Optional[Dict[str, int]] = None):
    """Configure loggers with individual log levels and separate file/console handlers"""
    import os
    from pathlib import Path

    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Set root logger to DEBUG to capture all messages
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # Remove any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Create formatters
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # Create rotating file handler (logs everything with size-based rotation)
    from logging.handlers import RotatingFileHandler

    # Get rotation settings from config, environment variables, or use defaults
    max_bytes = 10 * 1024 * 1024  # Default 10MB
    backup_count = 5  # Default 5 backup files

    # Try to get settings from config if available
    try:
        from trading_bot.config.settings import Config
        config = Config.from_yaml()
        if config.logging and config.logging.rotation:
            max_bytes = config.logging.rotation.max_file_size_mb * 1024 * 1024
            backup_count = config.logging.rotation.backup_count
    except Exception:
        # Fall back to environment variables if config loading fails
        max_bytes = int(os.environ.get('LOG_MAX_BYTES', max_bytes))
        backup_count = int(os.environ.get('LOG_BACKUP_COUNT', backup_count))

    # Create DEBUG-only file handler for autotrader.log
    debug_file_handler = RotatingFileHandler(
        logs_dir / "autotrader.log",
        maxBytes=max_bytes,
        backupCount=backup_count
    )
    debug_file_handler.setLevel(logging.DEBUG)  # Only DEBUG level for autotrader.log
    debug_file_handler.addFilter(lambda record: record.levelno == logging.DEBUG)  # Filter to only DEBUG
    debug_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    debug_file_handler.setFormatter(debug_formatter)

    # Create info+ file handler for run_autotrader.log
    info_file_handler = RotatingFileHandler(
        logs_dir / "run_autotrader.log",
        maxBytes=max_bytes,
        backupCount=backup_count
    )
    info_file_handler.setLevel(logging.INFO)  # Capture INFO, WARNING, ERROR, CRITICAL
    info_file_handler.addFilter(lambda record: record.levelno >= logging.INFO)  # Filter to INFO and above
    info_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    info_file_handler.setFormatter(info_formatter)

    # Create console handler (logs INFO and above)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)

    # Store the info file handler globally for use by run_autotrader logger
    global _error_file_handler
    _error_file_handler = info_file_handler

    # Add handlers to root logger
    root_logger.addHandler(debug_file_handler)  # DEBUG only to autotrader.log
    root_logger.addHandler(console_handler)
    root_logger.addHandler(info_file_handler)  # INFO+ to run_autotrader.log

    # Use provided log levels or default ones
    levels_to_use = log_levels if log_levels is not None else DEFAULT_LOG_LEVELS

    # Configure each logger with its specific level
    for logger_name, default_level in DEFAULT_LOG_LEVELS.items():
        logger = logging.getLogger(logger_name)
        # Use provided level, environment variable, or default
        if log_levels and logger_name in log_levels:
            level = log_levels[logger_name]
        else:
            level = get_log_level_from_env(logger_name, default_level)
        logger.setLevel(level)


def configure_run_autotrader_logging():
    """Configure logging specifically for run_autotrader to capture only its logs."""
    import os
    from pathlib import Path
    from logging.handlers import RotatingFileHandler

    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Get rotation settings (same defaults as configure_logging)
    max_bytes = 10 * 1024 * 1024  # Default 10MB
    backup_count = 5  # Default 5 backup files

    # Create a specific logger for run_autotrader
    run_autotrader_logger = logging.getLogger('run_autotrader')
    run_autotrader_logger.setLevel(logging.DEBUG)

    # Remove any existing handlers from this logger
    for handler in run_autotrader_logger.handlers[:]:
        run_autotrader_logger.removeHandler(handler)

    # Create formatter for run_autotrader logs
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # Don't propagate to root logger to avoid getting logs from other modules
    # The run_autotrader logger will only capture its own logs
    run_autotrader_logger.propagate = False

    # Create a dedicated file handler for run_autotrader logger only
    run_autotrader_file_handler = RotatingFileHandler(
        logs_dir / "run_autotrader.log",
        maxBytes=max_bytes,
        backupCount=backup_count
    )
    run_autotrader_file_handler.setLevel(logging.INFO)  # Only INFO+ for run_autotrader.log
    run_autotrader_file_handler.addFilter(lambda record: record.levelno >= logging.INFO)
    run_autotrader_file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    run_autotrader_logger.addHandler(run_autotrader_file_handler)

    # Add console handler for run_autotrader (single console output)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    run_autotrader_logger.addHandler(console_handler)

    return run_autotrader_logger

def set_logger_level(logger_name: str, level: int):
    """Set the log level for a specific logger."""
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)

def set_all_log_levels(log_levels: Dict[str, int]):
    """Set log levels for all specified loggers."""
    for logger_name, level in log_levels.items():
        set_logger_level(logger_name, level)

# Global error file handler for run_autotrader.log
_error_file_handler = None


class TelegramFilter(logging.Filter):
    """Filter to exclude telegram-related log messages."""

    def filter(self, record: logging.LogRecord) -> bool:
        """Filter out telegram-related messages."""
        # List of telegram-related logger names and message patterns to filter
        telegram_patterns = [
            'trading_bot.core.telegram',
            'telegram',
            'TelegramBot',
            'TelegramMonitor',
            'telegram_bot',
            'telegram_monitor',
            '📱',
            '🤖',
            'Telegram confirmation',
            'telegram confirmation',
            'send_trade_with_confirmation',
            'send_position_update',
            'send_cycle_summary',
            'send_system_health'
        ]

        # Check if logger name contains telegram-related patterns
        logger_name = record.name.lower()
        message = record.getMessage().lower()

        for pattern in telegram_patterns:
            if pattern.lower() in logger_name or pattern.lower() in message:
                return False  # Filter out this message

        return True  # Keep this message


class ConsoleCaptureHandler(logging.StreamHandler):
    """Custom handler that captures all console output to log files."""

    def __init__(self, stream=sys.stdout):
        super().__init__(stream)
        self.setLevel(logging.INFO)  # Only show INFO+ on console to reduce noise
        self.addFilter(TelegramFilter())  # Filter out telegram content
        self._log_file_handler = None  # Initialize the file handler reference
        self._processing_stdout_capture = False  # Flag to prevent recursion

    def set_log_file_handler(self, handler):
        """Set the file handler for logging to files."""
        self._log_file_handler = handler

    def emit(self, record: logging.LogRecord) -> None:
        """Emit a record, handling any errors gracefully."""
        try:
            # Skip if we're already processing stdout capture to prevent recursion
            if self._processing_stdout_capture:
                return

            # Format the message
            msg = self.format(record)

            # Write to the original stream (console)
            if self.stream:
                self.stream.write(msg + self.terminator)
                self.stream.flush()

            # Also write to our log file handler if available
            if self._log_file_handler:
                # Create a copy of the record for the file handler
                file_record = logging.LogRecord(
                    record.name, record.levelno, record.pathname,
                    record.lineno, record.msg, record.args, record.exc_info
                )
                file_record.created = record.created
                file_record.msecs = record.msecs

                # Write to file handler
                self._log_file_handler.handle(file_record)

        except Exception:
            # If we can't log the error, at least try to print it
            try:
                if self.stream:
                    self.stream.write("ERROR in ConsoleCaptureHandler: " + str(record) + "\n")
            except Exception:
                pass  # Give up if we can't even write to stream


class StdoutCapture:
    """Context manager to capture stdout and redirect to logging."""

    def __init__(self, logger_name="stdout_capture"):
        self.logger = logging.getLogger(logger_name)
        self.original_stdout = None
        self.original_stderr = None
        self.capturing = False

    def start_capture(self):
        """Start capturing stdout and stderr."""
        if not self.capturing:
            self.original_stdout = sys.stdout
            self.original_stderr = sys.stderr

            # Create a custom stream that writes to both original stream and logger
            class LoggingStream:
                def __init__(self, original_stream, logger_instance, is_error=False):
                    self.original_stream = original_stream
                    self.logger_instance = logger_instance
                    self.is_error = is_error
                    self.telegram_filter = TelegramFilter()

                def write(self, message):
                    if message.strip():  # Only log non-empty messages
                        # Create a log record to check if it should be filtered
                        record = logging.LogRecord(
                            self.logger_instance.name, logging.INFO, "", 0, message.rstrip(), (), None
                        )
                        if not self.telegram_filter.filter(record):
                            # Set flag to prevent recursion in ConsoleCaptureHandler
                            for handler in self.logger_instance.handlers:
                                if isinstance(handler, ConsoleCaptureHandler):
                                    handler._processing_stdout_capture = True

                            # Message passed telegram filter, log it
                            if self.is_error:
                                self.logger_instance.error(message.rstrip())
                            else:
                                self.logger_instance.info(message.rstrip())

                            # Clear flag after logging
                            for handler in self.logger_instance.handlers:
                                if isinstance(handler, ConsoleCaptureHandler):
                                    handler._processing_stdout_capture = False

                    # Also write to original stream
                    self.original_stream.write(message)
                    self.original_stream.flush()

                def flush(self):
                    self.original_stream.flush()

            sys.stdout = LoggingStream(self.original_stdout, self.logger, is_error=False)
            sys.stderr = LoggingStream(self.original_stderr, self.logger, is_error=True)
            self.capturing = True

    def stop_capture(self):
        """Stop capturing stdout and stderr."""
        if self.capturing and self.original_stdout and self.original_stderr:
            sys.stdout = self.original_stdout
            sys.stderr = self.original_stderr
            self.capturing = False


def setup_comprehensive_console_logging():
    """Set up comprehensive console logging that captures all output."""
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Get rotation settings from config, environment variables, or use defaults
    max_bytes = 10 * 1024 * 1024  # Default 10MB
    backup_count = 5  # Default 5 backup files

    # Try to get settings from config if available
    try:
        from trading_bot.config.settings import Config
        config = Config.from_yaml()
        if config.logging and config.logging.rotation:
            max_bytes = config.logging.rotation.max_file_size_mb * 1024 * 1024
            backup_count = config.logging.rotation.backup_count
    except Exception:
        # Fall back to environment variables if config loading fails
        max_bytes = int(os.environ.get('LOG_MAX_BYTES', max_bytes))
        backup_count = int(os.environ.get('LOG_BACKUP_COUNT', backup_count))

    # Create comprehensive log file handler
    from logging.handlers import RotatingFileHandler

    comprehensive_file_handler = RotatingFileHandler(
        logs_dir / "comprehensive.log",
        maxBytes=max_bytes,
        backupCount=backup_count
    )
    comprehensive_file_handler.setLevel(logging.DEBUG)  # Capture everything
    comprehensive_file_handler.addFilter(TelegramFilter())  # Filter telegram content

    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    comprehensive_file_handler.setFormatter(file_formatter)

    # Remove ALL existing console handlers to prevent any duplication
    root_logger = logging.getLogger()
    handlers_to_remove = []
    for handler in root_logger.handlers:
        if isinstance(handler, logging.StreamHandler):
            handlers_to_remove.append(handler)

    for handler in handlers_to_remove:
        root_logger.removeHandler(handler)

    # Create single consolidated console handler
    console_capture_handler = ConsoleCaptureHandler()
    console_capture_handler.setLevel(logging.INFO)  # Only show INFO+ on console
    console_capture_handler.addFilter(TelegramFilter())  # Filter telegram content

    # Use a simpler formatter for console output
    console_formatter = logging.Formatter('%(message)s')
    console_capture_handler.setFormatter(console_formatter)

    # Link the file handler to the console handler for comprehensive logging
    console_capture_handler.set_log_file_handler(comprehensive_file_handler)

    # Add handlers to root logger
    root_logger.addHandler(comprehensive_file_handler)
    root_logger.addHandler(console_capture_handler)

    # Ensure root logger captures all levels
    root_logger.setLevel(logging.DEBUG)

    # Set up stdout/stderr capture (this will capture print statements)
    stdout_capture = StdoutCapture()
    stdout_capture.start_capture()

    return comprehensive_file_handler, console_capture_handler, stdout_capture


def capture_unhandled_exceptions(exc_type, exc_value, exc_traceback):
    """Capture unhandled exceptions and log them."""
    if issubclass(exc_type, KeyboardInterrupt):
        # Handle KeyboardInterrupt specially
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # Get the root logger
    logger = logging.getLogger()

    # Create a formatted exception message
    import traceback
    exception_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))

    # Log the unhandled exception
    logger.critical(f"Unhandled exception: {exception_msg}")

    # Also call the original exception hook if it exists
    if sys.__excepthook__ != capture_unhandled_exceptions:
        sys.__excepthook__(exc_type, exc_value, exc_traceback)


# Auto-configure when module is imported
configure_logging()

# Set up comprehensive console logging
_comprehensive_file_handler, _console_capture_handler, _stdout_capture = setup_comprehensive_console_logging()

# Set up unhandled exception capture
sys.excepthook = capture_unhandled_exceptions
