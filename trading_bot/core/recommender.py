import logging
import warnings
from datetime import datetime, timezone

from trading_bot.core.secrets_manager import get_bybit_credentials
from trading_bot.core.utils import check_system_resources

from .intelligent_order_replacement import IntelligentOrderReplacementSystem
from .timestamp_validator import TimestampValidator

try:
    from pybit.unified_trading import HTTP
except ImportError:
    HTTP = None

class Recommender:
    def __init__(self, db_queue, trader=None, position_manager=None, config=None):
        self.db_queue = db_queue
        self.logger = logging.getLogger(__name__)
        self.session = None
        self.timestamp_validator = TimestampValidator()
        self.trader = trader
        self.position_manager = position_manager
        self.config = config
        
        # Initialize intelligent order replacement system if components are available
        self.intelligent_replacement = None
        if trader and position_manager and config:
            try:
                self.intelligent_replacement = IntelligentOrderReplacementSystem(
                    trader=trader,
                    position_manager=position_manager,
                    config=config,
                    logger=self.logger,
                    data_agent=db_queue.data_agent if hasattr(db_queue, 'data_agent') else None
                )
                self.logger.info("Intelligent order replacement system initialized")
            except Exception as e:
                self.logger.error(f"Failed to initialize intelligent order replacement: {e}")
        
        self._init_bybit_client()
    
    def _init_bybit_client(self):
        """Initialize Bybit client for fetching market data."""
        if HTTP is None:
            self.logger.warning("pybit not installed. Market data features disabled.")
            return
        
        try:
            import os
            
            # Get recv_window from config, fallback to 60000 if not available
            recv_window_ms = 60000  # Default fallback
            if self.config and hasattr(self.config, 'bybit'):
                recv_window_ms = self.config.bybit.recv_window
            
            self.session = HTTP(
                testnet=True,  # Use testnet for market data
                api_key=get_bybit_credentials()[0],
                api_secret=get_bybit_credentials()[1],
                recv_window=recv_window_ms,
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize Bybit client for recommender: {e}")
            self.session = None

    def parse_timeframe_minutes(self, timeframe: str) -> int:
        """
        Convert timeframe string to minutes.
        
        DEPRECATED: This method is deprecated and will be removed in a future version.
        Use TimestampValidator.timeframe_to_minutes() directly instead.
        """
        warnings.warn(
            "parse_timeframe_minutes() is deprecated. Use TimestampValidator.timeframe_to_minutes() instead.",
            DeprecationWarning,
            stacklevel=2
        )
        
        try:
            return self.timestamp_validator.timeframe_to_minutes(timeframe)
        except Exception as e:
            # Fallback to original logic for backward compatibility
            self.logger.warning(f"TimestampValidator failed for timeframe '{timeframe}': {e}. Using fallback logic.")
            timeframe = str(timeframe).lower().strip()
            
            if timeframe.endswith('m'):
                return int(timeframe[:-1])
            elif timeframe.endswith('min'):
                return int(timeframe[:-3])
            elif timeframe.endswith('h'):
                return int(timeframe[:-1]) * 60
            elif timeframe.endswith('hr'):
                return int(timeframe[:-2]) * 60
            elif timeframe.endswith('hour'):
                return int(timeframe[:-4]) * 60
            elif timeframe.endswith('d'):
                return int(timeframe[:-1]) * 1440
            elif timeframe.endswith('day'):
                return int(timeframe[:-3]) * 1440
            
            return 60

    def is_recommendation_valid(self, recommendation: dict, validate_risk: bool = True) -> bool:
        """
        Check if recommendation is valid (not older than current UTC - timeframe).
        
        DEPRECATED: This method now uses TimestampValidator internally for improved accuracy.
        Consider using TimestampValidator.is_recommendation_valid() directly for new code.
        """
        if not recommendation or recommendation.get('error'):
            return False
        
        timestamp_str = recommendation.get('timestamp')
        if not timestamp_str:
            self.logger.debug("Recommendation filtered - missing timestamp")
            return False
        
        timeframe = recommendation.get('timeframe')
        if not timeframe:
            self.logger.debug("Recommendation filtered - missing timeframe")
            return False
        
        try:
            # Use the new TimestampValidator for validation with current period allowance and grace period
            # First, validate timestamp using TimestampValidator
            timestamp_validation_result = self.timestamp_validator.is_recommendation_valid(
                timestamp_str, timeframe, allow_current_period=True, grace_period_minutes=15
            )
            if not timestamp_validation_result.is_valid:
                self.logger.debug(f"Recommendation filtered - timestamp expired: {timestamp_str} ({timeframe}) - next boundary: {timestamp_validation_result.next_boundary}")
                # Add detailed debug information
                try:
                    from trading_bot.core.timestamp_validator import TimestampValidator
                    validator = TimestampValidator()
                    parsed_timestamp = validator.parse_timestamp(timestamp_str)
                    current_time = datetime.now(timezone.utc)
                    self.logger.debug(f"Timestamp validation details - parsed: {parsed_timestamp}, current: {current_time}, diff: {current_time - parsed_timestamp}")
                except Exception as parse_error:
                    self.logger.debug(f"Error parsing timestamp for debug: {parse_error}")
                
                # Add additional debug information for troubleshooting
                try:
                    from trading_bot.core.timestamp_validator import TimestampValidator
                    validator = TimestampValidator()
                    parsed_timestamp = validator.parse_timestamp(timestamp_str)
                    current_time = datetime.now(timezone.utc)
                    # Calculate time difference in minutes
                    time_diff = (current_time - parsed_timestamp).total_seconds() / 60
                    self.logger.debug(f"DEBUG: Time difference: {time_diff:.2f} minutes")
                    # Check if it's within the timeframe boundary
                    timeframe_info = validator.normalize_timeframe(timeframe)
                    next_boundary = validator.calculate_next_boundary(parsed_timestamp, timeframe)
                    boundary_diff = (next_boundary - parsed_timestamp).total_seconds() / 60
                    self.logger.debug(f"DEBUG: Timeframe boundary: {timeframe_info.normalized}, boundary diff: {boundary_diff:.2f} minutes")
                    # Check if it's within current period
                    current_period_boundary = validator.calculate_next_boundary(current_time, timeframe) - timeframe_info.timedelta
                    is_in_current_period = parsed_timestamp >= current_period_boundary
                    self.logger.debug(f"DEBUG: Is in current period: {is_in_current_period}, current period boundary: {current_period_boundary}")
                except Exception as debug_error:
                    self.logger.debug(f"Error in additional debug: {debug_error}")
                return False
            
            # Second, validate data parameters using validate_risk_parameters (if requested)
            if validate_risk:
                risk_validation_result = self.validate_risk_parameters(recommendation)
                self.logger.debug(f"Risk validation result for {recommendation.get('symbol', 'Unknown')}: {risk_validation_result}")
                return risk_validation_result["valid"]
            else:
                # Only timestamp validation requested
                return True
                
        except Exception as e:
            self.logger.error(f"Error during recommendation validation for {recommendation.get('symbol', 'Unknown')} {timeframe}: {e}")
            return False

    def is_timestamp_valid(self, recommendation: dict) -> bool:
        """
        Check if recommendation timestamp is valid (without risk validation).
        
        Args:
            recommendation: Recommendation dictionary
            
        Returns:
            bool: True if recommendation timestamp is valid
        """
        return self.is_recommendation_valid(recommendation, validate_risk=False)

    def is_risk_valid(self, recommendation: dict) -> bool:
        """
        Check if recommendation risk parameters are valid (without timestamp validation).
        
        Args:
            recommendation: Recommendation dictionary
            
        Returns:
            bool: True if recommendation risk parameters are valid
        """
        if not recommendation or recommendation.get('error'):
            return False
        
        risk_validation_result = self.validate_risk_parameters(recommendation)
        return risk_validation_result["valid"]

    def validate_risk_parameters(self, recommendation: dict) -> dict:
        """
        Validate risk parameters including risk-reward ratio and confidence threshold.
        
        Args:
            recommendation: Recommendation dictionary containing trade parameters
            
        Returns:
            dict: {"valid": bool, "error": str} - validation result
        """
        try:
            # Check if trader is available for risk validation
            if not self.trader or not self.config:
                # If no trader/config available, skip risk validation (backward compatibility)
                self.logger.debug("Risk validation skipped - no trader or config available")
                return {"valid": True, "error": None}
            
            # Check confidence threshold
            confidence = recommendation.get('confidence', 0)
            try:
                confidence = float(confidence)
                min_confidence = getattr(self.config.trading, 'min_confidence_threshold')
                if confidence < min_confidence:
                    self.logger.debug(f"Recommendation filtered - confidence {confidence:.2f} below minimum threshold {min_confidence:.2f}")
                    return {
                        "valid": False,
                        "error": f"Confidence {confidence:.2f} below minimum threshold {min_confidence:.2f}"
                    }
            except (ValueError, TypeError):
                self.logger.debug(f"Recommendation filtered - invalid confidence value: {confidence}")
                return {"valid": False, "error": "Invalid confidence value"}
            
            # Check if recommendation has required fields for risk validation
            required_fields = ["entry_price", "take_profit", "stop_loss", "recommendation"]
            missing_fields = [field for field in required_fields if field not in recommendation]
            if missing_fields:
                self.logger.debug(f"Recommendation filtered - missing required fields: {missing_fields}")
                return {"valid": False, "error": f"Missing required fields for risk validation: {missing_fields}"}
            
            # Check for "hold" or "neutral" recommendations
            rec_type = recommendation.get("recommendation", "").upper()
            if rec_type in ["HOLD", "NEUTRAL"]:
                self.logger.debug(f"Recommendation filtered - non-actionable recommendation type: '{rec_type}'")
                return {"valid": False, "error": f"Recommendation type '{rec_type}' is not actionable"}

            # NEW FILTER: Check direction alignment with market direction
            market_direction = recommendation.get("market_direction", "").upper()
            market_condition = recommendation.get("market_condition", "").upper()
            if market_direction:
                # Determine trade direction from recommendation
                trade_direction = None
                if rec_type in ["BUY", "LONG"] or recommendation.get("direction", "").upper() in ["LONG", "BUY"]:
                    trade_direction = "LONG"
                elif rec_type in ["SELL", "SHORT"] or recommendation.get("direction", "").upper() in ["SHORT", "SELL"]:
                    trade_direction = "SHORT"

                # Check for misalignment
                if trade_direction == "SHORT" and market_direction == "UP":
                    symbol_name = recommendation.get('symbol', 'Unknown')
                    self.logger.debug(f"Recommendation filtered for {symbol_name} - SHORT recommendation but market direction is UP")
                    return {
                        "valid": False,
                        "error": "Direction misalignment: SHORT recommendation in UP market"
                    }
                elif trade_direction == "LONG" and market_direction == "DOWN":
                    symbol_name = recommendation.get('symbol', 'Unknown')
                    self.logger.debug(f"Recommendation filtered for {symbol_name} - LONG recommendation but market direction is DOWN")
                    return {
                        "valid": False,
                        "error": "Direction misalignment: LONG recommendation in DOWN market"
                    }
                elif market_condition == "TRENDING" and market_direction not in ["UP","DOWN"]:
                    symbol_name = recommendation.get('symbol', 'Unknown')
                    self.logger.debug(f"Recommendation filtered for {symbol_name} - Condition is TRENDING but direction not UP or down")
                    return {
                        "valid": False,
                        "error": "Direction misalignment: Trenfing market but not UP or DOWN"
                    }

            # Calculate risk-reward ratio using trader's method
            try:
                entry_price_raw = recommendation.get("entry_price")
                tp_price_raw = recommendation.get("take_profit")
                sl_price_raw = recommendation.get("stop_loss")
                
                # Ensure prices are not None and can be converted to float
                if entry_price_raw is None or tp_price_raw is None or sl_price_raw is None:
                    self.logger.debug(f"Recommendation filtered - missing price values: entry={entry_price_raw}, tp={tp_price_raw}, sl={sl_price_raw}")
                    return {"valid": False, "error": "One or more price values are missing (None)"}
                
                entry_price = float(entry_price_raw)
                tp_price = float(tp_price_raw)
                sl_price = float(sl_price_raw)
                
                # Determine direction
                if "direction" in recommendation:
                    direction = recommendation["direction"].upper()
                else:
                    # Derive direction from recommendation
                    rec = recommendation["recommendation"].upper()
                    if rec in ["BUY", "LONG"]:
                        direction = "LONG"
                    elif rec in ["SELL", "SHORT"]:
                        direction = "SHORT"
                    else:
                        direction = "NEUTRAL" # Should be caught by the "HOLD" / "NEUTRAL" check above
                
                # Get symbol for fee calculation
                symbol = recommendation.get("symbol", None)
                
                # Calculate risk-reward ratio
                rr_ratio = self.trader.calculate_risk_reward_ratio(
                    entry_price, tp_price, sl_price, direction, symbol
                )
                
                self.logger.info(f"Risk-reward ratio calculation for {symbol}: entry={entry_price}, tp={tp_price}, sl={sl_price}, direction={direction}, rr={rr_ratio:.2f}")
                
                # Check minimum risk-reward ratio
                min_rr = getattr(self.config.trading, 'min_rr')
                if rr_ratio < min_rr:
                    return {
                        "valid": False,
                        "error": f"Risk-reward ratio {rr_ratio:.2f} is below minimum {min_rr}"
                    }                
                self.logger.info(f"Risk validation passed for {symbol} with confidence {confidence:.2f} and RR {rr_ratio:.2f}")
                return {"valid": True, "error": None}
                
            except (ValueError, TypeError) as e:
                print(f"Recommendation filtered for {symbol} - invalid price values: {e}")
                return {"valid": False, "error": f"Invalid price values (conversion error): {e}"}
                
        except Exception as e:
            self.logger.error(f"Error validating risk parameters: {e}")
            # Return valid=True for backward compatibility if validation fails
            return {"valid": False, "error": f"Unhandled error during risk validation: {e}"}

    def get_latest_recommendation(self, symbol: str, timeframe: str):
        """
        Get the latest recommendation from the database with risk management filtering.
        This is now a simplified wrapper that uses get_recommendations_for_current_boundary.
        """
        try:
            # Get recommendations for current boundary
            recommendations = self.get_recommendations_for_current_boundary(symbol, timeframe)
            
            self.logger.info(f"Found {len(recommendations)} recommendations for {symbol} {timeframe}")
            
            # Return single recommendation for specific symbol, or list for "all"
            if symbol == "all":
                return recommendations
            else:
                # Return the first (most recent) recommendation for the symbol
                if recommendations:
                    return recommendations[0]
                else:
                    return {"error": f"No valid recommendations found for {symbol} {timeframe}"}
                    
        except Exception as e:
            self.logger.error(f"Error getting latest recommendation: {e}")
            return {"error": str(e)}
    
    def get_intelligent_recommendations(self, symbol, timeframe, max_slots=5):
        """
        Get intelligent recommendations that consider existing orders for replacement.
        
        This method integrates the intelligent order replacement system to provide
        recommendations that optimize across all symbols and timeframes.
        """
        try:
            if not self.intelligent_replacement:
                # Fallback to regular recommendation if intelligent system not available
                return self.get_latest_recommendation(symbol, timeframe)
            
            # Get the latest recommendation
            if symbol == "all":
                recommendations = self.get_latest_recommendation(symbol, timeframe)
                if isinstance(recommendations, list):
                    # Process multiple recommendations through intelligent system
                    return self._process_multiple_recommendations(recommendations, max_slots)
                else:
                    return recommendations
            else:
                # Single symbol recommendation
                recommendation = self.get_latest_recommendation(symbol, timeframe)
                if isinstance(recommendation, dict) and recommendation.get('error'):
                    return recommendation
                
                # Add to intelligent system for evaluation
                if isinstance(recommendation, dict):
                    self.intelligent_replacement.add_signal_for_evaluation(recommendation)
                    
                    # Check if we should process batch
                    if self.intelligent_replacement.should_process_batch():
                        return self._process_intelligent_batch(max_slots)
                    else:
                        # Return the recommendation with note about pending batch
                        recommendation['intelligent_status'] = 'pending_batch_evaluation'
                        return recommendation
                else:
                    return recommendation
                    
        except Exception as e:
            self.logger.error(f"Error getting intelligent recommendations: {e}")
            return {"error": str(e)}
    
    def _process_multiple_recommendations(self, recommendations, max_slots):
        """Process multiple recommendations through the intelligent replacement system."""
        try:
            if not recommendations:
                return []
            
            # Add all recommendations to the intelligent system
            for rec in recommendations:
                if isinstance(rec, dict) and not rec.get('error') and self.intelligent_replacement:
                    self.intelligent_replacement.add_signal_for_evaluation(rec)
            
            # Process the batch
            result = self._process_intelligent_batch(max_slots)
            
            if result.get('action') == 'execute_all':
                return result.get('recommended_signals', recommendations)
            elif result.get('action') == 'replace_and_execute':
                return {
                    'recommended_signals': result.get('recommended_signals', []),
                    'replacement_info': result.get('replacements', {}),
                    'intelligent_action': result.get('action')
                }
            else:
                return {
                    'recommended_signals': [],
                    'message': result.get('message', 'No recommendations after intelligent evaluation'),
                    'intelligent_action': result.get('action')
                }
                
        except Exception as e:
            self.logger.error(f"Error processing multiple recommendations: {e}")
            return {"error": str(e)}
    
    def _process_intelligent_batch(self, max_slots):
        """Process the current batch through intelligent replacement system."""
        try:
            if not self.intelligent_replacement:
                return {
                    'action': 'error',
                    'recommended_signals': [],
                    'error': 'Intelligent replacement system not available'
                }
            
            result = self.intelligent_replacement.process_intelligent_recommendations(max_slots)
            
            self.logger.info(f"Intelligent batch processing result: {result.get('action')}")
            
            if result.get('replacements'):
                replacement_info = result['replacements']
                self.logger.info(f"Executed {replacement_info.get('successful_replacements', 0)} "
                               f"order replacements")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing intelligent batch: {e}")
            return {
                'action': 'error',
                'recommended_signals': [],
                'error': str(e)
            }
    
    def force_process_pending_signals(self, max_slots=5):
        """Force process any pending signals in the intelligent replacement system."""
        try:
            if not self.intelligent_replacement:
                return {"error": "Intelligent replacement system not available"}
            
            if not self.intelligent_replacement.trade_collector.has_pending_signals():
                return {"message": "No pending signals to process"}
            
            result = self._process_intelligent_batch(max_slots)
            return result
            
        except Exception as e:
            self.logger.error(f"Error force processing pending signals: {e}")
            return {"error": str(e)}
    
    def get_replacement_system_status(self):
        """Get status of the intelligent replacement system."""
        try:
            if not self.intelligent_replacement:
                return {"status": "not_available", "message": "Intelligent replacement system not initialized"}
            
            return {
                "status": "available",
                "system_status": self.intelligent_replacement.get_system_status()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting replacement system status: {e}")
            return {"error": str(e)}

    def get_recommendations_for_current_boundary(self, symbol: str, timeframe: str) -> list:
        """
        Get recommendations for the current timeframe boundary.
        
        For example, if it's 1:33pm UTC and timeframe is 1h, 
        fetch only recommendations between 1:00pm and 2:00pm.
        
        Args:
            symbol: Symbol to filter for, or "all" for all symbols
            timeframe: Timeframe string (e.g., "1h", "15m", "4h")
            
        Returns:
            List of recommendations within the current boundary period
        """
        try:
            # Use the data agent method to get recommendations for current boundary
            recommendations = self.db_queue.data_agent.get_recommendations_for_current_boundary(symbol, timeframe)
            
            # Apply existing validation (timestamp + risk management) to filter results
            valid_recommendations = []
            filtering_stats = {
                'total_found': len(recommendations),
                'timestamp_expired': 0,
                'risk_filtered': 0,
                'risk_reasons': {}
            }
            
            for rec in recommendations:
                # First check timestamp validity
                if not self.is_recommendation_valid_timestamp_only(rec):
                    filtering_stats['timestamp_expired'] += 1
                    continue
                
                # Then check risk parameters
                risk_validation = self.validate_risk_parameters(rec)
                if not risk_validation["valid"]:
                    filtering_stats['risk_filtered'] += 1
                    error_msg = risk_validation.get("error", "Unknown risk validation error")
                    # Categorize risk filtering reasons
                    if "risk-reward ratio" in error_msg.lower() and "below minimum" in error_msg.lower():
                        reason_key = "risk_reward_ratio_below_minimum"
                    elif "confidence" in error_msg.lower() and "below minimum" in error_msg.lower():
                        reason_key = "confidence_below_threshold"
                    elif "hold" in error_msg.lower() or "neutral" in error_msg.lower():
                        reason_key = "hold_or_neutral_recommendation"
                    elif "missing required fields" in error_msg.lower():
                        reason_key = "missing_required_fields"
                    elif "invalid price values" in error_msg.lower():
                        reason_key = "invalid_price_values"
                    elif "direction misalignment" in error_msg.lower():
                        reason_key = "direction_misalignment"
                    else:
                        reason_key = "other_risk_validation"
                    
                    filtering_stats['risk_reasons'][reason_key] = filtering_stats['risk_reasons'].get(reason_key, 0) + 1
                    # Print the filtering reason for visibility
                    symbol_name = rec.get('symbol', 'Unknown')
                    print(f"Recommendation filtered for {symbol_name} - {error_msg}")
                    continue
                
                valid_recommendations.append(rec)
            
            # Log filtering statistics
            self.logger.info(f"Recommendations filtering summary for {timeframe} boundary:")
            self.logger.info(f"  • Total found: {filtering_stats['total_found']}")
            self.logger.info(f"  • Passed filtering: {len(valid_recommendations)}")
            self.logger.info(f"  • Timestamp expired: {filtering_stats['timestamp_expired']}")
            self.logger.info(f"  • Risk filtered: {filtering_stats['risk_filtered']}")
            
            if filtering_stats['risk_filtered'] > 0:
                # Get minimum RR value from config for display
                min_rr = getattr(self.config.trading, 'min_rr') if self.config else 0.0
                for reason, count in filtering_stats['risk_reasons'].items():
                    reason_display = reason.replace('_', ' ').title()
                    # Show actual minimum RR value for RR-related filtering
                    if "risk_reward_ratio" in reason:
                        print(f"    - {reason_display} ({min_rr}): {count}")
                    elif "direction_misalignment" in reason:
                        print(f"    - Direction Misalignment: {count}")
                    else:
                        print(f"    - {reason_display}: {count}")
            
            self.logger.info(f"Found {len(valid_recommendations)} valid recommendations within current {timeframe} boundary for {symbol}")
            
            # Sort by timestamp (newest first)
            sorted_results = sorted(valid_recommendations, key=lambda x: x['timestamp'], reverse=True)
            
            return sorted_results
            
        except Exception as e:
            self.logger.error(f"Error getting recommendations for current boundary: {e}")
            return []

    def get_fresh_recommendations_for_boundary(self, symbol: str, timeframe: str) -> list:
        """
        Get fresh recommendations for the current timeframe boundary without risk filtering.
        
        This method is used for mid-cycle checks where we only want to know if fresh
        recommendations exist, regardless of their risk parameters.
        
        Args:
            symbol: Symbol to filter for, or "all" for all symbols
            timeframe: Timeframe string (e.g., "1h", "15m", "4h")
            
        Returns:
            List of fresh recommendations within the current boundary period (timestamp validation only)
        """
        try:
            # Use the data agent method to get recommendations for current boundary
            recommendations = self.db_queue.data_agent.get_recommendations_for_current_boundary(symbol, timeframe)
            
            # Apply only timestamp validation (no risk management filtering)
            fresh_recommendations = []
            for rec in recommendations:
                if self.is_recommendation_valid_timestamp_only(rec):
                    fresh_recommendations.append(rec)
            
            self.logger.info(f"Found {len(fresh_recommendations)} fresh recommendations within current {timeframe} boundary for {symbol} (no risk filtering)")
            
            # Sort by timestamp (newest first)
            sorted_results = sorted(fresh_recommendations, key=lambda x: x['timestamp'], reverse=True)
            
            return sorted_results
            
        except Exception as e:
            self.logger.error(f"Error getting fresh recommendations for current boundary: {e}")
            return []

    def is_recommendation_valid_timestamp_only(self, recommendation: dict) -> bool:
        """
        Check if recommendation is valid based on timestamp only (no risk validation).
        
        Args:
            recommendation: Recommendation dictionary
            
        Returns:
            bool: True if recommendation timestamp is valid for current period
        """
        if not recommendation or recommendation.get('error'):
            return False
        
        timestamp_str = recommendation.get('timestamp')
        if not timestamp_str:
            self.logger.debug("Recommendation filtered - missing timestamp")
            return False
        
        timeframe = recommendation.get('timeframe')
        if not timeframe:
            self.logger.debug("Recommendation filtered - missing timeframe")
            return False
        
        try:
            # Use the new TimestampValidator for validation with current period allowance and grace period
            timestamp_validation_result = self.timestamp_validator.is_recommendation_valid(
                timestamp_str, timeframe, allow_current_period=True, grace_period_minutes=15
            )
            return timestamp_validation_result.is_valid
            
        except Exception as e:
            self.logger.error(f"Error during timestamp-only recommendation validation for {recommendation.get('symbol', 'Unknown')} {timeframe}: {e}")
            return False

    def apply_risk_filtering(self, recommendations: list) -> list:
        """
        Apply risk management filtering to a list of recommendations.
        
        This method separates risk validation from timestamp validation for better control.
        
        Args:
            recommendations: List of recommendation dictionaries
            
        Returns:
            List of recommendations that pass risk validation
        """
        valid_recommendations = []
        for rec in recommendations:
            risk_validation = self.validate_risk_parameters(rec)
            if risk_validation["valid"]:
                valid_recommendations.append(rec)
        
        return valid_recommendations

    def store_filtered_recommendations_to_latest(self, timeframe: str) -> bool:
        """
        Get filtered recommendations for current boundary and store them to latest_recommendations table.
        
        This method should be called after all recommender filtering is complete to store
        the final filtered recommendations that will be used for trading.
        
        Args:
            timeframe: Timeframe string (e.g., "1h", "15m", "4h")
            
        Returns:
            bool: True if storage was successful, False otherwise
        """
        try:
            # Get filtered recommendations for current boundary
            # This uses the same filtering logic as get_latest_recommendation but for all symbols
            recommendations = self.get_recommendations_for_current_boundary("all", timeframe)
            
            # Clear existing recommendations for this timeframe
            self.db_queue.data_agent.clear_latest_recommendations()
            
            # Store the filtered recommendations
            self.db_queue.data_agent.store_latest_recommendations(recommendations)
            
            self.logger.info(f"Stored {len(recommendations)} filtered recommendations to latest_recommendations table for {timeframe}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing filtered recommendations to latest_recommendations: {e}")
            return False
