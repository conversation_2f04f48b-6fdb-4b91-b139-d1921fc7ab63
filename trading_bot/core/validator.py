"""Validation module for trading signals."""
from datetime import datetime, timedelta, timezone
from typing import Dict, Any


class SignalValidator:
    """Validates and filters trading signals."""
    
    def __init__(self, config: Any):
        self.config = config
        self.signal_history = []
    
    def validate_signal(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a trading signal against rules and thresholds."""
        validation_result = {
            "valid": True,
            "reasons": [],
            "adjusted_confidence": signal.get("confidence", 0.0)
        }
        
        # Check confidence threshold
        if signal.get("confidence", 0) < self.config.trading.min_confidence_threshold:
            validation_result["valid"] = False
            validation_result["reasons"].append("Confidence below threshold")
        
        # Check for recent similar signals
        if self._has_recent_similar_signal(signal):
            validation_result["valid"] = False
            validation_result["reasons"].append("Similar signal recently executed")
        
        # Risk assessment
        risk_score = self._calculate_risk_score(signal)
        if risk_score > self.config.trading.risk_tolerance:
            validation_result["adjusted_confidence"] *= (1 - risk_score)
        
        return validation_result
    
    def _has_recent_similar_signal(self, signal: Dict[str, Any], window_hours: int = 24) -> bool:
        """Check if similar signal was executed recently."""
        cutoff = datetime.now(timezone.utc) - timedelta(hours=window_hours)
        
        for hist_signal in self.signal_history:
            if hist_signal["timestamp"] > cutoff:
                if (hist_signal["recommendation"] == signal["recommendation"] and
                    abs(hist_signal["confidence"] - signal["confidence"]) < 0.1):
                    return True
        
        return False
    
    def _calculate_risk_score(self, signal: Dict[str, Any]) -> float:
        """Calculate risk score based on signal characteristics."""
        risk_factors = signal.get("risk_factors", [])
        base_risk = len(risk_factors) * 0.1
        
        # Additional risk based on confidence
        confidence = signal.get("confidence", 0.0)
        if confidence < 0.5:
            base_risk += 0.3
        
        return min(base_risk, 1.0)
    
    def record_signal(self, signal: Dict[str, Any], executed: bool = False):
        """Record signal for history tracking."""
        self.signal_history.append({
            **signal,
            "timestamp": datetime.now(timezone.utc),
            "executed": executed
        })
        
        # Keep only last 100 signals
        if len(self.signal_history) > 100:
            self.signal_history = self.signal_history[-100:]
