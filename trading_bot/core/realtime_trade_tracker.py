"""Real-time trade tracking system for immediate trade recording and updates."""
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from trading_bot.core.data_agent import DataAgent


@dataclass
class TradeRecord:
    """Comprehensive trade record structure."""
    trade_id: str
    recommendation_id: str
    symbol: str
    side: str  # 'Buy' or 'Sell'
    quantity: float
    entry_price: float
    take_profit: float
    stop_loss: float
    order_id: Optional[str]  # Broker/exchange order ID
    order_link_id: Optional[str]  # Client-side order link ID
    status: str  # 'pending', 'filled', 'partially_filled', 'closed', 'cancelled'
    state: str  # 'trade', 'position'
    pnl: float = 0.0
    avg_exit_price: Optional[float] = None
    closed_size: Optional[float] = None
    prompt_name: Optional[str] = None
    timeframe: Optional[str] = None
    confidence: Optional[float] = None
    risk_reward_ratio: Optional[float] = None
    placed_by: str = 'BOT'
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    alteration_details: Optional[str] = None


class RealTimeTradeTracker:
    """
    Real-time trade tracking system that immediately records trades upon placement
    and updates them in real-time as they progress, eliminating expensive rebuilds.
    """
    
    def __init__(self, data_agent: DataAgent):
        self.data_agent = data_agent
        self.logger = logging.getLogger(__name__)
        self._pending_trades = {}  # Track trades awaiting order IDs
        
    def record_trade_placement(self, trade_signal: Dict[str, Any], symbol: str, 
                             trade_id: str, recommendation_id: str = "") -> str:
        """
        Immediately record a trade when it's placed, before getting order ID from broker.
        
        Args:
            trade_signal: The trading signal containing trade parameters
            symbol: Trading symbol
            trade_id: Unique trade identifier
            recommendation_id: Associated recommendation ID
            
        Returns:
            str: The trade record ID for tracking
        """
        try:
            # Create comprehensive trade record
            trade_record = TradeRecord(
                trade_id=trade_id,
                recommendation_id=recommendation_id,
                symbol=symbol,
                side=self._normalize_side(trade_signal.get('direction', trade_signal.get('recommendation', ''))),
                quantity=float(trade_signal.get('quantity', 0)),
                entry_price=float(trade_signal.get('entry_price', 0)),
                take_profit=float(trade_signal.get('take_profit', 0)),
                stop_loss=float(trade_signal.get('stop_loss', 0)),
                order_id=None,  # Will be updated when broker responds
                order_link_id=trade_id,  # Use trade_id as client-side link
                status='pending',
                state='trade',
                prompt_name=trade_signal.get('prompt_name', trade_signal.get('strategy_name')),
                timeframe=trade_signal.get('timeframe'),
                confidence=trade_signal.get('confidence'),
                risk_reward_ratio=trade_signal.get('risk_reward_ratio'),
                created_at=datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
            )
            
            # Store in database immediately
            success = self._store_trade_record(trade_record)
            
            if success:
                # Track as pending for order ID update
                self._pending_trades[trade_id] = trade_record
                self.logger.info(f"✅ Trade recorded immediately: {symbol} {trade_record.side} (ID: {trade_id})")
                return trade_id
            else:
                self.logger.error(f"❌ Failed to record trade: {symbol} {trade_record.side}")
                return ""
                
        except Exception as e:
            self.logger.error(f"❌ Error recording trade placement: {e}")
            return ""
    
    def update_trade_with_order_id(self, trade_id: str, order_response: Dict[str, Any]) -> bool:
        """
        Update trade record with order ID and details from broker response.
        
        Args:
            trade_id: The trade ID to update
            order_response: Response from broker containing order details
            
        Returns:
            bool: Success status
        """
        try:
            if trade_id not in self._pending_trades:
                self.logger.warning(f"⚠️ Trade {trade_id} not found in pending trades")
                return False
            
            # Extract order details from broker response
            order_result = order_response.get("result", {})
            order_id = order_result.get("orderId")
            order_link_id = order_result.get("orderLinkId")
            
            if not order_id:
                self.logger.error(f"❌ No order ID in broker response for trade {trade_id}")
                return False
            
            # Update trade record
            update_data = {
                'order_id': order_id,
                'orderLinkId': order_link_id,
                'status': 'submitted',
                'updated_at': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
            }
            
            success = self.data_agent.update_trade(trade_id, **update_data)
            
            if success:
                # Update pending trade record
                pending_trade = self._pending_trades[trade_id]
                pending_trade.order_id = order_id
                pending_trade.order_link_id = order_link_id
                pending_trade.status = 'submitted'
                
                self.logger.info(f"✅ Trade updated with order ID: {trade_id} -> {order_id}")
                return True
            else:
                self.logger.error(f"❌ Failed to update trade {trade_id} with order ID")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error updating trade with order ID: {e}")
            return False
    
    def update_trade_status(self, order_id: str, new_status: str, 
                          exchange_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Update trade status in real-time based on exchange updates.
        
        Args:
            order_id: Exchange order ID
            new_status: New trade status
            exchange_data: Additional data from exchange
            
        Returns:
            bool: Success status
        """
        try:
            # Find trade by order ID
            existing_trade = self.data_agent.get_trade_by_order_id(order_id)
            if not existing_trade:
                self.logger.warning(f"⚠️ Trade with order ID {order_id} not found")
                return False
            
            # Prepare update data
            update_data = {
                'status': new_status,
                'updated_at': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Add exchange-specific data if provided
            if exchange_data:
                if 'avgPrice' in exchange_data and float(exchange_data['avgPrice']) > 0:
                    update_data['entry_price'] = str(float(exchange_data['avgPrice']))

                if 'cumExecQty' in exchange_data:
                    update_data['quantity'] = str(float(exchange_data['cumExecQty']))
                
                # Handle position state changes
                if new_status in ['filled', 'partially_filled']:
                    update_data['state'] = 'position'
                elif new_status in ['closed', 'cancelled']:
                    update_data['state'] = 'trade'
                    if 'closedPnl' in exchange_data:
                        update_data['pnl'] = str(float(exchange_data['closedPnl']))
                    if 'avgExitPrice' in exchange_data:
                        update_data['avg_exit_price'] = str(float(exchange_data['avgExitPrice']))
                    if 'closedSize' in exchange_data:
                        update_data['closed_size'] = str(float(exchange_data['closedSize']))
            
            # Update in database
            trade_id = existing_trade.get('id')
            if not trade_id:
                self.logger.error(f"❌ No trade ID found for order {order_id}")
                return False
            success = self.data_agent.update_trade(trade_id, **update_data)
            
            if success:
                symbol = existing_trade.get('symbol', 'UNKNOWN')
                self.logger.info(f"✅ Trade status updated: {symbol} {order_id} -> {new_status}")
                
                # Remove from pending if completed
                if new_status in ['closed', 'cancelled'] and trade_id in self._pending_trades:
                    del self._pending_trades[trade_id]
                
                return True
            else:
                self.logger.error(f"❌ Failed to update trade status for order {order_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error updating trade status: {e}")
            return False
    
    def track_non_hold_trades(self, recommendations: List[Dict[str, Any]]) -> List[str]:
        """
        Track all non-HOLD trade recommendations by immediately recording them.
        
        Args:
            recommendations: List of trade recommendations
            
        Returns:
            List[str]: List of trade IDs that were recorded
        """
        recorded_trades = []
        
        for recommendation in recommendations:
            rec_type = recommendation.get('recommendation', '').upper()
            
            # Skip HOLD recommendations
            if rec_type == 'HOLD':
                continue
            
            # Only track actual trading signals
            if rec_type not in ['BUY', 'SELL', 'LONG', 'SHORT']:
                continue
            
            # Generate unique trade ID
            trade_id = str(uuid.uuid4())[:8]
            
            # Record the trade immediately
            recorded_id = self.record_trade_placement(
                trade_signal=recommendation,
                symbol=recommendation.get('symbol', 'UNKNOWN'),
                trade_id=trade_id,
                recommendation_id=recommendation.get('id', '')
            )
            
            if recorded_id:
                recorded_trades.append(recorded_id)
        
        return recorded_trades
    
    def _store_trade_record(self, trade_record: TradeRecord) -> bool:
        """Store trade record in database."""
        try:
            return self.data_agent.store_trade(
                trade_id=trade_record.trade_id,
                recommendation_id=trade_record.recommendation_id,
                symbol=trade_record.symbol,
                side=trade_record.side,
                quantity=trade_record.quantity,
                entry_price=trade_record.entry_price,
                take_profit=trade_record.take_profit,
                stop_loss=trade_record.stop_loss,
                order_id=trade_record.order_id,
                orderLinkId=trade_record.order_link_id,
                pnl=trade_record.pnl,
                status=trade_record.status,
                state=trade_record.state,
                avg_exit_price=trade_record.avg_exit_price,
                closed_size=trade_record.closed_size,
                created_at=trade_record.created_at,
                placed_by=trade_record.placed_by,
                alteration_details=trade_record.alteration_details,
                # Include comprehensive metadata
                prompt_name=trade_record.prompt_name,
                timeframe=trade_record.timeframe,
                confidence=trade_record.confidence,
                risk_reward_ratio=trade_record.risk_reward_ratio,
                order_type='Limit'  # Default order type
            )
        except Exception as e:
            self.logger.error(f"❌ Error storing trade record: {e}")
            return False
    
    def _normalize_side(self, direction: str) -> str:
        """Normalize trade direction to standard format."""
        direction_upper = direction.upper()
        if direction_upper in ['BUY', 'LONG']:
            return 'Buy'
        elif direction_upper in ['SELL', 'SHORT']:
            return 'Sell'
        else:
            return 'Buy'  # Default fallback
    
    def get_pending_trades(self) -> Dict[str, TradeRecord]:
        """Get all pending trades awaiting order IDs."""
        return self._pending_trades.copy()
    
    def cleanup_stale_pending_trades(self, max_age_minutes: int = 30) -> int:
        """Clean up stale pending trades that never received order IDs."""
        current_time = datetime.now(timezone.utc)
        stale_trades = []
        
        for trade_id, trade_record in self._pending_trades.items():
            if trade_record.created_at:
                created_time = datetime.fromisoformat(trade_record.created_at.replace('Z', '+00:00'))
                age_minutes = (current_time - created_time).total_seconds() / 60
                
                if age_minutes > max_age_minutes:
                    stale_trades.append(trade_id)
        
        # Remove stale trades
        for trade_id in stale_trades:
            del self._pending_trades[trade_id]
            self.logger.warning(f"⚠️ Removed stale pending trade: {trade_id}")
        
        return len(stale_trades)
