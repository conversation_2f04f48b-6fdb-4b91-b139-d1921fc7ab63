"""Position display module for formatting and showing trade information."""
import logging
from typing import Dict, Any, List
from datetime import datetime, timezone

from trading_bot.core.utils import smart_format_price
from trading_bot.core.data_agent import DataAgent


class PositionDisplay:
    """Handles display of position and trade information."""
    
    def __init__(self, data_agent: DataAgent):
        self.data_agent = data_agent
        self.logger = logging.getLogger(__name__)
    
    def get_position_summary(self) -> Dict[str, Any]:
        """Get a summary of all positions and trades."""
        try:
            all_trades = self.data_agent.get_trades()
            
            summary = {
                "total_trades": len(all_trades),
                "open_trades": 0,
                "closed_trades": 0,
                "total_pnl": 0.0,
                "trades_by_status": {},
                "trades_by_state": {}
            }
            
            for trade in all_trades:
                status = trade.get('status', 'unknown')
                state = trade.get('state', 'unknown')
                pnl_value = trade.get('pnl', 0)
                try:
                    pnl = float(pnl_value) if pnl_value and str(pnl_value).strip() else 0.0
                except (ValueError, TypeError):
                    pnl = 0.0
                
                # Count by status
                summary["trades_by_status"][status] = summary["trades_by_status"].get(status, 0) + 1
                
                # Count by state
                summary["trades_by_state"][state] = summary["trades_by_state"].get(state, 0) + 1
                
                # Count open/closed
                if status == 'open':
                    summary["open_trades"] += 1
                elif status == 'closed':
                    summary["closed_trades"] += 1
                
                # Sum PnL
                summary["total_pnl"] += pnl
            
            # Format total PnL - show 0.00 instead of N/A when total is zero
            if summary["total_pnl"] == 0:
                summary["total_pnl_formatted"] = "0.00"
            else:
                summary["total_pnl_formatted"] = smart_format_price(summary["total_pnl"])
            
            # Add analytics data
            try:
                portfolio_ev = self.data_agent.get_portfolio_ev_summary()
                summary["analytics"] = portfolio_ev
                
                # Add formatted analytics for display
                if portfolio_ev and not portfolio_ev.get('error'):
                    from trading_bot.core.analytics_utils import format_analytics_summary
                    summary["analytics_formatted"] = format_analytics_summary(portfolio_ev)
                else:
                    summary["analytics_formatted"] = "📊 No analytics data available"
                    
            except Exception as e:
                self.logger.warning(f"Failed to get analytics data: {e}")
                summary["analytics"] = {"error": str(e)}
                summary["analytics_formatted"] = "❌ Analytics unavailable"
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error getting position summary: {e}")
            return {"error": str(e)}
    
    def display_positions(self, show_lifecycle_analysis: bool = True) -> None:
        """Display positions in a formatted table with enhanced lifecycle information."""
        try:
            all_trades = self.data_agent.get_trades()
            
            if not all_trades:
                print("\n📊 No trades found in database.")
                return
            
            print(f"\n{'='*140}")
            print("POSITION MANAGER - ENHANCED TRADE HISTORY")
            print(f"{'='*140}")
            
            # Enhanced header with lifecycle indicators
            print(f"{'Trade ID':<12} {'Symbol':<12} {'Side':<6} {'Qty':<12} {'Entry':<10} {'PnL':<12} {'Status':<10} {'State':<10} {'Lifecycle':<12} {'Updated':<20}")
            print("-" * 140)
            
            total_pnl = 0.0
            lifecycle_stats = {'placed': 0, 'filled': 0, 'position': 0, 'closed': 0, 'cancelled': 0, 'issues': 0}
            problematic_trades = []
            
            for trade in all_trades:
                trade_id = trade.get('id', 'N/A')[:12]
                symbol = trade.get('symbol', 'N/A')
                side = trade.get('side', 'N/A')
                quantity = smart_format_price(trade.get('quantity', 0))
                entry_price = smart_format_price(trade.get('entry_price', 0))
                pnl = trade.get('pnl', 0)
                status = trade.get('status', 'N/A')
                state = trade.get('state', 'N/A')
                
                # Handle PnL formatting - for closed trades, show 0.00 instead of N/A
                if pnl is None:
                    pnl_formatted = "N/A"
                else:
                    pnl_float = float(pnl)
                    if status == 'closed' and pnl_float == 0:
                        # For closed trades, show 0.00 instead of N/A when PnL is zero
                        pnl_formatted = "0.00"
                    else:
                        pnl_formatted = smart_format_price(pnl_float)
                updated_at = trade.get('updated_at', trade.get('created_at', 'N/A'))[:19]
                
                # Determine lifecycle stage and detect issues
                lifecycle_stage, has_issue = self._determine_lifecycle_stage(status, state)
                lifecycle_stats[lifecycle_stage] += 1
                
                if has_issue:
                    lifecycle_stats['issues'] += 1
                    problematic_trades.append({
                        'id': trade_id,
                        'symbol': symbol,
                        'status': status,
                        'state': state,
                        'issue': f"Inconsistent {status}/{state}"
                    })
                
                # Color coding for lifecycle stages
                lifecycle_display = self._format_lifecycle_stage(lifecycle_stage, has_issue)
                
                # Add to total PnL - use the processed float value
                if pnl is not None:
                    total_pnl += pnl_float
                
                print(f"{trade_id:<12} {symbol:<12} {side:<6} {quantity:<12} {entry_price:<10} {pnl_formatted:<12} {status:<10} {state:<10} {lifecycle_display:<12} {updated_at:<20}")
            
            print("-" * 140)
            # Format total PnL - show 0.00 instead of N/A when total is zero
            if total_pnl == 0:
                total_pnl_formatted = "0.00"
            else:
                total_pnl_formatted = smart_format_price(total_pnl)
            print(f"Total PnL: {total_pnl_formatted}")
            
            # Display lifecycle statistics
            if show_lifecycle_analysis:
                print(f"\n{'='*80}")
                print("TRADE LIFECYCLE ANALYSIS")
                print(f"{'='*80}")
                
                print(f"📊 Lifecycle Distribution:")
                print(f"   🟡 Placed (open/trade):     {lifecycle_stats['placed']:>3}")
                print(f"   🟠 Filled (open/position):  {lifecycle_stats['filled']:>3}")
                print(f"   🟢 Closed (closed/position): {lifecycle_stats['closed']:>3}")
                print(f"   🔴 Cancelled:               {lifecycle_stats['cancelled']:>3}")
                print(f"   ⚠️  Issues detected:         {lifecycle_stats['issues']:>3}")
                
                # Show problematic trades
                if problematic_trades:
                    print(f"\n⚠️  TRADES REQUIRING ATTENTION:")
                    for trade in problematic_trades[:5]:  # Show first 5
                        print(f"   • {trade['id']} ({trade['symbol']}): {trade['issue']}")
                    if len(problematic_trades) > 5:
                        print(f"   ... and {len(problematic_trades) - 5} more trades with issues")
                    
                    print(f"\n💡 Run with --position-manager to automatically fix these issues")
                
                # Show recommendations
                if lifecycle_stats['issues'] > 0:
                    print(f"\n🔧 RECOMMENDATIONS:")
                    print(f"   • Run sync to fix {lifecycle_stats['issues']} problematic trades")
                    print(f"   • Check exchange connectivity if many trades are stuck")
                    
            print(f"{'='*140}")
            
        except Exception as e:
            self.logger.error(f"Error displaying positions: {e}")
            print(f"❌ Error displaying positions: {e}")
    
    def _determine_lifecycle_stage(self, status: str, state: str) -> tuple[str, bool]:
        """
        Determine the lifecycle stage and detect issues.
        
        Returns:
            Tuple of (lifecycle_stage, has_issue)
        """
        status = status.lower() if status else 'unknown'
        state = state.lower() if state else 'unknown'
        
        # Normal lifecycle stages
        if status == 'open' and state == 'trade':
            return 'placed', False
        elif status == 'open' and state == 'position':
            return 'filled', False
        elif status == 'closed' and state == 'position':
            return 'closed', False
        elif status == 'cancelled' and state == 'cancelled':
            return 'cancelled', False
        
        # Problematic combinations
        elif status == 'closed' and state == 'unknown':
            return 'closed', True  # Common issue - closed but state unknown
        elif status == 'open' and state == 'unknown':
            return 'placed', True  # Issue - open but state unknown
        elif status == 'closed' and state == 'trade':
            return 'closed', True  # Issue - closed but still in trade state
        else:
            return 'issues', True  # Other problematic combinations
    
    def _format_lifecycle_stage(self, stage: str, has_issue: bool) -> str:
        """Format lifecycle stage with appropriate indicators."""
        if has_issue:
            return f"⚠️ {stage.upper()}"
        
        stage_icons = {
            'placed': '🟡 PLACED',
            'filled': '🟠 FILLED',
            'closed': '🟢 CLOSED',
            'cancelled': '🔴 CANCEL',
            'issues': '❌ ISSUE'
        }
        
        return stage_icons.get(stage, f"❓ {stage.upper()}")
