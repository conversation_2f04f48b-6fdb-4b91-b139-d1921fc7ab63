"""Position analytics module for updating analytics when trades close."""
import logging
from typing import Dict, Any
from datetime import datetime, timezone

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.analytics_utils import get_timeframe_from_trade_data
from trading_bot.core.utils import normalize_symbol_for_bybit


class PositionAnalytics:
    """Handles analytics updates for position management."""
    
    def __init__(self, data_agent: DataAgent):
        self.data_agent = data_agent
        self.logger = logging.getLogger(__name__)
    
    def update_analytics_for_closed_trade(self, trade_data: Dict[str, Any]) -> None:
        """Update analytics when a trade closes."""
        try:
            # Get timeframe from recommendation_id
            timeframe = get_timeframe_from_trade_data(trade_data, self.data_agent)
            
            # Normalize the symbol to ensure consistency
            raw_symbol = trade_data.get('symbol', 'unknown')
            normalized_symbol = normalize_symbol_for_bybit(raw_symbol)
            
            # Add timeframe to trade data for analytics and use normalized symbol
            analytics_data = trade_data.copy()
            analytics_data['symbol'] = normalized_symbol
            analytics_data['timeframe'] = timeframe
            
            # Update trading stats
            self.data_agent.update_trading_stats_for_closed_trade(analytics_data)
            
            self.logger.info(f"📊 Updated analytics for closed trade: {normalized_symbol} ({timeframe})")
            
        except Exception as e:
            self.logger.warning(f"Failed to update analytics for closed trade: {e}")
