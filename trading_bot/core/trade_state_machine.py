"""
Trade State Machine for comprehensive trade lifecycle tracking.
Provides state management for trades from submission to final exit.
"""
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from trading_bot.core.data_agent import DataAgent
import sqlite3
import os
from pathlib import Path


class TradeState(Enum):
    """All possible states in the trade lifecycle."""

    # Order submission states
    ORDER_SUBMITTED = "order_submitted"
    ORDER_CONFIRMED = "order_confirmed"
    ORDER_REJECTED = "order_rejected"

    # Active position states
    POSITION_OPEN = "position_open"
    POSITION_OPEN_CONFIRMED = "position_open_confirmed"

    # Tightening states
    TIGHTENING_ACTIVE = "tightening_active"
    TIGHTENING_COMPLETED = "tightening_completed"
    TIGHTENING_FAILED = "tightening_failed"

    # Exit signal states
    EXIT_SIGNAL_TP = "exit_signal_tp"
    EXIT_SIGNAL_SL = "exit_signal_sl"
    EXIT_SIGNAL_MANUAL = "exit_signal_manual"
    EXIT_SIGNAL_TIMEOUT = "exit_signal_timeout"

    # Exit execution states
    EXIT_ORDER_SUBMITTED = "exit_order_submitted"
    EXIT_ORDER_CONFIRMED = "exit_order_confirmed"
    POSITION_CLOSING = "position_closing"

    # Final exit states
    POSITION_CLOSED_TP = "position_closed_tp"
    POSITION_CLOSED_SL = "position_closed_sl"
    POSITION_CLOSED_MANUAL = "position_closed_manual"
    POSITION_CLOSED_TIMEOUT = "position_closed_timeout"

    # Cancellation states
    ORDER_CANCELLED_SLOT_OPTIMIZER = "order_cancelled_slot_optimizer"
    POSITION_CANCELLED_SLOT_OPTIMIZER = "position_cancelled_slot_optimizer"
    CANCELLED_MANUAL = "cancelled_manual"
    CANCELLED_EXCHANGE = "cancelled_exchange"
    CANCELLED_TIMEOUT = "cancelled_timeout"

    # Error states
    ERROR_SUBMISSION = "error_submission"
    ERROR_CONFIRMATION = "error_confirmation"
    ERROR_EXCHANGE_SYNC = "error_exchange_sync"
    ERROR_RECOVERY_PENDING = "error_recovery_pending"
    ERROR_RESOLVED = "error_resolved"


class TradeStateMachine:
    """Manages trade state transitions and validation."""

    def __init__(self, db_path: Optional[str] = None):
        # Initialize logger first (needed by _init_database)
        self.logger = logging.getLogger(__name__)

        # Use separate database for state machine
        if db_path is None:
            db_path = os.getenv('TRADE_STATE_DB_PATH', 'trading_bot/data/trade_states.db')

        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Initialize database if it doesn't exist
        if not self.db_path.exists():
            self._init_database()

    def _init_database(self):
        """Initialize the trade states database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create trade_states table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trade_states (
                    id TEXT PRIMARY KEY,
                    trade_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    current_state TEXT NOT NULL,
                    previous_state TEXT,

                    -- Order tracking (Bybit submits main + TP + SL together)
                    main_order_id TEXT,
                    tp_order_id TEXT,
                    sl_order_id TEXT,
                    order_link_id TEXT,

                    -- Position data
                    entry_price REAL,
                    current_tp REAL,
                    current_sl REAL,
                    position_size REAL,
                    unrealized_pnl REAL,

                    -- Exit tracking
                    exit_triggered_by TEXT,    -- 'tp_order', 'sl_order', 'manual', 'slot_optimizer'
                    exit_order_id TEXT,        -- Which order caused the exit
                    exit_price REAL,           -- Price when exit was triggered
                    exit_reason TEXT,          -- Detailed exit reason

                    -- Tightening tracking
                    tightening_count INTEGER DEFAULT 0,
                    last_tightened_at DATETIME,
                    tightening_history TEXT,   -- JSON array of tightening events

                    -- Cancellation tracking
                    cancelled_by TEXT,         -- 'slot_optimizer', 'manual', 'exchange', 'timeout'
                    cancellation_reason TEXT,  -- Detailed reason for cancellation
                    cancelled_at DATETIME,
                    cancellation_order_id TEXT, -- Which order was cancelled

                    -- Slot optimizer data
                    slot_risk_data TEXT,       -- JSON with risk metrics
                    slot_allocation_data TEXT, -- Slot usage at time of action

                    -- State management
                    state_changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    state_changed_by TEXT DEFAULT 'SYSTEM',
                    exchange_data TEXT,        -- Latest JSON from Bybit
                    error_message TEXT,
                    retry_count INTEGER DEFAULT 0,

                    -- Metadata
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

                    FOREIGN KEY (trade_id) REFERENCES trades(id),
                    UNIQUE(trade_id)
                )
            ''')

            # Create trade_state_history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trade_state_history (
                    id TEXT PRIMARY KEY,
                    trade_state_id TEXT NOT NULL,
                    from_state TEXT,
                    to_state TEXT NOT NULL,
                    transition_reason TEXT,
                    transition_data TEXT,      -- JSON with transition details
                    exchange_data TEXT,        -- Exchange state at transition
                    transition_at DATETIME DEFAULT CURRENT_TIMESTAMP,

                    FOREIGN KEY (trade_state_id) REFERENCES trade_states(id)
                )
            ''')

            # Create indexes for performance
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_trade_states_trade_id
                ON trade_states(trade_id)
            ''')

            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_trade_states_symbol
                ON trade_states(symbol)
            ''')

            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_trade_states_current_state
                ON trade_states(current_state)
            ''')

            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_trade_states_main_order_id
                ON trade_states(main_order_id)
            ''')

            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_trade_state_history_trade_state_id
                ON trade_state_history(trade_state_id)
            ''')

            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_trade_state_history_transition_at
                ON trade_state_history(transition_at)
            ''')

            conn.commit()
            conn.close()

            self.logger.info(f"Initialized trade states database at {self.db_path}")

        except Exception as e:
            self.logger.error(f"Error initializing trade states database: {e}")
            raise

    def _get_connection(self):
        """Get database connection."""
        return sqlite3.connect(self.db_path)

    # Define valid state transitions
    VALID_TRANSITIONS = {
        # Order submission flow
        TradeState.ORDER_SUBMITTED: [
            TradeState.ORDER_CONFIRMED,
            TradeState.ORDER_REJECTED,
            TradeState.ORDER_CANCELLED_SLOT_OPTIMIZER,
            TradeState.CANCELLED_MANUAL,
            TradeState.ERROR_SUBMISSION
        ],

        # Order confirmation flow
        TradeState.ORDER_CONFIRMED: [
            TradeState.POSITION_OPEN,
            TradeState.ORDER_CANCELLED_SLOT_OPTIMIZER,
            TradeState.CANCELLED_MANUAL,
            TradeState.ERROR_CONFIRMATION
        ],

        # Active position flow
        TradeState.POSITION_OPEN: [
            TradeState.POSITION_OPEN_CONFIRMED,
            TradeState.POSITION_CANCELLED_SLOT_OPTIMIZER,
            TradeState.CANCELLED_MANUAL,
            TradeState.ERROR_EXCHANGE_SYNC
        ],

        TradeState.POSITION_OPEN_CONFIRMED: [
            TradeState.TIGHTENING_ACTIVE,
            TradeState.EXIT_SIGNAL_TP,
            TradeState.EXIT_SIGNAL_SL,
            TradeState.EXIT_SIGNAL_MANUAL,
            TradeState.EXIT_SIGNAL_TIMEOUT,
            TradeState.POSITION_CANCELLED_SLOT_OPTIMIZER,
            TradeState.CANCELLED_MANUAL,
            TradeState.ERROR_EXCHANGE_SYNC
        ],

        # Tightening flow
        TradeState.TIGHTENING_ACTIVE: [
            TradeState.TIGHTENING_COMPLETED,
            TradeState.TIGHTENING_FAILED,
            TradeState.EXIT_SIGNAL_TP,
            TradeState.EXIT_SIGNAL_SL,
            TradeState.EXIT_SIGNAL_MANUAL
        ],

        TradeState.TIGHTENING_COMPLETED: [
            TradeState.POSITION_OPEN_CONFIRMED,  # Back to active state with new SL
            TradeState.EXIT_SIGNAL_TP,
            TradeState.EXIT_SIGNAL_SL,
            TradeState.EXIT_SIGNAL_MANUAL
        ],

        TradeState.TIGHTENING_FAILED: [
            TradeState.POSITION_OPEN_CONFIRMED,  # Continue with old SL
            TradeState.EXIT_SIGNAL_SL,  # May need to exit if tightening failed
            TradeState.ERROR_EXCHANGE_SYNC
        ],

        # Exit signal flow
        TradeState.EXIT_SIGNAL_TP: [TradeState.EXIT_ORDER_SUBMITTED],
        TradeState.EXIT_SIGNAL_SL: [TradeState.EXIT_ORDER_SUBMITTED],
        TradeState.EXIT_SIGNAL_MANUAL: [TradeState.EXIT_ORDER_SUBMITTED],
        TradeState.EXIT_SIGNAL_TIMEOUT: [TradeState.EXIT_ORDER_SUBMITTED],

        # Exit execution flow
        TradeState.EXIT_ORDER_SUBMITTED: [
            TradeState.EXIT_ORDER_CONFIRMED,
            TradeState.POSITION_CLOSING,
            TradeState.ERROR_EXCHANGE_SYNC
        ],

        TradeState.EXIT_ORDER_CONFIRMED: [TradeState.POSITION_CLOSING],
        TradeState.POSITION_CLOSING: [
            TradeState.POSITION_CLOSED_TP,
            TradeState.POSITION_CLOSED_SL,
            TradeState.POSITION_CLOSED_MANUAL,
            TradeState.POSITION_CLOSED_TIMEOUT,
            TradeState.ERROR_EXCHANGE_SYNC
        ],

        # Error handling
        TradeState.ERROR_SUBMISSION: [
            TradeState.ORDER_SUBMITTED,  # Retry
            TradeState.ERROR_RECOVERY_PENDING
        ],

        TradeState.ERROR_CONFIRMATION: [
            TradeState.ORDER_CONFIRMED,  # Retry
            TradeState.ERROR_RECOVERY_PENDING
        ],

        TradeState.ERROR_EXCHANGE_SYNC: [
            TradeState.ERROR_RESOLVED,  # Manual resolution
            TradeState.ERROR_RECOVERY_PENDING
        ],

        TradeState.ERROR_RECOVERY_PENDING: [TradeState.ERROR_RESOLVED],
        TradeState.ERROR_RESOLVED: []  # Can transition to any valid state
    }

    # Terminal states (cannot transition from these)
    TERMINAL_STATES = {
        TradeState.POSITION_CLOSED_TP,
        TradeState.POSITION_CLOSED_SL,
        TradeState.POSITION_CLOSED_MANUAL,
        TradeState.POSITION_CLOSED_TIMEOUT,
        TradeState.ORDER_CANCELLED_SLOT_OPTIMIZER,
        TradeState.POSITION_CANCELLED_SLOT_OPTIMIZER,
        TradeState.CANCELLED_MANUAL,
        TradeState.CANCELLED_EXCHANGE,
        TradeState.CANCELLED_TIMEOUT,
        TradeState.ORDER_REJECTED
    }

    # Remove duplicate __init__ method

    def initialize_trade_state(self, trade_id: str, symbol: str,
                             main_order_id: Optional[str] = None, tp_order_id: Optional[str] = None,
                             sl_order_id: Optional[str] = None, order_link_id: Optional[str] = None) -> Optional[str]:
        """
        Initialize a new trade state record.

        Args:
            trade_id: Reference to existing trade in trades table
            symbol: Trading symbol
            main_order_id: Main position order ID from exchange
            tp_order_id: Take profit order ID from exchange
            sl_order_id: Stop loss order ID from exchange
            order_link_id: Client order link ID

        Returns:
            trade_state_id: ID of the created state record
        """
        try:
            # Generate state record ID
            import uuid
            trade_state_id = str(uuid.uuid4())

            # Create initial state record
            state_data = {
                'id': trade_state_id,
                'trade_id': trade_id,
                'symbol': symbol,
                'current_state': TradeState.ORDER_SUBMITTED.value,
                'previous_state': None,
                'main_order_id': main_order_id,
                'tp_order_id': tp_order_id,
                'sl_order_id': sl_order_id,
                'order_link_id': order_link_id,
                'state_changed_at': datetime.now(timezone.utc).isoformat(),
                'state_changed_by': 'SYSTEM',
                'tightening_count': 0,
                'created_at': datetime.now(timezone.utc).isoformat()
            }

            # Insert into trade_states table
            success = self._insert_trade_state(state_data)
            if success:
                # Record initial state in history
                self._record_state_transition(
                    trade_state_id=trade_state_id,
                    from_state=None,
                    to_state=TradeState.ORDER_SUBMITTED.value,
                    reason="Trade initialized",
                    transition_data={"initialization": True}
                )

                self.logger.info(f"Initialized trade state for trade {trade_id}: {trade_state_id}")
                return trade_state_id
            else:
                self.logger.error(f"Failed to initialize trade state for trade {trade_id}")
                return None

        except Exception as e:
            self.logger.error(f"Error initializing trade state: {e}")
            return None

    def transition_state(self, trade_state_id: str, new_state: TradeState,
                        reason: str = "", transition_data: Optional[Dict[str, Any]] = None,
                        changed_by: str = "SYSTEM") -> bool:
        """
        Transition a trade to a new state with validation.

        Args:
            trade_state_id: ID of the trade state record
            new_state: New state to transition to
            reason: Reason for the transition
            transition_data: Additional data about the transition
            changed_by: Who/what triggered the transition

        Returns:
            bool: True if transition was successful
        """
        try:
            # Get current state
            current_state_data = self._get_trade_state(trade_state_id)
            if not current_state_data:
                self.logger.error(f"Trade state {trade_state_id} not found")
                return False

            current_state = TradeState(current_state_data['current_state'])

            # Validate transition
            if not self._is_valid_transition(current_state, new_state):
                self.logger.error(f"Invalid transition from {current_state.value} to {new_state.value}")
                return False

            # Check if current state is terminal
            if current_state in self.TERMINAL_STATES:
                self.logger.error(f"Cannot transition from terminal state {current_state.value}")
                return False

            # Prepare transition data
            if transition_data is None:
                transition_data = {}
            transition_data.update({
                'previous_state': current_state.value,
                'new_state': new_state.value,
                'transition_reason': reason,
                'changed_by': changed_by,
                'transition_at': datetime.now(timezone.utc).isoformat()
            })

            # Update state record
            update_data = {
                'current_state': new_state.value,
                'previous_state': current_state.value,
                'state_changed_at': datetime.now(timezone.utc).isoformat(),
                'state_changed_by': changed_by
            }

            # Add state-specific data
            if new_state == TradeState.TIGHTENING_COMPLETED:
                update_data['tightening_count'] = current_state_data.get('tightening_count', 0) + 1
                update_data['last_tightened_at'] = datetime.now(timezone.utc).isoformat()

            elif new_state in [TradeState.POSITION_CLOSED_TP, TradeState.POSITION_CLOSED_SL,
                             TradeState.POSITION_CLOSED_MANUAL, TradeState.POSITION_CLOSED_TIMEOUT]:
                exit_price = transition_data.get('exit_price')
                if exit_price is not None:
                    update_data['exit_price'] = str(exit_price)
                update_data['closed_at'] = datetime.now(timezone.utc).isoformat()

            elif 'CANCELLED' in new_state.value:
                update_data['cancelled_by'] = transition_data.get('cancelled_by', changed_by)
                update_data['cancellation_reason'] = reason
                update_data['cancelled_at'] = datetime.now(timezone.utc).isoformat()

            # Update the state record
            success = self._update_trade_state(trade_state_id, update_data)
            if success:
                # Record transition in history
                self._record_state_transition(
                    trade_state_id=trade_state_id,
                    from_state=current_state.value,
                    to_state=new_state.value,
                    reason=reason,
                    transition_data=transition_data
                )

                self.logger.info(f"State transition successful: {trade_state_id} from {current_state.value} to {new_state.value}")
                return True
            else:
                self.logger.error(f"Failed to update trade state {trade_state_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error transitioning state: {e}")
            return False

    def get_trade_state(self, trade_state_id: str) -> Optional[Dict[str, Any]]:
        """Get current state of a trade."""
        return self._get_trade_state(trade_state_id)

    def get_trade_state_history(self, trade_state_id: str) -> List[Dict[str, Any]]:
        """Get complete state transition history for a trade."""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM trade_state_history
                WHERE trade_state_id = ?
                ORDER BY transition_at DESC
            ''', (trade_state_id,))

            rows = cursor.fetchall()
            history = []
            for row in rows:
                # Convert row to dict manually since we don't have data_agent
                columns = [description[0] for description in cursor.description]
                row_dict = dict(zip(columns, row))

                # Parse JSON fields
                if 'transition_data' in row_dict and row_dict['transition_data']:
                    try:
                        row_dict['transition_data'] = json.loads(row_dict['transition_data'])
                    except (json.JSONDecodeError, TypeError):
                        row_dict['transition_data'] = {}

                if 'exchange_data' in row_dict and row_dict['exchange_data']:
                    try:
                        row_dict['exchange_data'] = json.loads(row_dict['exchange_data'])
                    except (json.JSONDecodeError, TypeError):
                        row_dict['exchange_data'] = {}

                history.append(row_dict)

            conn.close()
            return history

        except Exception as e:
            self.logger.error(f"Error getting trade state history: {e}")
            return []

    def _is_valid_transition(self, from_state: TradeState, to_state: TradeState) -> bool:
        """Check if a state transition is valid."""
        # Allow staying in the same state (no-op transitions)
        if from_state == to_state:
            return True
        return to_state in self.VALID_TRANSITIONS.get(from_state, [])

    def _insert_trade_state(self, state_data: Dict[str, Any]) -> bool:
        """Insert a new trade state record."""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO trade_states (
                    id, trade_id, symbol, current_state, previous_state,
                    main_order_id, tp_order_id, sl_order_id, order_link_id,
                    state_changed_at, state_changed_by, tightening_count, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                state_data['id'],
                state_data['trade_id'],
                state_data['symbol'],
                state_data['current_state'],
                state_data.get('previous_state'),
                state_data.get('main_order_id'),
                state_data.get('tp_order_id'),
                state_data.get('sl_order_id'),
                state_data.get('order_link_id'),
                state_data['state_changed_at'],
                state_data['state_changed_by'],
                state_data.get('tightening_count', 0),
                state_data['created_at']
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            self.logger.error(f"Error inserting trade state: {e}")
            return False

    def _update_trade_state(self, trade_state_id: str, update_data: Dict[str, Any]) -> bool:
        """Update a trade state record."""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Build dynamic update query
            set_parts = []
            values = []

            for key, value in update_data.items():
                if key in ['current_state', 'previous_state', 'state_changed_at', 'state_changed_by',
                          'main_order_id', 'tp_order_id', 'sl_order_id', 'exit_price', 'closed_at',
                          'cancelled_by', 'cancellation_reason', 'cancelled_at', 'tightening_count',
                          'last_tightened_at', 'error_message']:
                    set_parts.append(f"{key} = ?")
                    # Handle None values for database - convert to None for SQL NULL
                    values.append(value)

            if not set_parts:
                return False

            values.append(trade_state_id)
            query = f"UPDATE trade_states SET {', '.join(set_parts)} WHERE id = ?"

            cursor.execute(query, values)
            conn.commit()
            conn.close()

            return cursor.rowcount > 0

        except Exception as e:
            self.logger.error(f"Error updating trade state: {e}")
            return False

    def _get_trade_state(self, trade_state_id: str) -> Optional[Dict[str, Any]]:
        """Get a trade state record."""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM trade_states WHERE id = ?', (trade_state_id,))
            row = cursor.fetchone()

            if row:
                # Convert row to dict manually since we don't have data_agent
                columns = [description[0] for description in cursor.description]
                result = dict(zip(columns, row))

                # Parse JSON fields
                if 'slot_risk_data' in result and result['slot_risk_data']:
                    try:
                        result['slot_risk_data'] = json.loads(result['slot_risk_data'])
                    except (json.JSONDecodeError, TypeError):
                        result['slot_risk_data'] = {}

                if 'slot_allocation_data' in result and result['slot_allocation_data']:
                    try:
                        result['slot_allocation_data'] = json.loads(result['slot_allocation_data'])
                    except (json.JSONDecodeError, TypeError):
                        result['slot_allocation_data'] = {}

                if 'tightening_history' in result and result['tightening_history']:
                    try:
                        result['tightening_history'] = json.loads(result['tightening_history'])
                    except (json.JSONDecodeError, TypeError):
                        result['tightening_history'] = []

                if 'exchange_data' in result and result['exchange_data']:
                    try:
                        result['exchange_data'] = json.loads(result['exchange_data'])
                    except (json.JSONDecodeError, TypeError):
                        result['exchange_data'] = {}

                conn.close()
                return result

            conn.close()
            return None

        except Exception as e:
            self.logger.error(f"Error getting trade state: {e}")
            return None

    def update_trade_state(self, trade_state_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update specific fields of a trade state record.
        Handles None values properly for database operations.
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Build dynamic update query
            set_parts = []
            values = []

            for key, value in updates.items():
                if key in ['main_order_id', 'tp_order_id', 'sl_order_id', 'order_link_id',
                          'entry_price', 'current_tp', 'current_sl', 'position_size',
                          'unrealized_pnl', 'exit_price', 'exit_reason', 'tightening_count',
                          'last_tightened_at', 'cancelled_by', 'cancellation_reason',
                          'cancelled_at', 'error_message']:
                    set_parts.append(f"{key} = ?")
                    values.append(value)

            if not set_parts:
                return False

            values.append(trade_state_id)
            query = f"UPDATE trade_states SET {', '.join(set_parts)}, updated_at = CURRENT_TIMESTAMP WHERE id = ?"

            cursor.execute(query, values)
            conn.commit()
            conn.close()

            return cursor.rowcount > 0

        except Exception as e:
            self.logger.error(f"Error updating trade state: {e}")
            return False

    def _record_state_transition(self, trade_state_id: str, from_state: Optional[str],
                               to_state: str, reason: str,
                               transition_data: Optional[Dict[str, Any]] = None) -> bool:
        """Record a state transition in history."""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            import uuid
            history_id = str(uuid.uuid4())

            cursor.execute('''
                INSERT INTO trade_state_history (
                    id, trade_state_id, from_state, to_state,
                    transition_reason, transition_data, transition_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                history_id,
                trade_state_id,
                from_state,
                to_state,
                reason,
                json.dumps(transition_data or {}),
                datetime.now(timezone.utc).isoformat()
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            self.logger.error(f"Error recording state transition: {e}")
            return False
