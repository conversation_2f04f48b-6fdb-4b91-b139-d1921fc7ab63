"""Cleaner module for managing outdated chart files."""
import logging
import os
import sqlite3
import warnings
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import List, Dict, Any, Optional

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.file_validator import FileValidator
from trading_bot.core.timestamp_validator import TimestampValidator


class ChartCleaner:
    """Manages cleanup of outdated chart files based on database timestamps."""
    
    def __init__(self, data_agent: Optional[DataAgent] = None, enable_backup: bool = True,
                 enable_age_based_cleaning: bool = True, max_file_age_hours: int = 24,
                 enable_cycle_based_cleaning: bool = True):
        self.data_agent = data_agent or DataAgent()
        self.logger = logging.getLogger(__name__)
        self.timestamp_validator = TimestampValidator()
        self.file_validator = FileValidator(enable_backup=enable_backup)

        # Hybrid cleaning configuration
        self.enable_age_based_cleaning = enable_age_based_cleaning
        self.max_file_age_hours = max_file_age_hours
        self.enable_cycle_based_cleaning = enable_cycle_based_cleaning
        
    def parse_timeframe_delta(self, timeframe: str) -> timedelta:
        """
        Convert timeframe string to timedelta.
        
        DEPRECATED: This method is deprecated and will be removed in a future version.
        Use TimestampValidator.timeframe_to_timedelta() instead.
        """
        warnings.warn(
            "parse_timeframe_delta is deprecated. Use TimestampValidator.timeframe_to_timedelta() instead.",
            DeprecationWarning,
            stacklevel=2
        )
        
        try:
            # Use the centralized TimestampValidator for consistency
            return self.timestamp_validator.timeframe_to_timedelta(timeframe)
        except Exception as e:
            self.logger.warning(f"Failed to parse timeframe '{timeframe}' with TimestampValidator: {e}")
            # Fallback to original logic for backward compatibility
            timeframe = str(timeframe).lower().strip()
            
            # Handle common formats
            if timeframe.endswith('m'):
                return timedelta(minutes=int(timeframe[:-1]))
            elif timeframe.endswith('min'):
                return timedelta(minutes=int(timeframe[:-3]))
            elif timeframe.endswith('h'):
                return timedelta(hours=int(timeframe[:-1]))
            elif timeframe.endswith('hr'):
                return timedelta(hours=int(timeframe[:-2]))
            elif timeframe.endswith('hour'):
                return timedelta(hours=int(timeframe[:-4]))
            elif timeframe.endswith('d'):
                return timedelta(days=int(timeframe[:-1]))
            elif timeframe.endswith('day'):
                return timedelta(days=int(timeframe[:-3]))
            
            # Default fallback
            return timedelta(hours=1)
    
    def get_file_timestamp(self, file_path: str) -> Optional[datetime]:
        """Get the file modification time as UTC datetime."""
        try:
            mtime = os.path.getmtime(file_path)
            return datetime.fromtimestamp(mtime, tz=timezone.utc)
        except (OSError, ValueError) as e:
            self.logger.error(f"Error getting timestamp for {file_path}: {e}")
            return None
    
    def get_database_timestamp(self, image_path: str) -> Optional[datetime]:
        """Get the timestamp from database for a given image path."""
        conn = self.data_agent.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT timestamp FROM analysis_results
                WHERE image_path = ?
                ORDER BY timestamp DESC LIMIT 1
            ''', (image_path,))
            
            row = cursor.fetchone()
            if row and row[0]:
                timestamp_str = row[0]
                try:
                    # Use centralized timestamp parsing from TimestampValidator
                    return self.timestamp_validator.parse_timestamp(timestamp_str)
                except Exception as e:
                    self.logger.warning(f"Failed to parse timestamp '{timestamp_str}' with TimestampValidator: {e}")
                    # Fallback to original logic for backward compatibility
                    try:
                        if isinstance(timestamp_str, str):
                            # Try common formats
                            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d']:
                                try:
                                    dt = datetime.strptime(timestamp_str, fmt)
                                    return dt.replace(tzinfo=timezone.utc)
                                except ValueError:
                                    continue
                        elif isinstance(timestamp_str, datetime):
                            return timestamp_str.replace(tzinfo=timezone.utc)
                    except Exception as fallback_e:
                        self.logger.error(f"Error parsing timestamp {timestamp_str}: {fallback_e}")
            return None
        except sqlite3.Error as e:
            self.logger.error(f"Database error: {e}")
            return None
        finally:
            conn.close()

    def file_exists_in_database(self, image_path: str) -> bool:
        """Check if a file exists in the database (has any analysis record)."""
        conn = self.data_agent.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT COUNT(*) FROM analysis_results
                WHERE image_path = ?
            ''', (image_path,))
            row = cursor.fetchone()
            return row[0] > 0 if row else False
        except sqlite3.Error as e:
            self.logger.error(f"Database error checking file existence: {e}")
            return False
        finally:
            conn.close()

    def get_timeframe_from_database(self, image_path: str) -> Optional[str]:
        """Get the timeframe from database for a given image path."""
        conn = self.data_agent.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT timeframe FROM analysis_results
                WHERE image_path = ?
                ORDER BY timestamp DESC LIMIT 1
            ''', (image_path,))
            row = cursor.fetchone()
            if row and row[0]:
                return row[0]
            return None
        except sqlite3.Error as e:
            self.logger.error(f"Database error: {e}")
            return None
        finally:
            conn.close()
    
    def is_file_outdated(self, file_path: str, current_time: datetime) -> bool:
        """Check if a file is outdated based on hybrid logic (cycle-aware + age-based)."""

        # First, validate file integrity
        file_validation = self.file_validator.validate_file_integrity(file_path)
        if not file_validation["is_valid"]:
            self.logger.warning(f"File integrity check failed for {file_path}: {file_validation['errors']}")
            return True  # Consider corrupted files as outdated

        # Extract filename for processing
        filename = Path(file_path).name

        # Import utility functions
        from trading_bot.core.utils import extract_timestamp_from_filename, extract_timeframe_from_filename

        # Extract timeframe from filename first (file-basis only)
        timeframe = extract_timeframe_from_filename(filename)

        # For legacy files without timeframe in filename, try to infer from database
        if not timeframe:
            db_status = self.file_validator.get_file_database_status(file_path)
            timeframe = db_status.get("timeframe") or self.get_timeframe_from_database(file_path)
            if not timeframe:
                timeframe = self.file_validator.infer_timeframe_from_database(file_path)

        # Skip files with missing timeframes
        if not timeframe:
            self.logger.warning(f"Skipping file {file_path}: Cannot determine timeframe from filename or database")
            return False

        # Extract timestamp from filename (file-basis only)
        timestamp_str = extract_timestamp_from_filename(filename)
        if not timestamp_str:
            self.logger.warning(f"Skipping file {file_path}: Cannot extract timestamp from filename")
            return False

        try:
            # Parse the timestamp using TimestampValidator
            file_timestamp = self.timestamp_validator.parse_timestamp(timestamp_str)

        except Exception as e:
            self.logger.warning(f"Failed to parse timestamp from filename {filename}: {e}")
            return False

        # Check age-based criteria first (simpler and more reliable)
        age_based_outdated = False
        if self.enable_age_based_cleaning:
            try:
                age_hours = (current_time - file_timestamp).total_seconds() / 3600
                age_based_outdated = age_hours > self.max_file_age_hours

                if age_based_outdated:
                    self.logger.info(f"⏰ File outdated by AGE: {os.path.basename(file_path)} "
                                   f"({age_hours:.1f}h > {self.max_file_age_hours}h threshold)")
                    return True
                else:
                    self.logger.debug(f"📅 File within age limit: {os.path.basename(file_path)} "
                                    f"({age_hours:.1f}h ≤ {self.max_file_age_hours}h)")
            except Exception as e:
                self.logger.warning(f"Failed to calculate age for {file_path}: {e}")

        # Check cycle-based criteria if enabled and file is not already outdated by age
        cycle_based_outdated = False
        if self.enable_cycle_based_cleaning:
            try:
                # Calculate current cycle boundaries using TimestampValidator
                current_cycle_boundary = self.timestamp_validator.calculate_next_boundary(current_time, timeframe)
                current_cycle_start = current_cycle_boundary - self.timestamp_validator.timeframe_to_timedelta(timeframe)

                # Calculate which cycle the file belongs to
                file_cycle_boundary = self.timestamp_validator.calculate_next_boundary(file_timestamp, timeframe)
                file_cycle_start = file_cycle_boundary - self.timestamp_validator.timeframe_to_timedelta(timeframe)

                # File is outdated if it's not from the current cycle
                cycle_based_outdated = file_cycle_start != current_cycle_start

                if cycle_based_outdated:
                    self.logger.info(f"🔄 File outdated by CYCLE: {os.path.basename(file_path)} "
                                   f"(file cycle: {file_cycle_start}, current cycle: {current_cycle_start})")
                else:
                    self.logger.debug(f"🔄 File within current cycle: {os.path.basename(file_path)} "
                                    f"(cycle: {file_cycle_start})")

            except Exception as e:
                self.logger.warning(f"Failed to calculate cycle boundaries for {file_path}: {e}")
                # COMMENTED OUT: Database fallback for fast mode
                # try:
                #     db_timestamp = self.get_database_timestamp(file_path)
                #     if db_timestamp:
                #         timeframe_delta = self.parse_timeframe_delta(timeframe)
                #         cutoff_time = current_time - timeframe_delta
                #         cycle_based_outdated = db_timestamp < cutoff_time
                #
                #         if cycle_based_outdated:
                #             self.logger.debug(f"File {file_path} is outdated by cycle (fallback): timeframe={timeframe}, cutoff={cutoff_time}")
                #
                # except Exception as fallback_e:
                #     self.logger.error(f"Fallback validation also failed: {fallback_e}")

        # File is outdated if either age-based OR cycle-based criteria are met
        is_outdated = age_based_outdated or cycle_based_outdated

        if is_outdated:
            reason = []
            if age_based_outdated:
                reason.append("age")
            if cycle_based_outdated:
                reason.append("cycle")

            reason_str = ', '.join(reason)
            self.logger.info(f"🎯 File marked as outdated: {os.path.basename(file_path)} "
                           f"(reasons: {reason_str})")

        return is_outdated

    def is_file_outdated_fast(self, file_path: str, current_time: datetime, file_timestamp: datetime, timeframe: str) -> bool:
        """Fast version of is_file_outdated that works with filename data only (no database operations)."""

        # Check age-based criteria first (simpler and more reliable)
        age_based_outdated = False
        if self.enable_age_based_cleaning:
            age_hours = (current_time - file_timestamp).total_seconds() / 3600
            age_based_outdated = age_hours > self.max_file_age_hours

            if age_based_outdated:
                self.logger.info(f"⏰ File outdated by AGE (FAST): {os.path.basename(file_path)} "
                               f"({age_hours:.1f}h > {self.max_file_age_hours}h threshold)")
                return True
            else:
                self.logger.debug(f"📅 File within age limit: {os.path.basename(file_path)} "
                                f"({age_hours:.1f}h ≤ {self.max_file_age_hours}h)")

        # Check cycle-based criteria if enabled and file is not already outdated by age
        cycle_based_outdated = False
        if self.enable_cycle_based_cleaning:
            try:
                # Calculate current cycle boundaries using TimestampValidator
                current_cycle_boundary = self.timestamp_validator.calculate_next_boundary(current_time, timeframe)
                current_cycle_start = current_cycle_boundary - self.timestamp_validator.timeframe_to_timedelta(timeframe)

                # Calculate which cycle the file belongs to
                file_cycle_boundary = self.timestamp_validator.calculate_next_boundary(file_timestamp, timeframe)
                file_cycle_start = file_cycle_boundary - self.timestamp_validator.timeframe_to_timedelta(timeframe)

                # File is outdated if it's not from the current cycle
                cycle_based_outdated = file_cycle_start != current_cycle_start

                if cycle_based_outdated:
                    self.logger.info(f"🔄 File outdated by CYCLE (FAST): {os.path.basename(file_path)} "
                                   f"(file cycle: {file_cycle_start}, current cycle: {current_cycle_start})")
                else:
                    self.logger.debug(f"🔄 File within current cycle: {os.path.basename(file_path)} "
                                    f"(cycle: {file_cycle_start})")

            except Exception as e:
                self.logger.warning(f"Failed to calculate cycle boundaries for {file_path}: {e}")

        # File is outdated if either age-based OR cycle-based criteria are met
        is_outdated = age_based_outdated or cycle_based_outdated

        if is_outdated:
            reason = []
            if age_based_outdated:
                reason.append("age")
            if cycle_based_outdated:
                reason.append("cycle")

            reason_str = ', '.join(reason)
            self.logger.info(f"🎯 File marked as outdated (FAST): {os.path.basename(file_path)} "
                           f"(reasons: {reason_str})")

        return is_outdated
    
    def extract_symbol_from_filename(self, filename: str) -> str:
        """Extract symbol from filename."""
        from trading_bot.core.utils import extract_symbol_from_filename
        return extract_symbol_from_filename(filename)
    
    def extract_timeframe_from_filename(self, filename: str) -> Optional[str]:
        """Extract timeframe from filename if available."""
        try:
            # Remove file extension
            name_without_ext = filename.rsplit('.', 1)[0]

            # Split by underscores and extract timeframe (second part)
            parts = name_without_ext.split('_')
            if len(parts) >= 2:
                timeframe = parts[1]  # Second part is the timeframe
                return timeframe
            else:
                self.logger.warning(f"Filename {filename} does not follow expected format: symbol_timeframe_date_time")
                return None
        except Exception as e:
            self.logger.error(f"Error extracting timeframe from filename {filename}: {e}")
            return None
    
    def scan_files(self, folder_path: str) -> List[Dict[str, Any]]:
        """Scan folder for chart files and check their status using filename parsing only (fast mode)."""
        self.logger.info(f"🔍 Scanning folder for chart files: {folder_path}")
        folder = Path(folder_path)
        current_time = datetime.now(timezone.utc)

        # Find all image files
        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp'}
        # Also include files with ':Zone.Identifier' in their name
        image_files = [f for f in folder.iterdir() if f.suffix.lower() in image_extensions or ':Zone.Identifier' in f.name]

        self.logger.info(f"📂 Found {len(image_files)} image files to analyze")

        results = []
        for file_path in image_files:
            file_str = str(file_path)
            filename = os.path.basename(file_str)

            # FAST MODE: Parse filename directly without database operations
            try:
                # Extract symbol, timeframe, and timestamp from filename
                symbol = self.extract_symbol_from_filename(filename)
                timeframe = self.extract_timeframe_from_filename(filename)

                if not timeframe:
                    self.logger.debug(f"Skipping file {filename}: Cannot determine timeframe from filename")
                    continue

                # Extract timestamp from filename
                from trading_bot.core.utils import extract_timestamp_from_filename
                timestamp_str = extract_timestamp_from_filename(filename)

                if not timestamp_str:
                    self.logger.debug(f"Skipping file {filename}: Cannot extract timestamp from filename")
                    continue

                # Parse timestamp
                try:
                    file_timestamp = self.timestamp_validator.parse_timestamp(timestamp_str)
                except Exception as e:
                    self.logger.debug(f"Skipping file {filename}: Cannot parse timestamp '{timestamp_str}': {e}")
                    continue

                # Calculate age in minutes
                age_minutes = int((current_time - file_timestamp).total_seconds() / 60)

                # Check if file is outdated using filename data only
                is_outdated = self.is_file_outdated_fast(file_str, current_time, file_timestamp, timeframe)

                results.append({
                    'file_path': file_str,
                    'symbol': symbol,
                    'is_outdated': is_outdated,
                    'file_timestamp': file_timestamp,
                    'age_minutes': age_minutes,
                    'timeframe': timeframe,
                    'filename_pattern': "parsed_from_filename",
                    'file_integrity': True,  # Assume valid for fast mode
                    'db_record_count': 0  # Not checked in fast mode
                })

            except Exception as e:
                self.logger.warning(f"Error processing file {filename}: {e}")
                continue

        self.logger.info(f"✅ Fast scan completed: {len(results)} files processed")
        return results
    
    def clean_outdated_files(self, folder_path: str, dry_run: bool = True) -> List[str]:
        """Clean outdated files from the specified folder using FAST filename-only processing."""
        self.logger.info(f"🧹 Starting FAST chart cleanup process for folder: {folder_path}")
        self.logger.info(f"   Configuration: Age-based={self.enable_age_based_cleaning}, "
                        f"Cycle-based={self.enable_cycle_based_cleaning}, "
                        f"Age threshold={self.max_file_age_hours}h, "
                        f"Dry run={dry_run}")

        # FAST SCAN: Use filename parsing only (no database operations)
        scan_results = self.scan_files(folder_path)
        deleted_files = []
        skipped_files = []

        total_files = len(scan_results)
        outdated_files = [r for r in scan_results if r['is_outdated']]
        current_files = [r for r in scan_results if not r['is_outdated']]

        self.logger.info(f"📊 FAST Cleanup scan results:")
        self.logger.info(f"   Total files scanned: {total_files}")
        self.logger.info(f"   Outdated files: {len(outdated_files)}")
        self.logger.info(f"   Current files: {len(current_files)}")

        if len(outdated_files) == 0:
            self.logger.info(f"✅ No files require cleanup at this time")
            return []

        # BATCH PROCESSING: Process files in batches for better performance
        batch_size = 10
        self.logger.info(f"🗑️  Processing {len(outdated_files)} outdated files in batches of {batch_size}:")

        for i in range(0, len(outdated_files), batch_size):
            batch = outdated_files[i:i + batch_size]
            self.logger.info(f"📦 Processing batch {i//batch_size + 1}/{(len(outdated_files) + batch_size - 1)//batch_size}")

            for result in batch:
                file_path = result['file_path']
                filename = os.path.basename(file_path)

                try:
                    if dry_run:
                        self.logger.info(f"🔍 DRY RUN: Would move to backup: {filename}")
                        deleted_files.append(file_path)
                    else:
                        # FAST FILE OPERATION: Direct move without heavy validation
                        self.logger.info(f"📦 Moving to backup: {filename}")

                        # Create backup directory if it doesn't exist
                        backup_dir = os.path.join(folder_path, '.backup')
                        os.makedirs(backup_dir, exist_ok=True)

                        # Move file directly
                        backup_path = os.path.join(backup_dir, filename)
                        os.rename(file_path, backup_path)

                        self.logger.info(f"✅ Successfully moved: {filename} → .backup/{filename}")
                        deleted_files.append(file_path)

                except Exception as e:
                    self.logger.error(f"💥 Error processing {filename}: {e}")
                    skipped_files.append(file_path)

        # Final summary
        self.logger.info(f"📋 FAST Cleanup Summary:")
        self.logger.info(f"   Files processed: {len(deleted_files) + len(skipped_files)}")
        self.logger.info(f"   Successfully moved: {len(deleted_files)}")
        self.logger.info(f"   Skipped (errors): {len(skipped_files)}")

        if len(deleted_files) > 0:
            self.logger.info(f"✅ FAST cleanup completed successfully - {len(deleted_files)} files moved to backup")
        else:
            self.logger.info(f"ℹ️  No files were moved during cleanup")

        return deleted_files
    
    def get_cleaning_summary(self, folder_path: str) -> Dict[str, Any]:
        """Get a summary of files that would be cleaned."""
        self.logger.info(f"📋 Generating cleaning summary for: {folder_path}")
        scan_results = self.scan_files(folder_path)

        total_files = len(scan_results)
        outdated_files = [r for r in scan_results if r['is_outdated']]
        current_files = [r for r in scan_results if not r['is_outdated']]

        self.logger.info(f"📊 Summary results: {total_files} total, {len(outdated_files)} outdated, {len(current_files)} current")

        if len(outdated_files) > 0:
            self.logger.info(f"🗑️  Outdated files found: {len(outdated_files)}")
            for i, file_result in enumerate(outdated_files[:5]):  # Log first 5
                filename = os.path.basename(file_result['file_path'])
                age_hours = file_result.get('age_minutes', 0) / 60
                self.logger.info(f"   {i+1}. {filename} ({age_hours:.1f}h old)")

            if len(outdated_files) > 5:
                self.logger.info(f"   ... and {len(outdated_files) - 5} more")
        else:
            self.logger.info(f"✅ No outdated files found - all files are current")

        return {
            'total_files': total_files,
            'outdated_files': len(outdated_files),
            'current_files': len(current_files),
            'outdated_file_list': [r['file_path'] for r in outdated_files],
            'current_file_list': [r['file_path'] for r in current_files],
            'details': scan_results
        }
