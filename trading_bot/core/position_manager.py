"""Position management module for tracking trades and positions."""
import logging
import asyncio # Import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.utils import smart_format_price
from trading_bot.core.position_display import PositionDisplay
from trading_bot.core.position_analytics import PositionAnalytics
from trading_bot.core.realtime_trade_tracker import RealTimeTradeTracker
from trading_bot.core.realtime_trade_monitor import RealTimeTradeMonitor


class PositionManager:
    """Manages trade recording and position updates using exchange synchronization."""
    
    def __init__(self, data_agent: DataAgent, trader):
        self.data_agent = data_agent
        self.trader = trader
        self.logger = logging.getLogger(__name__)

        # Initialize helper components
        self.display_manager = PositionDisplay(data_agent)
        self.analytics_manager = PositionAnalytics(data_agent)

        # Initialize real-time trade tracker
        self.trade_tracker = RealTimeTradeTracker(data_agent)

        # Initialize real-time trade monitor
        if hasattr(trader, 'api_manager'):
            self.trade_monitor = RealTimeTradeMonitor(data_agent, trader.api_manager, self.trade_tracker)
        else:
            self.trade_monitor = None
            self.logger.warning("⚠️ Cannot initialize trade monitor - no API manager available")

        # Set trade tracker and monitor in trader for immediate access
        if hasattr(trader, 'trade_tracker'):
            trader.trade_tracker = self.trade_tracker
            trader.trade_monitor = self.trade_monitor
            self.logger.info("✅ Real-time trade tracker and monitor initialized and linked to trader")
    
    def get_exchange_sync_manager(self):
        """Get exchange sync manager from trader."""
        try:
            return self.trader.get_exchange_sync_manager()
        except Exception as e:
            self.logger.error(f"Failed to get exchange sync manager: {e}")
            return None
    
    def handle_trade_replacement(self, old_trade_id: str, new_trade_data: Dict[str, Any]) -> bool:
        """
        Handle trade replacement by updating the database records.
        
        Args:
            old_trade_id: ID of the trade being replaced
            new_trade_data: Data for the new trade
            
        Returns:
            True if replacement was successful, False otherwise
        """
        try:
            # Mark the old trade as cancelled for replacement
            old_trade_update = {
                'status': 'cancelled',
                'state': 'cancelled_for_replacement',
                'updated_at': datetime.now(timezone.utc).isoformat()
            }
            
            if self.data_agent.update_trade(old_trade_id, **old_trade_update):
                self.logger.info(f"Marked old trade {old_trade_id} as cancelled for replacement")
                
                # Note: Trade recording is now handled by real-time trade tracker
                # The replacement trade will be automatically tracked when executed
                self.logger.info(f"Successfully marked old trade as cancelled for replacement {new_trade_data.get('id')}")
                return True
            else:
                self.logger.error(f"Failed to update old trade {old_trade_id} status")
                return False
                
        except Exception as e:
            self.logger.error(f"Error handling trade replacement: {e}")
            return False
    
    def get_position_summary(self) -> Dict[str, Any]:
        """Get a summary of all positions and trades."""
        try:
            return self.display_manager.get_position_summary()
        except Exception as e:
            self.logger.error(f"Error getting position summary: {e}")
            return {"error": str(e)}
    
    def display_positions(self, show_lifecycle_analysis: bool = True) -> None:
        """Display positions in a formatted table."""
        try:
            self.display_manager.display_positions(show_lifecycle_analysis)
        except Exception as e:
            self.logger.error(f"Error displaying positions: {e}")
            self.logger.error(f"❌ Error displaying positions: {e}")
    
    def ensure_trade_record(self, order_id: str, order_link_id: Optional[str] = None, recommendation_id: Optional[str] = None,
                           symbol: Optional[str] = None, exchange_data: Optional[Dict] = None,
                           alteration_details: Optional[str] = None) -> Dict[str, Any]:
        """
        Ensure a trade record exists for an order. Creates only if missing.
        
        Args:
            order_id: Exchange order ID (primary key for deduplication)
            order_link_id: Optional client-side order ID
            recommendation_id: Optional recommendation ID for grouping
            symbol: Trading symbol
            exchange_data: Order/position data from exchange
            alteration_details: Optional details about trade alterations
            
        Returns:
            Dict with trade record and creation status
        """
        try:
            # Use exchange sync manager to create trade record
            sync_manager = self.get_exchange_sync_manager()
            if sync_manager and hasattr(sync_manager, 'data_mapper'):
                # Map exchange data to trade data
                if exchange_data:
                    # Convert exchange_data dictionary to ExchangeOrderData object
                    from trading_bot.core.exchange_types import ExchangeOrderData
                    order_data_obj = ExchangeOrderData.from_dict(exchange_data)
                    
                    trade_data = sync_manager.data_mapper.map_order_to_trade(
                        order_data_obj,
                        self.data_agent.get_latest_analysis(symbol, "1h") if symbol else None
                    )

                    # Check if trade already exists before storing
                    existing_trade = self.data_agent.get_trade_by_order_id(order_id)
                    if existing_trade:
                        return {"trade": existing_trade, "created": False, "action": "found_existing"}

                    # Store the trade using validated SL/TP values from exchange_data
                    trade_id = trade_data.get('trade_id', str(hash(order_id))[:8])
                    success = self.data_agent.store_trade(
                        trade_id=trade_id,
                        recommendation_id=recommendation_id or trade_data.get('recommendation_id', ''),
                        symbol=trade_data.get('symbol', symbol or 'UNKNOWN'),
                        side=trade_data.get('side', 'Buy'),
                        quantity=trade_data.get('quantity', 0),
                        entry_price=trade_data.get('entry_price', 0),
                        take_profit=exchange_data.get('take_profit', 0),  # ✅ Use validated values from exchange_data
                        stop_loss=exchange_data.get('stop_loss', 0),      # ✅ Use validated values from exchange_data
                        order_id=order_id,
                        orderLinkId=order_link_id, # Pass orderLinkId here
                        placed_by=trade_data.get('placed_by', 'BOT'),
                        alteration_details=alteration_details
                    )

                    if success:
                        self.logger.info(f"Created new trade record: {trade_id}")
                        return {"trade": trade_data, "created": True, "action": "created_new"}
                    else:
                        error_msg = f"Failed to create trade record for order_id {order_id}"
                        self.logger.error(error_msg)
                        return {"error": error_msg}
                else:
                    # Check if trade already exists
                    existing_trade = self.data_agent.get_trade_by_order_id(order_id)
                    if existing_trade:
                        return {"trade": existing_trade, "created": False, "action": "found_existing"}
                    else:
                        error_msg = f"Cannot create trade record for order_id {order_id} without exchange_data."
                        self.logger.error(error_msg)
                        return {"error": error_msg}
            else:
                return {"error": "Exchange sync manager not available"}
                
        except Exception as e:
            error_msg = f"Error in ensure_trade_record for order_id {order_id}: {e}"
            self.logger.error(error_msg)
            return {"error": error_msg}
