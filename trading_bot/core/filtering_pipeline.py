import asyncio
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Tuple

from trading_bot.core.timestamp_validator import TimestampValidator
from trading_bot.core.utils import (
    get_file_modification_timestamp, 
    get_utc_now, 
    ensure_utc_timezone, 
    find_image_files,
    format_utc_time_for_display,
    calculate_sleep_until_boundary
)

logger = logging.getLogger(__name__)

class FilteringPipeline:
    """Smart filtering pipeline that handles mid-cycle checks, chart capture, and recommendation filtering."""
    
    def __init__(self, data_agent, sourcer, analyzer, recommender, config, timeframe, trader=None):
        self.data_agent = data_agent
        self.sourcer = sourcer
        self.analyzer = analyzer
        self.recommender = recommender
        self.config = config
        self.timeframe = timeframe
        self.trader = trader  # Add trader for position data validation
        self.timestamp_validator = TimestampValidator()
        self.stats = {}
        
    async def execute(self) -> Dict:
        """Execute the complete smart filtering pipeline."""
        try:            
            # Phase 1: Mid-cycle check and data freshness
            mid_cycle_result = await self._execute_mid_cycle_check()
            
            if mid_cycle_result.get("status") == "wait_for_boundary":
                return mid_cycle_result
            
            if mid_cycle_result.get("needs_charts"):
                # Capture new charts and analyze
                chart_result = await self._capture_and_analyze_charts()
                if not chart_result.get("success"):
                    return {"status": "error", "error": f"Chart capture failed: {chart_result.get('error')}"}
            
            # Phase 2: Apply filtering pipeline
            return await self._execute_filtering_pipeline()
            
        except Exception as e:
            logger.error(f"Error in filtering pipeline: {e}")
            return {"status": "error", "error": str(e)}
    
    async def _execute_mid_cycle_check(self) -> Dict:
        """Execute mid-cycle check with detailed output."""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Calculate mid-cycle status
            is_mid_cycle, cycle_progress = self._calculate_mid_cycle_status(current_time)
            
            # Check for fresh recommendations
            fresh_recs = self._check_fresh_recommendations()
            
            # Check for cycle charts
            have_cycle_charts, chart_count = self._check_cycle_charts(current_time)
            
            # Print mid-cycle check results
            print(f"✅ Mid-cycle check:")
            print(f"-> Is_mid_cycle={is_mid_cycle}")
            print(f"-> Cycle_progress={cycle_progress:.2f}%")
            print(f"-> Fresh_recs={fresh_recs}")
            print(f"-> Have_cycle_charts={have_cycle_charts} ({chart_count})")
            
            # Determine action needed
            if not is_mid_cycle:
                # Not mid-cycle - calculate next boundary and wait
                next_boundary = self.timestamp_validator.calculate_next_boundary(current_time, self.timeframe, add_random_delay=False)
                return {
                    "status": "wait_for_boundary",
                    "next_boundary": next_boundary,
                    "message": "Not mid-cycle yet - waiting for next boundary"
                }
            elif is_mid_cycle and not fresh_recs:
                # Mid-cycle with no fresh recommendations - need charts
                print(f"🚨 Mid-cycle with stale/no recommendations - capturing charts immediately")
                return {"needs_charts": True, "reason": "stale_recommendations"}
            elif is_mid_cycle and fresh_recs:
                # Mid-cycle with fresh recommendations - proceed
                print(f"✅ Mid-cycle with fresh recommendations - proceeding to process existing data")
                return {"needs_charts": False, "reason": "fresh_data_available"}
            else:
                # Default case - proceed with existing data
                print(f"✅ Proceeding with existing data")
                return {"needs_charts": False, "reason": "sufficient_data"}
                
        except Exception as e:
            logger.error(f"Error in mid-cycle check: {e}")
            return {"status": "error", "error": str(e)}
    
    def _calculate_mid_cycle_status(self, current_time: datetime) -> Tuple[bool, float]:
        """Calculate if we're mid-cycle and progress percentage."""
        try:
            timeframe_info = self.timestamp_validator.normalize_timeframe(self.timeframe)
            current_boundary = self.timestamp_validator.calculate_next_boundary(current_time, self.timeframe) - timeframe_info.timedelta
            next_boundary = self.timestamp_validator.calculate_next_boundary(current_time, self.timeframe)
            
            # Check if current time is not exactly at boundary (mid-cycle)
            is_mid_cycle = current_time > current_boundary
            
            # Calculate cycle progress percentage
            if next_boundary > current_boundary:
                cycle_duration = (next_boundary - current_boundary).total_seconds()
                time_elapsed = (current_time - current_boundary).total_seconds()
                cycle_progress = (time_elapsed / cycle_duration) * 100 if cycle_duration > 0 else 0
            else:
                cycle_progress = 0
                
            return is_mid_cycle, cycle_progress
            
        except Exception as e:
            logger.error(f"Error calculating mid-cycle status: {e}")
            return False, 0
    
    def _check_fresh_recommendations(self) -> bool:
        """Check if we have fresh recommendations for current timeframe."""
        try:
            # Use the existing recommender method to check for fresh recommendations
            fresh_recs = self.recommender.get_fresh_recommendations_for_boundary("all", self.timeframe)
            return len(fresh_recs) > 0
        except Exception as e:
            logger.error(f"Error checking fresh recommendations: {e}")
            return False
    
    def _check_cycle_charts(self, current_time: datetime) -> Tuple[bool, int]:
        """Check if chart files exist for current boundary timestamp."""
        try:
            charts_dir = Path(self.config.paths.charts)
            summary_file = charts_dir / "capture_summary.txt"
            
            if not summary_file.exists():
                return False, 0
            
            mod_time = get_file_modification_timestamp(str(summary_file))
            mod_time_utc = ensure_utc_timezone(mod_time)
            
            # Calculate current boundary time
            timeframe_info = self.timestamp_validator.normalize_timeframe(self.timeframe)
            current_boundary = self.timestamp_validator.calculate_next_boundary(current_time, self.timeframe) - timeframe_info.timedelta
            
            # Check if summary file is recent enough
            have_cycle_charts = mod_time_utc >= current_boundary
            
            # Count actual image files
            chart_files = find_image_files(charts_dir)
            chart_count = len(chart_files)
            
            return have_cycle_charts, chart_count
            
        except Exception as e:
            logger.error(f"Error checking cycle charts: {e}")
            return False, 0
    
    async def _capture_and_analyze_charts(self) -> Dict:
        """Capture new charts and analyze them."""
        try:
            print(f"🔄 Capturing fresh charts for {self.timeframe}...")
            
            # Capture charts
            chart_result = await self.sourcer.get_charts(timeframe_check=self.timeframe, force_capture=True)
            
            if chart_result.get("status") != "success":
                return chart_result
            
            # Analyze captured charts
            print(f"🔄 Analyzing captured charts...")
            analysis_results = self.analyzer.analyze_all_images(self.config.paths.charts, target_timeframe=self.timeframe)
            
            self.stats["charts_captured"] = chart_result.get("captured_count", 0)
            self.stats["analysis_completed"] = len([r for r in analysis_results if not r.get("error")])
            
            print(f"✅ Chart capture and analysis completed")
            return {"success": True}
            
        except Exception as e:
            logger.error(f"Error capturing and analyzing charts: {e}")
            return {"success": False, "error": str(e)}
    
    async def _execute_filtering_pipeline(self) -> Dict:
        """Execute the complete filtering pipeline with single-pass filtering."""
        try:
            # Get all raw analysis results
            all_analysis = self.data_agent.get_all_latest_analysis()
            self.stats["raw_count"] = len(all_analysis) if all_analysis else 0
            print(f"📊 Raw analysis results: {self.stats['raw_count']}")
            
            if not all_analysis:
                return {
                    "status": "success",
                    "recommendations": [],
                    "stats": self.stats
                }
            
            # Filter 1: Current Boundary Filtering
            boundary_filtered = self._filter_by_current_boundary(all_analysis)
            self.stats["boundary_count"] = len(boundary_filtered)
            self.stats["boundary_filtered"] = self.stats["raw_count"] - self.stats["boundary_count"]
            print(f"✅ Current boundary: {self.stats['boundary_count']} ({self.stats['boundary_filtered']} outside boundary)")
            
            if not boundary_filtered:
                return {
                    "status": "success",
                    "recommendations": [],
                    "stats": self.stats
                }
            
            # Filter 2: Timestamp Validation
            timestamp_valid = self._filter_by_timestamp(boundary_filtered)
            self.stats["timestamp_count"] = len(timestamp_valid)
            self.stats["timestamp_filtered"] = self.stats["boundary_count"] - self.stats["timestamp_count"]
            print(f"✅ Timestamp valid: {self.stats['timestamp_count']} ({self.stats['timestamp_filtered']} expired)")
            
            # Filter 3: Recommendation Type Filtering
            actionable_recs = self._filter_by_recommendation_type(timestamp_valid)
            self.stats["actionable_count"] = len(actionable_recs)
            self.stats["hold_filtered"] = self.stats["timestamp_count"] - self.stats["actionable_count"]
            print(f"✅ Actionable type: {self.stats['actionable_count']} ({self.stats['hold_filtered']} HOLD filtered)")
            
            # Filter 4: Risk Parameter Validation
            risk_validated = self._filter_by_risk_parameters(actionable_recs)
            self.stats["risk_count"] = len(risk_validated)
            self.stats["risk_filtered"] = self.stats["actionable_count"] - self.stats["risk_count"]
            
            # Get detailed risk filtering stats
            risk_stats = self._get_risk_filtering_stats(actionable_recs)
            self.stats["confidence_filtered"] = risk_stats.get("confidence_filtered", 0)
            self.stats["rr_filtered"] = risk_stats.get("rr_filtered", 0)
            
            risk_filter_details = []
            if self.stats["confidence_filtered"] > 0:
                risk_filter_details.append(f"{self.stats['confidence_filtered']} low confidence")
            if self.stats["rr_filtered"] > 0:
                risk_filter_details.append(f"{self.stats['rr_filtered']} low R:R")
            
            risk_details_str = f" ({', '.join(risk_filter_details)})" if risk_filter_details else ""
            print(f"✅ Risk validation: {self.stats['risk_count']}{risk_details_str}")
            
            # Filter 5: Position Conflict Check
            final_recommendations = self._filter_by_position_conflicts(risk_validated)
            self.stats["position_filtered"] = self.stats["risk_count"] - len(final_recommendations)
            print(f"✅ No position conflicts: {len(final_recommendations)} ({self.stats['position_filtered']} existing positions)")
            
            print(f"\n🎯 Final result: {len(final_recommendations)} recommendations ready for trading")
            
            return {
                "status": "success",
                "recommendations": final_recommendations,
                "stats": self.stats
            }
            
        except Exception as e:
            logger.error(f"Error in filtering pipeline: {e}")
            return {"status": "error", "error": str(e)}
    
    def _filter_by_current_boundary(self, recommendations: List[Dict]) -> List[Dict]:
        """Filter recommendations to only include those in current timeframe boundary."""
        try:
            return [
                rec for rec in recommendations 
                if rec.get('timeframe') == self.timeframe
            ]
        except Exception as e:
            logger.error(f"Error filtering by current boundary: {e}")
            return recommendations
    
    def _filter_by_timestamp(self, recommendations: List[Dict]) -> List[Dict]:
        """Filter recommendations by timestamp validity."""
        try:
            valid_recs = []
            for rec in recommendations:
                if self.recommender.is_recommendation_valid_timestamp_only(rec):
                    valid_recs.append(rec)
            return valid_recs
        except Exception as e:
            logger.error(f"Error filtering by timestamp: {e}")
            return recommendations
    
    def _filter_by_recommendation_type(self, recommendations: List[Dict]) -> List[Dict]:
        """Filter out HOLD/NEUTRAL recommendations."""
        try:
            actionable_recs = []
            for rec in recommendations:
                rec_type = rec.get("recommendation", "").upper()
                if rec_type not in ["HOLD", "NEUTRAL"]:
                    actionable_recs.append(rec)
            return actionable_recs
        except Exception as e:
            logger.error(f"Error filtering by recommendation type: {e}")
            return recommendations
    
    def _filter_by_risk_parameters(self, recommendations: List[Dict]) -> List[Dict]:
        """Filter recommendations by risk parameters."""
        try:
            valid_recs = []
            for rec in recommendations:
                risk_validation = self.recommender.validate_risk_parameters(rec)
                if risk_validation["valid"]:
                    valid_recs.append(rec)
            return valid_recs
        except Exception as e:
            logger.error(f"Error filtering by risk parameters: {e}")
            return recommendations
    
    def _get_risk_filtering_stats(self, recommendations: List[Dict]) -> Dict:
        """Get detailed statistics about risk filtering."""
        try:
            confidence_filtered = 0
            rr_filtered = 0
            
            for rec in recommendations:
                risk_validation = self.recommender.validate_risk_parameters(rec)
                if not risk_validation["valid"]:
                    error_msg = risk_validation.get("error", "")
                    if "confidence" in error_msg.lower() and "below minimum" in error_msg.lower():
                        confidence_filtered += 1
                    elif "risk-reward ratio" in error_msg.lower() and "below minimum" in error_msg.lower():
                        rr_filtered += 1
            
            return {
                "confidence_filtered": confidence_filtered,
                "rr_filtered": rr_filtered
            }
        except Exception as e:
            logger.error(f"Error getting risk filtering stats: {e}")
            return {"confidence_filtered": 0, "rr_filtered": 0}
    
    def _filter_by_position_conflicts(self, recommendations: List[Dict]) -> List[Dict]:
        """Filter out recommendations for symbols that already have open positions."""
        try:
            if not self.trader:
                logger.warning("No trader available for position conflict checking - skipping filter")
                return recommendations

            # Use unified SlotManager for consistent position checking
            if hasattr(self.trader, 'risk_manager') and self.trader.risk_manager:
                available_slots, slot_details = self.trader.risk_manager.slot_manager.get_available_order_slots()
                occupied_positions = slot_details.get('occupied_positions')

                final_recommendations = []
                position_filtered_count = 0

                for rec in recommendations:
                    symbol = rec.get('symbol')
                    if not symbol:
                        continue

                    # Check if we already have an open position for this symbol using SlotManager data
                    has_position = occupied_positions.get(symbol) or occupied_positions.get(f"{symbol}.P")

                    if not has_position:
                        final_recommendations.append(rec)
                    else:
                        position_filtered_count += 1
                        logger.debug(f"Filtered {symbol} - existing position detected")

                return final_recommendations
            else:
                logger.warning("No risk manager available for position conflict checking - skipping filter")
                return recommendations

        except Exception as e:
            logger.error(f"Error filtering by position conflicts: {e}")
            return recommendations
