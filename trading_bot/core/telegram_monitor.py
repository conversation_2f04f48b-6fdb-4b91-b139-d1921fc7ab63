"""Enhanced Telegram monitoring service for comprehensive bot status broadcasting."""

import logging
import time
from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
from typing import Optional, Dict, Any, List

try:
    import telegram
    from telegram import InlineKeyboardButton, InlineKeyboardMarkup
    from telegram.error import TelegramError
except ImportError:
    raise ImportError("python-telegram-bot is required. Install with: pip install python-telegram-bot")

from trading_bot.core.telegram_bot import TelegramBot
from trading_bot.core.secrets_manager import get_telegram_config
from trading_bot.core.utils import count_open_positions_and_orders

logger = logging.getLogger(__name__)

class NotificationLevel(Enum):
    """Notification priority levels."""
    CRITICAL = "🚨"
    IMPORTANT = "⚠️"
    INFO = "ℹ️"
    SUCCESS = "✅"
    DEBUG = "🔍"

class MonitoringCategory(Enum):
    """Categories of monitoring events."""
    CYCLE_UPDATES = "cycle"
    POSITION_MONITORING = "positions"
    SYSTEM_HEALTH = "health"
    MARKET_ALERTS = "market"
    ERROR_ALERTS = "errors"
    PERFORMANCE = "performance"

@dataclass
class MonitoringEvent:
    """Structure for monitoring events."""
    category: MonitoringCategory
    level: NotificationLevel
    title: str
    message: str
    timestamp: datetime
    data: Optional[Dict[str, Any]] = None
    interactive: bool = False
    callback_data: Optional[str] = None

class TelegramMonitor:
    """Enhanced Telegram monitoring service for bot status broadcasting."""
    
    def __init__(self, bot_token: Optional[str] = None, chat_id: Optional[str] = None):
        """Initialize the Telegram monitor."""
        try:
            self.bot_token = bot_token or get_telegram_config()[0]
            self.chat_id = chat_id or get_telegram_config()[1]
            
            if not self.bot_token:
                logger.warning("TELEGRAM_BOT_TOKEN not configured - Telegram monitoring disabled")
                self.bot = None
                self.chat_id_int = None
                return
                
            if not self.chat_id:
                logger.warning("TELEGRAM_CHAT_ID not configured - Telegram monitoring disabled")
                self.bot = None
                self.chat_id_int = None
                return
                
            try:
                self.chat_id_int = int(self.chat_id)
            except ValueError:
                logger.error("TELEGRAM_CHAT_ID must be a valid integer")
                self.bot = None
                self.chat_id_int = None
                return
            
            self.bot = TelegramBot(self.bot_token, self.chat_id)
            logger.info("Telegram monitor initialized successfully")
            
        except Exception as e:
            logger.warning(f"Telegram monitoring disabled: {e}")
            self.bot = None
            self.chat_id_int = None
        self._last_notification_time = {}
        self._notification_cache = {}
        
        # Configuration
        self.min_notification_interval = 30  # seconds
        self.enable_summary_reports = True
        self.summary_interval_minutes = 60
        
    def _should_send_notification(self, category: str, level: NotificationLevel) -> bool:
        """Check if notification should be sent based on rate limiting."""
        key = f"{category}_{level.value}"
        current_time = time.time()
        
        if key in self._last_notification_time:
            time_diff = current_time - self._last_notification_time[key]
            if time_diff < self.min_notification_interval:
                return False
        
        self._last_notification_time[key] = current_time
        return True
    
    def _format_compact_message(self, event: MonitoringEvent) -> str:
        """Format message in a compact, mobile-friendly way."""
        # Header with timestamp and category
        header = f"{event.level.value} **{event.title}**"
        
        # Main message
        message = event.message
        
        # Add data if present (formatted compactly)
        if event.data:
            data_lines = []
            for key, value in event.data.items():
                if isinstance(value, (dict, list)):
                    # For complex data, show summary
                    if isinstance(value, dict):
                        data_lines.append(f"📊 {key}: {len(value)} items")
                    elif isinstance(value, list):
                        data_lines.append(f"📊 {key}: {len(value)} items")
                else:
                    data_lines.append(f"📊 {key}: {value}")
            
            if data_lines:
                message += f"\n\n{'  '.join(data_lines[:3])}"  # Limit to 3 data items
        
        # Footer with timestamp
        timestamp_str = event.timestamp.strftime("%H:%M:%S")
        footer = f"\n\n`[{timestamp_str}]`"
        
        return f"{header}\n\n{message}{footer}"
    
    def _create_interactive_keyboard(self, event: MonitoringEvent) -> Optional[InlineKeyboardMarkup]:
        """Create interactive keyboard for actionable notifications."""
        if not event.interactive or not event.callback_data:
            return None
            
        keyboard = [
            [InlineKeyboardButton("🔄 Refresh", callback_data=f"refresh_{event.callback_data}"),
             InlineKeyboardButton("❌ Dismiss", callback_data=f"dismiss_{event.callback_data}")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    async def send_notification(self, event: MonitoringEvent, force: bool = False) -> bool:
        """Send a monitoring notification via Telegram."""
        # Check if Telegram is configured
        if not self.bot or self.chat_id_int is None:
            logger.debug("Telegram not configured - skipping notification")
            return False
            
        try:
            # Check rate limiting unless forced
            if not force and not self._should_send_notification(event.category.value, event.level):
                logger.debug(f"Skipping notification for {event.category.value} - rate limited")
                return False
            
            # Format message
            message_text = self._format_compact_message(event)
            
            # Create keyboard if interactive
            reply_markup = self._create_interactive_keyboard(event)
            
            # Send message
            await self.bot.bot.send_message(
                chat_id=self.chat_id_int,
                text=message_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
            logger.info(f"Sent {event.level.value} notification: {event.title}")
            return True
            
        except TelegramError as e:
            logger.error(f"Failed to send Telegram notification: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending notification: {e}")
            return False
    
    async def send_cycle_summary(self, cycle_data: Dict[str, Any]) -> bool:
        """Send comprehensive cycle summary report."""
        # Check if Telegram is configured
        if not self.bot or self.chat_id_int is None:
            logger.debug("Telegram not configured - skipping cycle summary")
            return False
            
        try:
            cycle_number = cycle_data.get('cycle_count', 'N/A')
            timeframe = cycle_data.get('timeframe', 'N/A')
            duration = cycle_data.get('duration', 0)
            
            # Header
            message = f"📊 **CYCLE #{cycle_number} SUMMARY** ({timeframe})\n"
            
            # Cycle metrics
            message += f"\n⏱️ **Duration:** {duration:.1f}s"
            
            # Trading results
            processed = cycle_data.get('processed_signals', 0)
            confirmed = cycle_data.get('confirmed_trades', 0)
            executed = cycle_data.get('executed_trades', 0)
            
            message += f"\n📈 **Trading Results:**"
            message += f"\n   • Processed: {processed}"
            message += f"\n   • Confirmed: {confirmed}"
            message += f"\n   • Executed: {executed}"
            
            # Position sizing
            total_risk = cycle_data.get('total_risk_allocated', 0)
            message += f"\n💰 **Risk Allocation:** {total_risk:.3f}%"
            
            # Performance metrics
            if 'performance_metrics' in cycle_data:
                metrics = cycle_data['performance_metrics']
                message += f"\n📊 **Performance:**"
                message += f"\n   • Success Rate: {metrics.get('success_rate', 0):.1f}%"
                message += f"\n   • Avg Response: {metrics.get('avg_response_time', 0):.1f}s"
            
            # Timestamp
            timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S UTC")
            message += f"\n\n`[{timestamp}]`"
            
            await self.bot.bot.send_message(
                chat_id=self.chat_id_int,
                text=message,
                parse_mode='Markdown'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send cycle summary: {e}")
            return False
    
    async def send_position_update(self, positions: List[Dict[str, Any]],
                                  total_pnl: float = 0.0, trader=None) -> bool:
        """Send enhanced position update with detailed information in mobile-friendly format."""
        # Check if Telegram is configured
        if not self.bot or self.chat_id_int is None:
            logger.debug("Telegram not configured - skipping position update")
            return False

        try:
            # Get comprehensive position data from trader
            detailed_positions = []
            total_position_value = 0.0

            if trader:
                # Get fresh position data with all details
                positions_response = trader.get_positions()
                if positions_response.get("retCode") == 0:
                    raw_positions = positions_response.get("result", {}).get("list", [])

                    for pos in raw_positions:
                        if float(pos.get('size', 0)) > 0:  # Only open positions
                            detailed_pos = await self._get_detailed_position_info(pos, trader)
                            if detailed_pos:
                                detailed_positions.append(detailed_pos)
                                total_position_value += detailed_pos.get('position_value', 0)

            # Use provided positions if detailed fetch failed
            if not detailed_positions and positions:
                detailed_positions = positions[:10]  # Limit to 10 positions
                total_position_value = sum(pos.get('position_value', 0) for pos in detailed_positions)

            # Header
            message = f"💰 **POSITION UPDATE**\n"

            # Summary
            open_positions = len(detailed_positions)
            message += f"\n📊 **Open Positions:** {open_positions}"

            # Total PnL with percentage
            if total_pnl != 0 or open_positions > 0:
                pnl_percentage = (total_pnl / total_position_value * 100) if total_position_value > 0 else 0
                pnl_emoji = "🟢" if total_pnl > 0 else "🔴" if total_pnl < 0 else "⚪"
                message += f"\n{pnl_emoji} **Total PnL:** ${total_pnl:.2f} ({pnl_percentage:+.1f}%)"

            if detailed_positions:
                message += f"\n\n{'─' * 40}"

                # Sort positions by P&L percentage (best performers first)
                sorted_positions = sorted(detailed_positions,
                                        key=lambda x: x.get('pnl_percentage', 0),
                                        reverse=True)

                for pos in sorted_positions:
                    symbol = pos.get('symbol', 'N/A')
                    side = pos.get('side', 'N/A')
                    size = pos.get('size', 0)
                    size_usd = pos.get('size_usd', 0)
                    entry_price = pos.get('entry_price', 0)
                    current_price = pos.get('current_price', 0)
                    pnl = pos.get('pnl_value', 0)
                    pnl_percentage = pos.get('pnl_percentage', 0)
                    stop_loss = pos.get('stop_loss')
                    take_profit = pos.get('take_profit')
                    rr_ratio = pos.get('rr_ratio', 'N/A')

                    # Position direction emoji
                    side_emoji = "🟢" if side.upper() == "BUY" else "🔴" if side.upper() == "SELL" else "⚪"
                    pnl_emoji = "🟢" if pnl > 0 else "🔴" if pnl < 0 else "⚪"

                    message += f"\n\n{side_emoji} **{symbol}** ({side.upper()})"
                    message += f"\n💵 Size: {size:.4f} (${size_usd:,.2f})"
                    message += f"\n📈 Entry: ${entry_price:.4f} → Current: ${current_price:.4f}"
                    message += f"\n{pnl_emoji} P&L: ${pnl:.2f} ({pnl_percentage:+.1f}%)"

                    if stop_loss and take_profit:
                        message += f"\n🛡️ SL: ${stop_loss:.4f} | TP: ${take_profit:.4f}"
                        if rr_ratio != 'N/A':
                            message += f"\n⚖️ RR: 1:{rr_ratio:.1f}"

                message += f"\n\n{'─' * 40}"

                # Performance summary
                if len(sorted_positions) > 1:
                    best_performer = sorted_positions[0]
                    worst_performer = sorted_positions[-1]

                    best_symbol = best_performer.get('symbol', 'N/A')
                    best_pnl_pct = best_performer.get('pnl_percentage', 0)
                    worst_symbol = worst_performer.get('symbol', 'N/A')
                    worst_pnl_pct = worst_performer.get('pnl_percentage', 0)

                    message += f"\n📈 Best: {best_symbol} ({best_pnl_pct:+.1f}%)"
                    message += f"\n📉 Worst: {worst_symbol} ({worst_pnl_pct:+.1f}%)"

                # Total portfolio value
                message += f"\n💼 Total Value: ${total_position_value:,.2f}"

            # Timestamp
            timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S UTC")
            message += f"\n\n`[{timestamp}]`"

            await self.bot.bot.send_message(
                chat_id=self.chat_id_int,
                text=message,
                parse_mode='Markdown'
            )

            return True

        except Exception as e:
            logger.error(f"Failed to send position update: {e}")
            return False

    async def _get_detailed_position_info(self, position: Dict[str, Any], trader) -> Optional[Dict[str, Any]]:
        """Get detailed position information including TP/SL and RR calculations."""
        try:
            symbol = position.get('symbol', '')
            side = position.get('side', '')
            size = float(position.get('size', 0))
            entry_price = float(position.get('avgPrice', 0))
            current_price = float(position.get('markPrice', 0))
            unrealized_pnl = float(position.get('unrealisedPnl', 0))

            # Get current price if mark price not available
            if current_price == 0:
                current_price = trader.get_last_close_price(symbol, "1m") or entry_price

            # Calculate position value
            position_value = size * current_price
            pnl_percentage = (unrealized_pnl / position_value * 100) if position_value > 0 else 0

            # Get TP/SL from position data or orders
            stop_loss = position.get('stopLoss')
            take_profit = position.get('takeProfit')

            if stop_loss and str(stop_loss).replace('.', '').isdigit():
                stop_loss = float(stop_loss)
            else:
                stop_loss = None

            if take_profit and str(take_profit).replace('.', '').isdigit():
                take_profit = float(take_profit)
            else:
                take_profit = None

            # Calculate current RR (same as position monitor)
            current_rr = 'N/A'
            if stop_loss and entry_price > 0:
                try:
                    # Determine direction
                    direction = "LONG" if side.upper() == "BUY" else "SHORT" if side.upper() == "SELL" else "LONG"

                    # Calculate 1R distance (risk per unit)
                    if direction == "LONG":
                        one_r_distance = abs(entry_price - stop_loss)
                        current_profit_distance = current_price - entry_price
                    else:  # SHORT
                        one_r_distance = abs(entry_price - stop_loss)
                        current_profit_distance = entry_price - current_price

                    # Calculate current profit in R units
                    if one_r_distance > 0:
                        current_rr = current_profit_distance / one_r_distance
                except:
                    pass

            # Calculate target RR ratio
            rr_ratio = 'N/A'
            if stop_loss and take_profit and entry_price > 0:
                try:
                    if side.upper() == 'BUY':
                        risk = abs(entry_price - stop_loss)
                        reward = abs(take_profit - entry_price)
                    else:  # SELL
                        risk = abs(entry_price - stop_loss)
                        reward = abs(entry_price - take_profit)

                    if risk > 0:
                        rr_ratio = reward / risk
                except:
                    pass

            return {
                'symbol': symbol,
                'side': side,
                'size': size,
                'size_usd': position_value,
                'entry_price': entry_price,
                'current_price': current_price,
                'pnl_value': unrealized_pnl,
                'pnl_percentage': pnl_percentage,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'rr_ratio': rr_ratio,
                'position_value': position_value
            }

        except Exception as e:
            logger.warning(f"Failed to get detailed position info for {position.get('symbol', 'unknown')}: {e}")
            return None
    
    async def send_error_alert(self, error_type: str, error_message: str, 
                              context: Optional[Dict[str, Any]] = None) -> bool:
        """Send critical error alert."""
        # Check if Telegram is configured
        if not self.bot or self.chat_id_int is None:
            logger.debug("Telegram not configured - skipping error alert")
            return False
            
        try:
            message = f"🚨 **CRITICAL ERROR ALERT**\n\n"
            message += f"💥 **Type:** {error_type}\n"
            message += f"📝 **Message:** {error_message}"
            
            if context:
                message += f"\n\n📋 **Context:**"
                for key, value in list(context.items())[:3]:  # Limit context items
                    message += f"\n   • {key}: {str(value)[:50]}..." if len(str(value)) > 50 else f"\n   • {key}: {value}"
            
            # Timestamp
            timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S")
            message += f"\n\n`[{timestamp}]`"
            
            await self.bot.bot.send_message(
                chat_id=self.chat_id_int,
                text=message,
                parse_mode='Markdown'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send error alert: {e}")
            return False
    
    async def send_system_health(self, health_data: Dict[str, Any]) -> bool:
        """Send system health status update."""
        # Check if Telegram is configured
        if not self.bot or self.chat_id_int is None:
            logger.debug("Telegram not configured - skipping system health")
            return False
            
        try:
            status = health_data.get('status', 'unknown')
            status_emoji = "🟢" if status == 'healthy' else "🔴"
            
            message = f"{status_emoji} **SYSTEM HEALTH CHECK**\n\n"
            message += f"📊 **Status:** {status.title()}"
            
            # Services
            if 'services' in health_data:
                message += f"\n\n🖥️ **Services:**"
                for service, service_status in health_data['services'].items():
                    service_emoji = "🟢" if service_status == 'running' else "🔴"
                    message += f"\n{service_emoji} {service.title()}: {service_status.title()}"
            
            # Performance metrics
            if 'metrics' in health_data:
                metrics = health_data['metrics']
                message += f"\n\n📈 **Performance:**"
                if 'cpu_percent' in metrics:
                    message += f"\n   • CPU: {metrics['cpu_percent']:.1f}%"
                if 'memory_percent' in metrics:
                    message += f"\n   • Memory: {metrics['memory_percent']:.1f}%"
                if 'disk_percent' in metrics:
                    message += f"\n   • Disk: {metrics['disk_percent']:.1f}%"
            
            # Timestamp
            timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S")
            message += f"\n\n`[{timestamp}]`"
            
            await self.bot.bot.send_message(
                chat_id=self.chat_id_int,
                text=message,
                parse_mode='Markdown'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send health update: {e}")
            return False
    
    async def send_startup_notification(self) -> bool:
        """Send bot startup notification."""
        # Check if Telegram is configured
        if not self.bot or self.chat_id_int is None:
            logger.debug("Telegram not configured - skipping startup notification")
            return False
            
        try:
            message = f"🚀 **AUTOTRADER BOT STARTED**\n\n"
            message += f"🤖 Enhanced monitoring system activated\n"
            message += f"📊 Real-time updates enabled\n"
            message += f"📱 Interactive controls available"
            
            # Timestamp
            timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S UTC")
            message += f"\n\n`[{timestamp}]`"
            
            await self.bot.bot.send_message(
                chat_id=self.chat_id_int,
                text=message,
                parse_mode='Markdown'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send startup notification: {e}")
            return False
    
    async def send_shutdown_notification(self, reason: str = "User request") -> bool:
        """Send bot shutdown notification."""
        # Check if Telegram is configured
        if not self.bot or self.chat_id_int is None:
            logger.debug("Telegram not configured - skipping shutdown notification")
            return False
            
        try:
            message = f"🛑 **AUTOTRADER BOT STOPPED**\n\n"
            message += f"📝 **Reason:** {reason}\n"
            message += f"📊 Monitoring session ended"
            
            # Timestamp
            timestamp = datetime.now(timezone.utc).strftime("%H:%M:%S UTC")
            message += f"\n\n`[{timestamp}]`"
            
            await self.bot.bot.send_message(
                chat_id=self.chat_id_int,
                text=message,
                parse_mode='Markdown'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send shutdown notification: {e}")
            return False

# Global instance management
_telegram_monitor: Optional[TelegramMonitor] = None

def get_telegram_monitor() -> TelegramMonitor:
    """Get the global Telegram monitor instance."""
    global _telegram_monitor
    if _telegram_monitor is None:
        _telegram_monitor = TelegramMonitor()
    return _telegram_monitor

# Convenience functions for common monitoring events
async def send_cycle_summary(cycle_data: Dict[str, Any]) -> bool:
    """Send cycle summary using global monitor instance."""
    monitor = get_telegram_monitor()
    return await monitor.send_cycle_summary(cycle_data)

async def send_position_update(positions: List[Dict[str, Any]],
                             total_pnl: float = 0.0, trader=None) -> bool:
    """Send position update using global monitor instance."""
    monitor = get_telegram_monitor()
    return await monitor.send_position_update(positions, total_pnl, trader)

async def send_error_alert(error_type: str, error_message: str, 
                          context: Optional[Dict[str, Any]] = None) -> bool:
    """Send error alert using global monitor instance."""
    monitor = get_telegram_monitor()
    return await monitor.send_error_alert(error_type, error_message, context)

async def send_system_health(health_data: Dict[str, Any]) -> bool:
    """Send system health update using global monitor instance."""
    monitor = get_telegram_monitor()
    return await monitor.send_system_health(health_data)

async def send_startup_notification() -> bool:
    """Send startup notification using global monitor instance."""
    monitor = get_telegram_monitor()
    return await monitor.send_startup_notification()

async def send_shutdown_notification(reason: str = "User request") -> bool:
    """Send shutdown notification using global monitor instance."""
    monitor = get_telegram_monitor()
    return await monitor.send_shutdown_notification(reason)
