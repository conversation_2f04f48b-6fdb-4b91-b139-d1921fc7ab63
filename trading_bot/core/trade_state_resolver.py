"""Trade state resolver for determining trade states from exchange data."""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from trading_bot.core.exchange_types import ExchangeOrderData, ExchangePositionData, ExchangeClosedPnlData


class TradeStateResolver:
    """Resolves trade states based on exchange data."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def resolve_trade_state(self, order_data: Optional[ExchangeOrderData] = None,
                          position_data: Optional[ExchangePositionData] = None,
                          closed_pnl_data: Optional[ExchangeClosedPnlData] = None) -> Dict[str, Any]:
        """
        Resolve the current state of a trade based on available exchange data.
        
        Args:
            order_data: Exchange order data (if available)
            position_data: Exchange position data (if available)
            closed_pnl_data: Exchange closed PnL data (if available)
            
        Returns:
            Dict containing resolved status and state
        """
        try:
            # Priority 1: Check closed PnL data first (highest priority)
            if closed_pnl_data:
                # Implement user's rule: if non-zero PnL, then position is closed
                if closed_pnl_data.closed_pnl != 0:
                    self.logger.info(f"Trade confirmed closed with non-zero PnL: {closed_pnl_data.closed_pnl}")
                    return {
                        'status': 'closed',
                        'state': 'position',
                        'pnl': closed_pnl_data.closed_pnl,
                        'avg_exit_price': closed_pnl_data.avg_exit_price,
                        'closed_size': closed_pnl_data.qty
                    }
                else:
                    # If PnL is zero, it might be a partial close or other scenario.
                    # For now, let lower priorities handle it if PnL is exactly zero.
                    self.logger.info(f"Closed PnL data found but PnL is zero ({closed_pnl_data.closed_pnl}). Deferring to lower priority checks.")
            
            # Priority 2: Check position data
            if position_data and position_data.size > 0:
                return {
                    'status': 'open',
                    'state': 'position',
                    'pnl': position_data.unrealised_pnl
                }
            
            # Priority 3: Check order data
            if order_data:
                status, state = self._map_order_status_to_trade_state(order_data.order_status)
                return {
                    'status': status,
                    'state': state
                }
            
            # If no exchange data is available, preserve existing status/state
            # This should ideally not be reached if sync is working correctly,
            # but acts as a safeguard against overwriting valid data with 'unknown'.
            self.logger.debug("No exchange data available, preserving existing trade status/state.")
            return {
                'status': 'unknown', # This will be overwritten by current_trade's status in detect_state_changes
                'state': 'unknown'   # This will be overwritten by current_trade's state in detect_state_changes
            }
            
        except Exception as e:
            self.logger.error(f"Error resolving trade state: {e}")
            # In case of an error during resolution, still return 'unknown' and log the error
            return {
                'status': 'unknown',
                'state': 'unknown',
                'error': str(e)
            }
    
    def detect_state_changes(self, current_trade: Dict[str, Any],
                           order_data: Optional[ExchangeOrderData] = None,
                           position_data: Optional[ExchangePositionData] = None,
                           closed_pnl_data: Optional[ExchangeClosedPnlData] = None) -> Dict[str, Any]:
        """
        Detect if there are any state changes for a trade.
        
        Args:
            current_trade: Current trade record from database
            order_data: Exchange order data
            position_data: Exchange position data
            closed_pnl_data: Exchange closed PnL data
            
        Returns:
            Dict containing change detection results
        """
        try:
            resolved_state = self.resolve_trade_state(order_data, position_data, closed_pnl_data)
            
            changes = {}
            needs_update = False
            
            # Check status change
            current_status = current_trade.get('status', 'unknown')
            resolved_status = resolved_state.get('status', 'unknown')
            if current_status != resolved_status:
                changes['status'] = {
                    'from': current_status,
                    'to': resolved_status
                }
                needs_update = True
            
            # Check state change
            current_state = current_trade.get('state', 'unknown')
            resolved_state_val = resolved_state.get('state', 'unknown')
            if current_state != resolved_state_val:
                changes['state'] = {
                    'from': current_state,
                    'to': resolved_state_val
                }
                needs_update = True
            
            # Check PnL change (significant changes only)
            current_pnl = float(current_trade.get('pnl', 0))
            resolved_pnl = float(resolved_state.get('pnl', 0))
            if abs(current_pnl - resolved_pnl) > 0.01:  # Only update if significant change
                changes['pnl'] = {
                    'from': current_pnl,
                    'to': resolved_pnl
                }
                needs_update = True
            
            # Check other fields that might need updating
            fields_to_check = ['avg_exit_price', 'closed_size']
            for field in fields_to_check:
                current_value = current_trade.get(field, 0)
                resolved_value = resolved_state.get(field, 0)
                if resolved_value and current_value != resolved_value:
                    changes[field] = {
                        'from': current_value,
                        'to': resolved_value
                    }
                    needs_update = True
            
            return {
                'has_changes': needs_update,
                'changes': changes,
                'resolved_state': resolved_state
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting state changes: {e}")
            return {
                'has_changes': False,
                'changes': {},
                'error': str(e)
            }
    
    def validate_state_transition(self, from_status: str, from_state: str,
                                to_status: str, to_state: str) -> bool:
        """
        Validate if a state transition is logically valid.
        
        Args:
            from_status: Current status
            from_state: Current state
            to_status: Target status
            to_state: Target state
            
        Returns:
            bool: True if transition is valid
        """
        # Define valid state transitions
        valid_transitions = {
            ('open', 'trade'): [('open', 'position'), ('cancelled', 'cancelled')],
            ('open', 'position'): [('closed', 'position'), ('open', 'position')],
            ('closed', 'position'): [],  # Closed trades shouldn't transition
            ('cancelled', 'cancelled'): [],  # Cancelled trades shouldn't transition
        }
        
        current_key = (from_status, from_state)
        target_key = (to_status, to_state)
        
        # Allow same state (no change)
        if current_key == target_key:
            return True
        
        # Check if transition is valid
        if current_key in valid_transitions:
            return target_key in valid_transitions[current_key] or len(valid_transitions[current_key]) == 0
        
        # Default to allowing transitions (conservative approach)
        return True
    
    def _map_order_status_to_trade_state(self, order_status: str) -> tuple[str, str]:
        """
        Map exchange order status to trade status and state.
        
        Args:
            order_status: Exchange order status
            
        Returns:
            Tuple of (status, state)
        """
        order_status = order_status.upper()
        
        if order_status in ['NEW', 'PENDING', 'PARTIALLYFILLED']:
            return ('open', 'trade')
        elif order_status == 'FILLED':
            return ('open', 'position')
        elif order_status in ['CANCELLED', 'REJECTED', 'EXPIRED']:
            return ('cancelled', 'cancelled')
        elif order_status == 'UNTRIGGERED':
            # Conditional order that hasn't been triggered yet
            return ('open', 'trade')
        else:
            self.logger.warning(f"Unknown order status: {order_status}, defaulting to open/trade")
            return ('open', 'trade')
    
    def get_trade_lifecycle_stage(self, trade_record: Dict[str, Any]) -> str:
        """
        Determine the lifecycle stage of a trade.
        
        Args:
            trade_record: Trade record from database
            
        Returns:
            str: Lifecycle stage name
        """
        status = trade_record.get('status', 'unknown').lower()
        state = trade_record.get('state', 'unknown').lower()
        
        if status == 'open' and state == 'trade':
            return 'placed'
        elif status == 'open' and state == 'position':
            return 'filled'
        elif status == 'closed' and state == 'position':
            return 'closed'
        elif status == 'cancelled' and state == 'cancelled':
            return 'cancelled'
        else:
            return 'unknown'
    
    def identify_problematic_trades(self, trades: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Identify trades with problematic status/state combinations.
        
        Args:
            trades: List of trade records
            
        Returns:
            List of problematic trades with issues
        """
        problematic_trades = []
        
        for trade in trades:
            issues = self._detect_trade_issues(trade)
            if issues:
                problematic_trades.append({
                    'trade_id': trade.get('id'),
                    'symbol': trade.get('symbol'),
                    'issues': issues
                })
        
        return problematic_trades
    
    def _detect_trade_issues(self, trade: Dict[str, Any]) -> List[str]:
        """
        Detect issues with a single trade record.
        
        Args:
            trade: Trade record
            
        Returns:
            List of issue descriptions
        """
        issues = []
        status = trade.get('status', 'unknown').lower()
        state = trade.get('state', 'unknown').lower()
        
        # Common problematic combinations
        if status == 'closed' and state == 'unknown':
            issues.append('Trade marked as closed but state is unknown')
        elif status == 'open' and state == 'unknown':
            issues.append('Trade marked as open but state is unknown')
        elif status == 'closed' and state == 'trade':
            issues.append('Trade marked as closed but still in trade state')
        elif status == 'cancelled' and state != 'cancelled':
            issues.append('Trade marked as cancelled but state is not cancelled')
        
        # Check for missing critical data
        if status == 'closed' and not trade.get('pnl'):
            issues.append('Closed trade missing PnL data')
        
        return issues
