"""Position analysis module for monitoring open positions and PnL."""
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from .utils import smart_format_price, smart_format_percentage


class PositionAnalyzer:
    """Analyzes and displays open positions with current PnL."""
    
    def __init__(self, trader):
        """Initialize with trader instance for accessing position data.
        
        Args:
            trader: TradeExecutor instance for fetching position data
        """
        self.trader = trader
        self.logger = logging.getLogger(__name__)
    
    def get_open_positions(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get all open positions with detailed information.
        
        Args:
            symbol: Optional symbol to filter positions
            
        Returns:
            Dictionary containing position data and summary
        """
        try:
            # Get positions from trader
            positions_response = self.trader.get_positions(symbol)
            
            if positions_response.get("error"):
                return {
                    "error": positions_response["error"],
                    "positions": [],
                    "summary": {}
                }
            
            # Extract positions from response
            positions_data = positions_response.get("result", {}).get("list", [])
            
            if not positions_data:
                return {
                    "positions": [],
                    "summary": {
                        "total_positions": 0,
                        "total_unrealized_pnl": 0.0,
                        "total_position_value": 0.0
                    }
                }
            
            # Get open orders to find TP/SL information
            orders_response = self.trader.get_open_orders()
            orders_data = []
            if not orders_response.get("error"):
                orders_data = orders_response.get("result", {}).get("list", [])
            
            # Process each position
            processed_positions = []
            total_unrealized_pnl = 0.0
            total_position_value = 0.0
            
            for position in positions_data:
                # Only include positions with actual size
                size = float(position.get("size", 0))
                if size == 0:
                    continue
                
                processed_position = self._process_position(position, orders_data)
                processed_positions.append(processed_position)
                
                # Add to totals
                total_unrealized_pnl += processed_position.get("unrealized_pnl_numeric", 0.0)
                total_position_value += processed_position.get("position_value_numeric", 0.0)
            
            # Create summary
            summary = {
                "total_positions": len(processed_positions),
                "total_unrealized_pnl": total_unrealized_pnl,
                "total_unrealized_pnl_formatted": f"${smart_format_price(total_unrealized_pnl)}",  # Add USD currency
                "total_position_value": total_position_value,
                "total_position_value_formatted": smart_format_price(total_position_value),
                "overall_pnl_percentage": self._calculate_percentage(total_unrealized_pnl, total_position_value)
            }
            
            return {
                "positions": processed_positions,
                "summary": summary,
                "timestamp": datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            self.logger.error(f"Error getting open positions: {str(e)}")
            return {
                "error": f"Failed to get positions: {str(e)}",
                "positions": [],
                "summary": {}
            }
    
    def _process_position(self, position: Dict[str, Any], orders_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Process a single position and calculate relevant metrics.
        
        Args:
            position: Raw position data from Bybit API
            orders_data: List of open orders to find TP/SL information
            
        Returns:
            Processed position with calculated metrics
        """
        try:
            # Extract basic position data
            symbol = position.get("symbol", "")
            side = position.get("side", "")
            size = float(position.get("size", 0))
            entry_price = float(position.get("avgPrice", 0))
            mark_price = float(position.get("markPrice", 0))
            unrealized_pnl = float(position.get("unrealisedPnl", 0))
            position_value = float(position.get("positionValue", 0))
            
            # Get last price using trader's method
            last_price = None
            try:
                last_price = self.trader.get_last_close_price(symbol, "1m")  # Use 1m for most recent price
            except Exception as e:
                self.logger.warning(f"Could not get last price for {symbol}: {str(e)}")
            
            # Find take profit and stop loss from open orders
            take_profit_price = None
            stop_loss_price = None
            
            if orders_data:
                for order in orders_data:
                    if (order.get("symbol") == symbol and
                        order.get("orderStatus") in ["New", "PartiallyFilled"]):
                        
                        order_type = order.get("orderType", "")
                        stop_order_type = order.get("stopOrderType", "")
                        
                        # Check for take profit orders
                        if (order_type == "Limit" and stop_order_type == "TakeProfit") or order.get("tpslMode") == "Partial":
                            if order.get("tpTriggerBy") or "takeprofit" in order_type.lower():
                                take_profit_price = float(order.get("price", 0)) or float(order.get("triggerPrice", 0))
                        
                        # Check for stop loss orders
                        if (order_type == "Market" and stop_order_type == "StopLoss") or order.get("tpslMode") == "Partial":
                            if order.get("slTriggerBy") or "stoploss" in order_type.lower():
                                stop_loss_price = float(order.get("triggerPrice", 0)) or float(order.get("price", 0))
            
            # Also check position data for TP/SL (some brokers include it in position data)
            if not take_profit_price and position.get("takeProfit"):
                try:
                    tp_val = float(position.get("takeProfit", 0))
                    if tp_val > 0:
                        take_profit_price = tp_val
                except (ValueError, TypeError):
                    pass
            
            if not stop_loss_price and position.get("stopLoss"):
                try:
                    sl_val = float(position.get("stopLoss", 0))
                    if sl_val > 0:
                        stop_loss_price = sl_val
                except (ValueError, TypeError):
                    pass
            
            # Calculate percentage PnL
            pnl_percentage = self._calculate_percentage(unrealized_pnl, position_value)
            
            # Calculate live current RR profit (current profit in R units)
            current_rr_profit = None
            if stop_loss_price and entry_price > 0:
                try:
                    # Determine direction from side
                    direction = "LONG" if side.upper() == "BUY" else "SHORT" if side.upper() == "SELL" else "LONG"
                    
                    # Calculate 1R distance (risk per unit)
                    if direction == "LONG":
                        one_r_distance = abs(entry_price - stop_loss_price)
                        current_profit_distance = mark_price - entry_price
                    else:  # SHORT
                        one_r_distance = abs(entry_price - stop_loss_price)
                        current_profit_distance = entry_price - mark_price
                    
                    # Calculate current profit in R units
                    if one_r_distance > 0:
                        current_rr_profit = current_profit_distance / one_r_distance
                    
                except Exception as e:
                    self.logger.warning(f"Could not calculate live RR profit for {symbol}: {str(e)}")
            
            # Calculate position status
            status = "Profit" if unrealized_pnl > 0 else "Loss" if unrealized_pnl < 0 else "Breakeven"
            
            # Calculate size in USD using mark price
            size_usd = None
            size_usd_formatted = "N/A"
            if mark_price and mark_price > 0:
                size_usd = size * mark_price
                size_usd_formatted = f"${size_usd:,.2f}"
            
            return {
                "symbol": symbol,
                "side": side,
                "size": size,
                "size_formatted": smart_format_price(size),
                "size_usd": size_usd,
                "size_usd_formatted": size_usd_formatted,
                "entry_price": entry_price,
                "entry_price_formatted": smart_format_price(entry_price),
                "mark_price": mark_price,
                "mark_price_formatted": smart_format_price(mark_price),
                "last_price": last_price,
                "last_price_formatted": smart_format_price(last_price) if last_price else "N/A",
                "unrealized_pnl": f"${smart_format_price(unrealized_pnl)}",  # Add USD currency
                "unrealized_pnl_numeric": unrealized_pnl,
                "position_value": smart_format_price(position_value),
                "position_value_numeric": position_value,
                "pnl_percentage": smart_format_percentage(pnl_percentage),
                "pnl_percentage_numeric": pnl_percentage,
                "current_rr_profit": current_rr_profit,
                "current_rr_profit_formatted": f"{current_rr_profit:.2f}R" if current_rr_profit is not None else "N/A",
                "take_profit": smart_format_price(take_profit_price) if take_profit_price else "N/A",
                "take_profit_numeric": take_profit_price,
                "stop_loss": smart_format_price(stop_loss_price) if stop_loss_price else "N/A",
                "stop_loss_numeric": stop_loss_price,
                "status": status,
                "leverage": position.get("leverage", "1"),
                "position_idx": position.get("positionIdx", 0),
                "created_time": position.get("createdTime", ""),
                "updated_time": position.get("updatedTime", "")
            }
            
        except Exception as e:
            self.logger.error(f"Error processing position: {str(e)}")
            return {
                "symbol": position.get("symbol", "UNKNOWN"),
                "error": f"Failed to process position: {str(e)}"
            }
    
    def _calculate_percentage(self, pnl: float, position_value: float) -> float:
        """Calculate percentage PnL.
        
        Args:
            pnl: Unrealized PnL amount
            position_value: Total position value
            
        Returns:
            Percentage PnL
        """
        if position_value == 0:
            return 0.0
        return (pnl / abs(position_value)) * 100
    
    def get_wallet_balance(self, coin: str = "USDT") -> Dict[str, Any]:
        """Get wallet balance information.
        
        Args:
            coin: Coin to get balance for (default: USDT)
            
        Returns:
            Wallet balance information
        """
        try:
            balance_response = self.trader.get_wallet_balance(coin)
            
            if balance_response.get("error"):
                return {
                    "error": balance_response["error"],
                    "balance": {}
                }
            
            # Extract balance data
            balance_data = balance_response.get("result", {}).get("list", [])
            
            if not balance_data:
                return {
                    "balance": {
                        "coin": coin,
                        "total_balance": "0.00",
                        "available_balance": "0.00",
                        "used_balance": "0.00"
                    }
                }
            
            # Process balance information according to Bybit API structure
            account_data = balance_data[0] if balance_data else {}
            
            # Extract and format balance values with safe conversion
            def safe_float_convert(value, default=0.0):
                """Safely convert value to float, handling empty strings and None."""
                if value is None or value == "" or value == "0":
                    return default
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return default
            
            # Get account-level balances first (these are the main balances)
            total_wallet_balance = safe_float_convert(account_data.get("totalWalletBalance", 0))
            total_available_balance = safe_float_convert(account_data.get("totalAvailableBalance", 0))
            
            # Find the specific coin data for more detailed info
            coin_data = {}
            for coin_info in account_data.get("coin", []):
                if coin_info.get("coin") == coin:
                    coin_data = coin_info
                    break
            
            # Use coin-specific data if available, otherwise use account totals
            if coin_data:
                wallet_balance = safe_float_convert(coin_data.get("walletBalance", 0))
                available_balance = safe_float_convert(coin_data.get("availableToWithdraw", 0))
                
                # If coin-specific availableToWithdraw is 0, try other fields
                if available_balance == 0:
                    available_balance = safe_float_convert(coin_data.get("equity", 0))
                if available_balance == 0:
                    available_balance = wallet_balance  # Fallback to wallet balance
            else:
                # Use account-level totals if coin-specific data not found
                wallet_balance = total_wallet_balance
                available_balance = total_available_balance
            
            # Calculate used balance
            used_balance = wallet_balance - available_balance
            
            # Ensure values make sense
            if used_balance < 0:
                used_balance = 0
            if available_balance > wallet_balance:
                available_balance = wallet_balance
                used_balance = 0
            
            return {
                "balance": {
                    "coin": coin,
                    "total_balance": smart_format_price(wallet_balance),
                    "total_balance_numeric": wallet_balance,
                    "available_balance": smart_format_price(available_balance),
                    "available_balance_numeric": available_balance,
                    "used_balance": smart_format_price(used_balance),
                    "used_balance_numeric": used_balance
                },
                "timestamp": datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            self.logger.error(f"Error getting wallet balance: {str(e)}")
            return {
                "error": f"Failed to get wallet balance: {str(e)}",
                "balance": {}
            }
    
    def get_position_summary(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get a comprehensive summary of positions and wallet.
        
        Args:
            symbol: Optional symbol to filter positions
            
        Returns:
            Complete position and wallet summary
        """
        try:
            # Get positions
            positions_data = self.get_open_positions(symbol)
            
            # Get wallet balance
            wallet_data = self.get_wallet_balance()
            
            # Combine data
            summary = {
                "positions": positions_data,
                "wallet": wallet_data,
                "timestamp": datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Add overall account summary if no errors
            if not positions_data.get("error") and not wallet_data.get("error"):
                total_pnl = positions_data.get("summary", {}).get("total_unrealized_pnl", 0.0)
                available_balance = wallet_data.get("balance", {}).get("available_balance_numeric", 0.0)
                
                summary["account_summary"] = {
                    "total_unrealized_pnl": smart_format_price(total_pnl),
                    "available_balance": smart_format_price(available_balance),
                    "net_worth_estimate": smart_format_price(available_balance + total_pnl)
                }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error getting position summary: {str(e)}")
            return {
                "error": f"Failed to get position summary: {str(e)}",
                "positions": {"positions": [], "summary": {}},
                "wallet": {"balance": {}}
            }
    
    def display_positions(self, symbol: Optional[str] = None) -> None:
        """Display positions in a formatted table.
        
        Args:
            symbol: Optional symbol to filter positions
        """
        try:
            data = self.get_position_summary(symbol)
            
            print(f"\n{'='*142}")
            print("POSITION ANALYSIS")
            print(f"{'='*142}")
            
            # Display wallet info
            wallet = data.get("wallet", {})
            if not wallet.get("error"):
                balance_info = wallet.get("balance", {})
                print(f"💰 Wallet Balance ({balance_info.get('coin', 'USDT')}):")
                print(f"   Total: {balance_info.get('total_balance', 'N/A')}")
                print(f"   Available: {balance_info.get('available_balance', 'N/A')}")
                print(f"   Used: {balance_info.get('used_balance', 'N/A')}")
            else:
                print(f"❌ Wallet Error: {wallet.get('error')}")
            
            # Display positions
            positions = data.get("positions", {})
            if not positions.get("error"):
                position_list = positions.get("positions", [])
                summary = positions.get("summary", {})
                
                print(f"\n 📊 Open Positions ({summary.get('total_positions', 0)}):")
                
                if not position_list:
                    print("   No open positions")
                else:
                    print(f"{'Symbol':<12} {'Side':<6} {'Size':<12} {'Size ($)':<12} {'Entry':<10} {'Last':<10} {'Mark':<10} {'PnL (USD)':<12} {'%':<8} {'TP':<10} {'SL':<10}")
                    print("-" * 142)
                    
                    for pos in position_list:
                        if pos.get("error"):
                            print(f"   Error: {pos.get('error')}")
                            continue
                        
                        symbol_str = pos.get("symbol", "")[:11]
                        side_str = pos.get("side", "")[:5]
                        size_str = pos.get("size_formatted", "")[:11]
                        size_usd_str = pos.get("size_usd_formatted", "N/A")[:11]
                        entry_str = pos.get("entry_price_formatted", "")[:9]
                        last_str = pos.get("last_price_formatted", "N/A")[:9]
                        mark_str = pos.get("mark_price_formatted", "")[:9]
                        pnl_str = pos.get("unrealized_pnl", "")[:11]
                        pct_str = pos.get("pnl_percentage", "")[:7]
                        tp_str = pos.get("take_profit", "N/A")[:9]
                        sl_str = pos.get("stop_loss", "N/A")[:9]
                        
                        print(f"{symbol_str:<12} {side_str:<6} {size_str:<12} {size_usd_str:<12} {entry_str:<10} {last_str:<10} {mark_str:<10} {pnl_str:<12} {pct_str:<8} {tp_str:<10} {sl_str:<10}")
                    
                    print("-" * 130)
                    print(f"Total PnL: {summary.get('total_unrealized_pnl_formatted', 'N/A')}")
                    print(f"Position Value: {summary.get('total_position_value_formatted', 'N/A')}")
            else:
                print(f"❌ Positions Error: {positions.get('error')}")
            
            # Display account summary
            account_summary = data.get("account_summary")
            if account_summary:
                print(f"\n💼 Account Summary:")
                print(f"   Net Worth Estimate: {account_summary.get('net_worth_estimate', 'N/A')}")
            
            print(f"{'='*142}")
            
        except Exception as e:
            print(f"❌ Error displaying positions: {str(e)}")
    
    def display_positions_table(self, symbol: Optional[str] = None) -> None:
        """Display only the positions table without full account summary.
        
        Args:
            symbol: Optional symbol to filter positions
        """
        try:
            data = self.get_position_summary(symbol)
            
            # Display positions only
            positions = data.get("positions", {})
            if not positions.get("error"):
                position_list = positions.get("positions", [])
                summary = positions.get("summary", {})
                
                print(f"📊 Open Positions ({summary.get('total_positions', 0)}):")
                
                if not position_list:
                    print("   No open positions")
                else:
                    print(f"{'Symbol':<12} {'Side':<6} {'Size':<12} {'Size ($)':<12} {'Entry':<10} {'Last':<10} {'Mark':<10} {'PnL (USD)':<12} {'%':<8} {'TP':<10} {'SL':<10}")
                    print("-" * 119)
                    
                    for pos in position_list:
                        if pos.get("error"):
                            print(f"   Error: {pos.get('error')}")
                            continue
                        
                        symbol_str = pos.get("symbol", "")[:11]
                        side_str = pos.get("side", "")[:5]
                        size_str = pos.get("size_formatted", "")[:11]
                        size_usd_str = pos.get("size_usd_formatted", "N/A")[:11]
                        entry_str = pos.get("entry_price_formatted", "")[:9]
                        last_str = pos.get("last_price_formatted", "N/A")[:9]
                        mark_str = pos.get("mark_price_formatted", "")[:9]
                        pnl_str = pos.get("unrealized_pnl", "")[:11]
                        pct_str = pos.get("pnl_percentage", "")[:7]
                        tp_str = pos.get("take_profit", "N/A")[:9]
                        sl_str = pos.get("stop_loss", "N/A")[:9]
                        
                        print(f"{symbol_str:<12} {side_str:<6} {size_str:<12} {size_usd_str:<12} {entry_str:<10} {last_str:<10} {mark_str:<10} {pnl_str:<12} {pct_str:<8} {tp_str:<10} {sl_str:<10}")
                    
                    print("-" * 119)
                    print(f"Total PnL: {summary.get('total_unrealized_pnl_formatted', 'N/A')}")
                    print(f"Position Value: {summary.get('total_position_value_formatted', 'N/A')}")
            else:
                print(f"❌ Positions Error: {positions.get('error')}")
                
        except Exception as e:
            print(f"❌ Error displaying positions table: {str(e)}")
