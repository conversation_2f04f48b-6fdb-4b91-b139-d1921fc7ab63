"""Exchange data mapper for converting exchange data to database format."""
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from trading_bot.core.exchange_types import ExchangeOrderData, ExchangePositionData, ExchangeClosedPnlData


class ExchangeDataMapper:
    """Maps exchange data to database trade records."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def map_order_to_trade(self, order_data: Any, 
                          recommendation_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Map exchange order data to database trade record format.
        
        Args:
            order_data: Exchange order data (can be ExchangeOrderData object or dict)
            recommendation_data: Optional recommendation data for TP/SL
            
        Returns:
            Dict containing trade data for database storage
        """
        try:
            # Ensure order_data is an ExchangeOrderData object
            if isinstance(order_data, dict):
                order_data = ExchangeOrderData.from_dict(order_data)

            # Determine trade status and state from order status
            status, state = self._map_order_status_to_trade_state(order_data.order_status)
            
            # Extract TP/SL from recommendation data if available
            take_profit = 0.0
            stop_loss = 0.0
            if recommendation_data:
                take_profit = float(recommendation_data.get('take_profit', 0))
                stop_loss = float(recommendation_data.get('stop_loss', 0))
            
            # Use order price as entry price, or avg price if available
            entry_price = order_data.avg_price if order_data.avg_price > 0 else order_data.price
            
            trade_data = {
                'symbol': order_data.symbol,
                'side': order_data.side,
                'quantity': order_data.qty,
                'entry_price': entry_price,
                'take_profit': take_profit,
                'stop_loss': stop_loss,
                'order_id': order_data.order_id,
                'status': status,
                'state': state,
                'pnl': 0.0,  # Will be updated when position closes
                'avg_exit_price': 0.0,
                'closed_size': 0.0,
                'placed_by': 'BOT',  # Default to BOT, can be overridden
                'alteration_details': None
            }
            
            self.logger.debug(f"Mapped order {order_data.order_id} to trade data: {trade_data}")
            return trade_data
            
        except Exception as e:
            self.logger.error(f"Error mapping order to trade: {e}")
            # Return minimal trade data to avoid complete failure
            # Attempt to get order_id and symbol even if order_data is problematic
            order_id_val = order_data.order_id if isinstance(order_data, ExchangeOrderData) else (order_data.get('orderId') if isinstance(order_data, dict) else '')
            symbol_val = order_data.symbol if isinstance(order_data, ExchangeOrderData) else (order_data.get('symbol') if isinstance(order_data, dict) else 'UNKNOWN')

            return {
                'symbol': symbol_val,
                'side': 'Buy',
                'quantity': 0.0,
                'entry_price': 0.0,
                'take_profit': 0.0,
                'stop_loss': 0.0,
                'order_id': order_id_val,
                'status': 'open',
                'state': 'trade',
                'pnl': 0.0,
                'avg_exit_price': 0.0,
                'closed_size': 0.0,
                'placed_by': 'BOT',
                'alteration_details': None
            }
    
    def map_position_to_trade(self, position_data: ExchangePositionData, 
                            existing_trade: Dict[str, Any]) -> Dict[str, Any]:
        """
        Map exchange position data to update existing trade record.
        
        Args:
            position_data: Exchange position data
            existing_trade: Current trade record from database
            
        Returns:
            Dict containing updated trade data
        """
        try:
            update_data = {}
            
            # Update PnL for open positions
            if position_data.unrealised_pnl != 0:
                update_data['pnl'] = position_data.unrealised_pnl
            
            # Update status/state for open positions
            if position_data.size > 0:
                update_data['status'] = 'open'
                update_data['state'] = 'position'
            
            # Update average price if available and different
            if position_data.avg_price > 0 and position_data.avg_price != existing_trade.get('entry_price', 0):
                update_data['entry_price'] = position_data.avg_price
            
            self.logger.debug(f"Mapped position for {position_data.symbol} to update data: {update_data}")
            return update_data
            
        except Exception as e:
            self.logger.error(f"Error mapping position to trade: {e}")
            return {}
    
    def map_closed_pnl_to_trade(self, pnl_data: ExchangeClosedPnlData) -> Dict[str, Any]:
        """
        Map exchange closed PnL data to update trade record.
        
        Args:
            pnl_data: Exchange closed PnL data
            
        Returns:
            Dict containing updated trade data for closed position
        """
        try:
            update_data = {
                'status': 'closed',
                'state': 'position',
                'pnl': pnl_data.closed_pnl,
                'avg_exit_price': pnl_data.avg_exit_price,
                'closed_size': pnl_data.qty
            }
            
            self.logger.debug(f"Mapped closed PnL for order {pnl_data.order_id} to update data: {update_data}")
            return update_data
            
        except Exception as e:
            self.logger.error(f"Error mapping closed PnL to trade: {e}")
            return {}
    
    def _map_order_status_to_trade_state(self, order_status: str) -> tuple[str, str]:
        """
        Map exchange order status to trade status and state.
        
        Args:
            order_status: Exchange order status
            
        Returns:
            Tuple of (status, state)
        """
        order_status = order_status.upper()
        
        if order_status in ['NEW', 'PENDING', 'PARTIALLYFILLED']:
            return ('open', 'trade')
        elif order_status == 'FILLED':
            return ('open', 'position')
        elif order_status in ['CANCELLED', 'REJECTED', 'EXPIRED']:
            return ('cancelled', 'cancelled')
        elif order_status == 'UNTRIGGERED':
            # Conditional order that hasn't been triggered yet
            return ('open', 'trade')
        else:
            self.logger.warning(f"Unknown order status: {order_status}, defaulting to open/trade")
            return ('open', 'trade')
    
    def map_trade_to_database_record(self, trade_data: Dict[str, Any], 
                                   trade_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert mapped trade data to database record format.
        
        Args:
            trade_data: Mapped trade data
            trade_id: Optional trade ID to use
            
        Returns:
            Dict containing database record format
        """
        try:
            # Generate trade ID if not provided
            if not trade_id:
                import uuid
                trade_id = str(uuid.uuid4())
            
            # Ensure all required fields are present
            db_record = {
                'id': trade_id,
                'recommendation_id': trade_data.get('recommendation_id', ''),
                'symbol': trade_data.get('symbol', 'UNKNOWN'),
                'side': trade_data.get('side', 'Buy'),
                'quantity': float(trade_data.get('quantity', 0)),
                'entry_price': float(trade_data.get('entry_price', 0)),
                'take_profit': float(trade_data.get('take_profit', 0)),
                'stop_loss': float(trade_data.get('stop_loss', 0)),
                'order_id': trade_data.get('order_id', ''),
                'pnl': float(trade_data.get('pnl', 0)),
                'status': trade_data.get('status', 'open'),
                'state': trade_data.get('state', 'trade'),
                'avg_exit_price': float(trade_data.get('avg_exit_price', 0)),
                'closed_size': float(trade_data.get('closed_size', 0)),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'placed_by': trade_data.get('placed_by', 'BOT'),
                'alteration_details': trade_data.get('alteration_details')
            }
            
            return db_record
            
        except Exception as e:
            self.logger.error(f"Error mapping trade to database record: {e}")
            raise
