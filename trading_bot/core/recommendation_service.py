"""
Unified recommendation service for trading bot.

This module provides centralized recommendation management including:
- Consolidated recommendation fetching and validation
- Caching to eliminate redundant operations
- Unified validation pipeline
- Boundary-aware recommendation filtering

Author: Trading Bot Core Team
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional

from .timestamp_validator import TimestampValidator
from .utils import create_success_response, create_error_response


class RecommendationService:
    """
    Centralized service for recommendation management and validation.
    
    This service consolidates all recommendation-related operations to eliminate
    redundancy and improve performance by caching results and using a unified
    validation pipeline.
    """
    
    def __init__(self, data_agent, recommender, timestamp_validator=None, risk_manager=None):
        """
        Initialize the recommendation service.
        
        Args:
            data_agent: DataAgent instance for database operations
            recommender: Recommender instance for validation
            timestamp_validator: TimestampValidator instance (optional, creates if not provided)
            risk_manager: RiskManager instance for risk validation (optional)
        """
        self.data_agent = data_agent
        self.recommender = recommender
        self.risk_manager = risk_manager
        self.timestamp_validator = timestamp_validator or TimestampValidator()
        self.logger = logging.getLogger(__name__)
        
        # Simple cache for recommendations and validation results
        self._recommendation_cache = {}
        self._validation_cache = {}
        self._cache_ttl = 30  # 30 seconds cache TTL
    
    def get_valid_recommendations(self, timeframe: str, symbols: Optional[List[str]] = None, 
                                open_positions: Optional[Dict] = None) -> Dict:
        """
        Get fully validated recommendations ready for trading.
        
        This is the main entry point that handles the complete recommendation pipeline:
        1. Fetch recommendations from database
        2. Filter by timeframe and symbols
        3. Validate timestamps
        4. Validate risk parameters
        5. Check for position conflicts
        6. Return ready-to-trade recommendations
        
        Args:
            timeframe: Target timeframe (e.g., "15m", "1h", "4h")
            symbols: Optional list of symbols to filter for
            open_positions: Optional dict of open positions to avoid conflicts
            
        Returns:
            Dict with status and list of valid recommendations
        """
        try:
            # Get all latest analysis from database
            all_analysis = self.data_agent.get_all_latest_analysis()
            if not all_analysis:
                return create_success_response("No analysis found", recommendations=[])
            
            # Filter by timeframe
            timeframe_analysis = [
                analysis for analysis in all_analysis 
                if analysis.get('timeframe') == timeframe
            ]
            
            if not timeframe_analysis:
                return create_success_response("No analysis for timeframe", recommendations=[])
            
            # Filter by symbols if specified
            if symbols:
                timeframe_analysis = [
                    analysis for analysis in timeframe_analysis
                    if analysis.get('symbol') in symbols
                ]
            
            # Apply unified validation pipeline
            validation_result = self._validate_recommendations_pipeline(
                timeframe_analysis, open_positions
            )
            
            valid_recommendations = validation_result.get('valid', [])
            
            self.logger.info(f"Found {len(valid_recommendations)} valid recommendations for {timeframe}")
            
            return create_success_response(
                f"Found {len(valid_recommendations)} valid recommendations",
                recommendations=valid_recommendations,
                validation_stats=validation_result.get('stats', {})
            )
            
        except Exception as e:
            self.logger.error(f"Error getting valid recommendations: {e}")
            return create_error_response(f"Failed to get valid recommendations: {str(e)}")
    
    def is_fresh_data_available(self, timeframe: str) -> bool:
        """
        Simple check if fresh recommendations exist for the current boundary.
        
        Args:
            timeframe: Target timeframe to check
            
        Returns:
            bool: True if fresh recommendations exist
        """
        try:
            # Use the existing recommender method but with simplified approach
            if hasattr(self.recommender, 'get_fresh_recommendations_for_boundary'):
                fresh_recs = self.recommender.get_fresh_recommendations_for_boundary("all", timeframe)
                return len(fresh_recs) > 0
            else:
                # Fallback to basic check
                all_analysis = self.data_agent.get_all_latest_analysis()
                timeframe_analysis = [
                    analysis for analysis in all_analysis
                    if analysis.get('timeframe') == timeframe
                ]
                
                # Check if any are still valid for current period
                for analysis in timeframe_analysis:
                    if self.recommender.is_recommendation_valid_timestamp_only(analysis):
                        return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"Error checking for fresh data: {e}")
            return False
    
    def _validate_recommendations_pipeline(self, recommendations: List[Dict], 
                                         open_positions: Optional[Dict] = None) -> Dict:
        """
        Unified validation pipeline for recommendations using optimized validation methods.
        
        Processes recommendations through a single pipeline using new optimized methods:
        1. Timestamp validation (using is_timestamp_valid)
        2. Risk validation (using is_risk_valid) 
        3. Position conflict checking
        
        Args:
            recommendations: List of recommendation dictionaries
            open_positions: Optional dict of open positions
            
        Returns:
            Dict with validation results and statistics
        """
        valid_recommendations = []
        invalid_recommendations = []
        validation_stats = {
            'total': len(recommendations),
            'timestamp_valid': 0,
            'risk_valid': 0,
            'position_valid': 0,
            'invalid_reasons': {}
        }
        
        for rec in recommendations:
            symbol = rec.get('symbol', 'Unknown')
            recommendation_type = rec.get('recommendation', '').upper()
            
            # Skip if no symbol or invalid recommendation type
            if not symbol or recommendation_type not in ['BUY', 'SELL', 'LONG', 'SHORT']:
                reason = f"Invalid recommendation type: {recommendation_type}" if recommendation_type else "Missing symbol"
                invalid_recommendations.append({'recommendation': rec, 'reason': reason})
                validation_stats['invalid_reasons'][reason] = validation_stats['invalid_reasons'].get(reason, 0) + 1
                continue
            
            # 1. Timestamp validation (optimized method - no risk validation)
            timestamp_valid = self.recommender.is_timestamp_valid(rec)
            
            if not timestamp_valid:
                reason = "Timestamp expired"
                invalid_recommendations.append({'recommendation': rec, 'reason': reason})
                validation_stats['invalid_reasons'][reason] = validation_stats['invalid_reasons'].get(reason, 0) + 1
                continue
            
            validation_stats['timestamp_valid'] += 1
            
            # 2. Risk validation (optimized method - no timestamp validation)
            risk_valid = self.recommender.is_risk_valid(rec)
            if not risk_valid:
                risk_result = self.recommender.validate_risk_parameters(rec)
                reason = risk_result.get('error', 'Risk validation failed')
                invalid_recommendations.append({'recommendation': rec, 'reason': reason})
                validation_stats['invalid_reasons'][reason] = validation_stats['invalid_reasons'].get(reason, 0) + 1
                continue
            
            validation_stats['risk_valid'] += 1
            
            # 3. Position conflict checking (using cached positions)
            has_position = False
            if open_positions is not None:
                has_position = open_positions.get(symbol, False) or open_positions.get(f"{symbol}.P", False)
            elif hasattr(self.recommender.trader, 'has_open_position'):
                has_position = self.recommender.trader.has_open_position(symbol)
            
            if has_position:
                reason = "Position already exists"
                invalid_recommendations.append({'recommendation': rec, 'reason': reason})
                validation_stats['invalid_reasons'][reason] = validation_stats['invalid_reasons'].get(reason, 0) + 1
                continue
            
            validation_stats['position_valid'] += 1
            
            # Recommendation passed all validation
            valid_recommendations.append(rec)
        
        return {
            'valid': valid_recommendations,
            'invalid': invalid_recommendations,
            'stats': validation_stats
        }
    
    def get_boundary_aware_symbols(self, timeframe: str, is_startup: bool = False) -> List[str]:
        """
        Get symbols with recommendations within boundary constraints.
        
        Args:
            timeframe: Target timeframe
            is_startup: If True, check previous + current boundary; if False, check current boundary only
            
        Returns:
            List of symbols that have valid recommendations within the boundary constraints
        """
        try:
            current_time = datetime.now(timezone.utc)
            
            # Calculate boundary times
            timeframe_info = self.timestamp_validator.normalize_timeframe(timeframe)
            
            if is_startup:
                # On startup: check previous boundary + current boundary
                current_boundary = self.timestamp_validator.calculate_next_boundary(current_time, timeframe) - timeframe_info.timedelta
                previous_boundary = current_boundary - timeframe_info.timedelta
                boundary_start = previous_boundary
            else:
                # Mid-cycle: only check current boundary
                current_boundary = self.timestamp_validator.calculate_next_boundary(current_time, timeframe) - timeframe_info.timedelta
                boundary_start = current_boundary
            
            # Get all analysis and filter by boundary and timeframe
            all_analysis = self.data_agent.get_all_latest_analysis()
            if not all_analysis:
                return []
            
            valid_symbols = {}  # Store symbol -> timestamp mapping
            for analysis in all_analysis:
                symbol = analysis.get('symbol')
                analysis_timeframe = analysis.get('timeframe')
                timestamp_str = analysis.get('timestamp', '')
                
                # Only check the target timeframe
                if analysis_timeframe != timeframe:
                    continue
                    
                if not symbol or not timestamp_str:
                    continue
                
                try:
                    # Parse timestamp
                    analysis_time = self.timestamp_validator.parse_timestamp(timestamp_str)
                    
                    # Check if within boundary constraints
                    if analysis_time >= boundary_start and self.recommender.is_recommendation_valid(analysis):
                        # Store the most recent timestamp for each symbol
                        if symbol not in valid_symbols or timestamp_str > valid_symbols[symbol]:
                            valid_symbols[symbol] = timestamp_str
                            
                except Exception as e:
                    continue
            
            return list(valid_symbols.keys())
            
        except Exception as e:
            self.logger.error(f"Error in boundary-aware symbol filtering: {e}")
            return []
    
    def get_fresh_recommendations_for_processing(self, timeframe: str, open_positions=None) -> list:
        """
        Get fresh recommendations that should be processed for trading.
        
        Args:
            timeframe: Timeframe string (e.g., "4h", "1h", "30m")
            open_positions: Optional list of open positions from batch check to avoid individual API calls
            
        Returns:
            List of trade info dictionaries for fresh recommendations ready for processing
        """
        try:
            # Get all latest analysis from database
            all_analysis = self.data_agent.get_all_latest_analysis()
            
            if not all_analysis:
                return []
            
            # Filter analysis to only include the requested timeframe
            matching_timeframe_analysis = [
                analysis for analysis in all_analysis
                if analysis.get('timeframe') == timeframe
            ]
            
            if not matching_timeframe_analysis:
                return []
            
            fresh_trades = []
            
            for analysis in matching_timeframe_analysis:
                # Check if this recommendation is still valid
                if self.recommender.is_recommendation_valid(analysis):
                    symbol = analysis.get('symbol')
                    recommendation_type = analysis.get('recommendation', '').upper()
                    
                    # Skip if symbol is missing
                    if not symbol:
                        continue
                    
                    # Check if this is a valid trading recommendation
                    if recommendation_type in ['BUY', 'SELL', 'LONG', 'SHORT']:
                        # Check if we already have an open position for this symbol
                        has_position = False
                        if open_positions is not None:
                            # Use batch position data to avoid individual API calls
                            has_position = open_positions.get(symbol, False) or open_positions.get(f"{symbol}.P", False)
                        else:
                            # Fallback to individual API call if batch data not provided
                            has_position = self.recommender.trader.has_open_position(symbol)
                        
                        if not has_position:
                            # Check confidence threshold
                            confidence = float(analysis.get('confidence', 0))
                            if confidence >= getattr(self.recommender.config.trading, 'min_confidence_threshold'):
                                fresh_trades.append({
                                    "symbol": symbol,
                                    "recommendation": analysis
                                })
                        else:
                            pass  # Position already exists
                    else:
                        pass  # Not a valid trading recommendation
            
            return fresh_trades
            
        except Exception as e:
            self.logger.error(f"Error getting fresh recommendations for processing: {e}")
            return []
    
    def check_current_cycle_recommendations(self, timeframe: str, current_time: datetime, open_positions=None) -> list:
        """
        Check for recommendations that belong to the current cycle and haven't been traded yet.
        
        For mid-cycle starts (e.g., starting at 1:15 for 1h timeframe), this method will
        process recommendations from the previous boundary to allow processing of the
        partial cycle that occurred before the autotrader started.
        
        Args:
            timeframe: Target timeframe (e.g., "15m", "1h", "4h")
            current_time: Current UTC time
            
        Returns:
            List of trade info dictionaries for pending trades in current cycle
        """
        try:
            # Calculate the current cycle boundaries
            timeframe_info = self.timestamp_validator.normalize_timeframe(timeframe)
            timeframe_minutes = timeframe_info.minutes
            
            # Calculate current cycle start boundary
            if timeframe == "1d":
                # Daily boundary: current day at 00:00 UTC
                current_boundary = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
            elif timeframe_minutes >= 60:
                # Hourly boundaries: align to hour boundaries
                hours = timeframe_minutes // 60
                current_hour = current_time.hour
                cycle_hour = (current_hour // hours) * hours
                current_boundary = current_time.replace(hour=cycle_hour, minute=0, second=0, microsecond=0)
            else:
                # Minute boundaries: align to minute boundaries
                current_minute = current_time.hour * 60 + current_time.minute
                cycle_minute = (current_minute // timeframe_minutes) * timeframe_minutes
                hour = cycle_minute // 60
                minute = cycle_minute % 60
                current_boundary = current_time.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # Check if we're starting mid-cycle (current time is not exactly at boundary)
            is_mid_cycle_start = current_time > current_boundary
            
            if is_mid_cycle_start:
                # For mid-cycle start, process the previous complete cycle
                if timeframe == "1d":
                    cycle_start = current_boundary - timedelta(days=1)
                    cycle_end = current_boundary
                elif timeframe_minutes >= 60:
                    hours = timeframe_minutes // 60
                    cycle_start = current_boundary - timedelta(hours=hours)
                    cycle_end = current_boundary
                else:
                    cycle_start = current_boundary - timedelta(minutes=timeframe_minutes)
                    cycle_end = current_boundary
            else:
                # Normal cycle start (exactly at boundary)
                cycle_start = current_boundary
                cycle_end = self.timestamp_validator.calculate_next_boundary(cycle_start, timeframe)
            
            # Get all latest analysis from database
            all_analysis = self.data_agent.get_all_latest_analysis()
            
            if not all_analysis:
                return []
            
            # Filter analysis to only include the requested timeframe
            matching_timeframe_analysis = [
                analysis for analysis in all_analysis
                if analysis.get('timeframe') == timeframe
            ]
            
            if not matching_timeframe_analysis:
                return []
            
            current_cycle_trades = []
            
            for analysis in matching_timeframe_analysis:
                try:
                    # Parse the recommendation timestamp
                    timestamp_str = analysis.get('timestamp')
                    if not timestamp_str:
                        continue
                    
                    rec_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    if rec_time.tzinfo is None:
                        rec_time = rec_time.replace(tzinfo=timezone.utc)
                    
                    # Check if this recommendation belongs to the current cycle
                    if cycle_start <= rec_time < cycle_end:
                        symbol = analysis.get('symbol')
                        recommendation_type = analysis.get('recommendation', '').upper()
                        
                        if not symbol:
                            continue
                        
                        if recommendation_type in ['BUY', 'SELL', 'LONG', 'SHORT']:
                            has_position = False
                            if open_positions is not None:
                                # Use batch position data to avoid individual API calls
                                has_position = open_positions.get(symbol, False) or open_positions.get(f"{symbol}.P", False)
                            else:
                                # Fallback to individual API call if batch data not provided
                                has_position = self.recommender.trader.has_open_position(symbol)
                            
                            if not has_position:
                                confidence = float(analysis.get('confidence', 0))
                                min_confidence = getattr(self.recommender.config.trading, 'min_confidence_threshold')
                                if confidence >= min_confidence:
                                    current_cycle_trades.append({
                                        "symbol": symbol,
                                        "recommendation": analysis,
                                        "cycle_start": cycle_start,
                                        "cycle_end": cycle_end
                                    })
                            else:
                                pass  # Position already exists
                        else:
                            pass  # Not a valid trading recommendation
                    
                except Exception as e:
                    continue
            
            return current_cycle_trades
            
        except Exception as e:
            self.logger.error(f"Error checking current cycle recommendations: {e}")
            return []
