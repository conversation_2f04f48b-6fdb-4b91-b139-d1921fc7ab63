"""Incremental synchronization manager to replace expensive rebuild operations."""
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Set
from dataclasses import field
from dataclasses import dataclass

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.bybit_api_manager import BybitAPIManager
from trading_bot.core.realtime_trade_tracker import RealTimeTradeTracker


@dataclass
class SyncStats:
    """Statistics for sync operations."""
    trades_checked: int = 0
    trades_updated: int = 0
    trades_created: int = 0
    positions_synced: int = 0
    errors: List[str] = field(default_factory=list)
    sync_duration: float = 0.0


class IncrementalSyncManager:
    """
    Efficient incremental synchronization manager that only processes new or changed data
    from the exchange, eliminating the need for expensive full rebuilds.
    """
    
    def __init__(self, data_agent: DataAgent, api_manager: BybitAPIManager, 
                 trade_tracker: RealTimeTradeTracker):
        self.data_agent = data_agent
        self.api_manager = api_manager
        self.trade_tracker = trade_tracker
        self.logger = logging.getLogger(__name__)
        
        # Sync state tracking
        self._last_sync_time = None
        self._known_order_ids: Set[str] = set()
        self._is_syncing = False
        
        # Initialize known order IDs from database
        self._initialize_known_orders()
    
    def _initialize_known_orders(self):
        """Initialize set of known order IDs from existing database records."""
        try:
            existing_trades = self.data_agent.get_trades()
            self._known_order_ids = {
                str(trade.get('order_id')) for trade in existing_trades
                if trade.get('order_id') is not None
            }
            self.logger.info(f"Initialized with {len(self._known_order_ids)} known order IDs")
        except Exception as e:
            self.logger.error(f"Failed to initialize known orders: {e}")
            self._known_order_ids = set()
    
    def sync_new_and_updated_trades(self, lookback_hours: int = 24) -> SyncStats:
        """
        Perform incremental sync of only new or updated trades from the exchange.
        
        Args:
            lookback_hours: How many hours back to check for updates
            
        Returns:
            SyncStats: Statistics about the sync operation
        """
        if self._is_syncing:
            self.logger.warning("Sync already in progress, skipping")
            return SyncStats(errors=["Sync already in progress"])
        
        self._is_syncing = True
        start_time = time.time()
        stats = SyncStats()
        
        try:
            self.logger.info(f"Starting incremental sync (lookback: {lookback_hours}h)")
            
            # 1. Sync open positions (these change frequently)
            self._sync_open_positions(stats)
            
            # 2. Check for new closed trades
            self._sync_recent_closed_trades(stats, lookback_hours)
            
            # 3. Update existing open trades with latest status
            self._update_existing_open_trades(stats)
            
            # 4. Clean up stale pending trades
            stale_count = self.trade_tracker.cleanup_stale_pending_trades()
            if stale_count > 0:
                self.logger.info(f"Cleaned up {stale_count} stale pending trades")
            
            stats.sync_duration = time.time() - start_time
            self._last_sync_time = datetime.now(timezone.utc)
            
            self.logger.info(
                f"Incremental sync completed: {stats.trades_updated} updated, "
                f"{stats.trades_created} created, {stats.positions_synced} positions synced "
                f"in {stats.sync_duration:.2f}s"
            )
            
            return stats
            
        except Exception as e:
            error_msg = f"Incremental sync failed: {e}"
            self.logger.error(error_msg)
            stats.errors.append(error_msg)
            return stats
        finally:
            self._is_syncing = False
    
    def _sync_open_positions(self, stats: SyncStats):
        """Sync current open positions from exchange."""
        try:
            response = self.api_manager.get_positions(category="linear", settleCoin="USDT")
            if response.get("retCode") != 0:
                error_msg = f"Failed to fetch positions: {response.get('retMsg', 'Unknown error')}"
                stats.errors.append(error_msg)
                return
            
            positions = response.get("result", {}).get("list", [])
            open_positions = [pos for pos in positions if float(pos.get('size', 0)) > 0]
            
            for position in open_positions:
                self._process_open_position(position, stats)
            
            stats.positions_synced = len(open_positions)
            
        except Exception as e:
            error_msg = f"Error syncing open positions: {e}"
            self.logger.error(error_msg)
            stats.errors.append(error_msg)
    
    def _process_open_position(self, position: Dict[str, Any], stats: SyncStats):
        """Process a single open position."""
        try:
            symbol = position.get('symbol', '').replace('.P', '')
            size = float(position.get('size', 0))
            
            if size == 0:
                return
            
            # Check if we have a trade record for this position
            existing_trades = self.data_agent.get_trades(symbol=symbol, status='open')
            
            # Look for matching trade by symbol and approximate size
            matching_trade = None
            for trade in existing_trades:
                trade_qty = float(trade.get('quantity', 0))
                if abs(trade_qty - size) / max(trade_qty, size) < 0.01:  # 1% tolerance
                    matching_trade = trade
                    break
            
            if matching_trade:
                # Update existing trade with current position data
                update_data = {
                    'status': 'filled',
                    'state': 'position',
                    'entry_price': float(position.get('avgPrice', matching_trade.get('entry_price', 0))),
                    'pnl': float(position.get('unrealisedPnl', 0)),
                    'updated_at': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                }
                
                if self.data_agent.update_trade(matching_trade['id'], **update_data):
                    stats.trades_updated += 1
                else:
                    stats.errors.append(f"Failed to update trade for position {symbol}")
            else:
                # This is a position without a corresponding trade record
                # This could happen if the trade was placed outside our system
                self.logger.warning(f"Found position without trade record: {symbol} (size: {size})")
                
        except Exception as e:
            error_msg = f"Error processing position {position.get('symbol', 'UNKNOWN')}: {e}"
            self.logger.error(error_msg)
            stats.errors.append(error_msg)
    
    def _sync_recent_closed_trades(self, stats: SyncStats, lookback_hours: int):
        """Sync recent closed trades from exchange."""
        try:
            # Calculate start time for lookback
            start_time = datetime.now(timezone.utc) - timedelta(hours=lookback_hours)
            start_timestamp = int(start_time.timestamp() * 1000)
            
            # Fetch recent closed PnL records
            response = self.api_manager.get_closed_pnl(
                category="linear",
                startTime=start_timestamp,
                limit=200
            )
            
            if response.get("retCode") != 0:
                error_msg = f"Failed to fetch closed PnL: {response.get('retMsg', 'Unknown error')}"
                stats.errors.append(error_msg)
                return
            
            closed_trades = response.get("result", {}).get("list", [])
            
            for closed_trade in closed_trades:
                self._process_closed_trade(closed_trade, stats)
            
            stats.trades_checked += len(closed_trades)
            
        except Exception as e:
            error_msg = f"Error syncing closed trades: {e}"
            self.logger.error(error_msg)
            stats.errors.append(error_msg)
    
    def _process_closed_trade(self, closed_trade: Dict[str, Any], stats: SyncStats):
        """Process a single closed trade from exchange."""
        try:
            order_id = closed_trade.get('orderId')
            if not order_id:
                return
            
            # Skip if we've already processed this order
            if order_id in self._known_order_ids:
                return
            
            # Check if we have an existing trade record
            existing_trade = self.data_agent.get_trade_by_order_id(order_id)
            
            if existing_trade:
                # Update existing trade with closed data
                update_data = {
                    'status': 'closed',
                    'state': 'trade',
                    'pnl': float(closed_trade.get('closedPnl', 0)),
                    'avg_exit_price': float(closed_trade.get('avgExitPrice', 0)),
                    'closed_size': float(closed_trade.get('qty', 0)),
                    'updated_at': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                }
                
                if self.data_agent.update_trade(existing_trade['id'], **update_data):
                    stats.trades_updated += 1
                    self._known_order_ids.add(order_id)
                else:
                    stats.errors.append(f"Failed to update closed trade {order_id}")
            else:
                # This is a closed trade we don't have a record for
                # Could be from manual trading or system restart
                self.logger.info(f"Found closed trade without record: {order_id}")
                # We could create a new record here if needed
                
        except Exception as e:
            error_msg = f"Error processing closed trade {closed_trade.get('orderId', 'UNKNOWN')}: {e}"
            self.logger.error(error_msg)
            stats.errors.append(error_msg)
    
    def _update_existing_open_trades(self, stats: SyncStats):
        """Update status of existing open trades."""
        try:
            # Get all open trades from database
            open_trades = self.data_agent.get_trades(status='open')
            
            for trade in open_trades:
                order_id = trade.get('order_id')
                if not order_id:
                    continue
                
                # Check current status on exchange
                self._check_trade_status_on_exchange(trade, stats)
                
        except Exception as e:
            error_msg = f"Error updating existing open trades: {e}"
            self.logger.error(error_msg)
            stats.errors.append(error_msg)
    
    def _check_trade_status_on_exchange(self, trade: Dict[str, Any], stats: SyncStats):
        """Check the current status of a trade on the exchange."""
        try:
            order_id = trade.get('order_id')
            symbol = trade.get('symbol')
            
            # Get order status from exchange
            response = self.api_manager.get_order_history(
                category="linear",
                symbol=symbol,
                orderId=order_id,
                limit=1
            )
            
            if response.get("retCode") != 0:
                return  # Skip if we can't get order status
            
            orders = response.get("result", {}).get("list", [])
            if not orders:
                return
            
            order = orders[0]
            order_status = order.get('orderStatus', '')
            
            # Map exchange status to our status
            new_status = self._map_exchange_status(order_status)
            
            if new_status and new_status != trade.get('status'):
                # Update trade status
                update_data = {
                    'status': new_status,
                    'updated_at': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # Add additional data if order is filled
                if new_status in ['filled', 'partially_filled']:
                    if order.get('avgPrice') and float(order.get('avgPrice', 0)) > 0:
                        update_data['entry_price'] = str(float(order['avgPrice']))
                    if order.get('cumExecQty'):
                        update_data['quantity'] = str(float(order['cumExecQty']))
                    update_data['state'] = 'position'
                
                if self.data_agent.update_trade(trade['id'], **update_data):
                    stats.trades_updated += 1
                    self.logger.info(f"Updated trade status: {symbol} {order_id} -> {new_status}")
                
        except Exception as e:
            self.logger.error(f"Error checking trade status for {trade.get('order_id', 'UNKNOWN')}: {e}")
    
    def _map_exchange_status(self, exchange_status: str) -> Optional[str]:
        """Map exchange order status to our internal status."""
        status_mapping = {
            'New': 'submitted',
            'PartiallyFilled': 'partially_filled',
            'Filled': 'filled',
            'Cancelled': 'cancelled',
            'Rejected': 'rejected',
            'PartiallyFilledCanceled': 'partially_filled'
        }
        return status_mapping.get(exchange_status)
    
    def force_sync_specific_order(self, order_id: str) -> bool:
        """Force sync of a specific order by order ID."""
        try:
            existing_trade = self.data_agent.get_trade_by_order_id(order_id)
            if not existing_trade:
                self.logger.warning(f"No trade found for order ID: {order_id}")
                return False
            
            stats = SyncStats()
            self._check_trade_status_on_exchange(existing_trade, stats)
            
            return stats.trades_updated > 0
            
        except Exception as e:
            self.logger.error(f"Error force syncing order {order_id}: {e}")
            return False
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get current sync status and statistics."""
        return {
            'is_syncing': self._is_syncing,
            'last_sync_time': self._last_sync_time.isoformat() if self._last_sync_time else None,
            'known_order_count': len(self._known_order_ids),
            'pending_trades_count': len(self.trade_tracker.get_pending_trades())
        }
