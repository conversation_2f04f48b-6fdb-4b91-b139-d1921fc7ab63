"""Exchange synchronization manager using rebuild logic as source of truth."""
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass
import sys
import os
from pathlib import Path

# Add the parent directory to path for imports
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))

from trading_bot.core.data_agent import DataAgent
from trading_bot.config.settings import Config
from trading_bot.core.bybit_api_manager import BybitAPIManager
from trading_bot.core.utils import normalize_symbol_for_bybit

@dataclass
class SyncConfig:
    """Configuration for exchange synchronization."""
    enabled: bool = True
    sync_interval_seconds: int = 30
    max_retries: int = 3
    rate_limit_requests_per_second: int = 5
    batch_size: int = 50
    error_recovery_delay_seconds: int = 60
    incremental_sync_enabled: bool = True
    full_sync_interval_minutes: int = 5

class RebuildSyncManager:
    """Manages synchronization using rebuild logic as source of truth."""
    
    def __init__(self, data_agent: DataAgent, trader, config: SyncConfig, api_manager: Optional[BybitAPIManager] = None):
        self.data_agent = data_agent
        self.trader = trader
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Use provided API manager or create new one
        if api_manager:
            self.api_manager = api_manager
        else:
            # Initialize Bybit API manager
            self.config_obj = Config.from_yaml()
            self.api_manager = BybitAPIManager(self.config_obj)
        
        # Sync tracking
        self._last_full_sync = None
        self._is_syncing = False
        self._sync_stats = {
            'total_syncs': 0,
            'successful_syncs': 0,
            'failed_syncs': 0,
            'last_sync_time': None,
            'last_error': None
        }
    
    async def fetch_all_bybit_closed_pnl(self, category: str = "linear", limit: int = 50, max_pages: int = 500) -> list:
        """Fetches all closed PnL with pagination."""
        all_pnl = []
        cursor = None
        for _ in range(max_pages):
            response = self.api_manager.get_closed_pnl(category=category, limit=limit, cursor=cursor)
            if response and response.get("retCode") == 0:
                data = response.get("result", {})
                pnl_records = data.get("list", [])
                all_pnl.extend(pnl_records)
                cursor = data.get("nextPageCursor")
                if not cursor:
                    break
            else:
                self.logger.error(f"Error fetching closed PnL: {response.get('retMsg', 'Unknown error')}")
                break
        return all_pnl
    
    def get_open_positions(self):
        """Get open positions from Bybit."""
        self.logger.info("Fetching open positions from Bybit...")
        try:
            response = self.api_manager.get_positions(category="linear", settleCoin="USDT")
            if response and response.get("retCode") == 0:
                positions = response.get("result", {}).get("list", [])
                # Filter to only include positions with size > 0
                open_positions = [pos for pos in positions if float(pos.get('size', 0)) > 0]
                self.logger.info(f"Fetched {len(open_positions)} open positions from Bybit")
                return open_positions
            else:
                self.logger.error(f"Error fetching open positions: {response.get('retMsg', 'Unknown error')}")
                return []
        except Exception as e:
            self.logger.error(f"Exception fetching open positions: {e}")
            return []
    
    def _get_existing_trades_by_status(self, status: str) -> List[Dict[str, Any]]:
        """Get existing trades from database by status."""
        try:
            return self.data_agent.get_trades(status=status)
        except Exception as e:
            self.logger.error(f"Error fetching existing trades with status {status}: {e}")
            return []
    
    def _find_matching_open_trade(self, pnl_record: Dict[str, Any], existing_open_trades: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Find matching open trade for a closed PnL record with improved matching logic."""
        order_id = pnl_record.get('orderId')
        symbol = self._clean_symbol(pnl_record.get('symbol', ''))
        entry_price = float(pnl_record.get('avgEntryPrice', 0))
        created_time_ms = pnl_record.get('createdTime')

        if not symbol or entry_price == 0:
            self.logger.debug(f"Skipping PnL record: invalid symbol or entry_price - symbol: {symbol}, entry_price: {entry_price}")
            return None

        self.logger.debug(f"Looking for match: symbol={symbol}, entry_price={entry_price}, order_id={order_id}")

        # First try to match by order_id if available
        if order_id:
            for trade in existing_open_trades:
                trade_order_id = trade.get('order_id') or trade.get('orderLinkId')
                if trade_order_id == order_id:
                    self.logger.debug(f"Found match by order_id: {order_id}")
                    return trade

        # If no order_id match, try to match by symbol, entry_price, and time proximity
        if created_time_ms:
            pnl_created_at = datetime.fromtimestamp(int(created_time_ms) / 1000, tz=timezone.utc)

            # Sort trades by time proximity for better matching
            trade_matches = []
            for trade in existing_open_trades:
                trade_symbol = self._clean_symbol(trade.get('symbol', ''))
                trade_entry_price = float(trade.get('entry_price', 0))

                if trade_symbol == symbol and trade_entry_price > 0:
                    # Check entry price tolerance (increased to 10% for better matching)
                    price_diff_percent = abs((entry_price - trade_entry_price) / trade_entry_price) * 100
                    if price_diff_percent <= 10.0:  # Increased from 5% to 10%
                        # Check time proximity (within 48 hours instead of 24)
                        try:
                            trade_created_str = trade.get('created_at', '')
                            if trade_created_str:
                                # Try multiple timestamp formats
                                try:
                                    if 'T' in trade_created_str:
                                        trade_created_at = datetime.fromisoformat(trade_created_str.replace('Z', '+00:00'))
                                    else:
                                        trade_created_at = datetime.strptime(trade_created_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=timezone.utc)
                                except ValueError:
                                    # Try alternative format
                                    trade_created_at = datetime.strptime(trade_created_str, '%Y-%m-%d %H:%M:%S.%f').replace(tzinfo=timezone.utc)

                                time_diff = abs(pnl_created_at - trade_created_at)
                                if time_diff <= timedelta(hours=48):  # Increased from 24 to 48 hours
                                    trade_matches.append((trade, time_diff, price_diff_percent))
                                    self.logger.debug(f"Potential match found: time_diff={time_diff}, price_diff={price_diff_percent:.2f}%")
                        except (ValueError, KeyError) as e:
                            self.logger.debug(f"Could not parse trade timestamp: {trade_created_str}, error: {e}")
                            continue

            # Return the best match (closest in time, then closest in price)
            if trade_matches:
                best_match = min(trade_matches, key=lambda x: (x[1], x[2]))  # Sort by time_diff, then price_diff
                self.logger.debug(f"Selected best match: time_diff={best_match[1]}, price_diff={best_match[2]:.2f}%")
                return best_match[0]

        self.logger.debug(f"No match found for PnL record: {symbol} @ {entry_price}")
        return None
    
    def _sync_open_positions_incremental(self, analysis_lookup: Dict, existing_open_trades: List[Dict[str, Any]]) -> int:
        """Sync open positions incrementally - only add new ones or update existing."""
        self.logger.info("Syncing open positions incrementally...")
        
        bybit_positions = self.get_open_positions()
        inserted_count = 0
        updated_count = 0
        
        # Create lookup for existing trades by symbol for faster matching
        existing_trades_by_symbol = {}
        for trade in existing_open_trades:
            symbol = self._clean_symbol(trade.get('symbol', ''))
            if symbol not in existing_trades_by_symbol:
                existing_trades_by_symbol[symbol] = []
            existing_trades_by_symbol[symbol].append(trade)
        
        for position in bybit_positions:
            symbol = self._clean_symbol(position.get('symbol', ''))
            side = position.get('side')
            size = float(position.get('size', 0))
            entry_price = float(position.get('avgPrice', 0))
            
            if size <= 0 or entry_price <= 0:
                continue
            
            # Check if this position already exists in DB
            existing_trade = None
            if symbol in existing_trades_by_symbol:
                for trade in existing_trades_by_symbol[symbol]:
                    trade_entry_price = float(trade.get('entry_price', 0))
                    trade_side = trade.get('side', '')
                    
                    # Match by symbol, side, and entry price tolerance
                    if (trade_side == side and trade_entry_price > 0):
                        price_diff_percent = abs((entry_price - trade_entry_price) / trade_entry_price) * 100
                        if price_diff_percent <= 5.0:
                            existing_trade = trade
                            break
            
            if existing_trade:
                # Update existing trade if needed
                update_data = {}
                if abs(float(existing_trade.get('quantity', 0)) - size) > 0.001:
                    update_data['quantity'] = size
                if abs(float(existing_trade.get('entry_price', 0)) - entry_price) > 0.001:
                    update_data['entry_price'] = entry_price
                
                tp = float(position.get('takeProfit', 0))
                sl = float(position.get('stopLoss', 0))
                if abs(float(existing_trade.get('take_profit', 0)) - tp) > 0.001:
                    update_data['take_profit'] = tp
                if abs(float(existing_trade.get('stop_loss', 0)) - sl) > 0.001:
                    update_data['stop_loss'] = sl
                
                if update_data:
                    if self.data_agent.update_trade(existing_trade['id'], **update_data):
                        updated_count += 1
                        self.logger.debug(f"Updated existing open trade for {symbol}")
            else:
                # Insert new open position
                best_match = self._match_position_to_analysis(position, analysis_lookup)
                recommendation_id = best_match.get('id', '') if best_match else ''
                
                trade_id = str(self._generate_uuid())
                created_at = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                
                success = self.data_agent.store_trade(
                    trade_id=trade_id,
                    recommendation_id=recommendation_id,
                    symbol=symbol,
                    side=side,
                    quantity=size,
                    entry_price=entry_price,
                    take_profit=float(position.get('takeProfit', 0)),
                    stop_loss=float(position.get('stopLoss', 0)),
                    order_id=None,  # Open positions don't have order_id - use None instead of empty string
                    orderLinkId=None,
                    pnl=0,  # No unrealized PnL as requested
                    status='open',
                    state='position',
                    created_at=created_at,
                    placed_by='BYBIT_POSITION'
                )
                
                if success:
                    inserted_count += 1
                    self.logger.debug(f"Inserted new open position for {symbol}")
        
        self.logger.info(f"Open positions sync: {inserted_count} inserted, {updated_count} updated")
        return inserted_count
    
    async def sync_with_exchange(self, rebuild: bool = False) -> Dict[str, Any]:
        """
        Sync trades table with exchange data.
        
        Args:
            rebuild: If True, completely rebuilds the trades table from scratch.
                    If False (default), performs incremental sync by only adding/updating
                    records that are on Bybit but not in DB, and managing transitions
                    from open to closed positions.
        
        Returns:
            Dict containing sync results and statistics
        """
        if self._is_syncing:
            return {'status': 'busy', 'message': 'Sync already in progress'}
        
        self._is_syncing = True
        start_time = time.time()
        
        try:
            sync_mode = "rebuild" if rebuild else "incremental"
            self.logger.info(f"Starting {sync_mode} trade synchronization")
            
            sync_results = {
                'status': 'success',
                'sync_mode': sync_mode,
                'trades_processed': 0,
                'trades_updated': 0,
                'trades_created': 0,
                'open_positions_inserted': 0,
                'open_positions_updated': 0,
                'transitions_processed': 0,
                'pnl_records_processed': 0,
                'errors': [],
                'sync_duration': 0
            }
            
            if self.data_agent is None:
                self.logger.warning("DataAgent is not initialized. Cannot synchronize trades.")
                return {'status': 'error', 'message': 'DataAgent not initialized'}

            # Validate database setup
            if not self._validate_database_setup():
                error_msg = "Database validation failed. Cannot proceed with synchronization."
                sync_results['errors'].append(error_msg)
                self.logger.error(error_msg)
                return {'status': 'error', 'message': error_msg}

            # Get analysis lookup table
            self.logger.info("Loading analysis results for matching...")
            analysis_lookup = self._get_analysis_lookup()
            
            if rebuild:
                # REBUILD MODE: Clear and rebuild entire table
                self.logger.info("Clearing existing trades table...")
                try:
                    conn = self.data_agent.get_connection()
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM trades")
                    conn.commit()
                    conn.close()
                    self.logger.info("Successfully cleared trades table")
                except Exception as e:
                    error_msg = f"Error clearing trades table: {str(e)}"
                    sync_results['errors'].append(error_msg)
                    self.logger.error(error_msg)
                    return {'status': 'error', 'message': error_msg}

                # Insert open positions first
                open_positions_inserted = self._insert_open_positions(analysis_lookup)
                sync_results['open_positions_inserted'] = open_positions_inserted
                
                # Fetch and insert all closed PnL records
                self.logger.info("Fetching closed PnL records from Bybit...")
                try:
                    all_closed_pnl = await self.fetch_all_bybit_closed_pnl(limit=50, max_pages=500)
                    sync_results['pnl_records_processed'] = len(all_closed_pnl)
                    self.logger.info(f"Fetched {len(all_closed_pnl)} closed PnL records")
                except Exception as e:
                    error_msg = f"Error fetching closed PnL records: {str(e)}"
                    sync_results['errors'].append(error_msg)
                    self.logger.error(error_msg)
                    return {'status': 'error', 'message': error_msg}
                
                # Process all PnL records for rebuild
                matched_count, trades_created = await self._process_closed_pnl_rebuild(all_closed_pnl, analysis_lookup)
                sync_results['trades_processed'] = len(all_closed_pnl)
                sync_results['trades_updated'] = matched_count
                sync_results['trades_created'] = trades_created
                
            else:
                # INCREMENTAL MODE: Only add/update new records and handle transitions
                self.logger.info("Starting incremental sync...")
                
                # Get existing trades from database
                existing_open_trades = self._get_existing_trades_by_status('open')
                self.logger.info(f"Found {len(existing_open_trades)} existing open trades in DB")
                
                # Sync open positions incrementally
                open_positions_inserted = self._sync_open_positions_incremental(analysis_lookup, existing_open_trades)
                sync_results['open_positions_inserted'] = open_positions_inserted
                
                # Fetch closed PnL records from Bybit
                self.logger.info("Fetching closed PnL records from Bybit...")
                try:
                    all_closed_pnl = await self.fetch_all_bybit_closed_pnl(limit=50, max_pages=500)
                    sync_results['pnl_records_processed'] = len(all_closed_pnl)
                    self.logger.info(f"Fetched {len(all_closed_pnl)} closed PnL records")
                except Exception as e:
                    error_msg = f"Error fetching closed PnL records: {str(e)}"
                    sync_results['errors'].append(error_msg)
                    self.logger.error(error_msg)
                    return {'status': 'error', 'message': error_msg}
                
                # Process closed PnL for incremental sync (handle transitions and new records)
                transitions, new_trades = await self._process_closed_pnl_incremental(all_closed_pnl, existing_open_trades, analysis_lookup)
                sync_results['transitions_processed'] = transitions
                sync_results['trades_created'] = new_trades
                sync_results['trades_processed'] = len(all_closed_pnl)
            
            # Update sync statistics
            sync_results['sync_duration'] = time.time() - start_time
            self._sync_stats['total_syncs'] += 1
            self._sync_stats['successful_syncs'] += 1
            self._sync_stats['last_sync_time'] = datetime.now().isoformat()
            self._last_full_sync = datetime.now()
            
            self.logger.info(f"Full sync completed: {sync_results}")
            return sync_results
            
        except Exception as e:
            self.logger.error(f"Error in full sync: {e}")
            self._sync_stats['failed_syncs'] += 1
            self._sync_stats['last_error'] = str(e)
            return {
                'status': 'error',
                'error': str(e),
                'trades_processed': 0,
                'trades_updated': 0,
                'trades_created': 0,
                'errors': [str(e)]
            }
        finally:
            self._is_syncing = False
    
    def _get_analysis_lookup(self):
        """Create lookup table for analysis results."""
        self.logger.info("Loading all analysis results from the database...")
        all_analysis_results = self.data_agent.get_analysis_history(limit=999999)
        self.logger.info(f"Loaded {len(all_analysis_results)} analysis results.")
        
        analysis_lookup = {}
        for analysis in all_analysis_results:
            symbol_raw = analysis.get('symbol')
            symbol = self._clean_symbol(symbol_raw) if symbol_raw is not None else ""
            entry_price = analysis.get('entry_price')
            timestamp_str = analysis.get('timestamp')
            
            if not symbol or entry_price is None or not timestamp_str:
                continue

            try:
                if 'T' in timestamp_str:
                    analysis_timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                else:
                    analysis_timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=timezone.utc)
                
                analysis['parsed_timestamp'] = analysis_timestamp
                
                # Create a key for lookup based on symbol, hour, and price bucket
                timestamp_hour = analysis_timestamp.replace(minute=0, second=0, microsecond=0)
                
                # Dynamic price bucketing for initial lookup
                if entry_price < 10:
                    price_bucket = round(entry_price * 1000) # More granularity for small prices
                else:
                    price_bucket = round(entry_price / 10) * 10 # Original bucketing for larger prices

                key = (symbol, timestamp_hour, price_bucket)
                if key not in analysis_lookup:
                    analysis_lookup[key] = []
                analysis_lookup[key].append(analysis)
                
            except ValueError:
                self.logger.warning(f"Could not parse timestamp for analysis ID {analysis.get('id')}: {timestamp_str}")
                continue
        
        self.logger.info(f"Analysis lookup table created with {len(analysis_lookup)} unique keys.")
        return analysis_lookup
    
    def _match_pnl_to_analysis(self, pnl_record, analysis_lookup):
        """Match a PnL record to an analysis result."""
        symbol_raw = pnl_record.get('symbol')
        symbol = self._clean_symbol(symbol_raw) if symbol_raw is not None else ""
        
        entry_price = float(pnl_record.get('avgEntryPrice', 0))
        created_time_ms = pnl_record.get('createdTime')
        
        if not symbol or entry_price == 0 or not created_time_ms:
            return None

        created_at = datetime.fromtimestamp(int(created_time_ms) / 1000, tz=timezone.utc)
        
        # Dynamic price bucketing for initial lookup
        if entry_price < 10:
            base_price_bucket = round(entry_price * 1000)
            potential_price_buckets = [
                base_price_bucket,
                base_price_bucket - 1, # Check adjacent buckets for small prices
                base_price_bucket + 1
            ]
        else:
            base_price_bucket = round(entry_price / 10) * 10
            potential_price_buckets = [
                base_price_bucket,
                base_price_bucket - 10, # Check adjacent buckets for larger prices
                base_price_bucket + 10
            ]
        
        potential_time_hours = [
            created_at.replace(minute=0, second=0, microsecond=0),
            (created_at - timedelta(hours=1)).replace(minute=0, second=0, microsecond=0),
            (created_at + timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)
        ]

        # Find candidate analyses
        candidate_analyses = []
        for p_bucket in potential_price_buckets:
            for t_hour in potential_time_hours:
                key = (symbol, t_hour, p_bucket)
                if key in analysis_lookup:
                    candidate_analyses.extend(analysis_lookup[key])
        
        # Remove duplicates from candidate_analyses
        seen_analysis_ids = set()
        unique_candidate_analyses = []
        for analysis in candidate_analyses:
            if analysis['id'] not in seen_analysis_ids:
                unique_candidate_analyses.append(analysis)
                seen_analysis_ids.add(analysis['id'])

        self.logger.debug(f"Found {len(unique_candidate_analyses)} candidate analyses for {symbol}.")

        best_match = None
        min_time_diff = timedelta.max

        for analysis in unique_candidate_analyses:
            analysis_timestamp = analysis['parsed_timestamp']
            analysis_entry_price = analysis.get('entry_price')

            if analysis_entry_price is None:
                self.logger.debug(f"Analysis {analysis.get('id')} has no entry_price. Skipping.")
                continue

            # Check time window (24 hours)
            time_diff = abs(created_at - analysis_timestamp)
            if time_diff > timedelta(hours=24):
                self.logger.debug(f"Analysis {analysis.get('id')} time diff {time_diff} outside window 24h. Skipping.")
                continue

            # Check entry price tolerance (5%)
            # Avoid division by zero if analysis_entry_price is 0
            if analysis_entry_price == 0:
                price_diff_percent = float('inf') # Effectively no match
            else:
                price_diff_percent = abs((entry_price - analysis_entry_price) / analysis_entry_price) * 100
            
            if price_diff_percent > 5.0:
                self.logger.debug(f"Analysis {analysis.get('id')} price diff {price_diff_percent:.2f}% outside tolerance 5%. Skipping.")
                continue

            self.logger.debug(f"Potential match found: Analysis ID={analysis.get('id')}, TimeDiff={time_diff}, PriceDiff={price_diff_percent:.2f}%")

            # If multiple matches, pick the one with the smallest time difference
            if time_diff < min_time_diff:
                min_time_diff = time_diff
                best_match = analysis

        return best_match
    
    def _insert_open_positions(self, analysis_lookup):
        """Insert open positions into trades table without unrealized PnL."""
        self.logger.info("Inserting open positions into trades table...")
        
        open_positions = self.get_open_positions()
        inserted_count = 0
        
        if open_positions:
            self.logger.info(f"Processing {len(open_positions)} open positions for insertion...")
            conn = None
            try:
                conn = self.data_agent.get_connection()
                cursor = conn.cursor()

                trades_to_insert = []
                positions_processed = 0
                positions_matched = 0
                positions_skipped = 0

                for position in open_positions:
                    symbol = self._clean_symbol(position.get('symbol', ''))
                    side = position.get('side')
                    size = float(position.get('size', 0))
                    entry_price = float(position.get('avgPrice', 0))

                    positions_processed += 1

                    if size > 0 and entry_price > 0:
                        # Match to analysis
                        best_match = self._match_position_to_analysis(position, analysis_lookup)
                        if best_match:
                            positions_matched += 1
                            recommendation_id = best_match['id']
                        else:
                            recommendation_id = ''
                            self.logger.debug(f"No analysis match found for position {symbol} {side} {entry_price}")

                        # Create trade record
                        trade_id = str(self._generate_uuid())
                        created_at = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')

                        trades_to_insert.append((
                            trade_id,
                            recommendation_id,
                            symbol,
                            side,
                            size,
                            entry_price,
                            float(position.get('takeProfit', 0)),
                            float(position.get('stopLoss', 0)),
                            None,  # order_id
                            None,  # orderLinkId
                            0,     # pnl (without unrealized PnL as requested)
                            'open', # status
                            'position', # state
                            0,     # avg_exit_price
                            0,     # closed_size
                            created_at,
                            'BYBIT_POSITION', # placed_by
                            None   # alteration_details
                        ))
                    else:
                        positions_skipped += 1
                        self.logger.debug(f"Skipped position {symbol}: size={size}, entry_price={entry_price}")
                
                if trades_to_insert:
                    cursor.executemany('''
                        INSERT INTO trades
                        (id, recommendation_id, symbol, side, quantity, entry_price, take_profit, stop_loss, order_id, orderLinkId,
                         pnl, status, state, avg_exit_price, closed_size, created_at, placed_by, alteration_details)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', trades_to_insert)
                    
                    conn.commit()
                    inserted_count = len(trades_to_insert)
                    self.logger.info(f"Successfully inserted {inserted_count} open positions into trades table.")
                    self.logger.info(f"Open positions processing summary: {positions_processed} processed, {positions_matched} matched, {positions_skipped} skipped, {inserted_count} inserted")

            except Exception as e:
                self.logger.error(f"Database error inserting open positions: {e}")
                self.logger.error(f"Failed after processing {positions_processed} positions, {positions_matched} matched")
            finally:
                if conn:
                    conn.close()
        
        return inserted_count
    
    def _match_position_to_analysis(self, position, analysis_lookup):
        """Match an open position to an analysis result."""
        symbol_raw = position.get('symbol')
        symbol = self._clean_symbol(symbol_raw) if symbol_raw is not None else ""
        
        entry_price = float(position.get('avgPrice', 0))
        # Use current time for open positions since they don't have a specific creation time
        created_at = datetime.now(timezone.utc)
        
        if not symbol or entry_price == 0:
            return None

        # Search within time window (24 hours)
        time_window_start = created_at - timedelta(hours=24)
        time_window_end = created_at + timedelta(hours=24)
        
        best_match = None
        min_time_diff = timedelta.max
        
        # Check all analysis results within time window
        for key, analyses in analysis_lookup.items():
            # The key structure is (symbol, timestamp_hour, price_bucket) from get_analysis_lookup
            if isinstance(key, tuple) and len(key) >= 1:
                analysis_symbol = key[0]
                # For simplicity, let's check if symbol matches and search through analyses
                if analysis_symbol == symbol:
                    for analysis in analyses:
                        analysis_timestamp = analysis.get('parsed_timestamp')
                        analysis_entry_price = analysis.get('entry_price')
                        
                        if analysis_entry_price is None or analysis_timestamp is None:
                            continue
                        
                        # Check time window
                        if time_window_start <= analysis_timestamp <= time_window_end:
                            # Check price tolerance (5%)
                            if analysis_entry_price == 0:
                                price_diff_percent = float('inf')
                            else:
                                price_diff_percent = abs((entry_price - analysis_entry_price) / analysis_entry_price) * 100
                            
                            if price_diff_percent <= 5.0:
                                time_diff = abs(created_at - analysis_timestamp)
                                if time_diff < min_time_diff:
                                    min_time_diff = time_diff
                                    best_match = analysis
        
        return best_match
    
    async def _process_closed_pnl_rebuild(self, all_closed_pnl: List[Dict[str, Any]], analysis_lookup: Dict) -> Tuple[int, int]:
        """Process closed PnL records for rebuild mode - insert all as new trades."""
        matched_count = 0
        trades_to_insert = []
        
        pnl_processed = 0
        pnl_no_match = 0
        pnl_no_order_id = 0

        for pnl_record in all_closed_pnl:
            pnl_processed += 1
            best_match = self._match_pnl_to_analysis(pnl_record, analysis_lookup)

            if best_match:
                matched_count += 1

                # Get order ID or generate one if missing
                order_id = pnl_record.get('orderId')
                if not order_id:
                    # Generate a unique order ID based on PnL record details
                    symbol = pnl_record.get('symbol', 'UNKNOWN')
                    created_time = pnl_record.get('createdTime', '0')
                    order_id = f"PNL_{symbol}_{created_time}"
                    pnl_no_order_id += 1
                    self.logger.debug(f"Generated order_id for PnL record without orderId: {order_id}")

                # Prepare trade data
                trade_id = str(self._generate_uuid())
                recommendation_id = best_match.get('id', '') if best_match else ''
                symbol = pnl_record.get('symbol')
                side = pnl_record.get('side', 'Buy')

                # Extract data from PnL record
                quantity = float(pnl_record.get('qty', 0))
                entry_price = float(pnl_record.get('avgEntryPrice', 0))
                exit_price = float(pnl_record.get('avgExitPrice', 0))
                pnl_amount = float(pnl_record.get('closedPnl', 0))

                # Status is always 'closed' for PnL records
                status = 'closed'
                state = 'position'

                # Parse created time
                created_time_ms = pnl_record.get('createdTime')
                if created_time_ms:
                    created_at = datetime.fromtimestamp(int(created_time_ms) / 1000, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    created_at = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')

                trades_to_insert.append({
                    'trade_id': trade_id,
                    'recommendation_id': recommendation_id,
                    'symbol': symbol,
                    'side': side,
                    'quantity': quantity,
                    'entry_price': entry_price,
                    'take_profit': 0,  # Not available in PnL
                    'stop_loss': 0,    # Not available in PnL
                    'order_id': order_id,
                    'orderLinkId': pnl_record.get('orderLinkId'),
                    'pnl': pnl_amount,
                    'status': status,
                    'state': state,
                    'avg_exit_price': exit_price,
                    'closed_size': quantity,  # Assuming full position closed
                    'created_at': created_at,
                    'placed_by': 'BOT'
                })
            else:
                pnl_no_match += 1
                self.logger.debug(f"No analysis match found for PnL record: {pnl_record.get('symbol')} {pnl_record.get('side')} {pnl_record.get('avgEntryPrice')}")
        
        # Bulk insert matched trades
        trades_created = 0
        if trades_to_insert:
            self.logger.info(f"Attempting to bulk insert {len(trades_to_insert)} trades...")
            try:
                conn = self.data_agent.get_connection()
                cursor = conn.cursor()
                
                data_for_executemany = []
                for trade_data in trades_to_insert:
                    data_for_executemany.append((
                        trade_data['trade_id'],
                        trade_data['recommendation_id'],
                        trade_data['symbol'],
                        trade_data['side'],
                        trade_data['quantity'],
                        trade_data['entry_price'],
                        trade_data['take_profit'],
                        trade_data['stop_loss'],
                        trade_data['order_id'],
                        trade_data['orderLinkId'],
                        trade_data['pnl'],
                        trade_data['status'],
                        trade_data['state'],
                        trade_data['avg_exit_price'],
                        trade_data['closed_size'],
                        trade_data['created_at'],
                        trade_data['placed_by'],
                        None  # alteration_details
                    ))
                
                cursor.executemany('''
                    INSERT INTO trades
                    (id, recommendation_id, symbol, side, quantity, entry_price, take_profit, stop_loss, order_id, orderLinkId,
                     pnl, status, state, avg_exit_price, closed_size, created_at, placed_by, alteration_details)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', data_for_executemany)
                
                conn.commit()
                conn.close()
                trades_created = len(trades_to_insert)
                self.logger.info(f"Successfully bulk inserted {trades_created} trades.")
                self.logger.info(f"Closed PnL processing summary: {pnl_processed} processed, {matched_count} matched, {pnl_no_match} no match, {pnl_no_order_id} missing order_id, {trades_created} trades created")
            except Exception as e:
                self.logger.error(f"Database error during bulk insert: {e}")
                self.logger.error(f"Failed after processing {pnl_processed} PnL records, {matched_count} matched")

        return matched_count, trades_created
    
    async def _process_closed_pnl_incremental(self, all_closed_pnl: List[Dict[str, Any]], existing_open_trades: List[Dict[str, Any]], analysis_lookup: Dict) -> Tuple[int, int]:
        """Process closed PnL records for incremental mode - handle transitions and new records."""
        transitions_processed = 0
        new_trades_created = 0
        
        # Get existing closed trades to avoid duplicates
        existing_closed_trades = self._get_existing_trades_by_status('closed')
        existing_closed_order_ids = {trade.get('order_id') for trade in existing_closed_trades if trade.get('order_id')}
        
        for pnl_record in all_closed_pnl:
            order_id = pnl_record.get('orderId')
            if not order_id:
                continue
            
            # Skip if this closed trade already exists in DB
            if order_id in existing_closed_order_ids:
                continue
            
            # Try to find matching open trade for transition
            matching_open_trade = self._find_matching_open_trade(pnl_record, existing_open_trades)
            
            if matching_open_trade:
                # CRITICAL: Handle open → closed transition
                self.logger.info(f"Processing transition: open → closed for {matching_open_trade.get('symbol')} (order_id: {order_id})")
                
                # Extract PnL data
                pnl_amount = float(pnl_record.get('closedPnl', 0))
                exit_price = float(pnl_record.get('avgExitPrice', 0))
                closed_size = float(pnl_record.get('qty', 0))
                
                # Parse updated time from Bybit
                created_time_ms = pnl_record.get('createdTime')
                if created_time_ms:
                    updated_at = datetime.fromtimestamp(int(created_time_ms) / 1000, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    updated_at = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                
                # Update the existing open trade to closed
                update_data = {
                    'status': 'closed',
                    'pnl': pnl_amount,
                    'avg_exit_price': exit_price,
                    'closed_size': closed_size,
                    'updated_at': updated_at
                }
                
                # Update order_id if it was missing
                if not matching_open_trade.get('order_id') and order_id:
                    update_data['order_id'] = order_id
                
                # Update orderLinkId if available
                order_link_id = pnl_record.get('orderLinkId')
                if order_link_id:
                    update_data['orderLinkId'] = order_link_id
                
                success = self.data_agent.update_trade(matching_open_trade['id'], **update_data)
                if success:
                    transitions_processed += 1
                    self.logger.info(f"Successfully transitioned trade {matching_open_trade['id']} from open to closed")
                else:
                    self.logger.error(f"Failed to update trade {matching_open_trade['id']} for transition")
            
            else:
                # No matching open trade found - insert as new closed trade (historical data)
                best_match = self._match_pnl_to_analysis(pnl_record, analysis_lookup)
                
                if best_match:
                    # Prepare trade data
                    trade_id = str(self._generate_uuid())
                    recommendation_id = best_match['id']
                    symbol = pnl_record.get('symbol')
                    side = pnl_record.get('side', 'Buy')
                    
                    # Extract data from PnL record
                    quantity = float(pnl_record.get('qty', 0))
                    entry_price = float(pnl_record.get('avgEntryPrice', 0))
                    exit_price = float(pnl_record.get('avgExitPrice', 0))
                    pnl_amount = float(pnl_record.get('closedPnl', 0))
                    
                    # Parse created time
                    created_time_ms = pnl_record.get('createdTime')
                    if created_time_ms:
                        created_at = datetime.fromtimestamp(int(created_time_ms) / 1000, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        created_at = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
                    
                    # Ensure symbol is not None
                    if not symbol:
                        symbol = 'UNKNOWN'
                    
                    success = self.data_agent.store_trade(
                        trade_id=trade_id,
                        recommendation_id=recommendation_id,
                        symbol=symbol,
                        side=side,
                        quantity=quantity,
                        entry_price=entry_price,
                        take_profit=0,  # Not available in PnL
                        stop_loss=0,    # Not available in PnL
                        order_id=order_id,
                        orderLinkId=pnl_record.get('orderLinkId'),
                        pnl=pnl_amount,
                        status='closed',
                        state='position',
                        avg_exit_price=exit_price,
                        closed_size=quantity,
                        created_at=created_at,
                        placed_by='BYBIT_HISTORICAL'
                    )
                    
                    if success:
                        new_trades_created += 1
                        self.logger.debug(f"Inserted new historical closed trade for {symbol}")
        
        self.logger.info(f"Incremental PnL processing: {transitions_processed} transitions, {new_trades_created} new trades")
        return transitions_processed, new_trades_created
    
    def _clean_symbol(self, symbol: str) -> str:
        """Removes .P suffix from symbols."""
        return symbol.replace('.P', '')

    def _validate_database_setup(self) -> bool:
        """Validate database connection and table schema."""
        try:
            conn = self.data_agent.get_connection()
            cursor = conn.cursor()

            # Check if trades table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='trades'")
            if not cursor.fetchone():
                self.logger.error("Trades table does not exist in database")
                return False

            # Check table schema
            cursor.execute("PRAGMA table_info(trades)")
            columns = cursor.fetchall()
            required_columns = ['id', 'recommendation_id', 'symbol', 'side', 'quantity',
                              'entry_price', 'status', 'created_at']
            column_names = [col[1] for col in columns]

            missing_columns = [col for col in required_columns if col not in column_names]
            if missing_columns:
                self.logger.error(f"Missing required columns in trades table: {missing_columns}")
                return False

            conn.close()
            self.logger.debug("Database validation successful")
            return True

        except Exception as e:
            self.logger.error(f"Database validation failed: {e}")
            return False
    
    def _generate_uuid(self):
        """Generate a simple UUID."""
        import uuid
        return uuid.uuid4()
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get current synchronization status and statistics."""
        return {
            'is_syncing': self._is_syncing,
            'last_full_sync': self._last_full_sync.isoformat() if self._last_full_sync else None,
            'sync_stats': self._sync_stats,
            'config': {
                'sync_interval': self.config.sync_interval_seconds,
                'batch_size': self.config.batch_size,
                'rate_limit': self.config.rate_limit_requests_per_second
            }
        }
