"""Intelligent Order Replacement System for cross-symbol trade optimization."""
import logging
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple

# Import centralized counting function - ONLY source of truth for order counting
from trading_bot.core.utils import count_open_positions_and_orders, cancel_order_with_verification


@dataclass
class TradeCandidate:
    """Represents a trade candidate for ranking and replacement evaluation."""
    signal_data: Dict[str, Any]
    score: float
    symbol: str
    timeframe: Optional[str] = None
    is_existing_order: bool = False
    order_id: Optional[str] = None
    trade_id: Optional[str] = None
    fill_percentage: float = 0.0
    can_be_replaced: bool = True
    created_at: Optional[datetime] = None


@dataclass
class ReplacementDecision:
    """Represents a replacement decision with detailed reasoning."""
    action: str  # 'execute', 'replace', 'skip'
    new_candidate: Optional[TradeCandidate] = None
    old_order: Optional[TradeCandidate] = None
    score_improvement: float = 0.0
    reason: str = ""
    replacement_pairs: Optional[List[Tuple[TradeCandidate, TradeCandidate]]] = None


class TradeCollector:
    """Collects and batches new trade signals for collective evaluation."""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.pending_signals = []
        self.last_batch_time = datetime.now(timezone.utc)
        
        # Get configuration with safe defaults
        replacement_config = getattr(config.trading, 'order_replacement', None)
        if replacement_config:
            self.batch_timeout = replacement_config.batch_evaluation_timeout_seconds
            self.enable_batching = replacement_config.enable_intelligent_replacement
        else:
            # Fallback defaults if no order_replacement config
            self.batch_timeout = 30
            self.enable_batching = True
        
        # Debug logging to understand configuration issues
        self.logger.info(f"🔧 TradeCollector Config Debug:")
        self.logger.info(f"   replacement_config type: {type(replacement_config)}")
        self.logger.info(f"   replacement_config: {replacement_config}")
        self.logger.info(f"   enable_batching: {self.enable_batching}")
        self.logger.info(f"   batch_timeout: {self.batch_timeout}")
    
    def add_signal(self, signal_data: Dict[str, Any]) -> None:
        """Add a new trade signal to the collection."""
        try:
            self.logger.info(f"🔧 add_signal called: enable_batching={self.enable_batching}, symbol={signal_data.get('symbol')}")
            
            if not self.enable_batching:
                # If batching is disabled, don't collect signals
                self.logger.warning(f"⚠️ Batching disabled - signal for {signal_data.get('symbol')} not added to batch")
                return
                
            self.pending_signals.append({
                'signal': signal_data,
                'received_at': datetime.now(timezone.utc)
            })
            self.logger.info(f"✅ Added signal for {signal_data.get('symbol')} to batch. Total pending: {len(self.pending_signals)}")
        except Exception as e:
            self.logger.error(f"Error adding signal to batch: {e}")
    
    def is_batch_ready(self) -> bool:
        """Check if batch should be processed based on timeout or signal count."""
        try:
            if not self.pending_signals:
                return False
            
            # For autotrader workflow, process signals immediately when we have valid trades
            # This ensures intelligent replacement doesn't delay trade execution
            if len(self.pending_signals) >= 1:
                self.logger.info(f"Batch ready: {len(self.pending_signals)} signal(s) available for processing")
                return True
            
            # Fallback timeout check (should not be reached with immediate processing)
            time_since_last_batch = (datetime.now(timezone.utc) - self.last_batch_time).total_seconds()
            if time_since_last_batch >= self.batch_timeout:
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"Error checking batch readiness: {e}")
            return True  # Process immediately on error
    
    def get_batch_and_clear(self) -> List[Dict[str, Any]]:
        """Return collected signals and reset the batch."""
        try:
            batch = [item['signal'] for item in self.pending_signals]
            self.pending_signals.clear()
            self.last_batch_time = datetime.now(timezone.utc)
            self.logger.info(f"Processing batch of {len(batch)} signals")
            return batch
        except Exception as e:
            self.logger.error(f"Error getting batch: {e}")
            return []
    
    def has_pending_signals(self) -> bool:
        """Check if there are pending signals waiting for batch processing."""
        return len(self.pending_signals) > 0


class GlobalTradeRanker:
    """Ranks trades globally across all symbols and timeframes."""
    
    def __init__(self, trader, config, logger, data_agent=None):
        self.trader = trader
        self.config = config
        self.logger = logger
        self.data_agent = data_agent  # For accessing trade database
    
    def rank_all_trades(self, new_signals: List[Dict[str, Any]], 
                       existing_orders: List[Dict[str, Any]]) -> List[TradeCandidate]:
        """Rank all trades (new + existing) by score."""
        try:
            all_candidates = []
            
            # Process new signals
            for signal in new_signals:
                candidate = self._create_candidate_from_signal(signal)
                if candidate:
                    all_candidates.append(candidate)
            
            # Process existing orders
            for order in existing_orders:
                candidate = self._create_candidate_from_order(order)
                if candidate:
                    all_candidates.append(candidate)
            
            # Sort by score (highest first)
            all_candidates.sort(key=lambda x: x.score, reverse=True)
            
            self.logger.info(f"Ranked {len(all_candidates)} trade candidates "
                           f"({len(new_signals)} new, {len(existing_orders)} existing)")
            
            return all_candidates
            
        except Exception as e:
            self.logger.error(f"Error ranking trades: {e}")
            return []
    
    def _create_candidate_from_signal(self, signal: Dict[str, Any]) -> Optional[TradeCandidate]:
        """Create a trade candidate from a new signal."""
        try:
            symbol = signal.get('symbol')
            if not symbol:
                return None
            
            # Get current price for scoring
            current_price = self.trader.get_last_close_price(symbol, "1m")
            if current_price is None:
                self.logger.warning(f"Could not get current price for {symbol}")
                current_price = signal.get('entry_price', 0)
            
            # Calculate trade score using existing method
            confidence = signal.get('confidence', 0.5)
            entry_price = signal.get('entry_price', current_price)
            take_profit = signal.get('take_profit', entry_price * 1.02)
            stop_loss = signal.get('stop_loss', entry_price * 0.98)
            direction = signal.get('direction', signal.get('recommendation', 'LONG'))
            
            # Calculate RR ratio
            rr_ratio = self.trader.calculate_risk_reward_ratio(
                entry_price, take_profit, stop_loss, direction, symbol
            )
            
            # Calculate score (new signal has 0 time elapsed)
            score = self.trader.calculate_trade_score(
                confidence=confidence,
                rr_ratio=rr_ratio,
                time_elapsed_hours=0.0,
                current_price=current_price,
                intended_entry_price=entry_price
            )
            
            return TradeCandidate(
                signal_data=signal,
                score=score,
                symbol=symbol,
                timeframe=signal.get('timeframe'),
                is_existing_order=False,
                created_at=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            self.logger.error(f"Error creating candidate from signal: {e}")
            return None
    
    def _create_candidate_from_order(self, order: Dict[str, Any], timeframe: Optional[str] = None) -> Optional[TradeCandidate]:
        """Create a trade candidate from an existing order."""
        try:
            symbol = order.get('symbol')
            if not symbol:
                return None
            
            # Get current price
            current_price = self.trader.get_last_close_price(symbol, "1m")
            if current_price is None:
                current_price = float(order.get('price', 0))
            
            # Calculate time elapsed since order creation
            created_time = order.get('createdTime')
            time_elapsed_hours = 0.0
            created_dt = None
            
            if created_time:
                try:
                    # Convert timestamp (usually in milliseconds)
                    if isinstance(created_time, str):
                        created_timestamp = int(created_time) / 1000
                    else:
                        created_timestamp = int(created_time) / 1000
                    
                    created_dt = datetime.fromtimestamp(created_timestamp, tz=timezone.utc)
                    time_elapsed_hours = (datetime.now(timezone.utc) - created_dt).total_seconds() / 3600
                except (ValueError, TypeError):
                    self.logger.warning(f"Could not parse order creation time: {created_time}")
            
            # Get order details
            entry_price = float(order.get('price', 0))
            qty = float(order.get('qty', 0))
            cum_exec_qty = float(order.get('cumExecQty', 0))
            
            # Calculate fill percentage
            fill_percentage = (cum_exec_qty / qty) if qty > 0 else 0.0
            
            # Determine if order can be replaced (< 50% filled)
            replacement_config = getattr(self.config.trading, 'order_replacement', None)
            protection_threshold = replacement_config.partially_filled_protection_threshold if replacement_config else 0.5
            can_be_replaced = fill_percentage < protection_threshold
            
            # Get actual trade data from database using order ID
            order_id = order.get('orderId')
            trade_data = self._get_trade_data_by_order_id(order_id) if order_id else None
            
            # Get side from order (needed for signal_data regardless of trade_data availability)
            side = order.get('side', 'Buy')
            
            if trade_data:
                # Use actual values from the original trade
                confidence = trade_data.get('confidence', 0.6)
                take_profit = trade_data.get('take_profit', 0)
                stop_loss = trade_data.get('stop_loss', 0)
                direction = trade_data.get('direction', 'LONG')
            else:
                # Fallback: try to get from order link ID or use conservative defaults
                confidence = 0.5  # Conservative confidence for unknown orders
                
                # Use conservative 1% TP/SL if we can't find the original trade data
                if side == 'Buy':
                    take_profit = entry_price * 1.01  # 1% profit target
                    stop_loss = entry_price * 0.99   # 1% stop loss
                    direction = 'LONG'
                else:
                    take_profit = entry_price * 0.99  # 1% profit target
                    stop_loss = entry_price * 1.01   # 1% stop loss
                    direction = 'SHORT'
                
                self.logger.warning(f"Could not find original trade data for order {order.get('orderId')}, using conservative estimates")
            
            # Calculate RR ratio
            rr_ratio = self.trader.calculate_risk_reward_ratio(
                entry_price, take_profit, stop_loss, direction, symbol
            )
            
            # Calculate score with time decay and age-based penalty
            base_score = self.trader.calculate_trade_score(
                confidence=confidence,
                rr_ratio=rr_ratio,
                time_elapsed_hours=time_elapsed_hours,
                current_price=current_price,
                intended_entry_price=entry_price
            )
            
            # Apply age-based penalty for orders older than 3 bars
            score = self._apply_age_penalty(base_score, time_elapsed_hours, trade_data, timeframe)
            
            # Create signal data for compatibility
            signal_data = {
                'symbol': symbol,
                'entry_price': entry_price,
                'take_profit': take_profit,
                'stop_loss': stop_loss,
                'direction': direction,
                'confidence': confidence,
                'side': side,
                'qty': qty
            }
            
            return TradeCandidate(
                signal_data=signal_data,
                score=score,
                symbol=symbol,
                is_existing_order=True,
                order_id=order.get('orderId'),
                fill_percentage=fill_percentage,
                can_be_replaced=can_be_replaced,
                created_at=created_dt
            )
            
        except Exception as e:
            self.logger.error(f"Error creating candidate from order: {e}")
            return None
    
    def _get_trade_data_by_order_id(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve original trade data from database using order ID."""
        try:
            if not self.data_agent or not order_id:
                return None
            
            # Query the trades table for this order ID using the correct method
            trade = self.data_agent.get_trade_by_order_id(order_id)
            
            if trade:
                
                # Return the relevant trade data
                return {
                    'confidence': trade.get('confidence', 0.6),
                    'take_profit': trade.get('take_profit', 0),
                    'stop_loss': trade.get('stop_loss', 0),
                    'direction': trade.get('direction', 'LONG'),
                    'entry_price': trade.get('entry_price', 0),
                    'symbol': trade.get('symbol', ''),
                    'side': trade.get('side', 'Buy')
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error retrieving trade data for order {order_id}: {e}")
            return None
    
    def _apply_age_penalty(self, base_score: float, time_elapsed_hours: float, trade_data: Optional[Dict[str, Any]], current_timeframe: Optional[str] = None) -> float:
        """
        Apply age-based penalty to orders older than 3 bars.
        
        Args:
            base_score: The original calculated score
            time_elapsed_hours: Hours since order creation
            trade_data: Original trade data containing timeframe info
            current_timeframe: Current timeframe being processed
            
        Returns:
            Adjusted score with age penalty applied
        """
        try:
            # Get timeframe from multiple sources (priority order)
            timeframe = current_timeframe  # First priority: current processing timeframe
            
            if not timeframe and trade_data:
                timeframe = trade_data.get('timeframe')  # Second priority: original trade data
            
            if not timeframe:
                # Default to 1h if we can't determine timeframe
                timeframe = "1h"
                self.logger.warning(f"No timeframe found for age penalty calculation, using default: {timeframe}")
            
            # Convert timeframe to hours
            timeframe_hours = self._timeframe_to_hours(timeframe)
            
            # Calculate age in bars (timeframe periods)
            age_in_bars = time_elapsed_hours / timeframe_hours if timeframe_hours > 0 else 0
            
            # Configuration for age penalty
            replacement_config = getattr(self.config.trading, 'order_replacement', None)
            if replacement_config:
                max_age_bars = replacement_config.max_age_bars
                age_penalty_factor = replacement_config.age_penalty_factor
            else:
                # Fallback defaults
                max_age_bars = 3
                age_penalty_factor = 0.1
            
            # Apply penalty if order is older than max_age_bars
            if age_in_bars > max_age_bars:
                # Calculate penalty: older orders get progressively lower scores
                # After 3 bars, score is reduced to age_penalty_factor of original
                excess_bars = age_in_bars - max_age_bars
                penalty_multiplier = max(age_penalty_factor, 1.0 / (1.0 + excess_bars))
                adjusted_score = base_score * penalty_multiplier
                
                self.logger.info(f"Age penalty applied: {age_in_bars:.1f} bars old (>{max_age_bars} bars), "
                               f"score: {base_score:.3f} -> {adjusted_score:.3f} "
                               f"(penalty: {penalty_multiplier:.3f})")
                
                return adjusted_score
            else:
                # No penalty for orders within age limit
                return base_score
                
        except Exception as e:
            self.logger.error(f"Error applying age penalty: {e}")
            return base_score  # Return original score on error
    
    def _timeframe_to_hours(self, timeframe: str) -> float:
        """
        Convert timeframe string to hours.
        
        Args:
            timeframe: Timeframe string (e.g., "1m", "5m", "1h", "4h", "1d")
            
        Returns:
            Hours as float
        """
        try:
            timeframe = timeframe.lower().strip()
            
            if timeframe.endswith('m'):
                # Minutes
                minutes = int(timeframe[:-1])
                return minutes / 60.0
            elif timeframe.endswith('h'):
                # Hours
                return float(timeframe[:-1])
            elif timeframe.endswith('d'):
                # Days
                days = int(timeframe[:-1])
                return days * 24.0
            elif timeframe.endswith('w'):
                # Weeks
                weeks = int(timeframe[:-1])
                return weeks * 24.0 * 7.0
            else:
                # Try to parse as minutes if no suffix
                try:
                    minutes = int(timeframe)
                    return minutes / 60.0
                except ValueError:
                    self.logger.warning(f"Could not parse timeframe: {timeframe}, defaulting to 1 hour")
                    return 1.0
                    
        except Exception as e:
            self.logger.error(f"Error parsing timeframe {timeframe}: {e}")
            return 1.0  # Default to 1 hour


class OrderReplacementEngine:
    """Executes order replacement decisions with state validation."""
    
    def __init__(self, trader, position_manager, config, logger):
        self.trader = trader
        self.position_manager = position_manager
        self.config = config
        self.logger = logger
        self.last_replacement_time = {}  # Track cooldowns per symbol
    
    def evaluate_replacement_strategy(self, ranked_candidates: List[TradeCandidate], 
                                    max_slots: int) -> ReplacementDecision:
        """Evaluate the optimal replacement strategy for all candidates."""
        try:
            replacement_config = getattr(self.config.trading, 'order_replacement', None)
            
            if not (replacement_config and replacement_config.enable_intelligent_replacement):
                return ReplacementDecision(
                    action='skip',
                    reason="Intelligent replacement disabled in configuration"
                )
            
            # Separate existing orders and new signals
            existing_orders = [c for c in ranked_candidates if c.is_existing_order]
            new_signals = [c for c in ranked_candidates if not c.is_existing_order]
            
            # Get available slots
            available_slots = max_slots - len([o for o in existing_orders if not o.can_be_replaced])
            
            if available_slots >= len(new_signals):
                # We have enough slots for all new signals
                return ReplacementDecision(
                    action='execute',
                    reason=f"Sufficient slots available ({available_slots}/{max_slots})"
                )
            
            # Need to find replacement opportunities
            replacement_pairs = self._find_optimal_replacements(
                new_signals, existing_orders, max_slots
            )
            
            if not replacement_pairs:
                # No replacement opportunities, but check if we can still execute some signals
                if available_slots > 0:
                    # Execute as many new signals as we have slots for
                    return ReplacementDecision(
                        action='execute',
                        reason=f"No replacements needed, executing {min(available_slots, len(new_signals))} signals with available slots"
                    )
                else:
                    return ReplacementDecision(
                        action='skip',
                        reason="No available slots and no beneficial replacement opportunities found"
                    )
            
            return ReplacementDecision(
                action='replace',
                replacement_pairs=replacement_pairs,
                reason=f"Found {len(replacement_pairs)} beneficial replacement(s)"
            )
            
        except Exception as e:
            self.logger.error(f"Error evaluating replacement strategy: {e}")
            return ReplacementDecision(
                action='skip',
                reason=f"Error in evaluation: {str(e)}"
            )
    
    def _find_optimal_replacements(self, new_signals: List[TradeCandidate],
                                 existing_orders: List[TradeCandidate],
                                 max_slots: int) -> List[Tuple[TradeCandidate, TradeCandidate]]:
        """Find optimal replacement pairs (new_signal, old_order)."""
        try:
            replacement_config = getattr(self.config.trading, 'order_replacement', None)
            if replacement_config:
                min_improvement = replacement_config.min_score_improvement_threshold
                max_replacements = replacement_config.max_replacements_per_batch
            else:
                # Fallback defaults
                min_improvement = 0.15
                max_replacements = 3
            
            # Find replaceable orders (sorted by score, lowest first)
            replaceable_orders = [
                order for order in existing_orders 
                if order.can_be_replaced and self._check_replacement_cooldown(order.symbol)
            ]
            replaceable_orders.sort(key=lambda x: x.score)
            
            # Find new signals that could benefit from replacement (sorted by score, highest first)
            new_signals.sort(key=lambda x: x.score, reverse=True)
            
            replacement_pairs = []
            used_orders = set()
            
            for new_signal in new_signals:
                if len(replacement_pairs) >= max_replacements:
                    break
                
                # Find the best replacement candidate for this signal
                best_replacement = None
                best_improvement = 0
                
                for old_order in replaceable_orders:
                    if old_order.order_id in used_orders:
                        continue
                    
                    score_improvement = new_signal.score - old_order.score
                    
                    if score_improvement >= min_improvement and score_improvement > best_improvement:
                        best_replacement = old_order
                        best_improvement = score_improvement
                
                if best_replacement:
                    replacement_pairs.append((new_signal, best_replacement))
                    used_orders.add(best_replacement.order_id)
                    self.logger.info(f"Planned replacement: {best_replacement.symbol} "
                                   f"(score: {best_replacement.score:.3f}) -> "
                                   f"{new_signal.symbol} (score: {new_signal.score:.3f}), "
                                   f"improvement: {best_improvement:.3f}")
            
            return replacement_pairs
            
        except Exception as e:
            self.logger.error(f"Error finding optimal replacements: {e}")
            return []
    
    def _check_replacement_cooldown(self, symbol: str) -> bool:
        """Check if enough time has passed since last replacement for this symbol."""
        try:
            replacement_config = getattr(self.config.trading, 'order_replacement', None)
            cooldown_minutes = replacement_config.replacement_cooldown_minutes if replacement_config else 5
            
            if symbol not in self.last_replacement_time:
                return True
            
            time_since_last = (datetime.now(timezone.utc) - self.last_replacement_time[symbol]).total_seconds() / 60
            return time_since_last >= cooldown_minutes
            
        except Exception as e:
            self.logger.error(f"Error checking replacement cooldown for {symbol}: {e}")
            return True  # Allow replacement on error
    
    def execute_replacements(self, replacement_pairs: List[Tuple[TradeCandidate, TradeCandidate]]) -> Dict[str, Any]:
        """Execute the planned replacements."""
        try:
            successful_replacements = 0
            failed_replacements = 0
            replacement_details = []
            
            for new_candidate, old_order in replacement_pairs:
                try:
                    # Get order details for logging
                    old_entry_price = old_order.signal_data.get('entry_price', 'N/A')
                    new_entry_price = new_candidate.signal_data.get('entry_price', 'N/A')
                    score_improvement = new_candidate.score - old_order.score
                    improvement_percentage = (score_improvement / old_order.score * 100) if old_order.score > 0 else 0
                    
                    # Enhanced replacement logging - before cancellation
                    self.logger.info(f"🔄 REPLACING ORDER: {old_order.symbol} (ID: {old_order.order_id or 'N/A'})")
                    self.logger.info(f"   OLD ORDER: Entry=${old_entry_price}, Score={old_order.score:.3f}")
                    self.logger.info(f"   NEW ORDER: {new_candidate.symbol}, Entry=${new_entry_price}, Score={new_candidate.score:.3f}")
                    self.logger.info(f"   IMPROVEMENT: +{score_improvement:.3f} ({improvement_percentage:+.1f}%)")
                    
                    # Cancel the old order using centralized function
                    cancel_result = cancel_order_with_verification(
                        trader=self.trader,
                        symbol=old_order.symbol,
                        order_id=old_order.order_id,
                        max_retries=3,
                        retry_delay=0.5
                    )
                    
                    if cancel_result.get('error'):
                        self.logger.error(f"Failed to cancel order {old_order.order_id}: {cancel_result['error']}")
                        failed_replacements += 1
                        continue
                    
                    # Mark the trade as cancelled in database
                    if self.position_manager.data_agent and old_order.order_id:
                        self.position_manager.data_agent.mark_trade_as_cancelled(old_order.order_id)
                        self.logger.info(f"Marked trade {old_order.order_id} as cancelled in database")
                    
                    # Update replacement time tracking
                    self.last_replacement_time[old_order.symbol] = datetime.now(timezone.utc)
                    
                    # Mark the old trade as cancelled for replacement in database
                    if hasattr(self.position_manager, 'handle_trade_replacement'):
                        self.position_manager.handle_trade_replacement(
                            old_order.trade_id or old_order.order_id,
                            new_candidate.signal_data
                        )
                    
                    successful_replacements += 1
                    replacement_details.append({
                        'old_symbol': old_order.symbol,
                        'new_symbol': new_candidate.symbol,
                        'old_score': old_order.score,
                        'new_score': new_candidate.score,
                        'improvement': new_candidate.score - old_order.score
                    })
                    
                    self.logger.info(f"✅ Successfully replaced {old_order.symbol} order with {new_candidate.symbol} signal")
                    
                except Exception as e:
                    self.logger.error(f"Error executing replacement for {old_order.symbol}: {e}")
                    failed_replacements += 1
            
            return {
                'successful_replacements': successful_replacements,
                'failed_replacements': failed_replacements,
                'replacement_details': replacement_details,
                'total_attempted': len(replacement_pairs)
            }
            
        except Exception as e:
            self.logger.error(f"Error executing replacements: {e}")
            return {
                'successful_replacements': 0,
                'failed_replacements': len(replacement_pairs),
                'error': str(e)
            }


class IntelligentOrderReplacementSystem:
    """Main orchestrator for the intelligent order replacement system."""
    
    def __init__(self, trader, position_manager, config, logger=None, data_agent=None):
        self.trader = trader
        self.position_manager = position_manager
        self.config = config
        self.logger = logger or logging.getLogger(__name__)

        # Get data_agent from position_manager if not provided
        if data_agent is None and hasattr(position_manager, 'data_agent'):
            data_agent = position_manager.data_agent
        self.data_agent = data_agent
        
        # Initialize components
        self.trade_collector = TradeCollector(config, self.logger)
        self.global_ranker = GlobalTradeRanker(trader, config, self.logger, data_agent)
        self.replacement_engine = OrderReplacementEngine(trader, position_manager, config, self.logger)
    
    def add_signal_for_evaluation(self, signal_data: Dict[str, Any]) -> None:
        """Add a new signal to the evaluation queue."""
        self.trade_collector.add_signal(signal_data)
    
    def should_process_batch(self) -> bool:
        """Check if signals should be processed as a batch."""
        # If batching is disabled, we should still process signals individually
        if not self.trade_collector.enable_batching:
            self.logger.info("🔧 Batching disabled - will process signals individually")
            return True  # Allow processing even without batch
        
        return self.trade_collector.is_batch_ready()
    
    def _calculate_risk_allocation_multiplier(self, total_active_trades: int, max_slots: int) -> float:
        """
        Calculate risk allocation multiplier to deploy full available risk capital.
        
        When we have fewer trades than max slots, we increase allocation per trade
        to ensure full risk capital deployment.
        
        Args:
            total_active_trades: Total number of trades that will be active (existing + new)
            max_slots: Maximum number of concurrent trades allowed
            
        Returns:
            Risk allocation multiplier (1.0 = normal allocation, >1.0 = increased allocation)
        """
        # Check configuration for dynamic risk allocation
        if not self.config.trading.enable_dynamic_risk_allocation:
            self.logger.info("Dynamic risk allocation disabled. Returning multiplier of 1.0.")
            return 1.0

        if total_active_trades <= 0 or max_slots <= 0:
            return 1.0
        
        # If we have fewer trades than max slots, increase allocation per trade
        # to deploy the full available risk capital
        if total_active_trades < max_slots:
            return max_slots / total_active_trades
        
        # If we're at or above max slots, use normal allocation
        return 1.0
    
    def process_intelligent_recommendations(self, max_slots: int, direct_signals: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Process all pending signals and return intelligent recommendations."""
        try:
            # Get pending signals - either from batch or directly provided
            if direct_signals is not None:
                # Use directly provided signals (when batching is disabled)
                new_signals = direct_signals
                self.logger.info(f"🔧 Processing {len(new_signals)} direct signals (batching disabled)")
            else:
                # Get signals from batch collector (when batching is enabled)
                new_signals = self.trade_collector.get_batch_and_clear()
                self.logger.info(f"🔧 Processing {len(new_signals)} batched signals")
            
            if not new_signals:
                return {
                    'action': 'no_signals',
                    'recommended_signals': [],
                    'replacements': [],
                    'message': 'No pending signals to process'
                }
            
            # Get existing orders
            existing_orders = self._get_existing_orders()
            self.logger.info(f"🔍 Found {len(existing_orders)} existing orders for replacement evaluation")
            
            # Log existing orders for debugging
            for order in existing_orders:
                symbol = order.get('symbol', 'Unknown')
                order_id = order.get('orderId', 'Unknown')
                status = order.get('orderStatus', 'Unknown')
                side = order.get('side', 'Unknown')
                self.logger.info(f"  📋 Existing order: {symbol} {side} (ID: {order_id}, Status: {status})")
            
            # Log new signals for comparison
            self.logger.info(f"🆕 Processing {len(new_signals)} new signals:")
            for signal in new_signals:
                symbol = signal.get('symbol', 'Unknown')
                direction = signal.get('direction', 'Unknown')
                self.logger.info(f"  📈 New signal: {symbol} {direction}")
            
            # Calculate total trades that will be active (existing + new)
            total_active_trades = len(existing_orders) + len(new_signals)
            
            # Calculate risk allocation adjustment for full capital deployment
            risk_allocation_multiplier = self._calculate_risk_allocation_multiplier(
                total_active_trades, max_slots
            )
            
            self.logger.info(f"💰 Risk allocation adjustment: {total_active_trades} total trades, "
                           f"multiplier: {risk_allocation_multiplier:.2f}x")
            
            # Apply risk allocation adjustment to all new signals
            for signal in new_signals:
                signal['risk_allocation_multiplier'] = risk_allocation_multiplier
            
            # Rank all candidates
            ranked_candidates = self.global_ranker.rank_all_trades(new_signals, existing_orders)
            self.logger.info(f"🏆 Ranked candidates: {len(ranked_candidates)} total candidates")
            
            # Evaluate replacement strategy
            replacement_decision = self.replacement_engine.evaluate_replacement_strategy(
                ranked_candidates, max_slots
            )
            
            if replacement_decision.action == 'execute':
                # Simple case: execute all new signals
                return {
                    'action': 'execute_all',
                    'recommended_signals': new_signals,
                    'replacements': [],
                    'message': replacement_decision.reason
                }
            
            elif replacement_decision.action == 'replace':
                # Execute replacements and return remaining signals
                if replacement_decision.replacement_pairs:
                    replacement_result = self.replacement_engine.execute_replacements(
                        replacement_decision.replacement_pairs
                    )
                else:
                    replacement_result = {
                        'successful_replacements': 0,
                        'failed_replacements': 0,
                        'replacement_details': [],
                        'total_attempted': 0
                    }
                
                # Get signals that should be executed after replacements
                executed_replacements = replacement_result.get('successful_replacements', 0)
                remaining_signals = new_signals[:executed_replacements]  # Execute top signals
                
                return {
                    'action': 'replace_and_execute',
                    'recommended_signals': remaining_signals,
                    'replacements': replacement_result,
                    'message': f"Executed {executed_replacements} replacements"
                }
            
            else:
                # Skip all signals
                return {
                    'action': 'skip_all',
                    'recommended_signals': [],
                    'replacements': [],
                    'message': replacement_decision.reason
                }
                
        except Exception as e:
            self.logger.error(f"Error processing intelligent recommendations: {e}")
            return {
                'action': 'error',
                'recommended_signals': [],
                'replacements': [],
                'error': str(e)
            }
    
    def _get_existing_orders(self) -> List[Dict[str, Any]]:
        """Get all existing open orders from the exchange using centralized counting function."""
        try:
            self.logger.info("🔍 Fetching existing orders from exchange using centralized counting...")

            # Use centralized counting function - ONLY source of truth for order counting
            count_result = count_open_positions_and_orders(trader=self.trader)

            if count_result.get('status') == 'error':
                self.logger.warning(f"Could not get existing orders: {count_result.get('message', 'Unknown error')}")
                return []

            # Get raw orders from the centralized function
            orders_list = count_result.get('raw_orders', [])
            self.logger.info(f"📋 Retrieved {len(orders_list)} total orders from centralized counting function")

            # Log all orders for debugging
            for order in orders_list:
                symbol = order.get('symbol', 'Unknown')
                status = order.get('orderStatus', 'Unknown')
                side = order.get('side', 'Unknown')
                order_id = order.get('orderId', 'Unknown')
                self.logger.info(f"  📄 Order: {symbol} {side} (Status: {status}, ID: {order_id})")

            # Filter for active orders only (maintain existing logic for compatibility)
            active_orders = [
                order for order in orders_list
                if order.get('orderStatus') in ['New', 'PartiallyFilled']
            ]

            self.logger.info(f"✅ Found {len(active_orders)} existing active orders (filtered from {len(orders_list)} total)")
            return active_orders

        except Exception as e:
            self.logger.error(f"Error getting existing orders: {e}")
            return []
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current status of the replacement system."""
        try:
            return {
                'pending_signals': len(self.trade_collector.pending_signals),
                'batch_ready': self.trade_collector.is_batch_ready(),
                'last_batch_time': self.trade_collector.last_batch_time.isoformat(),
                'replacement_cooldowns': {
                    symbol: time.isoformat()
                    for symbol, time in self.replacement_engine.last_replacement_time.items()
                }
            }
        except Exception as e:
            self.logger.error(f"Error getting system status: {e}")
            return {
                'error': str(e),
                'pending_signals': 0,
                'batch_ready': False,
                'last_batch_time': None,
                'replacement_cooldowns': {}
            }
    
    def execute_global_slot_optimization(self, trade_signals: List[Dict[str, Any]],
                                       timeframe: str, dry_run: bool = False) -> Dict[str, Any]:
        """
        Execute global slot optimization - ensures only one order per symbol+timeframe
        and keeps only the top N orders that fit within available slots.

        This implements the enhanced STEP 3 logic:
        1. **NEW**: Cleanup existing orders per symbol (cancel duplicates)
        2. Get all existing entry orders from exchange
        3. Combine with new trade signals
        4. Rank ALL orders by score
        5. Keep only top N orders that fit within available slots
        6. Cancel existing orders that fall outside top N
        7. Ensure only one order per symbol+timeframe

        Args:
            trade_signals: New trade signals to process
            timeframe: Current timeframe being processed
            dry_run: If True, don't execute actual cancellations

        Returns:
            Dict with optimization results
        """
        try:
            self.logger.info(f"🔍 Starting global slot optimization for {len(trade_signals)} new signals...")

            # **NEW STEP 0**: Cleanup existing orders per symbol before optimization
            cleanup_result = self._cleanup_existing_orders_per_symbol(trade_signals, dry_run)
            cancelled_cleanup_orders = cleanup_result.get('cancelled_orders', 0)
            self.logger.info(f"🧹 Pre-optimization cleanup: cancelled {cancelled_cleanup_orders} duplicate orders")

            # Step 1: Get available slots and existing orders
            max_slots = getattr(self.trader, 'max_slots', 3)
            if hasattr(self.trader, 'risk_manager') and hasattr(self.trader.risk_manager, 'max_slots'):
                max_slots = self.trader.risk_manager.max_slots

            # Get existing entry orders (reuse existing method)
            existing_orders = self._get_existing_entry_orders()

            # Use centralized counting function for ALL position and order counting
            count_result = count_open_positions_and_orders(trader=self.trader)
            current_positions = count_result.get('active_positions_count', 0)
            current_open_orders = count_result.get('open_entry_orders_count', 0)

            # Calculate total active trades (positions + open orders)
            total_active_trades = current_positions + current_open_orders

            # Calculate how many more trades we can have (positions + entry orders consume slots)
            available_slots = max(0, max_slots - current_positions - current_open_orders)

            # Calculate if we can still replace orders (different from available slots for new orders)
            can_replace_orders = (current_positions + current_open_orders) <= max_slots

            self.logger.info(f"📊 Slot analysis: {max_slots} max, {current_positions} positions, "
                           f"{current_open_orders} open orders, {total_active_trades} total active trades, "
                           f"{available_slots} slots available")
            self.logger.info(f"📋 Found {len(existing_orders)} existing entry orders")
            
            # Step 2: Create candidates using existing GlobalTradeRanker methods
            all_candidates = []
            
            # Add existing orders as candidates (reuse existing method)
            for order in existing_orders:
                candidate = self.global_ranker._create_candidate_from_order(order, timeframe)
                if candidate:
                    candidate.timeframe = timeframe  # Set timeframe for deduplication
                    all_candidates.append(candidate)
            
            # Add new signals as candidates (reuse existing method)
            for signal in trade_signals:
                candidate = self.global_ranker._create_candidate_from_signal(signal)
                if candidate:
                    candidate.timeframe = timeframe  # Set timeframe for deduplication
                    all_candidates.append(candidate)
            
            self.logger.info(f"🎯 Created {len(all_candidates)} total candidates ({len(existing_orders)} existing + {len(trade_signals)} new)")
            
            # Step 3: Ensure only one order per symbol
            deduplicated_candidates = self._deduplicate_by_symbol(all_candidates)
            self.logger.info(f"🔄 After deduplication: {len(deduplicated_candidates)} candidates")
            
            # Step 4: Rank all candidates by score (highest first)
            deduplicated_candidates.sort(key=lambda x: x.score, reverse=True)
            
            # Step 5: Determine which candidates to keep (respecting max concurrent trades constraint)
            # We can have at most (max_slots - total_active_trades) NEW orders
            max_orders_allowed = available_slots

            # Handle case where we already exceed max slots (negative orders allowed)
            if max_orders_allowed <= 0 and len(trade_signals) > 0:
                self.logger.warning(f"⚠️ Trade limit reached: {total_active_trades} active trades ≥ {max_slots} max slots")
                self.logger.info(f"🚫 No new orders allowed - filtering out all trade signals to enforce limit")
                max_orders_allowed = 0
                # Enforce the limit by filtering out all trade signals
                trade_signals = []
            elif 0 < max_orders_allowed < len(trade_signals):
                # We have some slots available but not enough for all signals
                self.logger.info(f"📊 Limiting trade signals from {len(trade_signals)} to {max_orders_allowed} to respect slot limit")
                # Sort signals by priority (assuming they're already sorted by score) and take only allowed amount
                trade_signals = trade_signals[:max_orders_allowed]

            # Split candidates into existing and new
            existing_candidates = [c for c in deduplicated_candidates if c.is_existing_order]
            new_candidates = [c for c in deduplicated_candidates if not c.is_existing_order]

            # Apply proper constraint enforcement based on user requirements:
            # 1. If open_positions = max_slots → No new trades, skip cycle
            # 2. If open_positions + open_entry_orders <= max_slots → Check for better signals and REPLACE entry orders
            # 3. If open_positions + open_entry_orders > max_slots → No new orders and cancel existing entry orders

            total_current_occupied = current_positions + current_open_orders

            if current_positions >= max_slots:
                # Case 1: No new trades allowed - positions already at max
                self.logger.warning(f"🚫 Positions at max capacity: {current_positions}/{max_slots} - no new trades allowed")
                candidates_to_keep = existing_candidates  # Keep existing orders only
                candidates_to_cancel = []  # Don't cancel existing orders
                # Filter out all new signals since we can't add more positions
                final_signals = []

            elif not can_replace_orders:
                # Case 3: Cannot replace orders - total capacity exceeded
                self.logger.warning(f"⚠️ Cannot replace orders: {total_current_occupied} total occupied > {max_slots} max slots")
                self.logger.info(f"🗑️ Need to cancel {total_current_occupied - max_slots} excess entry orders")

                # Keep only the top candidates up to max_slots
                candidates_to_keep = deduplicated_candidates[:max_slots]
                candidates_to_cancel = deduplicated_candidates[max_slots:]

                # No new signals can be added when we're over capacity
                final_signals = []

            else:
                # Case 2: We can replace entry orders with better signals
                self.logger.info(f"✅ Can replace orders: {total_current_occupied}/{max_slots} occupied - looking for better signals")

                # Keep the highest scoring candidates up to max_slots
                candidates_to_keep = deduplicated_candidates[:max_slots]
                candidates_to_cancel = deduplicated_candidates[max_slots:]

                # Include new signals that made it into the keep list
                new_signals_in_keep = [c for c in candidates_to_keep if not c.is_existing_order]
                final_signals = [c.signal_data.copy() for c in new_signals_in_keep]

                self.logger.info(f"🎯 Found {len(new_signals_in_keep)} better signal(s) to replace existing orders")

            self.logger.info(f"🎯 Constraint enforcement: max {max_slots} total trades allowed")

            self.logger.info(f"📈 Optimization plan:")
            self.logger.info(f"   🎯 Keep top {len(candidates_to_keep)} candidates")
            self.logger.info(f"   ❌ Cancel {len(candidates_to_cancel)} lower-scored candidates")

            # Step 6: Identify orders to cancel and new signals to execute
            orders_to_cancel = [c for c in candidates_to_cancel if c.is_existing_order]
            new_signals_to_execute = [c for c in candidates_to_keep if not c.is_existing_order]
            
            # Limit new signals to available slots
            if len(new_signals_to_execute) > available_slots:
                # We can only execute as many new signals as we have available slots
                new_signals_to_execute = new_signals_to_execute[:available_slots]
                self.logger.info(f"🎯 Limited new signals to {available_slots} available slots")
            
            cancelled_orders = []
            
            # Step 7: Execute cancellations
            if orders_to_cancel:
                self.logger.info(f"🗑️ Cancelling {len(orders_to_cancel)} lower-scored existing orders...")
                for candidate in orders_to_cancel:
                    if not dry_run:
                        cancel_result = cancel_order_with_verification(
                            trader=self.trader,
                            symbol=candidate.symbol,
                            order_id=candidate.order_id,
                            max_retries=3,
                            retry_delay=0.5
                        )

                        if not cancel_result["success"]:
                            self.logger.error(f"Failed to cancel order {candidate.order_id}: {cancel_result.get('message', 'Unknown error')}")
                        else:
                            # Mark the trade as cancelled in database
                            if self.position_manager.data_agent and candidate.order_id:
                                self.position_manager.data_agent.mark_trade_as_cancelled(candidate.order_id)
                                self.logger.info(f"Marked trade {candidate.order_id} as cancelled in database")

                            self.logger.info(f"✅ Cancelled {candidate.symbol} order (score: {candidate.score:.3f})")
                            cancelled_orders.append({
                                'symbol': candidate.symbol,
                                'order_id': candidate.order_id,
                                'score': candidate.score,
                                'reason': 'Lower score in global optimization'
                            })
                    else:
                        self.logger.info(f"🔍 DRY RUN: Would cancel {candidate.symbol} order (score: {candidate.score:.3f})")
                        cancelled_orders.append({
                            'symbol': candidate.symbol,
                            'order_id': candidate.order_id,
                            'score': candidate.score,
                            'reason': 'Lower score in global optimization (DRY RUN)'
                        })
            
            # Step 8: Prepare final signals for execution with scores preserved
            final_signals = []
            for c in new_signals_to_execute:
                signal_with_score = c.signal_data.copy()  # Create a copy to avoid modifying original
                signal_with_score['score'] = c.score  # Add the calculated score
                final_signals.append(signal_with_score)
            
            # Log optimization results
            self.logger.info(f"✅ Global slot optimization complete:")
            self.logger.info(f"   📊 Final signals to execute: {len(final_signals)}")
            self.logger.info(f"   ❌ Orders cancelled: {len(cancelled_orders)}")
            
            if final_signals:
                self.logger.info(f"   🎯 Signals selected for execution:")
                for i, candidate in enumerate(new_signals_to_execute):  # Show all final signals
                    direction = candidate.signal_data.get('direction', candidate.signal_data.get('recommendation', 'UNKNOWN'))
                    entry_price = candidate.signal_data.get('entry_price', 'N/A')
                    self.logger.info(f"      ✅ {candidate.symbol} {direction} @ {entry_price} (score: {candidate.score:.3f})")
            
            return {
                'success': True,
                'final_signals': final_signals,
                'cancelled_orders': cancelled_orders,
                'total_candidates': len(all_candidates),
                'deduplicated_candidates': len(deduplicated_candidates),
                'kept_candidates': len(candidates_to_keep),
                'optimization_summary': {
                    'max_slots': max_slots,
                    'current_positions': current_positions,
                    'current_open_orders': current_open_orders,
                    'total_active_trades': total_active_trades,
                    'available_slots': available_slots,
                    'existing_orders': len(existing_orders),
                    'new_signals': len(trade_signals),
                    'final_signals': len(final_signals),
                    'cancelled_orders': len(cancelled_orders)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error in global slot optimization: {e}")
            return {
                'success': False,
                'error': str(e),
                'final_signals': trade_signals,  # Fallback to original signals
                'cancelled_orders': []
            }
    
    def _get_existing_entry_orders(self) -> List[Dict[str, Any]]:
        """Get existing entry orders (excludes TP/SL orders)."""
        try:
            # Reuse existing method but filter for entry orders only
            all_orders = self._get_existing_orders()
            
            # Filter out TP/SL orders - keep only entry orders
            entry_orders = []
            for order in all_orders:
                # Check if this is an entry order (not TP/SL)
                stop_order_type = order.get('stopOrderType', '')
                order_type = order.get('orderType', '')
                
                # Skip TP/SL orders
                if stop_order_type in ['TakeProfit', 'StopLoss'] or order_type in ['TakeProfit', 'StopLoss']:
                    continue
                
                entry_orders.append(order)
            
            self.logger.info(f"📋 Filtered to {len(entry_orders)} entry orders (from {len(all_orders)} total orders)")
            return entry_orders
            
        except Exception as e:
            self.logger.error(f"Error getting existing entry orders: {e}")
            return []
    
    def _cleanup_existing_orders_per_symbol(self, trade_signals: List[Dict[str, Any]], dry_run: bool = False) -> Dict[str, Any]:
        """
        Check for existing entry orders for each symbol in trade signals and cancel them.
        This ensures only one order per symbol before optimization begins.

        Args:
            trade_signals: List of new trade signals to check
            dry_run: If True, don't execute actual cancellations

        Returns:
            Dict with cleanup results:
            {
                "cancelled_orders": int,  # Number of orders cancelled
                "symbols_processed": int,  # Number of symbols checked
                "errors": List[str]       # Any errors encountered
            }
        """
        try:
            cancelled_orders = 0
            symbols_processed = 0
            errors = []

            # Extract unique symbols from trade signals
            symbols_to_check = set()
            for signal in trade_signals:
                symbol = signal.get('symbol')
                if symbol:
                    symbols_to_check.add(symbol)

            self.logger.info(f"🧹 Starting cleanup for {len(symbols_to_check)} symbols from {len(trade_signals)} signals")

            # Use centralized counting function to get current exchange state
            count_result = count_open_positions_and_orders(trader=self.trader)

            if count_result.get('status') == 'error':
                error_msg = f"Could not get exchange data for cleanup: {count_result.get('message', 'Unknown error')}"
                self.logger.error(f"❌ {error_msg}")
                return {
                    "cancelled_orders": 0,
                    "symbols_processed": 0,
                    "errors": [error_msg]
                }

            # Get entry orders by symbol
            open_entry_orders = count_result.get('open_entry_orders', {})
            raw_orders = count_result.get('raw_orders', [])

            # Process each symbol
            for symbol in symbols_to_check:
                symbols_processed += 1
                existing_orders_count = open_entry_orders.get(symbol, 0)

                if existing_orders_count == 0:
                    self.logger.debug(f"✅ {symbol}: No existing entry orders")
                    continue

                self.logger.info(f"🔍 {symbol}: Found {existing_orders_count} existing entry order(s)")

                # Get the actual order details for this symbol
                symbol_orders = [order for order in raw_orders if order.get('symbol') == symbol]

                # Cancel all existing orders for this symbol
                for order in symbol_orders:
                    order_id = order.get('orderId')
                    order_link_id = order.get('orderLinkId')
                    order_status = order.get('orderStatus', 'Unknown')

                    # Skip if order is not active
                    if order_status not in ['New', 'PartiallyFilled']:
                        self.logger.debug(f"⏭️ {symbol}: Skipping {order_status} order {order_id or order_link_id}")
                        continue

                    if dry_run:
                        self.logger.info(f"🔍 DRY RUN: Would cancel {symbol} order {order_id or order_link_id}")
                        cancelled_orders += 1
                    else:
                        self.logger.info(f"🗑️ Cancelling duplicate order: {symbol} {order_id or order_link_id}")

                        # Cancel the order using centralized function
                        cancel_result = cancel_order_with_verification(
                            trader=self.trader,
                            symbol=symbol,
                            order_id=order_id,
                            order_link_id=order_link_id,
                            max_retries=3,
                            retry_delay=0.5
                        )

                        if cancel_result.get("success"):
                            self.logger.info(f"✅ Successfully cancelled {symbol} order")
                            cancelled_orders += 1

                            # Mark as cancelled in database if available
                            if self.position_manager.data_agent and order_id:
                                try:
                                    self.position_manager.data_agent.mark_trade_as_cancelled(order_id)
                                    self.logger.debug(f"✅ Marked {order_id} as cancelled in database")
                                except Exception as db_error:
                                    self.logger.warning(f"⚠️ Could not mark {order_id} as cancelled in database: {db_error}")
                        else:
                            error_msg = cancel_result.get("message", "Unknown cancellation error")
                            self.logger.error(f"❌ Failed to cancel {symbol} order: {error_msg}")
                            errors.append(f"Failed to cancel {symbol} order {order_id or order_link_id}: {error_msg}")

            self.logger.info(f"🧹 Cleanup complete: processed {symbols_processed} symbols, cancelled {cancelled_orders} orders")
            if errors:
                self.logger.warning(f"⚠️ Cleanup encountered {len(errors)} errors")

            return {
                "cancelled_orders": cancelled_orders,
                "symbols_processed": symbols_processed,
                "errors": errors
            }

        except Exception as e:
            error_msg = f"Error in cleanup_existing_orders_per_symbol: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            return {
                "cancelled_orders": 0,
                "symbols_processed": 0,
                "errors": [error_msg]
            }

    def _deduplicate_by_symbol(self, candidates: List[TradeCandidate]) -> List[TradeCandidate]:
        """
        Ensure only one order per symbol.
        When duplicates exist, keep the one with the highest score.
        """
        try:
            # Group candidates by symbol
            symbol_groups = {}

            for candidate in candidates:
                key = candidate.symbol

                if key not in symbol_groups:
                    symbol_groups[key] = []
                symbol_groups[key].append(candidate)

            # Keep only the highest-scored candidate from each group
            deduplicated = []
            duplicates_removed = 0

            for key, group in symbol_groups.items():
                if len(group) > 1:
                    # Sort by score (highest first) and keep the best one
                    group.sort(key=lambda x: x.score, reverse=True)
                    best_candidate = group[0]
                    duplicates_removed += len(group) - 1

                    self.logger.info(f"🔄 Deduplication for {key}: kept best score {best_candidate.score:.3f}, "
                                   f"removed {len(group) - 1} duplicate(s)")
                    deduplicated.append(best_candidate)
                else:
                    deduplicated.append(group[0])

            if duplicates_removed > 0:
                self.logger.info(f"🔄 Removed {duplicates_removed} duplicate candidates across symbols")

            return deduplicated

        except Exception as e:
            self.logger.error(f"Error in deduplication: {e}")
            return candidates  # Return original list on error
