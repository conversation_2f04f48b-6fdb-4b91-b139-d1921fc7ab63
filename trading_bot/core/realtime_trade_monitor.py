"""Real-time trade status monitoring and update system."""
import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.bybit_api_manager import BybitAPIManager
from trading_bot.core.realtime_trade_tracker import RealTimeTradeTracker


@dataclass
class TradeUpdate:
    """Represents a trade status update."""
    order_id: str
    symbol: str
    old_status: str
    new_status: str
    exchange_data: Dict[str, Any]
    timestamp: datetime


class RealTimeTradeMonitor:
    """
    Real-time trade monitoring system that continuously checks for trade status updates
    and immediately updates the database when changes are detected.
    """
    
    def __init__(self, data_agent: DataAgent, api_manager: BybitAPIManager, 
                 trade_tracker: RealTimeTradeTracker):
        self.data_agent = data_agent
        self.api_manager = api_manager
        self.trade_tracker = trade_tracker
        self.logger = logging.getLogger(__name__)
        
        # Monitoring state
        self._is_monitoring = False
        self._monitor_task = None
        self._last_check_time = None
        self._monitored_orders: Set[str] = set()
        self._update_queue: List[TradeUpdate] = []
        
        # Configuration
        self.check_interval_seconds = 10  # Check every 10 seconds
        self.max_order_age_hours = 24     # Stop monitoring orders older than 24 hours
        
    async def start_monitoring(self):
        """Start the real-time trade monitoring system."""
        if self._is_monitoring:
            self.logger.warning("Trade monitoring already running")
            return
        
        self._is_monitoring = True
        self.logger.info("🚀 Starting real-time trade monitoring")
        
        # Initialize monitored orders from database
        await self._initialize_monitored_orders()
        
        # Start monitoring task
        self._monitor_task = asyncio.create_task(self._monitoring_loop())
        
    async def stop_monitoring(self):
        """Stop the real-time trade monitoring system."""
        if not self._is_monitoring:
            return
        
        self._is_monitoring = False
        self.logger.info("🛑 Stopping real-time trade monitoring")
        
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
    async def _initialize_monitored_orders(self):
        """Initialize the set of orders to monitor from database."""
        try:
            # Get all open trades from database
            open_trades = self.data_agent.get_trades(status='open')
            submitted_trades = self.data_agent.get_trades(status='submitted')
            pending_trades = self.data_agent.get_trades(status='pending')
            
            all_active_trades = open_trades + submitted_trades + pending_trades
            
            for trade in all_active_trades:
                order_id = trade.get('order_id')
                if order_id:
                    self._monitored_orders.add(order_id)
            
            self.logger.info(f"Initialized monitoring for {len(self._monitored_orders)} active orders")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize monitored orders: {e}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop that checks for trade updates."""
        while self._is_monitoring:
            try:
                await self._check_trade_updates()
                await self._process_update_queue()
                await self._cleanup_old_orders()
                
                # Wait before next check
                await asyncio.sleep(self.check_interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.check_interval_seconds)
    
    async def _check_trade_updates(self):
        """Check for updates on all monitored trades."""
        if not self._monitored_orders:
            return
        
        try:
            # Get current status of all monitored orders
            for order_id in list(self._monitored_orders):
                await self._check_single_order_status(order_id)
                
        except Exception as e:
            self.logger.error(f"Error checking trade updates: {e}")
    
    async def _check_single_order_status(self, order_id: str):
        """Check the status of a single order."""
        try:
            # Get current trade record from database
            current_trade = self.data_agent.get_trade_by_order_id(order_id)
            if not current_trade:
                # Order not found in database, remove from monitoring
                self._monitored_orders.discard(order_id)
                return
            
            symbol = current_trade.get('symbol')
            current_status = current_trade.get('status')
            
            # Get order status from exchange
            response = self.api_manager.get_order_history(
                category="linear",
                symbol=symbol,
                orderId=order_id,
                limit=1
            )
            
            if response.get("retCode") != 0:
                return  # Skip if we can't get order status
            
            orders = response.get("result", {}).get("list", [])
            if not orders:
                return
            
            order_data = orders[0]
            exchange_status = order_data.get('orderStatus', '')
            
            # Map exchange status to our internal status
            new_status = self._map_exchange_status(exchange_status)
            
            if new_status and new_status != current_status:
                # Status changed - queue update
                update = TradeUpdate(
                    order_id=order_id,
                    symbol=symbol or "UNKNOWN",
                    old_status=current_status or "unknown",
                    new_status=new_status,
                    exchange_data=order_data,
                    timestamp=datetime.now(timezone.utc)
                )
                self._update_queue.append(update)
                
                self.logger.info(f"📊 Status change detected: {symbol} {order_id} {current_status} -> {new_status}")
                
                # If order is completed, remove from monitoring
                if new_status in ['filled', 'closed', 'cancelled']:
                    self._monitored_orders.discard(order_id)
                    
        except Exception as e:
            self.logger.error(f"Error checking order {order_id}: {e}")
    
    async def _process_update_queue(self):
        """Process all queued trade updates."""
        while self._update_queue:
            try:
                update = self._update_queue.pop(0)
                await self._apply_trade_update(update)
            except Exception as e:
                self.logger.error(f"Error processing trade update: {e}")
    
    async def _apply_trade_update(self, update: TradeUpdate):
        """Apply a single trade update to the database."""
        try:
            # Use the real-time trade tracker to update the trade
            success = self.trade_tracker.update_trade_status(
                order_id=update.order_id,
                new_status=update.new_status,
                exchange_data=update.exchange_data
            )
            
            if success:
                self.logger.info(f"✅ Trade updated: {update.symbol} {update.order_id} -> {update.new_status}")
            else:
                self.logger.error(f"❌ Failed to update trade: {update.order_id}")
                
        except Exception as e:
            self.logger.error(f"Error applying trade update for {update.order_id}: {e}")
    
    async def _cleanup_old_orders(self):
        """Remove old orders from monitoring to prevent memory leaks."""
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=self.max_order_age_hours)
            orders_to_remove = []
            
            for order_id in self._monitored_orders:
                trade = self.data_agent.get_trade_by_order_id(order_id)
                if trade:
                    created_at_str = trade.get('created_at')
                    if created_at_str:
                        try:
                            created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
                            if created_at < cutoff_time:
                                orders_to_remove.append(order_id)
                        except ValueError:
                            # Invalid timestamp format, remove from monitoring
                            orders_to_remove.append(order_id)
                else:
                    # Trade not found, remove from monitoring
                    orders_to_remove.append(order_id)
            
            for order_id in orders_to_remove:
                self._monitored_orders.discard(order_id)
            
            if orders_to_remove:
                self.logger.info(f"Cleaned up {len(orders_to_remove)} old orders from monitoring")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old orders: {e}")
    
    def add_order_to_monitoring(self, order_id: str):
        """Add a new order to the monitoring system."""
        if order_id and order_id not in self._monitored_orders:
            self._monitored_orders.add(order_id)
            self.logger.info(f"📊 Added order to monitoring: {order_id}")
    
    def remove_order_from_monitoring(self, order_id: str):
        """Remove an order from the monitoring system."""
        if order_id in self._monitored_orders:
            self._monitored_orders.discard(order_id)
            self.logger.info(f"📊 Removed order from monitoring: {order_id}")
    
    def _map_exchange_status(self, exchange_status: str) -> Optional[str]:
        """Map exchange order status to our internal status."""
        status_mapping = {
            'New': 'submitted',
            'PartiallyFilled': 'partially_filled',
            'Filled': 'filled',
            'Cancelled': 'cancelled',
            'Rejected': 'rejected',
            'PartiallyFilledCanceled': 'partially_filled'
        }
        return status_mapping.get(exchange_status)
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status and statistics."""
        return {
            'is_monitoring': self._is_monitoring,
            'monitored_orders_count': len(self._monitored_orders),
            'pending_updates_count': len(self._update_queue),
            'last_check_time': self._last_check_time.isoformat() if self._last_check_time else None,
            'check_interval_seconds': self.check_interval_seconds
        }
    
    async def force_check_order(self, order_id: str) -> bool:
        """Force an immediate check of a specific order."""
        try:
            await self._check_single_order_status(order_id)
            await self._process_update_queue()
            return True
        except Exception as e:
            self.logger.error(f"Error force checking order {order_id}: {e}")
            return False
