#!/usr/bin/env python3
"""Script to update comprehensive trading statistics with new granular holding period buckets."""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.analytics_utils import update_comprehensive_trading_stats

def main():
    """Update trading statistics with new granular holding period buckets."""
    print("🔄 Updating trading statistics with new granular holding period buckets...")
    
    try:
        # Initialize data agent
        data_agent = DataAgent()
        
        # Update comprehensive trading stats
        result = update_comprehensive_trading_stats(data_agent)
        
        if result["status"] == "success":
            print(f"✅ {result['message']}")
            print(f"📊 Updated {result['updated_records']} symbol/timeframe records")
            
            # Show some statistics about the new buckets
            if result.get('holding_period_stats'):
                buckets = list(result['holding_period_stats'].keys())
                print(f"⏱️  New holding period buckets created: {len(buckets)}")
                if buckets:
                    # Extract unique bucket names and sort them chronologically
                    bucket_names = list(set([b.split('_')[-1] for b in buckets]))
                    
                    # Define the correct chronological order
                    correct_order = [
                        '0-1h', '1-2h', '2-5h', '5-8h', '8-12h', '12-24h', 
                        '1-3d', '3-7d', '7d+'
                    ]
                    
                    # Sort bucket names according to correct order
                    sorted_buckets = []
                    for bucket in correct_order:
                        if bucket in bucket_names:
                            sorted_buckets.append(bucket)
                    
                    # Add any buckets not in the predefined order
                    remaining = [b for b in bucket_names if b not in correct_order]
                    sorted_buckets.extend(remaining)
                    
                    print("📋 Sample buckets (chronological order):")
                    for bucket in sorted_buckets[:10]:
                        print(f"   • {bucket}")
        else:
            print(f"❌ Error: {result.get('message', 'Unknown error')}")
            if result.get('error'):
                print(f"   Details: {result['error']}")
                
    except Exception as e:
        print(f"❌ Failed to update trading statistics: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("✨ Trading statistics update completed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
