import streamlit as st
import pandas as pd
import sqlite3
import json
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go

# Database connection
def get_db_connection():
    conn = sqlite3.connect('trading_bot/data/analysis_results.db')
    conn.row_factory = sqlite3.Row
    return conn
 
# Data loading functions
def load_trading_stats():
    conn = get_db_connection()
    query = """
    SELECT symbol, timeframe, total_trades, winning_trades, losing_trades,
           total_pnl, win_rate, profit_factor, expected_value,
           avg_win, avg_loss, max_win, max_loss
    FROM trading_stats
    ORDER BY total_pnl DESC
    """
    df = pd.read_sql_query(query, conn)
    conn.close()
    return df

def load_holding_period_stats():
    conn = get_db_connection()
    query = """
    SELECT symbol, timeframe, holding_period_bucket, total_trades, 
           winning_trades, losing_trades, total_pnl, win_rate, 
           profit_factor, avg_pnl, avg_holding_hours
    FROM holding_period_stats
    ORDER BY symbol, timeframe, holding_period_bucket
    """
    df = pd.read_sql_query(query, conn)
    conn.close()
    return df

def load_trades():
    conn = get_db_connection()
    query = """
    SELECT id, symbol, side, quantity, entry_price, take_profit, stop_loss,
           pnl, status, created_at, updated_at
    FROM trades
    ORDER BY created_at DESC
    """
    df = pd.read_sql_query(query, conn)
    conn.close()
    if not df.empty:
        df['created_at'] = pd.to_datetime(df['created_at'], format="%Y-%m-%d %H:%M:%S", errors='coerce')
        df['date'] = df['created_at'].dt.date
    return df

# Analysis functions
def calculate_daily_pnl(trades_df):
    if trades_df.empty:
        return pd.DataFrame()
    daily_pnl = trades_df.groupby('date').agg({
        'pnl': ['sum', 'count', lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0]
    }).round(2)
    daily_pnl.columns = ['daily_pnl', 'trade_count', 'win_rate']
    daily_pnl = daily_pnl.reset_index()
    daily_pnl['date'] = pd.to_datetime(daily_pnl['date'])
    return daily_pnl.sort_values('date')

def get_portfolio_summary(trading_stats_df):
    if trading_stats_df.empty:
        return {
            'total_pnl': 0,
            'win_rate': 0,
            'profit_factor': 0,
            'total_trades': 0,
            'expected_value': 0
        }
    
    total_pnl = trading_stats_df['total_pnl'].sum()
    total_trades = trading_stats_df['total_trades'].sum()
    winning_trades = trading_stats_df['winning_trades'].sum()
    losing_trades = trading_stats_df['losing_trades'].sum()
    
    win_rate = winning_trades / total_trades if total_trades > 0 else 0
    
    total_win_pnl = trading_stats_df['avg_win'] * trading_stats_df['winning_trades']
    total_loss_pnl = trading_stats_df['avg_loss'] * trading_stats_df['losing_trades']
    total_win_pnl_sum = total_win_pnl.sum()
    total_loss_pnl_sum = total_loss_pnl.sum()
    
    profit_factor = total_win_pnl_sum / abs(total_loss_pnl_sum) if total_loss_pnl_sum != 0 else 0
    
    expected_value = (win_rate * trading_stats_df['avg_win'].mean()) - ((1 - win_rate) * trading_stats_df['avg_loss'].mean())
    
    return {
        'total_pnl': round(total_pnl, 2),
        'win_rate': round(win_rate, 3),
        'profit_factor': round(profit_factor, 2),
        'total_trades': int(total_trades),
        'expected_value': round(expected_value, 2)
    }

def get_actionable_insights(trading_stats_df, holding_stats_df, daily_pnl_df):
    insights = {
        'strategy_recommendations': {},
        'performance_insights': {},
        'improvement_actions': []
    }
    
    if not trading_stats_df.empty:
        # Top performing symbols
        top_symbols = trading_stats_df.nlargest(3, 'total_pnl')[['symbol', 'total_pnl', 'win_rate']].to_dict('records')
        insights['strategy_recommendations']['top_performing_symbols'] = top_symbols
        
        # Worst performing symbols
        worst_symbols = trading_stats_df.nsmallest(3, 'total_pnl')[['symbol', 'total_pnl', 'win_rate']].to_dict('records')
        insights['strategy_recommendations']['avoid_symbols'] = worst_symbols
        
        # Best timeframes
        timeframe_perf = trading_stats_df.groupby('timeframe')['total_pnl'].sum().sort_values(ascending=False)
        insights['strategy_recommendations']['best_timeframes'] = timeframe_perf.head(3).to_dict()
        
        # Overall metrics
        insights['performance_insights']['total_portfolio_pnl'] = round(trading_stats_df['total_pnl'].sum(), 2)
        insights['performance_insights']['portfolio_win_rate'] = round(trading_stats_df['winning_trades'].sum() / trading_stats_df['total_trades'].sum() if trading_stats_df['total_trades'].sum() > 0 else 0, 3)
        
    if not holding_stats_df.empty:
        # Optimal holding periods
        avg_pnl_by_period = holding_stats_df.groupby('holding_period_bucket')['avg_pnl'].mean().sort_values(ascending=False)
        insights['strategy_recommendations']['optimal_holding_period'] = avg_pnl_by_period.head(1).index[0] if not avg_pnl_by_period.empty else "Unknown"
        
    if not daily_pnl_df.empty:
        # Best trading days
        best_day_avg = daily_pnl_df[daily_pnl_df['daily_pnl'] > 0]['daily_pnl'].mean()
        worst_day_avg = daily_pnl_df[daily_pnl_df['daily_pnl'] < 0]['daily_pnl'].mean()
        insights['performance_insights']['avg_profitable_day'] = round(best_day_avg, 2) if not pd.isna(best_day_avg) else 0
        insights['performance_insights']['avg_losing_day'] = round(worst_day_avg, 2) if not pd.isna(worst_day_avg) else 0
        
        # Daily win rate
        avg_daily_win_rate = daily_pnl_df['win_rate'].mean()
        insights['performance_insights']['avg_daily_win_rate'] = round(avg_daily_win_rate, 3)
    
    # Generate improvement actions
    if not trading_stats_df.empty:
        if insights['performance_insights'].get('portfolio_win_rate', 0) < 0.5:
            insights['improvement_actions'].append("Focus on improving win rate - consider stricter entry criteria")
        
        if insights['performance_insights'].get('portfolio_win_rate', 0) > 0.7:
            insights['improvement_actions'].append("High win rate achieved - consider optimizing position sizing for better returns")
    
    insights['export_timestamp'] = datetime.utcnow().isoformat()
    return insights

# Streamlit UI
def main():
    st.set_page_config(page_title="Trading Dashboard", layout="wide")
    st.title("📈 Trading Performance Dashboard")
    
    # Load data
    with st.spinner("Loading trading data..."):
        trading_stats_df = load_trading_stats()
        holding_stats_df = load_holding_period_stats()
        trades_df = load_trades()
        daily_pnl_df = calculate_daily_pnl(trades_df) if not trades_df.empty else pd.DataFrame()
    
    # Sidebar filters
    st.sidebar.header("Filters")
    
    if not trading_stats_df.empty:
        # Get all unique symbols and timeframes
        all_symbols = sorted(trading_stats_df['symbol'].unique())
        all_timeframes = sorted(trading_stats_df['timeframe'].unique())
        
        # Add filter management buttons
        col1, col2, col3 = st.sidebar.columns(3)
        with col1:
            if st.button("Select All"):
                st.session_state.selected_symbols = all_symbols
        with col2:
            if st.button("Clear All"):
                st.session_state.selected_symbols = []
        with col3:
            if st.button("Top 10"):
                top_symbols = trading_stats_df.groupby('symbol')['total_pnl'].sum().nlargest(10).index.tolist()
                st.session_state.selected_symbols = top_symbols
        
        # Initialize session state for selected symbols
        if 'selected_symbols' not in st.session_state:
            st.session_state.selected_symbols = all_symbols  # Default to ALL symbols
        
        # Initialize session state for selected timeframes
        if 'selected_timeframes' not in st.session_state:
            st.session_state.selected_timeframes = all_timeframes  # Default to ALL timeframes
        
        # Symbol selection with search
        st.sidebar.subheader("Symbols")
        search_term = st.sidebar.text_input("Search symbols...", "")
        
        # Filter symbols based on search
        filtered_symbols = [s for s in all_symbols if search_term.lower() in s.lower()] if search_term else all_symbols
        
        # Display symbol multiselect with trade counts
        symbol_options_with_counts = {}
        for symbol in filtered_symbols:
            symbol_trades = trading_stats_df[trading_stats_df['symbol'] == symbol]['total_trades'].sum()
            symbol_options_with_counts[f"{symbol} ({symbol_trades} trades)"] = symbol
        
        # Display the multiselect with formatted labels
        selected_labels = st.sidebar.multiselect(
            "Select Symbols", 
            options=list(symbol_options_with_counts.keys()),
            default=[f"{symbol} ({trading_stats_df[trading_stats_df['symbol'] == symbol]['total_trades'].sum()} trades)" 
                    for symbol in st.session_state.selected_symbols if symbol in filtered_symbols]
        )
        
        # Extract actual symbol names from selected labels
        selected_symbols = [symbol_options_with_counts[label] for label in selected_labels if label in symbol_options_with_counts]
        
        # Update session state
        st.session_state.selected_symbols = selected_symbols
        
        # Timeframe selection
        st.sidebar.subheader("Timeframes")
        selected_timeframes = st.sidebar.multiselect(
            "Select Timeframes",
            options=all_timeframes,
            default=st.session_state.selected_timeframes
        )
        
        st.session_state.selected_timeframes = selected_timeframes
        
        # Apply filters
        if selected_symbols:
            trading_stats_df = trading_stats_df[trading_stats_df['symbol'].isin(selected_symbols)]
            holding_stats_df = holding_stats_df[holding_stats_df['symbol'].isin(selected_symbols)]
            trades_df = trades_df[trades_df['symbol'].isin(selected_symbols)] if not trades_df.empty else trades_df
        
        if selected_timeframes:
            trading_stats_df = trading_stats_df[trading_stats_df['timeframe'].isin(selected_timeframes)]
            holding_stats_df = holding_stats_df[holding_stats_df['timeframe'].isin(selected_timeframes)]
            if not trades_df.empty:
                # Filter trades by timeframe would require joining with trading_stats or another approach
                pass
        
        # Recalculate daily PnL with filtered data
        daily_pnl_df = calculate_daily_pnl(trades_df)
        
        # Show filter summary
        total_symbols = len(all_symbols)
        total_timeframes = len(all_timeframes)
        selected_symbol_count = len(selected_symbols) if selected_symbols else 0
        selected_timeframe_count = len(selected_timeframes) if selected_timeframes else 0
        
        st.sidebar.markdown(f"**Filter Summary:**")
        st.sidebar.markdown(f"- Symbols: {selected_symbol_count}/{total_symbols}")
        st.sidebar.markdown(f"- Timeframes: {selected_timeframe_count}/{total_timeframes}")
        
        # Show warning if filters are applied
        if selected_symbol_count < total_symbols or selected_timeframe_count < total_timeframes:
            st.sidebar.info("⚠️ Filters applied - showing subset of data")
        else:
            st.sidebar.success("✅ Showing all data")
    
    # Portfolio Summary
    st.header("📊 Portfolio Summary")
    portfolio_summary = get_portfolio_summary(trading_stats_df)
    
    col1, col2, col3, col4, col5 = st.columns(5)
    col1.metric("Total PnL", f"${portfolio_summary['total_pnl']:,}")
    col2.metric("Win Rate", f"{portfolio_summary['win_rate']:.1%}")
    col3.metric("Profit Factor", f"{portfolio_summary['profit_factor']:.2f}")
    col4.metric("Total Trades", f"{portfolio_summary['total_trades']:,}")
    col5.metric("Expected Value", f"${portfolio_summary['expected_value']:.2f}")
    
    # Daily PnL Chart
    st.header("📅 Daily Performance")
    if not daily_pnl_df.empty:
        fig_daily = px.line(daily_pnl_df, x='date', y='daily_pnl', 
                           title='Daily PnL Trend', 
                           labels={'daily_pnl': 'PnL ($)', 'date': 'Date'})
        fig_daily.add_hline(y=0, line_dash="dash", line_color="red")
        st.plotly_chart(fig_daily, use_container_width=True)
        
        # Daily metrics
        col1, col2, col3 = st.columns(3)
        col1.metric("Best Day", f"${daily_pnl_df['daily_pnl'].max():,.2f}")
        col2.metric("Worst Day", f"${daily_pnl_df['daily_pnl'].min():,.2f}")
        col3.metric("Avg Daily Win Rate", f"{daily_pnl_df['win_rate'].mean():.1%}")
    else:
        st.info("No daily trading data available")
    
    # Symbol Performance
    st.header("🏆 Symbol Performance")
    if not trading_stats_df.empty:
        fig_symbol = px.bar(trading_stats_df, x='symbol', y='total_pnl', 
                           color='win_rate', title='PnL by Symbol',
                           labels={'total_pnl': 'Total PnL ($)', 'win_rate': 'Win Rate'})
        st.plotly_chart(fig_symbol, use_container_width=True)
        
        # Symbol performance table
        st.subheader("Detailed Symbol Performance")
        symbol_perf = trading_stats_df[['symbol', 'timeframe', 'total_trades', 'win_rate', 'profit_factor', 'total_pnl']].sort_values('total_pnl', ascending=False)
        st.dataframe(symbol_perf.style.format({
            'win_rate': '{:.1%}',
            'profit_factor': '{:.2f}',
            'total_pnl': '${:,.2f}'
        }))
    else:
        st.info("No symbol performance data available")
    
    # Holding Period Analysis
    st.header("⏱️ Holding Period Analysis")
    if not holding_stats_df.empty:
        # Add holding period filter
        st.subheader("Holding Period Filter")
        col1, col2 = st.columns(2)
        with col1:
            # Get unique holding period buckets and sort them logically
            holding_buckets = sorted(holding_stats_df['holding_period_bucket'].unique())
            selected_holding_buckets = st.multiselect(
                "Select Holding Period Buckets",
                options=holding_buckets,
                default=holding_buckets  # Default to all buckets
            )
            
            # Filter holding stats based on selection
            if selected_holding_buckets:
                filtered_holding_stats = holding_stats_df[holding_stats_df['holding_period_bucket'].isin(selected_holding_buckets)]
            else:
                filtered_holding_stats = holding_stats_df
        with col2:
            # Timeframe filter for holding period analysis
            holding_timeframes = sorted(holding_stats_df['timeframe'].unique())
            selected_holding_timeframes = st.multiselect(
                "Select Timeframes for Holding Period Analysis",
                options=holding_timeframes,
                default=holding_timeframes  # Default to all timeframes
            )
            
            # Filter by timeframes
            if selected_holding_timeframes:
                filtered_holding_stats = filtered_holding_stats[filtered_holding_stats['timeframe'].isin(selected_holding_timeframes)]
        
        # Create enhanced holding period visualization
        if not filtered_holding_stats.empty:
            fig_holding = px.scatter(filtered_holding_stats, 
                                   x='holding_period_bucket', 
                                   y='avg_pnl',
                                   size='total_trades', 
                                   color='win_rate',
                                   title='Performance by Holding Period',
                                   labels={'avg_pnl': 'Average PnL ($)', 'holding_period_bucket': 'Holding Period'},
                                   hover_data=['symbol', 'timeframe', 'total_trades', 'win_rate'])
            st.plotly_chart(fig_holding, use_container_width=True)
            
            # Enhanced holding period summary with more metrics
            st.subheader("Holding Period Summary")
            holding_summary = filtered_holding_stats.groupby('holding_period_bucket').agg({
                'total_trades': 'sum',
                'avg_pnl': 'mean',
                'win_rate': 'mean',
                'profit_factor': 'mean',
                'avg_holding_hours': 'mean'
            }).round(2).sort_values('avg_pnl', ascending=False)
            
            st.dataframe(holding_summary.style.format({
                'avg_pnl': '${:,.2f}',
                'win_rate': '{:.1%}',
                'profit_factor': '{:.2f}',
                'avg_holding_hours': '{:.1f}h'
            }))
            
            # Best performing holding periods
            st.subheader("Best Performing Holding Periods")
            best_periods = filtered_holding_stats.nlargest(5, 'avg_pnl')[['holding_period_bucket', 'avg_pnl', 'win_rate', 'total_trades', 'symbol', 'timeframe']]
            st.dataframe(best_periods.style.format({
                'avg_pnl': '${:,.2f}',
                'win_rate': '{:.1%}'
            }))
        else:
            st.info("No data available for selected holding period filters")
    else:
        st.info("No holding period data available")
    
    # Export actionable insights
    st.header("📥 Actionable Insights")
    if st.button("Export Strategy Insights"):
        insights = get_actionable_insights(trading_stats_df, holding_stats_df, daily_pnl_df)
        
        # Create download button
        json_str = json.dumps(insights, indent=2, default=str)
        st.download_button(
            label="Download Insights JSON",
            data=json_str,
            file_name=f"trading_insights_{datetime.now().strftime('%Y%m%d_%H%M')}.json",
            mime="application/json"
        )
        
        # Display key insights
        st.subheader("Key Strategy Recommendations")
        if 'strategy_recommendations' in insights:
            recs = insights['strategy_recommendations']
            if 'top_performing_symbols' in recs:
                st.write("**📈 Focus on these symbols:**")
                for symbol_data in recs['top_performing_symbols']:
                    st.write(f"- {symbol_data['symbol']}: ${symbol_data['total_pnl']:.2f} PnL ({symbol_data['win_rate']:.1%} win rate)")
            
            if 'avoid_symbols' in recs:
                st.write("**⚠️ Avoid these symbols:**")
                for symbol_data in recs['avoid_symbols']:
                    st.write(f"- {symbol_data['symbol']}: ${symbol_data['total_pnl']:.2f} PnL ({symbol_data['win_rate']:.1%} win rate)")
        
        if 'improvement_actions' in insights:
            st.write("**💡 Improvement Actions:**")
            for action in insights['improvement_actions']:
                st.write(f"- {action}")

if __name__ == "__main__":
    main()
