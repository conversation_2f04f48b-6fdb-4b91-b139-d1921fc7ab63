import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import pytz
from typing import Dict, List, Any, Optional
import json
import sqlite3
import openai
from dotenv import load_dotenv
import os

# Try to import pybit, if not available, create a mock client
try:
    from pybit.unified_trading import HTTP
    PYBIT_AVAILABLE = True
    HTTP_CLIENT = HTTP
except ImportError:
    PYBIT_AVAILABLE = False
    HTTP_CLIENT = None

class BybitLiveDashboard:
    """Standalone Bybit Live Dashboard focusing only on closed trades."""
    
    def __init__(self):
        self.session = None
        self.closed_trades_data = []
        self._init_client()
    
    def _init_client(self):
        """Initialize Bybit client."""
        if not PYBIT_AVAILABLE or HTTP_CLIENT is None:
            st.warning("pybit library not available. Using mock data.")
            return
            
        try:
            # Load environment variables
            import os
            from dotenv import load_dotenv
            load_dotenv()
            
            api_key = os.getenv('BYBIT_API_KEY', '')
            api_secret = os.getenv('BYBIT_API_SECRET', '')
            
            if not api_key or not api_secret:
                st.warning("Bybit API credentials not found. Using mock data.")
                return
            
            self.session = HTTP_CLIENT(
                testnet=False,  # Per custom instructions: ALWAYS ignore TESTNET
                api_key=api_key,
                api_secret=api_secret,
                recv_window=60000,
            )
            
            # Test connection
            test_response = self.session.get_wallet_balance(accountType="UNIFIED")
            if isinstance(test_response, dict) and test_response.get("retCode") == 0:
                st.success("✅ Connected to Bybit API successfully")
            else:
                st.warning("Failed to connect to Bybit API. Using mock data.")
                self.session = None
                
        except Exception as e:
            st.warning(f"Failed to initialize Bybit client: {e}. Using mock data.")
            self.session = None
    
    def get_closed_pnl_data(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Fetch closed PnL data from Bybit with pagination support."""
        if not self.session:
            return self._get_mock_closed_pnl_data(limit)
        
        try:
            all_positions = []
            import time
            current_end_time = int(time.time() * 1000)
            
            # Fetch data in 7-day windows, moving backwards in time
            # Maximum historical data is 730 days
            max_days_back = 730
            days_processed = 0
            
            while len(all_positions) < limit and days_processed < max_days_back:
                # Calculate 7-day window
                window_end_time = current_end_time
                window_start_time = current_end_time - (7 * 24 * 60 * 60 * 1000)  # 7 days ago
                
                # Ensure we don't go beyond the maximum historical limit
                max_start_time = int(time.time() * 1000) - (max_days_back * 24 * 60 * 60 * 1000)
                if window_start_time < max_start_time:
                    window_start_time = max_start_time
                
                # Fetch data for this 7-day window
                remaining_needed = limit - len(all_positions)
                batch_limit = min(remaining_needed, 100)
                
                params = {
                    "category": "linear",
                    "limit": batch_limit,
                    "startTime": window_start_time,
                    "endTime": window_end_time
                }
                
                response = self.session.get_closed_pnl(**params)
                
                if isinstance(response, dict) and response.get("retCode") == 0:
                    result = response.get("result", {})
                    batch_positions = result.get("list", [])
                    
                    if batch_positions:
                        # Remove duplicates and add to all positions
                        existing_ids = {pos.get('orderId', '') for pos in all_positions}
                        new_positions = [pos for pos in batch_positions if pos.get('orderId', '') not in existing_ids]
                        all_positions.extend(new_positions)
                    
                    # Move to the previous 7-day window
                    current_end_time = window_start_time - 1
                    days_processed += 7
                    
                    # If we got fewer than requested, we might be at the end of available data
                    if len(batch_positions) < batch_limit:
                        # Try to get more by continuing to earlier periods
                        continue
                else:
                    error_msg = response.get('retMsg', 'Unknown error') if isinstance(response, dict) else str(response)
                    st.error(f"Failed to fetch closed PnL data: {error_msg}")
                    break
            
            # Sort by time and return the requested limit
            if all_positions:
                # Sort by createdTime descending (most recent first)
                all_positions.sort(key=lambda x: int(x.get('createdTime', 0)), reverse=True)
            
            return all_positions[:limit]  # Return exactly the requested limit
            
        except Exception as e:
            st.error(f"Error fetching closed PnL data: {e}")
            return self._get_mock_closed_pnl_data(limit)
    
    def _get_mock_closed_pnl_data(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Generate mock closed PnL data for demonstration."""
        import random
        
        symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "XRPUSDT", "ADAUSDT", "DOGEUSDT"]
        mock_data = []
        
        for i in range(min(limit, 20)):
            symbol = random.choice(symbols)
            pnl = round(random.uniform(-50, 200), 2)
            side = random.choice(["Buy", "Sell"])
            size = round(random.uniform(0.1, 5), 2)
            
            mock_data.append({
                "symbol": symbol,
                "realisedPnl": pnl,
                "side": side,
                "size": size,
                "orderId": f"MOCK_{i:04d}",
                "closedPnl": pnl,
                "avgEntryPrice": round(random.uniform(30000, 50000), 2),
                "avgExitPrice": round(random.uniform(30000, 50000), 2),
                "createdTime": (datetime.now() - timedelta(days=random.randint(0, 30))).isoformat(),
                "updatedTime": (datetime.now() - timedelta(hours=random.randint(0, 24))).isoformat()
            })
        
        return mock_data
    
    def process_closed_trades_data(self, closed_pnl_data: List[Dict[str, Any]]) -> pd.DataFrame:
        """Process raw closed PnL data into a structured DataFrame."""
        if not closed_pnl_data:
            return pd.DataFrame()
        
        processed_data = []
        
        for trade in closed_pnl_data:
            # Handle different API response formats
            pnl = trade.get('realisedPnl') or trade.get('closedPnl', 0)
            symbol = trade.get('symbol', 'UNKNOWN')
            side = trade.get('side', 'UNKNOWN')
            size = trade.get('size', trade.get('closedSize', 0))
            order_id = trade.get('orderId', 'UNKNOWN')
            
            # Parse timestamps - Bybit API returns milliseconds as strings
            try:
                # Safely get timestamp values
                created_time = trade.get('createdTime') or trade.get('created_at', '')
                updated_time = trade.get('updatedTime') or trade.get('updated_at', '')
                
                # Parse created time
                if created_time and str(created_time).strip():
                    if isinstance(created_time, (str, int, float)):
                        # Convert to milliseconds timestamp to seconds
                        created_timestamp = int(created_time) / 1000
                        created_dt = datetime.fromtimestamp(created_timestamp, tz=pytz.UTC)
                    else:
                        created_dt = datetime.now(pytz.UTC)
                else:
                    created_dt = datetime.now(pytz.UTC)
                
                # Parse updated time
                if updated_time and str(updated_time).strip():
                    if isinstance(updated_time, (str, int, float)):
                        # Convert to milliseconds timestamp to seconds
                        updated_timestamp = int(updated_time) / 1000
                        updated_dt = datetime.fromtimestamp(updated_timestamp, tz=pytz.UTC)
                    else:
                        updated_dt = created_dt
                else:
                    updated_dt = created_dt
                    
            except Exception:
                created_dt = datetime.now(pytz.UTC)
                updated_dt = created_dt
            
            # Calculate holding time in hours
            holding_time_hours = (updated_dt - created_dt).total_seconds() / 3600
            holding_time_hours = max(0, holding_time_hours)  # Ensure non-negative
            
            processed_data.append({
                'symbol': symbol,
                'pnl': float(pnl),
                'side': side,
                'size': float(size),
                'order_id': order_id,
                'entry_price': float(trade.get('avgEntryPrice', 0)),
                'exit_price': float(trade.get('avgExitPrice', 0)),
                'created_time': created_dt,
                'updated_time': updated_dt,
                'holding_time_hours': holding_time_hours,
                'date': created_dt.date(),
                'hour': created_dt.hour
            })
        
        df = pd.DataFrame(processed_data)
        return df.sort_values('created_time', ascending=False).reset_index(drop=True)
    
    def calculate_performance_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics from closed trades."""
        if df.empty:
            return {
                'total_pnl': 0,
                'win_rate': 0,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'avg_win': 0,
                'avg_loss': 0,
                'largest_win': 0,
                'largest_loss': 0,
                'profit_factor': 0,
                'expectancy': 0,
                # Additional metrics
                'avg_trade_size': 0,
                'max_consecutive_wins': 0,
                'max_consecutive_losses': 0,
                'current_streak': 0,
                'current_streak_type': 'None',
                'win_loss_ratio': 0,
                'avg_holding_time': 0,
                'sharpe_ratio': 0,
                'sortino_ratio': 0,
                'max_drawdown': 0,
                'recovery_factor': 0,
                'calmar_ratio': 0,
                'risk_reward_ratio': 0,
                'consistency_score': 0
            }
        
        winning_trades = df[df['pnl'] > 0]
        losing_trades = df[df['pnl'] < 0]
        
        total_pnl = df['pnl'].sum()
        total_trades = len(df)
        winning_count = len(winning_trades)
        losing_count = len(losing_trades)
        
        win_rate = winning_count / total_trades if total_trades > 0 else 0
        
        avg_win = winning_trades['pnl'].mean() if not winning_trades.empty else 0
        avg_loss = losing_trades['pnl'].mean() if not losing_trades.empty else 0
        
        largest_win = winning_trades['pnl'].max() if not winning_trades.empty else 0
        largest_loss = losing_trades['pnl'].min() if not losing_trades.empty else 0
        
        total_wins = winning_trades['pnl'].sum()
        total_losses = abs(losing_trades['pnl'].sum()) if not losing_trades.empty else 1
        
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        expectancy = total_pnl / total_trades if total_trades > 0 else 0
        
        # Additional metrics
        avg_trade_size = df['size'].mean() if 'size' in df.columns else 0
        avg_holding_time = df['holding_time_hours'].mean() if 'holding_time_hours' in df.columns else 0
        
        # Win/Loss streaks
        df_sorted = df.sort_values('created_time').reset_index(drop=True)
        df_sorted['win'] = df_sorted['pnl'] > 0
        df_sorted['streak_change'] = df_sorted['win'] != df_sorted['win'].shift(1)
        df_sorted['streak_id'] = df_sorted['streak_change'].cumsum()
        streaks = df_sorted.groupby(['streak_id', 'win']).size()
        
        max_consecutive_wins = streaks[streaks.index.get_level_values('win') == True].max() if not streaks[streaks.index.get_level_values('win') == True].empty else 0
        max_consecutive_losses = streaks[streaks.index.get_level_values('win') == False].max() if not streaks[streaks.index.get_level_values('win') == False].empty else 0
        
        # Current streak
        current_streak = 0
        current_streak_type = 'None'
        if not df_sorted.empty:
            current_win = df_sorted.iloc[-1]['win']
            current_streak_type = 'Win' if current_win else 'Loss'
            for i in range(len(df_sorted) - 1, -1, -1):
                if df_sorted.iloc[i]['win'] == current_win:
                    current_streak += 1
                else:
                    break
        
        win_loss_ratio = winning_count / losing_count if losing_count > 0 else winning_count
        
        # Risk metrics
        returns = df['pnl']
        sharpe_ratio = (returns.mean() / returns.std()) * (252 ** 0.5) if returns.std() != 0 else 0  # Annualized
        
        # Sortino ratio (downside deviation)
        negative_returns = returns[returns < 0]
        downside_deviation = negative_returns.std() if len(negative_returns) > 0 else 1
        sortino_ratio = (returns.mean() / downside_deviation) * (252 ** 0.5) if downside_deviation != 0 else 0
        
        # Max drawdown
        cumulative_pnl = df.sort_values('created_time')['pnl'].cumsum()
        rolling_max = cumulative_pnl.expanding().max()
        drawdown = cumulative_pnl - rolling_max
        max_drawdown = drawdown.min() if not drawdown.empty else 0
        
        # Recovery factor
        recovery_factor = total_pnl / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Calmar ratio
        calmar_ratio = (total_pnl / len(df)) / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Risk/Reward ratio
        risk_reward_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        # Consistency score (based on win rate stability)
        consistency_score = win_rate * (1 - (abs(win_rate - 0.5) / 0.5))  # Higher when closer to 100% or 0%
        
        return {
            'total_pnl': round(total_pnl, 2),
            'win_rate': round(win_rate, 3),
            'total_trades': total_trades,
            'winning_trades': winning_count,
            'losing_trades': losing_count,
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'largest_win': round(largest_win, 2),
            'largest_loss': round(largest_loss, 2),
            'profit_factor': round(profit_factor, 2),
            'expectancy': round(expectancy, 2),
            # Additional metrics
            'avg_trade_size': round(avg_trade_size, 4),
            'avg_holding_time': round(avg_holding_time, 1),
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses,
            'current_streak': current_streak,
            'current_streak_type': current_streak_type,
            'win_loss_ratio': round(win_loss_ratio, 2),
            'sharpe_ratio': round(sharpe_ratio, 2),
            'sortino_ratio': round(sortino_ratio, 2),
            'max_drawdown': round(max_drawdown, 2),
            'recovery_factor': round(recovery_factor, 2),
            'calmar_ratio': round(calmar_ratio, 2),
            'risk_reward_ratio': round(risk_reward_ratio, 2),
            'consistency_score': round(consistency_score, 3)
        }
    
    def get_symbol_performance(self, df: pd.DataFrame) -> pd.DataFrame:
        """Get performance metrics by symbol."""
        if df.empty:
            return pd.DataFrame()
        
        symbol_perf = df.groupby('symbol').agg({
            'pnl': ['sum', 'count', lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0],
            'size': 'mean'
        }).round(2)
        
        symbol_perf.columns = ['total_pnl', 'trade_count', 'win_rate', 'avg_size']
        symbol_perf = symbol_perf.reset_index()
        symbol_perf = symbol_perf.sort_values('total_pnl', ascending=False)
        
        return symbol_perf
    
    def get_holding_period_analysis(self, df: pd.DataFrame) -> pd.DataFrame:
        """Get performance analysis by holding period buckets."""
        if df.empty:
            return pd.DataFrame()
        
        # Create holding period buckets
        def categorize_holding_time(hours):
            if hours <= 1:
                return "0-1h"
            elif hours <= 4:
                return "1-4h"
            elif hours <= 12:
                return "4-12h"
            elif hours <= 24:
                return "12-24h"
            elif hours <= 72:
                return "1-3d"
            elif hours <= 168:
                return "3-7d"
            else:
                return "7d+"
        
        df_analysis = df.copy()
        df_analysis['holding_period_bucket'] = df_analysis['holding_time_hours'].apply(categorize_holding_time)
        
        # Calculate performance metrics by holding period
        holding_analysis = df_analysis.groupby('holding_period_bucket').agg({
            'pnl': ['count', 'sum', lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0, 'mean'],
            'holding_time_hours': 'mean'
        }).round(2)
        
        holding_analysis.columns = ['total_trades', 'total_pnl', 'win_rate', 'avg_pnl', 'avg_holding_hours']
        holding_analysis = holding_analysis.reset_index()
        
        # Sort by total PnL descending
        holding_analysis = holding_analysis.sort_values('total_pnl', ascending=False)
        
        return holding_analysis
    
    def get_time_analysis(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Get time-based performance analysis."""
        if df.empty:
            return {'hourly': pd.DataFrame(), 'daily': pd.DataFrame(), 'holding': pd.DataFrame()}
        
        # Hourly analysis
        hourly = df.groupby('hour').agg({
            'pnl': ['sum', 'count', lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0]
        }).round(2)
        hourly.columns = ['pnl_sum', 'trade_count', 'win_rate']
        hourly = hourly.reset_index()
        
        # Daily analysis
        daily = df.groupby('date').agg({
            'pnl': ['sum', 'count', lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0]
        }).round(2)
        daily.columns = ['daily_pnl', 'trade_count', 'win_rate']
        daily = daily.reset_index()
        daily['date'] = pd.to_datetime(daily['date'])
        
        # Holding period analysis
        holding = self.get_holding_period_analysis(df)
        
        return {'hourly': hourly, 'daily': daily, 'holding': holding}
    
    def calculate_daily_pnl(self, trades_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate daily PnL from trades data."""
        if trades_df.empty:
            return pd.DataFrame()
        daily_pnl = trades_df.groupby('date').agg({
            'pnl': ['sum', 'count', lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0]
        }).round(2)
        daily_pnl.columns = ['daily_pnl', 'trade_count', 'win_rate']
        daily_pnl = daily_pnl.reset_index()
        daily_pnl['date'] = pd.to_datetime(daily_pnl['date'])
        return daily_pnl.sort_values('date')
    
    def get_portfolio_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate portfolio summary metrics."""
        if df.empty:
            return {
                'total_pnl': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'total_trades': 0,
                'expected_value': 0
            }
        
        total_pnl = df['pnl'].sum()
        total_trades = len(df)
        winning_trades = len(df[df['pnl'] > 0])
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Calculate expected value
        avg_win = df[df['pnl'] > 0]['pnl'].mean() if len(df[df['pnl'] > 0]) > 0 else 0
        avg_loss = df[df['pnl'] < 0]['pnl'].mean() if len(df[df['pnl'] < 0]) > 0 else 0
        expected_value = (win_rate * avg_win) - ((1 - win_rate) * abs(avg_loss))
        
        # Profit factor
        total_wins = df[df['pnl'] > 0]['pnl'].sum()
        total_losses = abs(df[df['pnl'] < 0]['pnl'].sum()) if len(df[df['pnl'] < 0]) > 0 else 1
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'total_pnl': round(total_pnl, 2),
            'win_rate': round(win_rate, 3),
            'profit_factor': round(profit_factor, 2),
            'total_trades': int(total_trades),
            'expected_value': round(expected_value, 2)
        }
    
    def get_actionable_insights(self, df: pd.DataFrame, symbol_perf: pd.DataFrame, holding_analysis: pd.DataFrame, daily_pnl_df: pd.DataFrame) -> Dict[str, Any]:
        """Generate actionable insights from trading data."""
        insights = {
            'strategy_recommendations': {},
            'performance_insights': {},
            'improvement_actions': []
        }
        
        if not df.empty:
            # Top performing symbols
            if not symbol_perf.empty:
                top_symbols = symbol_perf.nlargest(3, 'total_pnl')[['symbol', 'total_pnl', 'win_rate']].to_dict('records')
                insights['strategy_recommendations']['top_performing_symbols'] = top_symbols
                
                # Worst performing symbols
                worst_symbols = symbol_perf.nsmallest(3, 'total_pnl')[['symbol', 'total_pnl', 'win_rate']].to_dict('records')
                insights['strategy_recommendations']['avoid_symbols'] = worst_symbols
            
            # Overall metrics
            insights['performance_insights']['total_portfolio_pnl'] = round(df['pnl'].sum(), 2)
            insights['performance_insights']['portfolio_win_rate'] = round(len(df[df['pnl'] > 0]) / len(df) if len(df) > 0 else 0, 3)
        
        if not holding_analysis.empty:
            # Optimal holding periods
            avg_pnl_by_period = holding_analysis.groupby('holding_period_bucket')['avg_pnl'].mean().sort_values(ascending=False)
            insights['strategy_recommendations']['optimal_holding_period'] = avg_pnl_by_period.head(1).index[0] if not avg_pnl_by_period.empty else "Unknown"
        
        if not daily_pnl_df.empty:
            # Best trading days
            best_day_avg = daily_pnl_df[daily_pnl_df['daily_pnl'] > 0]['daily_pnl'].mean()
            worst_day_avg = daily_pnl_df[daily_pnl_df['daily_pnl'] < 0]['daily_pnl'].mean()
            insights['performance_insights']['avg_profitable_day'] = round(best_day_avg, 2) if not pd.isna(best_day_avg) else 0
            insights['performance_insights']['avg_losing_day'] = round(worst_day_avg, 2) if not pd.isna(worst_day_avg) else 0
            
            # Daily win rate
            avg_daily_win_rate = daily_pnl_df['win_rate'].mean()
            insights['performance_insights']['avg_daily_win_rate'] = round(avg_daily_win_rate, 3)
        
        # Generate improvement actions
        if not df.empty:
            win_rate = len(df[df['pnl'] > 0]) / len(df) if len(df) > 0 else 0
            if insights['performance_insights'].get('portfolio_win_rate', 0) < 0.5:
                insights['improvement_actions'].append("Focus on improving win rate - consider stricter entry criteria")
            
            if insights['performance_insights'].get('portfolio_win_rate', 0) > 0.7:
                insights['improvement_actions'].append("High win rate achieved - consider optimizing position sizing for better returns")
        
        insights['export_timestamp'] = datetime.utcnow().isoformat()
        return insights

    def get_ai_insights(self, df: pd.DataFrame, symbol_perf: pd.DataFrame, time_analysis: Dict[str, pd.DataFrame], metrics: Dict[str, Any], portfolio_summary: Dict[str, Any]) -> Dict[str, Any]:
        """Get AI-powered trading insights and recommendations."""
        try:
            # Load environment variables
            load_dotenv()
            openai.api_key = os.getenv('OPENAI_API_KEY')
            
            if not openai.api_key:
                return {
                    'error': 'OpenAI API key not found. Please set OPENAI_API_KEY in your environment variables.',
                    'recommendations': [],
                    'improvements': []
                }
            
            # Extract comprehensive trading statistics for AI analysis
            total_pnl = df['pnl'].sum() if not df.empty else 0
            win_rate = len(df[df['pnl'] > 0]) / len(df) if len(df) > 0 else 0
            total_trades = len(df)
            winning_trades = len(df[df['pnl'] > 0])
            losing_trades = len(df[df['pnl'] < 0])
            
            # Portfolio metrics
            avg_win = df[df['pnl'] > 0]['pnl'].mean() if len(df[df['pnl'] > 0]) > 0 else 0
            avg_loss = df[df['pnl'] < 0]['pnl'].mean() if len(df[df['pnl'] < 0]) > 0 else 0
            largest_win = df[df['pnl'] > 0]['pnl'].max() if len(df[df['pnl'] > 0]) > 0 else 0
            largest_loss = df[df['pnl'] < 0]['pnl'].min() if len(df[df['pnl'] < 0]) > 0 else 0
            
            # Risk metrics
            sharpe_ratio = metrics.get('sharpe_ratio', 0)
            sortino_ratio = metrics.get('sortino_ratio', 0)
            max_drawdown = metrics.get('max_drawdown', 0)
            profit_factor = metrics.get('profit_factor', 0)
            expectancy = metrics.get('expectancy', 0)
            consistency_score = metrics.get('consistency_score', 0)
            
            # Advanced metrics
            current_streak = metrics.get('current_streak', 0)
            current_streak_type = metrics.get('current_streak_type', 'None')
            max_consecutive_wins = metrics.get('max_consecutive_wins', 0)
            max_consecutive_losses = metrics.get('max_consecutive_losses', 0)
            risk_reward_ratio = metrics.get('risk_reward_ratio', 0)
            recovery_factor = metrics.get('recovery_factor', 0)
            
            # Symbol performance data (top 10 symbols with detailed metrics)
            top_symbols_detailed = []
            if not symbol_perf.empty:
                top_symbols_df = symbol_perf.nlargest(10, 'total_pnl')
                for _, row in top_symbols_df.iterrows():
                    symbol_trades = df[df['symbol'] == row['symbol']]
                    symbol_win_rate = len(symbol_trades[symbol_trades['pnl'] > 0]) / len(symbol_trades) if len(symbol_trades) > 0 else 0
                    symbol_avg_win = symbol_trades[symbol_trades['pnl'] > 0]['pnl'].mean() if len(symbol_trades[symbol_trades['pnl'] > 0]) > 0 else 0
                    symbol_avg_loss = symbol_trades[symbol_trades['pnl'] < 0]['pnl'].mean() if len(symbol_trades[symbol_trades['pnl'] < 0]) > 0 else 0
                    symbol_largest_win = symbol_trades['pnl'].max()
                    symbol_largest_loss = symbol_trades['pnl'].min()
                    
                    top_symbols_detailed.append({
                        'symbol': row['symbol'],
                        'total_pnl': round(row['total_pnl'], 2),
                        'win_rate': round(symbol_win_rate, 3),
                        'trade_count': int(row['trade_count']),
                        'avg_win': round(symbol_avg_win, 2),
                        'avg_loss': round(symbol_avg_loss, 2),
                        'largest_win': round(symbol_largest_win, 2),
                        'largest_loss': round(symbol_largest_loss, 2),
                        'profit_factor': round(abs(symbol_avg_win / symbol_avg_loss) if symbol_avg_loss != 0 else 0, 2)
                    })
            
            # Hourly performance insights
            hourly_insights = []
            if not time_analysis['hourly'].empty:
                for _, row in time_analysis['hourly'].head(10).iterrows():  # Top 10 best hours
                    hourly_insights.append({
                        'hour': int(row['hour']),
                        'pnl_sum': round(row['pnl_sum'], 2),
                        'win_rate': round(row['win_rate'], 3),
                        'trade_count': int(row['trade_count'])
                    })
            
            # Worst performing hours
            worst_hourly_insights = []
            if not time_analysis['hourly'].empty:
                for _, row in time_analysis['hourly'].nsmallest(5, 'pnl_sum').iterrows():
                    worst_hourly_insights.append({
                        'hour': int(row['hour']),
                        'pnl_sum': round(row['pnl_sum'], 2),
                        'win_rate': round(row['win_rate'], 3),
                        'trade_count': int(row['trade_count'])
                    })
            
            # Holding period analysis
            holding_period_insights = []
            if not time_analysis['holding'].empty:
                for _, row in time_analysis['holding'].head(5).iterrows():
                    holding_period_insights.append({
                        'holding_period': row['holding_period_bucket'],
                        'avg_pnl': round(row['avg_pnl'], 2),
                        'win_rate': round(row['win_rate'], 3),
                        'total_trades': int(row['total_trades']),
                        'avg_holding_hours': round(row['avg_holding_hours'], 1)
                    })
            
            # Prepare comprehensive data for AI analysis
            ai_data = {
                'portfolio_summary': {
                    'total_pnl': round(total_pnl, 2),
                    'win_rate': round(win_rate, 3),
                    'total_trades': total_trades,
                    'winning_trades': winning_trades,
                    'losing_trades': losing_trades,
                    'avg_win': round(avg_win, 2),
                    'avg_loss': round(avg_loss, 2),
                    'largest_win': round(largest_win, 2),
                    'largest_loss': round(largest_loss, 2),
                    'profit_factor': round(profit_factor, 2),
                    'expectancy': round(expectancy, 2),
                    'sharpe_ratio': round(sharpe_ratio, 2),
                    'sortino_ratio': round(sortino_ratio, 2),
                    'max_drawdown': round(max_drawdown, 2),
                    'consistency_score': round(consistency_score, 3)
                },
                'risk_metrics': {
                    'current_streak': f"{current_streak} {current_streak_type}",
                    'max_consecutive_wins': max_consecutive_wins,
                    'max_consecutive_losses': max_consecutive_losses,
                    'risk_reward_ratio': round(risk_reward_ratio, 2),
                    'recovery_factor': round(recovery_factor, 2)
                },
                'top_performing_symbols': top_symbols_detailed[:10],  # Top 10 symbols
                'hourly_performance': {
                    'best_hours': hourly_insights[:10],
                    'worst_hours': worst_hourly_insights
                },
                'holding_period_analysis': holding_period_insights
            }
            
            # Create comprehensive AI prompt with detailed trading statistics
            prompt = f"""
            As a professional trading analyst and quantitative strategist, analyze the following comprehensive trading performance data and provide specific, actionable recommendations that will directly improve trading performance:

            PORTFOLIO OVERVIEW:
            - Total PnL: ${ai_data['portfolio_summary']['total_pnl']}
            - Win Rate: {ai_data['portfolio_summary']['win_rate']:.1%}
            - Total Trades: {ai_data['portfolio_summary']['total_trades']}
            - Profit Factor: {ai_data['portfolio_summary']['profit_factor']:.2f}
            - Expectancy: ${ai_data['portfolio_summary']['expectancy']:.2f}
            - Sharpe Ratio: {ai_data['portfolio_summary']['sharpe_ratio']:.2f}
            - Sortino Ratio: {ai_data['portfolio_summary']['sortino_ratio']:.2f}
            - Max Drawdown: ${ai_data['portfolio_summary']['max_drawdown']:.2f}
            - Consistency Score: {ai_data['portfolio_summary']['consistency_score']:.1%}

            RISK METRICS:
            - Current Streak: {ai_data['risk_metrics']['current_streak']}
            - Max Consecutive Wins: {ai_data['risk_metrics']['max_consecutive_wins']}
            - Max Consecutive Losses: {ai_data['risk_metrics']['max_consecutive_losses']}
            - Risk/Reward Ratio: {ai_data['risk_metrics']['risk_reward_ratio']:.2f}
            - Recovery Factor: {ai_data['risk_metrics']['recovery_factor']:.2f}

            TOP 10 PERFORMING SYMBOLS (with detailed metrics):
            {json.dumps(ai_data['top_performing_symbols'], indent=2)}

            BEST 10 TRADING HOURS:
            {json.dumps(ai_data['hourly_performance']['best_hours'], indent=2)}

            WORST 5 TRADING HOURS:
            {json.dumps(ai_data['hourly_performance']['worst_hours'], indent=2)}

            HOLDING PERIOD ANALYSIS:
            {json.dumps(ai_data['holding_period_analysis'], indent=2)}

            Based on this comprehensive trading data, please provide SPECIFIC, ACTIONABLE recommendations that will directly improve trading performance. Focus on concrete, implementable advice rather than generic suggestions.

            Provide the following:

            1. Top 10 symbol + timeframe combinations to focus on (rank by profitability and risk metrics)
               - For each symbol, specify exact entry/exit improvements based on avg_win/avg_loss ratios
               - Include specific position sizing recommendations per symbol

            2. Specific improvement recommendations based on the data patterns:
               - If win rate is below 50%, suggest concrete entry filter improvements
               - If profit factor is below 1.5, suggest specific exit strategy improvements
               - If max consecutive losses > 5, suggest concrete risk management improvements
               - Based on worst performing hours, suggest specific time-based trading restrictions
               - Based on holding period analysis, suggest concrete holding time optimizations

            3. Risk management suggestions tailored to the current performance:
               - Specific stop loss adjustments based on largest_loss metrics
               - Position sizing adjustments based on current drawdown patterns
               - Concrete advice for managing current streak (if losing streak > 3)

            4. Position sizing advice considering win rate and risk metrics:
               - Specific position size adjustments for symbols with profit factor < 1.2
               - Kelly criterion based position sizing recommendations
               - Concrete adjustments based on current volatility patterns

            5. Optimal trading hours based on hourly performance analysis:
               - Specific "avoid these hours" recommendations with exact time ranges
               - "best performance window" with concrete entry/exit timing advice

            6. Holding period optimization strategies:
               - Specific holding time adjustments based on avg_holding_hours vs profitability
               - Concrete exit timing improvements for underperforming holding periods

            CRITICAL: Provide concrete, specific advice that a trader can immediately implement. Avoid generic suggestions like "improve entry strategy". Instead, provide specific numerical recommendations like "increase stop loss distance by 15%" or "avoid trading between 14:00-16:00 UTC".

            Format your response as JSON with keys: "top_symbols", "improvements", "risk_management", "position_sizing", "optimal_hours", "holding_strategies"
            """

            # Get AI response
            from openai import OpenAI
            client = OpenAI()
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional trading analyst providing actionable insights."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )
            
            # Parse AI response
            ai_response = response.choices[0].message.content
            if ai_response:
                try:
                    # Try to parse as JSON
                    ai_insights = json.loads(ai_response)
                except json.JSONDecodeError:
                    # If not JSON, create structured response
                    ai_insights = {
                        'top_symbols': ['AI analysis completed - check full response below'],
                        'improvements': ['Comprehensive analysis provided'],
                        'risk_management': ['See detailed recommendations'],
                        'position_sizing': ['Review full AI response']
                    }
            else:
                ai_insights = {
                    'top_symbols': ['No response from AI'],
                    'improvements': ['AI service returned empty response'],
                    'risk_management': ['Check API connectivity'],
                    'position_sizing': ['Try again later']
                }
            
            return {
                'ai_response': ai_response,
                'recommendations': ai_insights.get('top_symbols', []),
                'improvements': ai_insights.get('improvements', []),
                'risk_management': ai_insights.get('risk_management', []),
                'position_sizing': ai_insights.get('position_sizing', [])
            }
            
        except Exception as e:
            return {
                'error': f'AI analysis failed: {str(e)}',
                'recommendations': ['Use manual analysis'],
                'improvements': ['Check API connectivity'],
                'risk_management': ['Review error logs'],
                'position_sizing': ['Manual review recommended']
            }

def main():
    st.set_page_config(page_title="Bybit Live Trading Dashboard", layout="wide")
    st.title("📈 Bybit Live Trading Dashboard")
    st.markdown("Real-time trading performance based on actual closed trades from Bybit exchange")
    
    # Initialize dashboard
    dashboard = BybitLiveDashboard()
    
    # Sidebar controls
    st.sidebar.header("Dashboard Controls")
    limit = st.sidebar.slider("Number of trades to fetch", 10, 200, 50, 10)
    
    # Use session state to track refresh
    if 'refresh_count' not in st.session_state:
        st.session_state.refresh_count = 0
    
    refresh = st.sidebar.button("🔄 Refresh Data")
    if refresh:
        st.session_state.refresh_count += 1
        st.rerun()
    
    # Fetch data - use session state to trigger refresh
    with st.spinner("Fetching closed trades data from Bybit..."):
        closed_pnl_data = dashboard.get_closed_pnl_data(limit)
        df = dashboard.process_closed_trades_data(closed_pnl_data)
        metrics = dashboard.calculate_performance_metrics(df)
        symbol_perf = dashboard.get_symbol_performance(df)
        time_analysis = dashboard.get_time_analysis(df)
        daily_pnl_df = dashboard.calculate_daily_pnl(df)
        portfolio_summary = dashboard.get_portfolio_summary(df)
    
    # Sidebar filters with enhanced UX
    st.sidebar.header("Filters")
    
    if not df.empty:
        # Get all unique symbols
        all_symbols = sorted(df['symbol'].unique())
        
        # Add filter management buttons
        col1, col2, col3 = st.sidebar.columns(3)
        with col1:
            if st.button("Select All"):
                st.session_state.selected_symbols = all_symbols
        with col2:
            if st.button("Clear All"):
                st.session_state.selected_symbols = []
        with col3:
            if st.button("Top 10"):
                if not symbol_perf.empty:
                    top_symbols = symbol_perf.nlargest(10, 'total_pnl')['symbol'].tolist()
                    st.session_state.selected_symbols = top_symbols
                else:
                    st.session_state.selected_symbols = all_symbols[:10]
        
        # Initialize session state for selected symbols
        if 'selected_symbols' not in st.session_state:
            st.session_state.selected_symbols = all_symbols  # Default to ALL symbols
        
        # Symbol selection with search
        st.sidebar.subheader("Symbols")
        search_term = st.sidebar.text_input("Search symbols...", "")
        
        # Filter symbols based on search
        filtered_symbols = [s for s in all_symbols if search_term.lower() in s.lower()] if search_term else all_symbols
        
        # Display symbol multiselect with trade counts
        symbol_options_with_counts = {}
        for symbol in filtered_symbols:
            symbol_trades = len(df[df['symbol'] == symbol])
            symbol_options_with_counts[f"{symbol} ({symbol_trades} trades)"] = symbol
        
        # Display the multiselect with formatted labels
        selected_labels = st.sidebar.multiselect(
            "Select Symbols", 
            options=list(symbol_options_with_counts.keys()),
            default=[f"{symbol} ({len(df[df['symbol'] == symbol])} trades)" 
                    for symbol in st.session_state.selected_symbols if symbol in filtered_symbols]
        )
        
        # Extract actual symbol names from selected labels
        selected_symbols = [symbol_options_with_counts[label] for label in selected_labels if label in symbol_options_with_counts]
        
        # Update session state
        st.session_state.selected_symbols = selected_symbols
        
        # Apply filters
        if selected_symbols:
            df = df[df['symbol'].isin(selected_symbols)]
            # Recalculate all metrics with filtered data
            metrics = dashboard.calculate_performance_metrics(df)
            symbol_perf = dashboard.get_symbol_performance(df)
            time_analysis = dashboard.get_time_analysis(df)
            daily_pnl_df = dashboard.calculate_daily_pnl(df)
            portfolio_summary = dashboard.get_portfolio_summary(df)
        
        # Show filter summary
        total_symbols = len(all_symbols)
        selected_symbol_count = len(selected_symbols) if selected_symbols else 0
        
        st.sidebar.markdown(f"**Filter Summary:**")
        st.sidebar.markdown(f"- Symbols: {selected_symbol_count}/{total_symbols}")
        
        # Show warning if filters are applied
        if selected_symbol_count < total_symbols:
            st.sidebar.info("⚠️ Filters applied - showing subset of data")
        else:
            st.sidebar.success("✅ Showing all data")
    
    # Portfolio Summary
    st.header("📊 Portfolio Summary")
    col1, col2, col3, col4, col5 = st.columns(5)
    col1.metric("Total PnL", f"${portfolio_summary['total_pnl']:,}")
    col2.metric("Win Rate", f"{portfolio_summary['win_rate']:.1%}")
    col3.metric("Profit Factor", f"{portfolio_summary['profit_factor']:.2f}")
    col4.metric("Total Trades", f"{portfolio_summary['total_trades']:,}")
    col5.metric("Expected Value", f"${portfolio_summary['expected_value']:.2f}")
    
    # Daily PnL Chart
    st.header("📅 Daily Performance")
    if not daily_pnl_df.empty:
        fig_daily = px.line(daily_pnl_df, x='date', y='daily_pnl', 
                           title='Daily PnL Trend', 
                           labels={'daily_pnl': 'PnL ($)', 'date': 'Date'})
        fig_daily.add_hline(y=0, line_dash="dash", line_color="red")
        st.plotly_chart(fig_daily, use_container_width=True)
        
        # Daily metrics
        col1, col2, col3 = st.columns(3)
        col1.metric("Best Day", f"${daily_pnl_df['daily_pnl'].max():,.2f}")
        col2.metric("Worst Day", f"${daily_pnl_df['daily_pnl'].min():,.2f}")
        col3.metric("Avg Daily Win Rate", f"{daily_pnl_df['win_rate'].mean():.1%}")
    else:
        st.info("No daily trading data available")
    
    # Performance Summary
    st.header("📈 Performance Summary")
    
    # Main metrics row
    col1, col2, col3, col4, col5 = st.columns(5)
    col1.metric("Total PnL", f"${metrics['total_pnl']:,}", 
                delta=f"{metrics['total_pnl']:.2f}" if metrics['total_pnl'] != 0 else "0.00")
    col2.metric("Win Rate", f"{metrics['win_rate']:.1%}")
    col3.metric("Total Trades", f"{metrics['total_trades']:,}")
    col4.metric("Avg Win", f"${metrics['avg_win']:.2f}")
    col5.metric("Avg Loss", f"${abs(metrics['avg_loss']):.2f}")
    
    # Additional metrics row
    col6, col7, col8, col9, col10 = st.columns(5)
    col6.metric("Profit Factor", f"{metrics['profit_factor']:.2f}")
    col7.metric("Expectancy", f"${metrics['expectancy']:.2f}")
    col8.metric("Sharpe Ratio", f"{metrics['sharpe_ratio']:.2f}")
    col9.metric("Max Drawdown", f"${metrics['max_drawdown']:.2f}")
    col10.metric("Consistency", f"{metrics['consistency_score']:.1%}")
    
    # Streaks and advanced metrics
    col11, col12, col13, col14, col15 = st.columns(5)
    col11.metric("Current Streak", f"{metrics['current_streak']} {metrics['current_streak_type']}")
    col12.metric("Max Win Streak", f"{metrics['max_consecutive_wins']}")
    col13.metric("Max Loss Streak", f"{metrics['max_consecutive_losses']}")
    col14.metric("Risk/Reward", f"{metrics['risk_reward_ratio']:.2f}")
    col15.metric("Recovery Factor", f"{metrics['recovery_factor']:.2f}")
    
    # Tabs for different views
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs(["📈 Recent Trades", "🏆 Symbol Performance", "⏱️ Holding Period", "⏰ Time Analysis", "📥 Actionable Insights", "🤖 AI Insights"])
    
    with tab1:
        st.subheader("Recent Closed Trades")
        if not df.empty:
            # Recent trades table
            recent_trades = df[['symbol', 'side', 'size', 'pnl', 'entry_price', 'exit_price', 'holding_time_hours', 'created_time']].head(20)
            st.dataframe(recent_trades.style.format({
                'pnl': '${:,.2f}',
                'size': '{:,.4f}',
                'entry_price': '${:,.2f}',
                'exit_price': '${:,.2f}',
                'holding_time_hours': '{:,.1f}h',
                'created_time': lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if isinstance(x, datetime) else str(x)
            }).background_gradient(cmap='RdYlGn', subset=['pnl']), use_container_width=True)
            
            # PnL distribution chart
            fig_pnl_dist = px.histogram(df, x='pnl', nbins=30, 
                                       title='PnL Distribution',
                                       labels={'pnl': 'Profit/Loss ($)', 'count': 'Number of Trades'})
            fig_pnl_dist.add_vline(x=0, line_dash="dash", line_color="red")
            st.plotly_chart(fig_pnl_dist, use_container_width=True)
            
            # Holding time distribution chart
            fig_holding_time = px.histogram(df, x='holding_time_hours', nbins=30,
                                           title='Position Holding Time Distribution',
                                           labels={'holding_time_hours': 'Holding Time (hours)', 'count': 'Number of Trades'})
            fig_holding_time.add_vline(x=df['holding_time_hours'].median(), line_dash="dash", line_color="red", 
                                     annotation_text=f"Median: {df['holding_time_hours'].median():.1f}h")
            st.plotly_chart(fig_holding_time, use_container_width=True)
        else:
            st.info("No closed trades data available")
    
    with tab2:
        st.subheader("Symbol Performance Analysis")
        if not symbol_perf.empty:
            # Symbol PnL chart
            fig_symbol_pnl = px.bar(symbol_perf, x='symbol', y='total_pnl', 
                                   color='win_rate', title='PnL by Symbol',
                                   labels={'total_pnl': 'Total PnL ($)', 'win_rate': 'Win Rate'})
            fig_symbol_pnl.add_hline(y=0, line_dash="dash", line_color="red")
            st.plotly_chart(fig_symbol_pnl, use_container_width=True)
            
            # Symbol performance table
            st.dataframe(symbol_perf.style.format({
                'total_pnl': '${:,.2f}',
                'win_rate': '{:.1%}',
                'avg_size': '{:,.4f}'
            }).background_gradient(cmap='RdYlGn', subset=['total_pnl', 'win_rate']), use_container_width=True)
            
            # Detailed symbol performance
            st.subheader("Detailed Symbol Performance")
            detailed_perf = df.groupby('symbol').agg({
                'pnl': ['sum', 'count', 'mean', 'std', lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0],
                'size': 'mean',
                'holding_time_hours': 'mean'
            }).round(2)
            detailed_perf.columns = ['total_pnl', 'trade_count', 'avg_pnl', 'pnl_std', 'win_rate', 'avg_size', 'avg_holding_hours']
            detailed_perf = detailed_perf.reset_index()
            detailed_perf = detailed_perf.sort_values('total_pnl', ascending=False)
            st.dataframe(detailed_perf.style.format({
                'total_pnl': '${:,.2f}',
                'win_rate': '{:.1%}',
                'avg_pnl': '${:,.2f}',
                'pnl_std': '${:,.2f}',
                'avg_size': '{:,.4f}',
                'avg_holding_hours': '{:.1f}h'
            }).background_gradient(cmap='RdYlGn', subset=['total_pnl', 'win_rate']), use_container_width=True)
        else:
            st.info("No symbol performance data available")
    
    with tab3:
        st.subheader("Holding Period Analysis")
        if not time_analysis['holding'].empty:
            # Add holding period filter
            st.subheader("Holding Period Filter")
            col1, col2 = st.columns(2)
            with col1:
                # Get unique holding period buckets and sort them logically
                holding_buckets = sorted(time_analysis['holding']['holding_period_bucket'].unique())
                selected_holding_buckets = st.multiselect(
                    "Select Holding Period Buckets",
                    options=holding_buckets,
                    default=holding_buckets  # Default to all buckets
                )
                
                # Filter holding stats based on selection
                if selected_holding_buckets:
                    filtered_holding_stats = time_analysis['holding'][time_analysis['holding']['holding_period_bucket'].isin(selected_holding_buckets)]
                else:
                    filtered_holding_stats = time_analysis['holding']
            
            # Create enhanced holding period visualization
            if not filtered_holding_stats.empty:
                fig_holding = px.scatter(filtered_holding_stats, 
                                       x='holding_period_bucket', 
                                       y='avg_pnl',
                                       size='total_trades', 
                                       color='win_rate',
                                       title='Performance by Holding Period',
                                       labels={'avg_pnl': 'Average PnL ($)', 'holding_period_bucket': 'Holding Period'},
                                       hover_data=['total_trades', 'win_rate'])
                fig_holding.add_hline(y=0, line_dash="dash", line_color="red")
                st.plotly_chart(fig_holding, use_container_width=True)
                
                # Enhanced holding period summary with more metrics
                st.subheader("Holding Period Summary")
                holding_summary = filtered_holding_stats[['holding_period_bucket', 'total_trades', 'win_rate', 'avg_pnl', 'avg_holding_hours']].sort_values('avg_pnl', ascending=False)
                st.dataframe(holding_summary.style.format({
                    'win_rate': '{:.1%}',
                    'avg_pnl': '${:,.2f}',
                    'avg_holding_hours': '{:.1f}h'
                }).background_gradient(cmap='RdYlGn', subset=['avg_pnl', 'win_rate']), use_container_width=True)
                
                # Best performing holding periods
                st.subheader("Best Performing Holding Periods")
                best_periods = filtered_holding_stats.nlargest(5, 'avg_pnl')[['holding_period_bucket', 'avg_pnl', 'win_rate', 'total_trades']]
                st.dataframe(best_periods.style.format({
                    'avg_pnl': '${:,.2f}',
                    'win_rate': '{:.1%}'
                }), use_container_width=True)
            else:
                st.info("No data available for selected holding period filters")
        else:
            st.info("No holding period data available")
    
    with tab4:
        st.subheader("Time-Based Analysis")
        
        # Daily PnL chart
        if not time_analysis['daily'].empty:
            fig_daily = px.line(time_analysis['daily'], x='date', y='daily_pnl', 
                               title='Daily PnL Trend',
                               labels={'daily_pnl': 'Daily PnL ($)', 'date': 'Date'})
            fig_daily.add_hline(y=0, line_dash="dash", line_color="red")
            st.plotly_chart(fig_daily, use_container_width=True)
        
        # Hourly performance charts
        if not time_analysis['hourly'].empty:
            # Hourly performance chart
            fig_hourly = px.bar(time_analysis['hourly'], x='hour', y='pnl_sum',
                               title='Hourly PnL Performance',
                               labels={'pnl_sum': 'PnL ($)', 'hour': 'Hour of Day'})
            fig_hourly.add_hline(y=0, line_dash="dash", line_color="red")
            st.plotly_chart(fig_hourly, use_container_width=True)
            
            # Hourly win rate
            fig_win_rate = px.line(time_analysis['hourly'], x='hour', y='win_rate',
                                  title='Hourly Win Rate',
                                  labels={'win_rate': 'Win Rate', 'hour': 'Hour of Day'})
            fig_win_rate.add_hline(y=0.5, line_dash="dash", line_color="red")
            st.plotly_chart(fig_win_rate, use_container_width=True)
        
        # Holding Period Analysis
        if not time_analysis['holding'].empty:
            st.subheader("Holding Period Analysis")
            
            # Holding period performance scatter plot
            fig_holding = px.scatter(time_analysis['holding'], 
                                   x='holding_period_bucket', 
                                   y='avg_pnl',
                                   size='total_trades', 
                                   color='win_rate',
                                   title='Performance by Holding Period',
                                   labels={'avg_pnl': 'Average PnL ($)', 
                                          'holding_period_bucket': 'Holding Period',
                                          'total_trades': 'Number of Trades',
                                          'win_rate': 'Win Rate'})
            fig_holding.add_hline(y=0, line_dash="dash", line_color="red")
            st.plotly_chart(fig_holding, use_container_width=True)
            
            # Holding period summary table
            st.subheader("Holding Period Summary")
            holding_summary = time_analysis['holding'][['holding_period_bucket', 'total_trades', 'win_rate', 'avg_pnl', 'avg_holding_hours']].sort_values('avg_pnl', ascending=False)
            st.dataframe(holding_summary.style.format({
                'win_rate': '{:.1%}',
                'avg_pnl': '${:,.2f}',
                'avg_holding_hours': '{:.1f}h'
            }).background_gradient(cmap='RdYlGn', subset=['avg_pnl', 'win_rate']), use_container_width=True)
        else:
            st.info("No time-based analysis data available")
    
    with tab5:
        st.subheader("Actionable Insights")
        if st.button("Export Strategy Insights"):
            insights = dashboard.get_actionable_insights(df, symbol_perf, time_analysis['holding'], daily_pnl_df)
            
            # Create download button
            json_str = json.dumps(insights, indent=2, default=str)
            st.download_button(
                label="Download Insights JSON",
                data=json_str,
                file_name=f"trading_insights_{datetime.now().strftime('%Y%m%d_%H%M')}.json",
                mime="application/json"
            )
            
            # Display key insights
            st.subheader("Key Strategy Recommendations")
            if 'strategy_recommendations' in insights:
                recs = insights['strategy_recommendations']
                if 'top_performing_symbols' in recs:
                    st.write("**📈 Focus on these symbols:**")
                    for symbol_data in recs['top_performing_symbols']:
                        st.write(f"- {symbol_data['symbol']}: ${symbol_data['total_pnl']:.2f} PnL ({symbol_data['win_rate']:.1%} win rate)")
                
                if 'avoid_symbols' in recs:
                    st.write("**⚠️ Avoid these symbols:**")
                    for symbol_data in recs['avoid_symbols']:
                        st.write(f"- {symbol_data['symbol']}: ${symbol_data['total_pnl']:.2f} PnL ({symbol_data['win_rate']:.1%} win rate)")
                
                if 'optimal_holding_period' in recs:
                    st.write(f"**⏱️ Optimal holding period:** {recs['optimal_holding_period']}")
            
            if 'improvement_actions' in insights:
                st.write("**💡 Improvement Actions:**")
                for action in insights['improvement_actions']:
                    st.write(f"- {action}")
    
    with tab6:
        st.subheader("🤖 AI Trading Insights")
        st.markdown("AI-powered analysis of your trading performance to identify top opportunities and improvements.")
        
        if st.button("🤖 Generate AI Insights"):
            with st.spinner("Analyzing trading data with AI..."):
                ai_insights = dashboard.get_ai_insights(df, symbol_perf, time_analysis, metrics, portfolio_summary)
                
                if 'error' in ai_insights:
                    st.error(ai_insights['error'])
                else:
                    # Display AI recommendations
                    st.subheader("🎯 Top Symbol + Timeframe Recommendations")
                    if ai_insights['recommendations']:
                        for i, rec in enumerate(ai_insights['recommendations'], 1):
                            st.write(f"{i}. {rec}")
                    else:
                        st.info("No specific recommendations available. Check full analysis below.")
                    
                    # Display improvements
                    st.subheader("💡 Improvement Recommendations")
                    if ai_insights['improvements']:
                        for improvement in ai_insights['improvements']:
                            st.write(f"• {improvement}")
                    
                    # Display risk management
                    st.subheader("🛡️ Risk Management")
                    if ai_insights['risk_management']:
                        for risk_tip in ai_insights['risk_management']:
                            st.write(f"• {risk_tip}")
                    
                    # Display position sizing
                    st.subheader("📊 Position Sizing Advice")
                    if ai_insights['position_sizing']:
                        for sizing_advice in ai_insights['position_sizing']:
                            st.write(f"• {sizing_advice}")
                    
                    # Show full AI response
                    with st.expander("🔍 View Full AI Analysis"):
                        st.text(ai_insights.get('ai_response', 'No detailed analysis available'))
        
        st.info("Note: AI analysis requires OpenAI API key. Set OPENAI_API_KEY environment variable to enable.")
    
    # Footer with data freshness
    st.markdown("---")
    st.caption(f"📊 Data refreshed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC | "
              f"Total closed trades analyzed: {len(df)}")

if __name__ == "__main__":
    main()
