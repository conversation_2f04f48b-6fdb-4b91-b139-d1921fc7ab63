#!/usr/bin/env python3
"""
Cleanup script to remove old/stale orders from Bybit account.

This script identifies and cancels orders that are:
- Older than a specified age (default: 7 days)
- In terminal states (Filled, Cancelled, Deactivated)
- Not associated with active positions

Usage:
    python cleanup_stale_orders.py [--dry-run] [--age-days 7] [--batch-size 10]
"""

import sys
import argparse
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any

# Add current directory to path for imports
sys.path.insert(0, '.')

from trading_bot.config.settings import Config
from trading_bot.core.bybit_api_manager import BybitAPIManager


def setup_logging():
    """Setup basic logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def get_stale_orders(api_manager: BybitAPIManager, age_days: int = 1, include_terminal_states: bool = True) -> List[Dict[str, Any]]:
    """
    Get list of stale orders that should be cancelled.

    IMPORTANT: Only returns ENTRY orders. TP/SL orders are protected and should not be cancelled.

    Args:
        api_manager: Bybit API manager instance
        age_days: Orders older than this many days will be considered stale
        include_terminal_states: If True, include orders in terminal states regardless of age

    Returns:
        List of stale ENTRY order dictionaries (excludes TP/SL orders)
    """
    cutoff_time = datetime.now(timezone.utc) - timedelta(days=age_days)

    # Get all orders (we'll need to paginate if there are many)
    all_stale_orders = []

    # Get open orders first (these might include stale ones)
    response = api_manager.get_open_orders(category="linear", settleCoin="USDT", limit=50)
    if not response or response.get("retCode") != 0:
        logging.error(f"Failed to get open orders: {response}")
        return []

    orders = response.get("result", {}).get("list", [])

    for order in orders:
        order_status = order.get("orderStatus", "")
        created_time_str = order.get("createdTime", "")
        stop_order_type = order.get("stopOrderType", "")
        order_type = order.get("orderType", "").lower()

        # CRITICAL: Skip TP/SL orders - they should never be cancelled automatically
        if stop_order_type in ["TakeProfit", "StopLoss"] or order_type in ["takeprofit", "stoploss"]:
            logging.info(f"⏭️ Skipping TP/SL order: {order.get('symbol')} {order.get('side')} "
                        f"(stopOrderType: {stop_order_type}, orderType: {order_type})")
            continue

        # Skip if order is still active (New/PartiallyFilled are not stale)
        if order_status in ["New", "PartiallyFilled"]:
            continue

        # Always include orders in terminal states if requested
        if include_terminal_states and order_status in ["Filled", "Cancelled", "Deactivated"]:
            all_stale_orders.append(order)
            logging.info(f"Found terminal ENTRY order: {order.get('symbol')} {order.get('side')} "
                        f"{order.get('qty')} @ {order.get('price')} (status: {order_status})")
            continue

        # Check if order is old enough to be considered stale
        if created_time_str:
            try:
                # Bybit timestamps are in milliseconds
                created_timestamp = int(created_time_str) / 1000
                created_datetime = datetime.fromtimestamp(created_timestamp, timezone.utc)

                if created_datetime < cutoff_time:
                    all_stale_orders.append(order)
                    logging.info(f"Found stale ENTRY order: {order.get('symbol')} {order.get('side')} "
                               f"{order.get('qty')} @ {order.get('price')} "
                               f"(status: {order_status}, age: {(datetime.now(timezone.utc) - created_datetime).days} days)")
            except (ValueError, TypeError) as e:
                logging.warning(f"Could not parse createdTime for order {order.get('orderId')}: {e}")
                # If we can't parse the time, consider it stale anyway if it's in a terminal state
                if order_status in ["Filled", "Cancelled", "Deactivated"]:
                    all_stale_orders.append(order)

    logging.info(f"✅ Found {len(all_stale_orders)} stale ENTRY orders (TP/SL orders excluded)")
    return all_stale_orders


def cancel_stale_orders(api_manager: BybitAPIManager, stale_orders: List[Dict[str, Any]],
                       dry_run: bool = True, batch_size: int = 10) -> Dict[str, Any]:
    """
    Cancel the provided stale orders.

    Args:
        api_manager: Bybit API manager instance
        stale_orders: List of orders to cancel
        dry_run: If True, only log what would be cancelled
        batch_size: Number of orders to cancel in each batch

    Returns:
        Dict with cancellation results
    """
    results = {
        "total_attempted": len(stale_orders),
        "successful": 0,
        "failed": 0,
        "errors": []
    }

    if dry_run:
        logging.info(f"DRY RUN: Would cancel {len(stale_orders)} stale orders")
        for order in stale_orders[:10]:  # Show first 10
            logging.info(f"  Would cancel: {order.get('symbol')} {order.get('side')} "
                        f"{order.get('qty')} @ {order.get('price')} ({order.get('orderStatus')})")
        if len(stale_orders) > 10:
            logging.info(f"  ... and {len(stale_orders) - 10} more")
        return results

    # Process in batches to avoid overwhelming the API
    for i in range(0, len(stale_orders), batch_size):
        batch = stale_orders[i:i + batch_size]
        logging.info(f"Processing batch {i//batch_size + 1}/{(len(stale_orders) + batch_size - 1)//batch_size} "
                    f"({len(batch)} orders)")

        for order in batch:
            order_id = order.get("orderId")
            symbol = order.get("symbol")

            if not order_id or not symbol:
                logging.warning(f"Skipping order with missing ID or symbol: {order}")
                results["failed"] += 1
                continue

            try:
                # Import the centralized cancellation function
                import sys
                import os
                sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

                from trading_bot.core.utils import cancel_order_with_verification
                from trading_bot.core.bybit_api_manager import BybitAPIManager

                # Create a mock trader object with the api_manager
                class MockTrader:
                    def __init__(self, api_manager):
                        self.api_manager = api_manager

                mock_trader = MockTrader(api_manager)

                # Cancel the order using centralized function
                cancel_result = cancel_order_with_verification(
                    trader=mock_trader,
                    symbol=symbol,
                    order_id=order_id,
                    max_retries=3,
                    retry_delay=0.5
                )

                # Convert result to expected format
                if cancel_result.get("success"):
                    cancel_response = {"retCode": 0, "retMsg": "OK"}
                else:
                    cancel_response = {"retCode": -1, "retMsg": cancel_result.get("error", "Cancellation failed")}

                if cancel_response and cancel_response.get("retCode") == 0:
                    logging.info(f"✅ Successfully cancelled order: {symbol} {order_id}")
                    results["successful"] += 1
                else:
                    error_msg = cancel_response.get("retMsg", "Unknown error") if cancel_response else "No response"
                    logging.warning(f"❌ Failed to cancel order {order_id}: {error_msg}")
                    results["failed"] += 1
                    results["errors"].append({
                        "order_id": order_id,
                        "symbol": symbol,
                        "error": error_msg
                    })

            except Exception as e:
                logging.error(f"❌ Exception cancelling order {order_id}: {e}")
                results["failed"] += 1
                results["errors"].append({
                    "order_id": order_id,
                    "symbol": symbol,
                    "error": str(e)
                })

    return results


def main():
    """Main entry point."""
    setup_logging()

    parser = argparse.ArgumentParser(description="Clean up stale orders from Bybit account")
    parser.add_argument("--dry-run", action="store_true", help="Only show what would be cancelled")
    parser.add_argument("--age-days", type=int, default=7, help="Orders older than this many days are considered stale")
    parser.add_argument("--batch-size", type=int, default=10, help="Number of orders to cancel in each batch")
    parser.add_argument("--config", type=str, default="config.yaml", help="Path to config file")

    args = parser.parse_args()

    try:
        # Load configuration
        config = Config.from_yaml(args.config)
        logging.info(f"Loaded configuration from {args.config}")

        # Initialize API manager
        api_manager = BybitAPIManager(config, use_testnet=config.bybit.use_testnet)
        logging.info(f"Initialized API manager (testnet: {config.bybit.use_testnet})")

        # Get stale orders
        logging.info(f"Looking for orders older than {args.age_days} days...")
        stale_orders = get_stale_orders(api_manager, args.age_days)

        if not stale_orders:
            logging.info("No stale orders found.")
            return

        logging.info(f"Found {len(stale_orders)} stale orders to clean up")

        # Cancel stale orders
        results = cancel_stale_orders(api_manager, stale_orders, args.dry_run, args.batch_size)

        # Print summary
        logging.info("\n" + "="*50)
        logging.info("CLEANUP SUMMARY")
        logging.info("="*50)
        logging.info(f"Total stale orders found: {results['total_attempted']}")
        if not args.dry_run:
            logging.info(f"Successfully cancelled: {results['successful']}")
            logging.info(f"Failed to cancel: {results['failed']}")

            if results['errors']:
                logging.info("Errors encountered:")
                for error in results['errors'][:5]:  # Show first 5 errors
                    logging.info(f"  {error['symbol']} {error['order_id']}: {error['error']}")
                if len(results['errors']) > 5:
                    logging.info(f"  ... and {len(results['errors']) - 5} more errors")
        else:
            logging.info("This was a dry run - no orders were actually cancelled")

    except Exception as e:
        logging.error(f"Script failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
