import streamlit as st
import sqlite3
import pandas as pd
import os
from PIL import Image

DATABASE_PATH = "trading_bot/data/analysis_results.db"
CHARTS_PATH = "trading_bot/data/charts/.backup"

def load_data():
    conn = sqlite3.connect(DATABASE_PATH)
    analysis_df = pd.read_sql_query("SELECT * FROM analysis_results", conn)
    trades_df = pd.read_sql_query("SELECT * FROM trades", conn)
    conn.close()
    return analysis_df, trades_df

def display_chart_image(image_path):
    full_path = os.path.join(CHARTS_PATH, image_path)
    # Handle .backup extension
    if full_path.endswith(".backup"):
        actual_path = full_path[:-len(".backup")]
    else:
        actual_path = full_path

    if os.path.exists(actual_path):
        st.image(actual_path, caption=os.path.basename(actual_path), use_column_width=True)
    else:
        st.warning(f"Image not found: {actual_path}")

st.set_page_config(layout="wide", page_title="Trading Analysis Dashboard")

st.title("Trading Analysis & Optimization Dashboard")

analysis_df, trades_df = load_data()

st.subheader("Analysis Results Overview")
st.dataframe(analysis_df.head())

st.subheader("Trades Overview")
st.dataframe(trades_df.head())

st.subheader("Chart Image Viewer")
selected_image = st.selectbox("Select an image to display", analysis_df["image_path"].unique())
if selected_image:
    display_chart_image(selected_image)
