#!/bin/bash

# OpenVPN Client Setup Script
# This script automates the OpenVPN client setup process

set -e  # Exit on any error

echo "🔧 OpenVPN Client Setup Script"
echo "=============================="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root"
   exit 1
fi

# Update system
echo "📦 Updating system packages..."
apt update && apt upgrade -y

# Install OpenVPN
echo "🔧 Installing OpenVPN..."
apt install -y openvpn

# Install and configure UFW
echo "🔥 Installing and configuring UFW..."
apt install -y ufw
ufw allow ssh
ufw allow 1194/udp
ufw --force enable
sed -i 's/DEFAULT_FORWARD_POLICY="DROP"/DEFAULT_FORWARD_POLICY="ACCEPT"/' /etc/default/ufw
ufw reload

# Enable IP forwarding
echo "🌐 Enabling IP forwarding..."
sed -i 's/#net.ipv4.ip_forward=1/net.ipv4.ip_forward=1/' /etc/sysctl.conf
sysctl -p

# Create OpenVPN directory
echo "📁 Creating OpenVPN directory..."
mkdir -p /etc/openvpn

# Check if configuration file exists
if [[ ! -f /etc/openvpn/server.conf ]]; then
    echo "❌ Configuration file /etc/openvpn/server.conf not found!"
    echo "📝 Please create the configuration file with your VPN settings"
    echo "📖 See OpenVPN_Setup_README.md for complete configuration"
    exit 1
fi

# Set proper permissions
echo "🔒 Setting configuration file permissions..."
chmod 600 /etc/openvpn/server.conf
chown root:root /etc/openvpn/server.conf

# Test configuration
echo "🔍 Testing configuration..."
if openvpn --config /etc/openvpn/server.conf --test-crypto >/dev/null 2>&1; then
    echo "✅ Configuration syntax is valid"
else
    echo "❌ Configuration syntax error!"
    echo "🔧 Please check /etc/openvpn/server.conf"
    exit 1
fi

# Enable service
echo "⚙️ Enabling OpenVPN service..."
<NAME_EMAIL>

# Start service
echo "🚀 Starting OpenVPN service..."
<NAME_EMAIL>

# Wait a moment and check status
sleep 3
if systemctl is-active --quiet <EMAIL>; then
    echo "✅ OpenVPN service started successfully!"
    echo ""
    echo "📊 Service Status:"
    <NAME_EMAIL> --no-pager -l
else
    echo "❌ Failed to start OpenVPN service"
    echo "🔍 Checking logs..."
    journalctl -u <EMAIL> -n 10 --no-pager
    exit 1
fi

echo ""
echo "🎉 OpenVPN setup completed successfully!"
echo ""
echo "📋 Useful commands:"
echo "   Status: sudo <NAME_EMAIL>"
echo "   Logs:   sudo journalctl -u <EMAIL> -f"
echo "   Stop:   sudo <NAME_EMAIL>"
echo "   Restart: sudo <NAME_EMAIL>"
echo ""
echo "📖 For troubleshooting, see: OpenVPN_Setup_README.md"