# Python Development Rules & Guidelines

## Core Principles
- **One task at a time** - Simple, minimal changes only!!!!!!!!
- **SIMPLE MINIMAL CHANGES** - your goal is to achive the maximum effect with the least amount of code!!!
- **No assumptions** - Verify everything before coding
- **Code reuse** - Always reuse existing code when possible
- **Minimalist approach** - Only add code when absolutely necessary
- **Consistent Behavior** - Always try to used Standardized Medthods and fucntions across all components

# - ALLWAYS ignore TESTNET we do NOT use Testnet in this project!

## Python Code Standards
- Follow PEP 8 styling guidelines
- Use type hints for all function parameters and returns
- Write docstrings for all modules, classes, and functions
- Keep functions small and focused (single responsibility)
- Use meaningful variable names
- Prefer composition over inheritance

## Project Structure
- Organize code in `src/` directory
- Place tests in `./tests` directory Always reuse test file and updated rather than write a new one for the same test!!
- Use modular design with clear separation of concerns
- Maintain consistent package structure

## Testing Requirements
- Write unit tests for all new functionality
- Place all tests in `./tests` directory
- Use pytest framework
- Aim for high code coverage
- Test edge cases and error conditions

## Technical Practices
- Always check existing code before adding new features
- Reuse existing functions and modules
- Use standard library when possible
- Leverage well-maintained third-party libraries
- Avoid code duplication
- Use context managers for resource management

## Process Management
- Checkpoint after every major change
- Run tests before committing code
- Verify database schemas and connections before use
- Test all code paths locally before deployment

## Success Metrics
- Clean, maintainable, minimal code
- High test coverage
- No redundant or duplicate code
- Efficient and performant implementation

## Best Practice
- Never use  notations like .2f for print statements 
- for test files in test folder because they are in a subfolder you must make sure that all imports inside the test file are always from root folder.

## Importaint
- Entry point for this project is always run_autrotrader.py
