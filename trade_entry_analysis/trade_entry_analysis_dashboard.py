#!/usr/bin/env python3
"""
Trade Entry Analysis Dashboard - Focused on improving entry handling using Bybit API data.
Analyzes entry timing, price levels, and market conditions to identify what works and what doesn't.
"""

import logging
import sys
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
import statistics

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from trading_bot.config.settings import Config
from trading_bot.core.bybit_api_manager import BybitAPIManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trade_entry_analysis.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class EntryAnalysis:
    """Detailed analysis of trade entries."""
    symbol: str
    side: str
    entry_price: float
    exit_price: float
    entry_time: datetime
    exit_time: datetime
    pnl: float
    return_pct: float
    duration_minutes: float
    price_distance: float

    # Entry quality metrics
    entry_efficiency: float  # How close to optimal entry
    market_regime: str      # trending, ranging, volatile
    entry_timing_score: float  # 0-100 score for entry timing

@dataclass
class EntryPattern:
    """Pattern analysis for entries."""
    pattern_type: str  # breakout, pullback, reversal, etc.
    success_rate: float
    avg_return: float
    frequency: int
    best_symbols: List[str]
    worst_symbols: List[str]

class TradeEntryAnalyzer:
    """Advanced analyzer for trade entry effectiveness."""

    def __init__(self, config: Config, use_testnet: bool = False):
        self.config = config
        self.bybit_api = BybitAPIManager(config, use_testnet)
        self.logger = logging.getLogger(__name__)

    def get_comprehensive_trade_data(self, days: int = 14) -> Dict[str, Any]:
        """
        Get comprehensive trade data including executions and market data.

        Args:
            days: Number of days of history to analyze

        Returns:
            Dictionary with comprehensive trade analysis
        """
        try:
            # Get execution history
            executions = self.get_execution_history(days)
            if not executions:
                return {"error": "No execution data available"}

            # Get market data for context
            market_data = self.get_market_context(executions)

            # Analyze entries
            entry_analyses = self.analyze_entries(executions, market_data)

            # Generate insights
            insights = self.generate_entry_insights(entry_analyses)

            return {
                "summary": self.generate_summary_stats(entry_analyses),
                "entry_patterns": self.analyze_entry_patterns(entry_analyses),
                "timing_analysis": self.analyze_entry_timing(entry_analyses),
                "symbol_performance": self.analyze_symbol_performance(entry_analyses),
                "market_regime_analysis": self.analyze_market_regimes(entry_analyses),
                "improvement_recommendations": insights,
                "raw_data": entry_analyses
            }

        except Exception as e:
            self.logger.error(f"Error in comprehensive analysis: {e}")
            return {"error": str(e)}

    def get_execution_history(self, days: int = 14) -> List[Dict[str, Any]]:
        """Get execution history from Bybit (max 7 days per request)."""
        try:
            self.logger.info(f"Fetching {days} days of execution history")

            all_executions = []

            # Bybit API limit is 7 days, so fetch in chunks
            chunk_size = 7
            for i in range(0, days, chunk_size):
                current_chunk = min(chunk_size, days - i)
                end_time = datetime.now(timezone.utc) - timedelta(days=i)
                start_time = end_time - timedelta(days=current_chunk)

                start_time_ms = int(start_time.timestamp() * 1000)
                end_time_ms = int(end_time.timestamp() * 1000)

                self.logger.info(f"Fetching chunk: {start_time} to {end_time}")

                cursor = None
                while True:
                    params = {
                        "startTime": start_time_ms,
                        "endTime": end_time_ms,
                        "limit": 100
                    }

                    if cursor:
                        params["cursor"] = cursor

                    response = self.bybit_api.get_executions(**params)

                    if response.get("retCode") != 0:
                        self.logger.error(f"Failed to get executions for chunk: {response}")
                        break

                    executions = response.get("result", {}).get("list", [])
                    if not executions:
                        break

                    all_executions.extend(executions)
                    self.logger.info(f"Fetched {len(executions)} executions for chunk (total: {len(all_executions)})")

                    next_cursor = response.get("result", {}).get("nextPageCursor")
                    if not next_cursor or cursor == next_cursor:
                        break

                    cursor = next_cursor

            self.logger.info(f"Total executions fetched: {len(all_executions)}")
            return all_executions

        except Exception as e:
            self.logger.error(f"Error getting execution history: {e}")
            return []

    def get_market_context(self, executions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get market data context for better entry analysis."""
        try:
            # Get unique symbols from executions
            symbols = list(set(exec.get("symbol", "") for exec in executions))
            symbols = [s for s in symbols if s]  # Filter out empty symbols

            market_context = {}

            for symbol in symbols:
                try:
                    # Get ticker data for current market conditions
                    ticker_response = self.bybit_api.get_tickers(symbol=symbol)
                    if ticker_response.get("retCode") == 0:
                        ticker_data = ticker_response.get("result", {}).get("list", [{}])[0]
                        market_context[symbol] = {
                            "last_price": float(ticker_data.get("lastPrice", 0)),
                            "bid_price": float(ticker_data.get("bid1Price", 0)),
                            "ask_price": float(ticker_data.get("ask1Price", 0)),
                            "volume_24h": float(ticker_data.get("volume24h", 0)),
                            "price_change_24h": float(ticker_data.get("price24hPcnt", 0))
                        }
                except Exception as e:
                    self.logger.warning(f"Could not get market data for {symbol}: {e}")
                    continue

            return market_context

        except Exception as e:
            self.logger.error(f"Error getting market context: {e}")
            return {}

    def analyze_entries(self, executions: List[Dict[str, Any]], market_data: Dict[str, Any]) -> List[EntryAnalysis]:
        """Analyze individual trade entries for effectiveness."""
        try:
            # Group executions by order
            order_groups = self.group_executions_by_order(executions)

            entry_analyses = []

            for order_id, execs in order_groups.items():
                if len(execs) < 2:  # Need at least entry and exit
                    continue

                try:
                    analysis = self.create_entry_analysis(execs, market_data)
                    if analysis:
                        entry_analyses.append(analysis)

                except Exception as e:
                    self.logger.warning(f"Error analyzing order {order_id}: {e}")
                    continue

            return entry_analyses

        except Exception as e:
            self.logger.error(f"Error in entry analysis: {e}")
            return []

    def group_executions_by_order(self, executions: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Group executions by order ID."""
        order_groups = defaultdict(list)

        for exec_data in executions:
            order_id = exec_data.get("orderId", "")
            if order_id:
                order_groups[order_id].append(exec_data)

        return dict(order_groups)

    def create_entry_analysis(self, executions: List[Dict[str, Any]], market_data: Dict[str, Any]) -> Optional[EntryAnalysis]:
        """Create detailed entry analysis from executions."""
        try:
            # Sort executions by time
            executions = sorted(executions, key=lambda x: int(x.get("execTime", 0)))

            symbol = executions[0].get("symbol", "")
            side = executions[0].get("side", "")

            # Get first and last execution
            entry_exec = executions[0]
            exit_exec = executions[-1]

            entry_price = float(entry_exec.get("execPrice", 0))
            exit_price = float(exit_exec.get("execPrice", 0))
            entry_time = datetime.fromtimestamp(int(entry_exec.get("execTime", 0)) / 1000, timezone.utc)
            exit_time = datetime.fromtimestamp(int(exit_exec.get("execTime", 0)) / 1000, timezone.utc)

            # Calculate metrics
            if side.upper() == "BUY":
                pnl = (exit_price - entry_price) * float(entry_exec.get("execQty", 0))
                price_distance = exit_price - entry_price
            else:  # SELL
                pnl = (entry_price - exit_price) * float(entry_exec.get("execQty", 0))
                price_distance = entry_price - exit_price

            return_pct = (price_distance / entry_price) * 100 if entry_price > 0 else 0
            duration_minutes = (exit_time - entry_time).total_seconds() / 60

            # Calculate entry quality metrics
            entry_efficiency = self.calculate_entry_efficiency(entry_price, side, market_data.get(symbol, {}))
            market_regime = self.determine_market_regime(symbol, entry_time, market_data)
            entry_timing_score = self.score_entry_timing(entry_price, entry_time, side, market_data.get(symbol, {}))

            return EntryAnalysis(
                symbol=symbol,
                side=side,
                entry_price=entry_price,
                exit_price=exit_price,
                entry_time=entry_time,
                exit_time=exit_time,
                pnl=pnl,
                return_pct=return_pct,
                duration_minutes=duration_minutes,
                price_distance=price_distance,
                entry_efficiency=entry_efficiency,
                market_regime=market_regime,
                entry_timing_score=entry_timing_score
            )

        except Exception as e:
            self.logger.error(f"Error creating entry analysis: {e}")
            return None

    def calculate_entry_efficiency(self, entry_price: float, side: str, market_data: Dict[str, Any]) -> float:
        """Calculate how efficient the entry price was (0-100 score)."""
        try:
            if not market_data:
                return 50  # Neutral score if no market data

            current_price = market_data.get("last_price", 0)
            if current_price == 0:
                return 50

            # For buy orders, better entry is lower price relative to current
            if side.upper() == "BUY":
                # Ideal entry would be at the bid price or lower
                bid_price = market_data.get("bid_price", 0)
                if bid_price > 0:
                    # Score based on how close to bid price (lower is better for buys)
                    distance_from_bid = abs(entry_price - bid_price)
                    efficiency = max(0, 100 - (distance_from_bid / bid_price * 100))
                    return efficiency
            else:  # SELL
                # Ideal entry would be at the ask price or higher
                ask_price = market_data.get("ask_price", 0)
                if ask_price > 0:
                    # Score based on how close to ask price (higher is better for sells)
                    distance_from_ask = abs(entry_price - ask_price)
                    efficiency = max(0, 100 - (distance_from_ask / ask_price * 100))
                    return efficiency

            return 50  # Neutral if can't calculate

        except Exception as e:
            self.logger.warning(f"Error calculating entry efficiency: {e}")
            return 50

    def determine_market_regime(self, symbol: str, entry_time: datetime, market_data: Dict[str, Any]) -> str:
        """Determine market regime at entry time."""
        try:
            if not market_data:
                return "unknown"

            price_change_24h = market_data.get("price_change_24h", 0)
            volume_24h = market_data.get("volume_24h", 0)

            # Simple regime classification based on price change and volume
            if abs(price_change_24h) > 5:  # > 5% move in 24h
                if volume_24h > 1000000:  # High volume
                    return "volatile"
                else:
                    return "trending"
            else:
                return "ranging"

        except Exception as e:
            self.logger.warning(f"Error determining market regime: {e}")
            return "unknown"

    def score_entry_timing(self, entry_price: float, entry_time: datetime, side: str, market_data: Dict[str, Any]) -> float:
        """Score entry timing on a scale of 0-100."""
        try:
            if not market_data:
                return 50

            # This is a simplified scoring - in reality would use more sophisticated analysis
            # Consider: price momentum, volume, spread, market microstructure

            base_score = 50

            # Adjust based on spread efficiency
            bid_price = market_data.get("bid_price", 0)
            ask_price = market_data.get("ask_price", 0)

            if bid_price > 0 and ask_price > 0:
                spread = ask_price - bid_price
                spread_pct = spread / bid_price * 100

                if side.upper() == "BUY":
                    # Better if entry is closer to bid
                    entry_distance_from_bid = abs(entry_price - bid_price)
                    spread_efficiency = max(0, 100 - (entry_distance_from_bid / spread * 100))
                    base_score += (spread_efficiency - 50) * 0.3
                else:  # SELL
                    # Better if entry is closer to ask
                    entry_distance_from_ask = abs(entry_price - ask_price)
                    spread_efficiency = max(0, 100 - (entry_distance_from_ask / spread * 100))
                    base_score += (spread_efficiency - 50) * 0.3

            return max(0, min(100, base_score))

        except Exception as e:
            self.logger.warning(f"Error scoring entry timing: {e}")
            return 50

    def generate_summary_stats(self, analyses: List[EntryAnalysis]) -> Dict[str, Any]:
        """Generate summary statistics."""
        if not analyses:
            return {"error": "No analyses available"}

        winning_trades = [a for a in analyses if a.pnl > 0]
        losing_trades = [a for a in analyses if a.pnl <= 0]

        return {
            "total_trades": len(analyses),
            "winning_trades": len(winning_trades),
            "losing_trades": len(losing_trades),
            "win_rate": len(winning_trades) / len(analyses) * 100 if analyses else 0,
            "avg_return_winning": statistics.mean([a.return_pct for a in winning_trades]) if winning_trades else 0,
            "avg_return_losing": statistics.mean([a.return_pct for a in losing_trades]) if losing_trades else 0,
            "total_pnl": sum(a.pnl for a in analyses),
            "avg_entry_efficiency": statistics.mean([a.entry_efficiency for a in analyses]) if analyses else 0,
            "avg_entry_timing_score": statistics.mean([a.entry_timing_score for a in analyses]) if analyses else 0
        }

    def analyze_entry_patterns(self, analyses: List[EntryAnalysis]) -> Dict[str, EntryPattern]:
        """Analyze common entry patterns and their success rates."""
        try:
            patterns = {}

            # Group by market regime and side
            regime_side_groups = defaultdict(list)
            for analysis in analyses:
                key = f"{analysis.market_regime}_{analysis.side}"
                regime_side_groups[key].append(analysis)

            for pattern_key, trades in regime_side_groups.items():
                if len(trades) >= 3:  # Minimum sample size
                    winning_trades = [t for t in trades if t.pnl > 0]
                    success_rate = len(winning_trades) / len(trades) * 100
                    avg_return = statistics.mean([t.return_pct for t in trades])

                    # Find best and worst performing symbols for this pattern
                    symbol_performance = {}
                    for trade in trades:
                        if trade.symbol not in symbol_performance:
                            symbol_performance[trade.symbol] = []
                        symbol_performance[trade.symbol].append(trade.return_pct)

                    symbol_avg_returns = {symbol: statistics.mean(returns)
                                        for symbol, returns in symbol_performance.items()}

                    best_symbols = sorted(symbol_avg_returns.items(), key=lambda x: x[1], reverse=True)[:3]
                    worst_symbols = sorted(symbol_avg_returns.items(), key=lambda x: x[1])[:3]

                    patterns[pattern_key] = EntryPattern(
                        pattern_type=pattern_key,
                        success_rate=success_rate,
                        avg_return=avg_return,
                        frequency=len(trades),
                        best_symbols=[s[0] for s in best_symbols],
                        worst_symbols=[s[0] for s in worst_symbols]
                    )

            return patterns

        except Exception as e:
            self.logger.error(f"Error analyzing entry patterns: {e}")
            return {}

    def analyze_entry_timing(self, analyses: List[EntryAnalysis]) -> Dict[str, Any]:
        """Analyze entry timing patterns."""
        try:
            timing_data = {
                "by_hour": defaultdict(list),
                "by_day_of_week": defaultdict(list),
                "duration_analysis": {
                    "quick_exits": [],  # < 5 minutes
                    "normal_trades": [],  # 5-60 minutes
                    "long_trades": []  # > 60 minutes
                }
            }

            for analysis in analyses:
                # By hour
                hour = analysis.entry_time.hour
                timing_data["by_hour"][hour].append(analysis.return_pct)

                # By day of week
                day_of_week = analysis.entry_time.weekday()
                timing_data["by_day_of_week"][day_of_week].append(analysis.return_pct)

                # By duration
                if analysis.duration_minutes < 5:
                    timing_data["duration_analysis"]["quick_exits"].append(analysis)
                elif analysis.duration_minutes <= 60:
                    timing_data["duration_analysis"]["normal_trades"].append(analysis)
                else:
                    timing_data["duration_analysis"]["long_trades"].append(analysis)

            # Calculate averages
            for hour in timing_data["by_hour"]:
                returns = timing_data["by_hour"][hour]
                timing_data["by_hour"][hour] = {
                    "avg_return": statistics.mean(returns),
                    "count": len(returns),
                    "win_rate": len([r for r in returns if r > 0]) / len(returns) * 100
                }

            for day in timing_data["by_day_of_week"]:
                returns = timing_data["by_day_of_week"][day]
                timing_data["by_day_of_week"][day] = {
                    "avg_return": statistics.mean(returns),
                    "count": len(returns),
                    "win_rate": len([r for r in returns if r > 0]) / len(returns) * 100
                }

            # Duration analysis
            for duration_type, trades in timing_data["duration_analysis"].items():
                if trades:
                    returns = [t.return_pct for t in trades]
                    pnls = [t.pnl for t in trades]
                    timing_data["duration_analysis"][duration_type] = {
                        "count": len(trades),
                        "avg_return": statistics.mean(returns),
                        "total_pnl": sum(pnls),
                        "win_rate": len([t for t in trades if t.pnl > 0]) / len(trades) * 100,
                        "avg_duration": statistics.mean([t.duration_minutes for t in trades])
                    }

            return timing_data

        except Exception as e:
            self.logger.error(f"Error analyzing entry timing: {e}")
            return {}

    def analyze_symbol_performance(self, analyses: List[EntryAnalysis]) -> Dict[str, Any]:
        """Analyze performance by symbol."""
        try:
            symbol_data = defaultdict(list)

            for analysis in analyses:
                symbol_data[analysis.symbol].append(analysis)

            symbol_performance = {}

            for symbol, trades in symbol_data.items():
                if len(trades) >= 2:  # Minimum sample size
                    returns = [t.return_pct for t in trades]
                    pnls = [t.pnl for t in trades]

                    symbol_performance[symbol] = {
                        "trade_count": len(trades),
                        "avg_return": statistics.mean(returns),
                        "total_pnl": sum(pnls),
                        "win_rate": len([t for t in trades if t.pnl > 0]) / len(trades) * 100,
                        "avg_entry_efficiency": statistics.mean([t.entry_efficiency for t in trades]),
                        "best_trade": max(returns),
                        "worst_trade": min(returns),
                        "consistency_score": 100 - (statistics.stdev(returns) if len(returns) > 1 else 0)
                    }

            # Sort by total PnL
            sorted_symbols = sorted(symbol_performance.items(),
                                  key=lambda x: x[1]["total_pnl"], reverse=True)

            return {
                "symbol_details": dict(sorted_symbols),
                "top_performers": [s[0] for s in sorted_symbols[:5]],
                "worst_performers": [s[0] for s in sorted_symbols[-5:]]
            }

        except Exception as e:
            self.logger.error(f"Error analyzing symbol performance: {e}")
            return {}

    def analyze_market_regimes(self, analyses: List[EntryAnalysis]) -> Dict[str, Any]:
        """Analyze performance across different market regimes."""
        try:
            regime_data = defaultdict(list)

            for analysis in analyses:
                regime_data[analysis.market_regime].append(analysis)

            regime_analysis = {}

            for regime, trades in regime_data.items():
                if len(trades) >= 2:
                    returns = [t.return_pct for t in trades]
                    pnls = [t.pnl for t in trades]

                    regime_analysis[regime] = {
                        "trade_count": len(trades),
                        "avg_return": statistics.mean(returns),
                        "total_pnl": sum(pnls),
                        "win_rate": len([t for t in trades if t.pnl > 0]) / len(trades) * 100,
                        "avg_entry_efficiency": statistics.mean([t.entry_efficiency for t in trades]),
                        "best_performing_symbols": self.get_top_symbols_in_regime(trades, 3)
                    }

            return regime_analysis

        except Exception as e:
            self.logger.error(f"Error analyzing market regimes: {e}")
            return {}

    def get_top_symbols_in_regime(self, trades: List[EntryAnalysis], top_n: int) -> List[str]:
        """Get top performing symbols in a market regime."""
        try:
            symbol_performance = defaultdict(list)

            for trade in trades:
                symbol_performance[trade.symbol].append(trade.return_pct)

            symbol_avg = {symbol: statistics.mean(returns)
                         for symbol, returns in symbol_performance.items()}

            return [s[0] for s in sorted(symbol_avg.items(), key=lambda x: x[1], reverse=True)[:top_n]]

        except Exception as e:
            self.logger.warning(f"Error getting top symbols in regime: {e}")
            return []

    def generate_entry_insights(self, analyses: List[EntryAnalysis]) -> List[str]:
        """Generate actionable insights for improving entry handling."""
        try:
            insights = []

            if not analyses:
                return ["No trade data available for analysis"]

            # Analyze entry efficiency
            avg_efficiency = statistics.mean([a.entry_efficiency for a in analyses])
            if avg_efficiency < 60:
                insights.append(f"⚠️ Entry efficiency is low ({avg_efficiency:.1f}/100). "
                              "Consider improving order placement relative to bid/ask spread.")

            # Analyze timing scores
            avg_timing = statistics.mean([a.entry_timing_score for a in analyses])
            if avg_timing < 50:
                insights.append(f"⚠️ Entry timing score is below average ({avg_timing:.1f}/100). "
                              "Review market timing and entry conditions.")

            # Analyze win rate by market regime
            regime_analysis = self.analyze_market_regimes(analyses)
            if regime_analysis:
                best_regime = max(regime_analysis.items(), key=lambda x: x[1]["win_rate"])
                worst_regime = min(regime_analysis.items(), key=lambda x: x[1]["win_rate"])

                if best_regime[1]["win_rate"] > 60:
                    insights.append(f"✅ Best performance in {best_regime[0]} conditions "
                                  f"({best_regime[1]['win_rate']:.1f}% win rate). "
                                  "Focus entries during these market conditions.")

                if worst_regime[1]["win_rate"] < 30:
                    insights.append(f"⚠️ Poor performance in {worst_regime[0]} conditions "
                                  f"({worst_regime[1]['win_rate']:.1f}% win rate). "
                                  "Avoid entries during these market conditions.")

            # Analyze duration patterns
            timing_analysis = self.analyze_entry_timing(analyses)
            if timing_analysis.get("duration_analysis", {}).get("quick_exits"):
                quick_exits = timing_analysis["duration_analysis"]["quick_exits"]
                quick_exit_win_rate = quick_exits.get("win_rate", 0)

                if quick_exit_win_rate < 40:
                    insights.append(f"⚠️ Quick exits (< 5 min) have poor win rate ({quick_exit_win_rate:.1f}%). "
                                  "Consider wider stops or better entry confirmation.")

            # Symbol-specific insights
            symbol_analysis = self.analyze_symbol_performance(analyses)
            if symbol_analysis.get("worst_performers"):
                worst_symbols = symbol_analysis["worst_performers"][:3]
                if worst_symbols:
                    insights.append(f"⚠️ Poor performance on: {', '.join(worst_symbols)}. "
                                  "Consider reducing position sizes or avoiding these symbols.")

            if not insights:
                insights.append("✅ Entry handling appears reasonable. Continue monitoring for optimization opportunities.")

            return insights

        except Exception as e:
            self.logger.error(f"Error generating insights: {e}")
            return [f"Error generating insights: {e}"]

def print_dashboard(results: Dict[str, Any]):
    """Print a formatted dashboard of the analysis results."""
    print("\n" + "="*80)
    print("📊 TRADE ENTRY ANALYSIS DASHBOARD")
    print("="*80)

    if "error" in results:
        print(f"❌ Error: {results['error']}")
        return

    summary = results.get("summary", {})
    if "error" in summary:
        print(f"❌ Error: {summary['error']}")
        return

    # Summary section
    print("\n📈 SUMMARY STATISTICS")
    print("-" * 50)
    print(f"Total Trades Analyzed: {summary.get('total_trades', 0)}")
    print(f"Win Rate: {summary.get('win_rate', 0):.2f}%")
    print(f"Total PnL: ${summary.get('total_pnl', 0):.2f}")
    print(f"Avg Entry Efficiency: {summary.get('avg_entry_efficiency', 0):.1f}/100")
    print(f"Avg Entry Timing Score: {summary.get('avg_entry_timing_score', 0):.1f}/100")

    # Entry patterns
    patterns = results.get("entry_patterns", {})
    if patterns:
        print("🎯 ENTRY PATTERNS ANALYSIS" )       
        print("-" * 50)

        for pattern_name, pattern_data in patterns.items():
            print(f"\n{pattern_name.upper()}:")
            print(f"  Success Rate: {pattern_data.success_rate:.1f}%")
            print(f"  Avg Return: {pattern_data.avg_return:.4f}%")
            print(f"  Frequency: {pattern_data.frequency} trades")
            if pattern_data.best_symbols:
                print(f"  Best Symbols: {', '.join(pattern_data.best_symbols)}")
            if pattern_data.worst_symbols:
                print(f"  Worst Symbols: {', '.join(pattern_data.worst_symbols)}")

    # Symbol performance
    symbol_perf = results.get("symbol_performance", {})
    if symbol_perf.get("symbol_details"):
        print("TOP PERFORMING SYMBOLS")
        print("-" * 50)

        details = symbol_perf["symbol_details"]
        for i, (symbol, data) in enumerate(list(details.items())[:5], 1):
            print(f"{i}. {symbol}:")
            print(f"   Trades: {data['trade_count']}")
            print(f"   Win Rate: {data['win_rate']:.1f}%")
            print(f"   Total PnL: ${data['total_pnl']:.2f}")
            print(f"   Avg Return: {data['avg_return']:.4f}%")
            print(f"   Entry Efficiency: {data['avg_entry_efficiency']:.1f}/100")

    # Market regime analysis
    regime_analysis = results.get("market_regime_analysis", {})
    if regime_analysis:
        print("🌍 MARKET REGIME ANALYSIS")
        print("-" * 50)

        for regime, data in regime_analysis.items():
            print(f"\n{regime.upper()} Conditions:")
            print(f"  Trades: {data['trade_count']}")
            print(f"  Win Rate: {data['win_rate']:.1f}%")
            print(f"  Avg Return: {data['avg_return']:.4f}%")
            print(f"  Entry Efficiency: {data['avg_entry_efficiency']:.1f}/100")
            if data.get("best_performing_symbols"):
                print(f"  Best Symbols: {', '.join(data['best_performing_symbols'])}")

    # Improvement recommendations
    recommendations = results.get("improvement_recommendations", [])
    if recommendations:
        print("IMPROVEMENT RECOMMENDATIONS")     
        print("-" * 50)
        for i, recommendation in enumerate(recommendations, 1):
            print(f"{i}. {recommendation}")

    print("\n" + "="*80)

def main():
    """Main function to run the entry analysis dashboard."""
    try:
        # Load configuration
        config = Config.from_yaml()

        # Create analyzer
        analyzer = TradeEntryAnalyzer(config, use_testnet=False)

        print("🔍 Analyzing trade entries for improved entry handling...")

        # Get comprehensive analysis (last 14 days for better sample size)
        results = analyzer.get_comprehensive_trade_data(days=14)

        # Print dashboard
        print_dashboard(results)

        # Save detailed results to file (serialize properly)
        import json

        def serialize_for_json(obj):
            """Custom serializer to handle complex objects."""
            if hasattr(obj, '__dict__'):
                # Convert dataclass objects to dictionaries
                return obj.__dict__
            elif hasattr(obj, '__dataclass_fields__'):
                # Handle dataclass objects
                return {field: getattr(obj, field) for field in obj.__dataclass_fields__}
            elif isinstance(obj, datetime):
                return obj.isoformat()
            return str(obj)

        with open('trade_entry_analysis_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=serialize_for_json)

        print("✅ Analysis complete! Detailed results saved to 'trade_entry_analysis_results.json'")

    except Exception as e:
        logger.error(f"Error in main: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()