#!/usr/bin/env python3
"""
Test file to analyze distance from entry to last price for winning and losing trades using real Bybit data.
This script fetches execution history from Bybit API and calculates price movements for both profitable and losing trades.
"""

import logging
import sys
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from trading_bot.config.settings import Config
from trading_bot.core.bybit_api_manager import BybitAPIManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trade_distance_analysis.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class TradeExecution:
    """Represents a single execution/trade from Bybit."""
    symbol: str
    side: str  # Buy or Sell
    order_price: float
    exec_price: float
    exec_qty: float
    exec_time: datetime
    order_id: str
    pnl: float = 0.0

@dataclass
class TradeAnalysis:
    """Analysis results for a trade."""
    symbol: str
    side: str
    entry_price: float
    exit_price: float
    total_quantity: float
    avg_entry_price: float
    avg_exit_price: float
    pnl: float
    price_distance: float
    return_pct: float
    duration_minutes: float
    executions: List[TradeExecution]

class TradeDistanceAnalyzer:
    """Analyzes distance from entry to exit price for trades."""

    def __init__(self, config: Config, use_testnet: bool = False):
        self.config = config
        self.bybit_api = BybitAPIManager(config, use_testnet)
        self.logger = logging.getLogger(__name__)

    def get_execution_history(self, days: int = 7) -> List[Dict[str, Any]]:
        """
        Get execution history from Bybit for the specified number of days.

        Args:
            days: Number of days of history to fetch

        Returns:
            List of execution records
        """
        try:
            # Calculate time range
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=days)

            # Convert to milliseconds
            start_time_ms = int(start_time.timestamp() * 1000)
            end_time_ms = int(end_time.timestamp() * 1000)

            self.logger.info(f"Fetching execution history from {start_time} to {end_time}")

            # Get executions with pagination
            all_executions = []
            cursor = None

            while True:
                params = {
                    "startTime": start_time_ms,
                    "endTime": end_time_ms,
                    "limit": 100
                }

                if cursor:
                    params["cursor"] = cursor

                response = self.bybit_api.get_executions(**params)

                if response.get("retCode") != 0:
                    self.logger.error(f"Failed to get executions: {response}")
                    break

                executions = response.get("result", {}).get("list", [])
                if not executions:
                    break

                all_executions.extend(executions)
                self.logger.info(f"Fetched {len(executions)} executions (total: {len(all_executions)})")

                # Check if there's a next page
                next_cursor = response.get("result", {}).get("nextPageCursor")
                if not next_cursor or cursor == next_cursor:
                    break

                cursor = next_cursor

            self.logger.info(f"Total executions fetched: {len(all_executions)}")
            return all_executions

        except Exception as e:
            self.logger.error(f"Error getting execution history: {e}")
            return []

    def group_executions_by_order(self, executions: List[Dict[str, Any]]) -> Dict[str, List[TradeExecution]]:
        """
        Group executions by order ID to reconstruct complete trades.

        Args:
            executions: List of execution records

        Returns:
            Dictionary mapping order_id to list of executions
        """
        order_groups = defaultdict(list)

        for exec_data in executions:
            try:
                execution = TradeExecution(
                    symbol=exec_data.get("symbol", ""),
                    side=exec_data.get("side", ""),
                    order_price=float(exec_data.get("orderPrice", 0)),
                    exec_price=float(exec_data.get("execPrice", 0)),
                    exec_qty=float(exec_data.get("execQty", 0)),
                    exec_time=datetime.fromtimestamp(
                        int(exec_data.get("execTime", 0)) / 1000, timezone.utc
                    ),
                    order_id=exec_data.get("orderId", "")
                )
                order_groups[execution.order_id].append(execution)

            except (ValueError, KeyError) as e:
                self.logger.warning(f"Skipping invalid execution data: {e}")
                continue

        return dict(order_groups)

    def calculate_trade_metrics(self, executions: List[TradeExecution]) -> TradeAnalysis:
        """
        Calculate trade metrics from executions.

        Args:
            executions: List of executions for a single order

        Returns:
            TradeAnalysis object with calculated metrics
        """
        if not executions:
            raise ValueError("No executions provided")

        # Sort executions by time
        executions = sorted(executions, key=lambda x: x.exec_time)

        symbol = executions[0].symbol
        side = executions[0].side

        # For simplicity, assume first execution is entry, last is exit
        # In reality, this would need more sophisticated logic to handle partial fills
        entry_exec = executions[0]
        exit_exec = executions[-1]

        # Calculate weighted average prices
        total_qty = sum(exec.exec_qty for exec in executions)
        avg_entry_price = entry_exec.exec_price  # Simplified
        avg_exit_price = exit_exec.exec_price    # Simplified

        # Calculate PnL (simplified calculation)
        if side.upper() == "BUY":
            pnl = (avg_exit_price - avg_entry_price) * total_qty
            price_distance = avg_exit_price - avg_entry_price
        else:  # SELL
            pnl = (avg_entry_price - avg_exit_price) * total_qty
            price_distance = avg_entry_price - avg_exit_price

        # Calculate return percentage
        return_pct = (price_distance / avg_entry_price) * 100 if avg_entry_price > 0 else 0

        # Calculate duration
        duration_minutes = (exit_exec.exec_time - entry_exec.exec_time).total_seconds() / 60

        return TradeAnalysis(
            symbol=symbol,
            side=side,
            entry_price=entry_exec.exec_price,
            exit_price=exit_exec.exec_price,
            total_quantity=total_qty,
            avg_entry_price=avg_entry_price,
            avg_exit_price=avg_exit_price,
            pnl=pnl,
            price_distance=price_distance,
            return_pct=return_pct,
            duration_minutes=duration_minutes,
            executions=executions
        )

    def analyze_trade_distances(self, days: int = 7) -> Dict[str, Any]:
        """
        Main analysis function to calculate distances for winning and losing trades.

        Args:
            days: Number of days of history to analyze

        Returns:
            Dictionary with analysis results
        """
        self.logger.info(f"Starting trade distance analysis for {days} days")

        # Get execution history
        executions = self.get_execution_history(days)
        if not executions:
            return {"error": "No execution data available"}

        # Group by order
        order_groups = self.group_executions_by_order(executions)

        # Analyze each order
        winning_trades = []
        losing_trades = []
        all_trades = []

        for order_id, execs in order_groups.items():
            if len(execs) < 2:  # Need at least entry and exit
                continue

            try:
                trade_analysis = self.calculate_trade_metrics(execs)

                # Classify as winning or losing
                if trade_analysis.pnl > 0:
                    winning_trades.append(trade_analysis)
                else:
                    losing_trades.append(trade_analysis)

                all_trades.append(trade_analysis)

            except Exception as e:
                self.logger.warning(f"Error analyzing order {order_id}: {e}")
                continue

        # Calculate statistics
        def calculate_stats(trades: List[TradeAnalysis]) -> Dict[str, Any]:
            if not trades:
                return {
                    "count": 0,
                    "avg_price_distance": 0,
                    "avg_return_pct": 0,
                    "total_pnl": 0,
                    "min_price_distance": 0,
                    "max_price_distance": 0
                }

            price_distances = [t.price_distance for t in trades]
            returns = [t.return_pct for t in trades]
            pnls = [t.pnl for t in trades]

            return {
                "count": len(trades),
                "avg_price_distance": sum(price_distances) / len(price_distances),
                "avg_return_pct": sum(returns) / len(returns),
                "total_pnl": sum(pnls),
                "min_price_distance": min(price_distances),
                "max_price_distance": max(price_distances)
            }

        winning_stats = calculate_stats(winning_trades)
        losing_stats = calculate_stats(losing_trades)

        # Get top trades by return percentage
        top_winning = sorted(winning_trades, key=lambda x: x.return_pct, reverse=True)[:10]
        top_losing = sorted(losing_trades, key=lambda x: x.return_pct)[:10]

        return {
            "summary": {
                "total_trades_analyzed": len(all_trades),
                "winning_trades": winning_stats,
                "losing_trades": losing_stats,
                "win_rate": len(winning_trades) / len(all_trades) * 100 if all_trades else 0
            },
            "top_winning_trades": [
                {
                    "symbol": t.symbol,
                    "side": t.side,
                    "entry_price": t.entry_price,
                    "exit_price": t.exit_price,
                    "return_pct": round(t.return_pct, 4),
                    "pnl": round(t.pnl, 2),
                    "duration_minutes": round(t.duration_minutes, 2)
                }
                for t in top_winning
            ],
            "top_losing_trades": [
                {
                    "symbol": t.symbol,
                    "side": t.side,
                    "entry_price": t.entry_price,
                    "exit_price": t.exit_price,
                    "return_pct": round(t.return_pct, 4),
                    "pnl": round(t.pnl, 2),
                    "duration_minutes": round(t.duration_minutes, 2)
                }
                for t in top_losing
            ]
        }

def main():
    """Main function to run the trade distance analysis."""
    try:
        # Load configuration
        config = Config.from_yaml()

        # Create analyzer (set use_testnet=True for testing)
        analyzer = TradeDistanceAnalyzer(config, use_testnet=False)

        # Analyze trades for the last 7 days
        results = analyzer.analyze_trade_distances(days=7)

        if "error" in results:
            print(f"Error: {results['error']}")
            return

        # Print results
        summary = results["summary"]
        print("\n" + "="*60)
        print("TRADE DISTANCE ANALYSIS RESULTS")
        print("="*60)

        print(f"\nTotal Trades Analyzed: {summary['total_trades_analyzed']}")
        print(f"Win Rate: {summary['win_rate']:.2f}%")

        print("\n" + "-"*40)
        print("WINNING TRADES")
        print("-"*40)
        winning = summary["winning_trades"]
        print(f"Count: {winning['count']}")
        print(f"Average Price Distance: {winning['avg_price_distance']:.6f}")
        print(f"Average Return: {winning['avg_return_pct']:.4f}%")
        print(f"Total PnL: ${winning['total_pnl']:.2f}")
        print(f"Price Distance Range: {winning['min_price_distance']:.6f} to {winning['max_price_distance']:.6f}")

        print("\n" + "-"*40)
        print("LOSING TRADES")
        print("-"*40)
        losing = summary["losing_trades"]
        print(f"Count: {losing['count']}")
        print(f"Average Price Distance: {losing['avg_price_distance']:.6f}")
        print(f"Average Return: {losing['avg_return_pct']:.4f}%")
        print(f"Total PnL: ${losing['total_pnl']:.2f}")
        print(f"Price Distance Range: {losing['min_price_distance']:.6f} to {losing['max_price_distance']:.6f}")

        print("\n" + "-"*40)
        print("TOP 5 WINNING TRADES")
        print("-"*40)
        for i, trade in enumerate(results["top_winning_trades"][:5], 1):
            print(f"{i}. {trade['symbol']} {trade['side']}: "
                  f"{trade['entry_price']:.4f} → {trade['exit_price']:.4f} "
                  f"({trade['return_pct']:.2f}%, ${trade['pnl']:.2f})")

        print("\n" + "-"*40)
        print("TOP 5 LOSING TRADES")
        print("-"*40)
        for i, trade in enumerate(results["top_losing_trades"][:5], 1):
            print(f"{i}. {trade['symbol']} {trade['side']}: "
                  f"{trade['entry_price']:.4f} → {trade['exit_price']:.4f} "
                  f"({trade['return_pct']:.2f}%, ${trade['pnl']:.2f})")

        print("\n" + "="*60)

    except Exception as e:
        logger.error(f"Error in main: {e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()