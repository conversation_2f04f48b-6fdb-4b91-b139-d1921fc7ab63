#!/usr/bin/env python3
"""
Simple Raw Data Fetcher - Gets ONLY raw trade data from Bybit API and saves as simple table.
No analysis, no processing - just pure raw data for the dashboard.
"""

import logging
import sys
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any
import json

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from trading_bot.config.settings import Config
from trading_bot.core.bybit_api_manager import BybitAPIManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('raw_data_fetch.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class RawDataFetcher:
    """Fetches ONLY raw execution data from Bybit API."""

    def __init__(self, config: Config, use_testnet: bool = False):
        self.config = config
        self.bybit_api = BybitAPIManager(config, use_testnet)
        self.logger = logging.getLogger(__name__)

    def fetch_raw_executions(self, days: int = 7) -> List[Dict[str, Any]]:
        """
        Fetch raw execution data from Bybit API.

        Args:
            days: Number of days of history to fetch

        Returns:
            List of raw execution records
        """
        try:
            self.logger.info(f"🔍 Fetching {days} days of raw execution data...")

            all_executions = []

            # Bybit API limit is 7 days, so fetch in chunks if needed
            chunk_size = 7
            for i in range(0, min(days, 30), chunk_size):  # Max 30 days
                current_chunk = min(chunk_size, days - i)
                end_time = datetime.now(timezone.utc) - timedelta(days=i)
                start_time = end_time - timedelta(days=current_chunk)

                start_time_ms = int(start_time.timestamp() * 1000)
                end_time_ms = int(end_time.timestamp() * 1000)

                self.logger.info(f"📊 Fetching chunk: {start_time.strftime('%Y-%m-%d')} to {end_time.strftime('%Y-%m-%d')}")

                cursor = None
                while True:
                    params = {
                        "startTime": start_time_ms,
                        "endTime": end_time_ms,
                        "limit": 100
                    }

                    if cursor:
                        params["cursor"] = cursor

                    response = self.bybit_api.get_executions(**params)

                    if response.get("retCode") != 0:
                        self.logger.error(f"❌ Failed to get executions: {response}")
                        break

                    executions = response.get("result", {}).get("list", [])
                    if not executions:
                        break

                    all_executions.extend(executions)
                    self.logger.info(f"  ✅ Fetched {len(executions)} executions (total: {len(all_executions)})")

                    next_cursor = response.get("result", {}).get("nextPageCursor")
                    if not next_cursor or cursor == next_cursor:
                        break

                    cursor = next_cursor

            self.logger.info(f"🎯 Total raw executions fetched: {len(all_executions)}")
            return all_executions

        except Exception as e:
            self.logger.error(f"❌ Error fetching raw data: {e}")
            return []

def main():
    """Main function to fetch and save raw data."""
    try:
        print("🔍 SIMPLE RAW DATA FETCHER")
        print("=" * 40)

        # Load configuration
        config = Config.from_yaml()

        # Create fetcher
        fetcher = RawDataFetcher(config, use_testnet=False)

        print("📊 Fetching raw execution data from Bybit API...")

        # Fetch raw executions for the last 7 days
        raw_executions = fetcher.fetch_raw_executions(days=7)

        if not raw_executions:
            print("❌ No execution data fetched!")
            return

        # Save raw data to simple JSON file
        print(f"💾 Saving {len(raw_executions)} raw executions to JSON...")

        output_data = {
            "metadata": {
                "fetch_date": datetime.now(timezone.utc).isoformat(),
                "total_executions": len(raw_executions),
                "data_type": "raw_executions_only"
            },
            "raw_executions": raw_executions
        }

        with open('trade_entry_analysis/raw_trade_data_only.json', 'w') as f:
            json.dump(output_data, f, indent=2)

        print("✅ RAW DATA SAVED!")
        print("=" * 40)
        print(f"📊 Total Executions: {len(raw_executions)}")
        print(f"💾 File: 'raw_trade_data_only.json'")
        print()
        print("🎯 This file contains ONLY raw execution data")
        print("🚀 Ready for dashboard or manual analysis!")

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()