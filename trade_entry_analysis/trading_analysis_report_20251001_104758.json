{"generated_at": "2025-10-01T10:47:58.214447", "database_overview": {"database_info": {"sqlite_version": "3.45.1", "tables": ["analysis_results", "latest_recommendations", "trades"]}, "table_structure": {"analysis_results": {"row_count": 1028, "columns": {"id": {"type": "TEXT", "nullable": true, "primary_key": 1}, "symbol": {"type": "TEXT", "nullable": false, "primary_key": 0}, "timeframe": {"type": "TEXT", "nullable": false, "primary_key": 0}, "recommendation": {"type": "TEXT", "nullable": false, "primary_key": 0}, "confidence": {"type": "REAL", "nullable": false, "primary_key": 0}, "summary": {"type": "TEXT", "nullable": true, "primary_key": 0}, "evidence": {"type": "TEXT", "nullable": true, "primary_key": 0}, "support_level": {"type": "REAL", "nullable": true, "primary_key": 0}, "resistance_level": {"type": "REAL", "nullable": true, "primary_key": 0}, "entry_price": {"type": "REAL", "nullable": true, "primary_key": 0}, "stop_loss": {"type": "REAL", "nullable": true, "primary_key": 0}, "take_profit": {"type": "REAL", "nullable": true, "primary_key": 0}, "direction": {"type": "TEXT", "nullable": true, "primary_key": 0}, "rr": {"type": "REAL", "nullable": true, "primary_key": 0}, "risk_factors": {"type": "TEXT", "nullable": true, "primary_key": 0}, "analysis_data": {"type": "TEXT", "nullable": true, "primary_key": 0}, "analysis_prompt": {"type": "TEXT", "nullable": true, "primary_key": 0}, "timestamp": {"type": "DATETIME", "nullable": true, "primary_key": 0}, "image_path": {"type": "TEXT", "nullable": true, "primary_key": 0}, "market_condition": {"type": "TEXT", "nullable": true, "primary_key": 0}, "market_direction": {"type": "TEXT", "nullable": true, "primary_key": 0}}, "sample_data": {"id": "de2913ce-fe13-4729-9bf0-2a82e860f3d4", "symbol": "HYPERUSDT", "timeframe": "1h", "recommendation": "hold", "confidence": 0.0, "summary": "The chart shows a recent strong downtrend with a recovery pullback failing near EMA200. Price now pulls back testing Fibonacci 0.618 support with weak momentum and volume. Risk-reward is insufficient for a confident long trade.", "evidence": "Price formed a sharp downtrend, bounced from 0.2449 low, then pulled back near 0.3. The EMA200 (~0.29) acts as strong resistance. The current pullback is testing the 0.618 Fib level (~0.2662) with low volume and MACD still negative, signaling weak bullish momentum. Stoch RSI is near oversold but not confirming reversal. OBV declining indicates weak buying interest. Risk-reward for entry near 0.269 with stop ~0.2568 is below 2:1. Thus, no high-confidence long setup yet.", "support_level": 0.2662, "resistance_level": 0.2875, "entry_price": 0.269, "stop_loss": 0.2568, "take_profit": 0.2875, "direction": "LONG", "rr": 1.52, "risk_factors": "[\"Weak MACD momentum below zero\", \"Price below EMA200 resistance\", \"Low volume on recent pullback\"]", "analysis_data": "{\"image\": \"trading_bot/data/charts/HYPERUSDT_1h_20250925_120000.png\", \"symbol\": \"HYPERUSDT\", \"timeframe\": \"1h\", \"analysis\": {\"recommendation\": \"hold\", \"summary\": \"The chart shows a recent strong downtrend with a recovery pullback failing near EMA200. Price now pulls back testing Fibonacci 0.618 support with weak momentum and volume. Risk-reward is insufficient for a confident long trade.\", \"key_levels\": {\"support\": 0.2662, \"resistance\": 0.2875}, \"risk_factors\": [\"Weak MACD momentum below zero\", \"Price below EMA200 resistance\", \"Low volume on recent pullback\"], \"market_condition\": \"TRENDING\", \"market_direction\": \"DOWN\", \"evidence\": \"Price formed a sharp downtrend, bounced from 0.2449 low, then pulled back near 0.3. The EMA200 (~0.29) acts as strong resistance. The current pullback is testing the 0.618 Fib level (~0.2662) with low volume and MACD still negative, signaling weak bullish momentum. Stoch RSI is near oversold but not confirming reversal. OBV declining indicates weak buying interest. Risk-reward for entry near 0.269 with stop ~0.2568 is below 2:1. Thus, no high-confidence long setup yet.\", \"entry_price\": 0.269, \"stop_loss\": 0.2568, \"take_profit\": 0.2875, \"direction\": \"Long\", \"entry_explanation\": \"Entry near Fibonacci 0.618 support after pullback but without volume or momentum confirmation, making timing poor.\", \"take_profit_explanation\": \"Take profit at resistance near recent swing high and EMA200 (~0.2875) is realistic if bullish strength returns.\", \"stop_loss_explanation\": \"Stop placed just below 0.786 Fib level (0.2568) plus ATR buffer accounts for volatility and prevents premature stop out.\", \"confidence\": 0.0, \"risk_reward_ratio\": 1.3, \"market_regime\": \"trending_moderate\", \"pattern_type\": \"pullback_entry\", \"timeframe\": \"short_term\", \"extracted_timeframe\": \"1h\", \"normalized_timeframe\": \"1h\", \"symbol\": \"HYPERUSDT\", \"last_close_price\": 0.2684, \"trade_confidence\": 0.0, \"timestamp\": \"2025-09-25 12:00:00\", \"original_timeframe\": \"1h\", \"analysis_method\": \"assistant\", \"analysis_prompt\": \"Analyze the chart image and market data to provide a high-probability trading recommendation.\\n    Focus on clear chart patterns and technical setups for consistent trading results.\\n\\n    ### CURRENT MARKET DATA\\n        \\n    - Best Bid: 0.2691\\n    - Best Ask: 0.2692\\n    - Funding Rate: 0.0000125\\n    - Long/Short Ratio: Buy: 0.617, Sell: 0.383\\n    - Timeframe: \\\"1h\\\"\\n    - Symbol: \\\"HYPERUSDT\\n\\n    ### MARKET CONTEXT\\n    **Funding Rate**: 0.0000125 (Normal conditions)\\n    **Long/Short Ratio**: Buy: 0.617, Sell: 0.383 (Crowded trade)\\n    **Timeframe**: 1h (Standard analysis)\\n\\n    ### STEP 1: MARKET REGIME ANALYSIS\\n    1. **Trend Direction**: UP (higher highs/lows), DOWN (lower highs/lows), or SIDEWAYS (no clear direction)\\n    2. **Trend Strength**: Strong (clear momentum), Moderate (some hesitation), Weak (choppy price action)\\n    3. **Key Levels**: Identify support/resistance with multiple touches and reactions\\n\\n    ### STEP 2: HIGH-PROBABILITY PATTERN RECOGNITION\\n    Look for these proven patterns with high success rates:\\n\\n    **TRENDING MARKETS:**\\n    - **Breakout Retest**: Price breaks level, pulls back to test, then continues (85% success rate)\\n    - **Pullback to EMA**: Price pulls back to 20/50 EMA in strong trend (78% success rate)\\n    - **Trend Continuation**: After consolidation, price resumes trend (82% success rate)\\n\\n    **RANGING MARKETS:**\\n    - **Range Bounce**: Price bounces off support/resistance with volume (75% success rate)\\n    - **False Breakout Fade**: Price breaks level but fails, reverses back (70% success rate)\\n    - **Mean Reversion**: Price returns to range midpoint (65% success rate)\\n\\n    **ALL CONDITIONS:**\\n    - **Support/Resistance Test**: Multiple touches with clear reactions (80% success rate)\\n    - **Volume Confirmation**: Price moves with increasing volume (75% success rate)\\n    - **RSI Divergence**: Price makes new high/low but RSI doesn't confirm (70% success rate)\\n\\n    ### STEP 3: ENTRY STRATEGY BY MARKET REGIME\\n\\n    **TRENDING MARKETS:**\\n    - **Conservative**: Wait for pullback to support/resistance + confirmation\\n    - **Moderate**: Enter on breakout with volume confirmation\\n    - **Aggressive**: Enter immediately on trend resumption (\\u22644h timeframes only)\\n\\n    **RANGING MARKETS:**\\n    - **Conservative**: Wait for price to reach extreme of range + reversal signal\\n    - **Moderate**: Enter at range boundary with confirmation\\n    - **Hold**: Avoid trading unless exceptional setup\\n\\n    ### STEP 4: RISK MANAGEMENT\\n    **Risk-Reward Requirements:**\\n    - Minimum RR: 2.0:1 (adjusted for market conditions)\\n    - Target RR: 3.0:1 for strong setups\\n\\n    **Stop Loss Placement:**\\n    - **Long**: Below recent swing low or support level\\n    - **Short**: Above recent swing high or resistance level\\n    - **Buffer**: Add 1 ATR for volatility\\n\\n    **Take Profit Placement:**\\n    - **Primary**: Next logical support/resistance level\\n    - **Secondary**: 1.5x distance of stop loss\\n    - **Tertiary**: 2x distance for very strong trends\\n\\n    ### FIXED CONFIDENCE TIERS (0.0-1.0)\\n    Use these fixed confidence values based on setup quality:\\n\\n    **0.85 - HIGH CONFIDENCE:**\\n    - Multiple technical confluences (3+ indicators align)\\n    - Proven high-success pattern (75%+ historical win rate)\\n    - Strong market regime alignment\\n    - Risk-reward \\u2265 3.0:1\\n    - Clear volume confirmation\\n\\n    **0.65 - MEDIUM CONFIDENCE:**\\n    - Good pattern with 1-2 confluences\\n    - Moderate market conditions\\n    - Risk-reward = 2.0:1\\n    - Some volume confirmation\\n\\n    **0.35 - LOW CONFIDENCE:**\\n    - Weak pattern or conflicting signals\\n    - Poor market conditions (ranging/choppy)\\n    - Risk-reward < 2.0:1\\n    - No clear volume confirmation\\n\\n    **0.0 - NO TRADE:**\\n    - Recommend \\\"hold\\\" for any setup below minimum standards\\n\\n    ### RISK FACTORS TO CONSIDER\\n    - **Pattern Risk**: False breakout, pattern failure rate\\n    - **Sentiment Risk**: Extreme funding rates, crowd positioning\\n    - **Timeframe Risk**: Short timeframe noise\\n\\n    ### OUTPUT FORMAT (JSON)\\n    {\\n        \\\"recommendation\\\": \\\"buy\\\" | \\\"hold\\\" | \\\"sell\\\",\\n        \\\"summary\\\": \\\"Concise analysis summary with key setup details (max 50 words)\\\",\\n        \\\"key_levels\\\": {\\\"support\\\": 0.0, \\\"resistance\\\": 0.0},\\n        \\\"risk_factors\\\": [\\\"Specific risk 1\\\", \\\"Specific risk 2\\\"],\\n        \\\"market_condition\\\": \\\"TRENDING\\\" | \\\"RANGING\\\",\\n        \\\"market_direction\\\": \\\"UP\\\" | \\\"DOWN\\\" | \\\"SIDEWAYS\\\",\\n        \\\"evidence\\\": \\\"Specific evidence supporting recommendation with pattern success rate (max 200 words)\\\",\\n        \\\"entry_price\\\": 0.0,\\n        \\\"stop_loss\\\": 0.0,\\n        \\\"take_profit\\\": 0.0,\\n        \\\"direction\\\": \\\"Long\\\" | \\\"Short\\\",\\n        \\\"entry_explanation\\\": \\\"Why this entry timing and price level is optimal\\\",\\n        \\\"take_profit_explanation\\\": \\\"Why this take profit level is realistic and achievable\\\",\\n        \\\"stop_loss_explanation\\\": \\\"Why this stop loss accounts for volatility and pattern risk\\\",\\n        \\\"confidence\\\": 0.0,\\n        \\\"risk_reward_ratio\\\": 0.0,\\n        \\\"market_regime\\\": \\\"trending_strong\\\" | \\\"trending_moderate\\\" | \\\"ranging\\\" | \\\"volatile\\\",\\n        \\\"pattern_type\\\": \\\"breakout_retest\\\" | \\\"pullback_entry\\\" | \\\"range_bounce\\\" | \\\"other\\\",\\n    }\\n\\n    ### VALIDATION RULES\\n    1. **Risk-Reward**: Must be \\u22652.0:1; otherwise, recommend HOLD\\n    2. **Take Profit**: Must be realistic based on timeframe and volatility\\n    3. **Stop Loss**: Must account for normal volatility (use ATR-based placement)\\n    4. **Confidence**: Must use fixed tier system (0.85, 0.65, 0.35, or 0.0)\\n    5. **Market Regime**: Must identify and adapt strategy to current conditions\\n\\n    ### SUCCESS OPTIMIZATION GUIDELINES\\n    - **Pattern Priority**: Focus on patterns with 75%+ historical success rates\\n    - **Risk Management**: Ensure minimum 2.0:1 risk-reward ratio\\n    - **Market Awareness**: Use funding rates and sentiment for additional confirmation\\n    - **Consistency**: Prioritize setup quality over trade frequency\\n    - **Adaptability**: Adjust strategy based on changing market regimes\", \"passed_image\": \"trading_bot/data/charts/HYPERUSDT_1h_20250925_120000.png\", \"prompt_version\": \"get_analyzer_prompt_improved_v28\"}, \"validation\": {\"valid\": false, \"reasons\": [\"Confidence below threshold\"], \"adjusted_confidence\": 0.0}, \"recommendation\": \"hold\", \"confidence\": 0.0, \"cost_usd\": 0.0045, \"tokens\": 1200}", "analysis_prompt": "Analyze the chart image and market data to provide a high-probability trading recommendation.\n    Focus on clear chart patterns and technical setups for consistent trading results.\n\n    ### CURRENT MARKET DATA\n        \n    - Best Bid: 0.2691\n    - Best Ask: 0.2692\n    - Funding Rate: 0.0000125\n    - Long/Short Ratio: Buy: 0.617, Sell: 0.383\n    - Timeframe: \"1h\"\n    - Symbol: \"HYPERUSDT\n\n    ### MARKET CONTEXT\n    **Funding Rate**: 0.0000125 (Normal conditions)\n    **Long/Short Ratio**: Buy: 0.617, Sell: 0.383 (Crowded trade)\n    **Timeframe**: 1h (Standard analysis)\n\n    ### STEP 1: MARKET REGIME ANALYSIS\n    1. **Trend Direction**: UP (higher highs/lows), DOWN (lower highs/lows), or SIDEWAYS (no clear direction)\n    2. **Trend Strength**: Strong (clear momentum), Moderate (some hesitation), Weak (choppy price action)\n    3. **Key Levels**: Identify support/resistance with multiple touches and reactions\n\n    ### STEP 2: HIGH-PROBABILITY PATTERN RECOGNITION\n    Look for these proven patterns with high success rates:\n\n    **TRENDING MARKETS:**\n    - **Breakout Retest**: Price breaks level, pulls back to test, then continues (85% success rate)\n    - **Pullback to EMA**: Price pulls back to 20/50 EMA in strong trend (78% success rate)\n    - **Trend Continuation**: After consolidation, price resumes trend (82% success rate)\n\n    **RANGING MARKETS:**\n    - **Range Bounce**: Price bounces off support/resistance with volume (75% success rate)\n    - **False Breakout Fade**: Price breaks level but fails, reverses back (70% success rate)\n    - **Mean Reversion**: Price returns to range midpoint (65% success rate)\n\n    **ALL CONDITIONS:**\n    - **Support/Resistance Test**: Multiple touches with clear reactions (80% success rate)\n    - **Volume Confirmation**: Price moves with increasing volume (75% success rate)\n    - **RSI Divergence**: Price makes new high/low but RSI doesn't confirm (70% success rate)\n\n    ### STEP 3: ENTRY STRATEGY BY MARKET REGIME\n\n    **TRENDING MARKETS:**\n    - **Conservative**: Wait for pullback to support/resistance + confirmation\n    - **Moderate**: Enter on breakout with volume confirmation\n    - **Aggressive**: Enter immediately on trend resumption (≤4h timeframes only)\n\n    **RANGING MARKETS:**\n    - **Conservative**: Wait for price to reach extreme of range + reversal signal\n    - **Moderate**: Enter at range boundary with confirmation\n    - **Hold**: Avoid trading unless exceptional setup\n\n    ### STEP 4: RISK MANAGEMENT\n    **Risk-Reward Requirements:**\n    - Minimum RR: 2.0:1 (adjusted for market conditions)\n    - Target RR: 3.0:1 for strong setups\n\n    **Stop Loss Placement:**\n    - **Long**: Below recent swing low or support level\n    - **Short**: Above recent swing high or resistance level\n    - **Buffer**: Add 1 ATR for volatility\n\n    **Take Profit Placement:**\n    - **Primary**: Next logical support/resistance level\n    - **Secondary**: 1.5x distance of stop loss\n    - **Tertiary**: 2x distance for very strong trends\n\n    ### FIXED CONFIDENCE TIERS (0.0-1.0)\n    Use these fixed confidence values based on setup quality:\n\n    **0.85 - HIGH CONFIDENCE:**\n    - Multiple technical confluences (3+ indicators align)\n    - Proven high-success pattern (75%+ historical win rate)\n    - Strong market regime alignment\n    - Risk-reward ≥ 3.0:1\n    - Clear volume confirmation\n\n    **0.65 - MEDIUM CONFIDENCE:**\n    - Good pattern with 1-2 confluences\n    - Moderate market conditions\n    - Risk-reward = 2.0:1\n    - Some volume confirmation\n\n    **0.35 - LOW CONFIDENCE:**\n    - Weak pattern or conflicting signals\n    - Poor market conditions (ranging/choppy)\n    - Risk-reward < 2.0:1\n    - No clear volume confirmation\n\n    **0.0 - NO TRADE:**\n    - Recommend \"hold\" for any setup below minimum standards\n\n    ### RISK FACTORS TO CONSIDER\n    - **Pattern Risk**: False breakout, pattern failure rate\n    - **Sentiment Risk**: Extreme funding rates, crowd positioning\n    - **Timeframe Risk**: Short timeframe noise\n\n    ### OUTPUT FORMAT (JSON)\n    {\n        \"recommendation\": \"buy\" | \"hold\" | \"sell\",\n        \"summary\": \"Concise analysis summary with key setup details (max 50 words)\",\n        \"key_levels\": {\"support\": 0.0, \"resistance\": 0.0},\n        \"risk_factors\": [\"Specific risk 1\", \"Specific risk 2\"],\n        \"market_condition\": \"TRENDING\" | \"RANGING\",\n        \"market_direction\": \"UP\" | \"DOWN\" | \"SIDEWAYS\",\n        \"evidence\": \"Specific evidence supporting recommendation with pattern success rate (max 200 words)\",\n        \"entry_price\": 0.0,\n        \"stop_loss\": 0.0,\n        \"take_profit\": 0.0,\n        \"direction\": \"Long\" | \"Short\",\n        \"entry_explanation\": \"Why this entry timing and price level is optimal\",\n        \"take_profit_explanation\": \"Why this take profit level is realistic and achievable\",\n        \"stop_loss_explanation\": \"Why this stop loss accounts for volatility and pattern risk\",\n        \"confidence\": 0.0,\n        \"risk_reward_ratio\": 0.0,\n        \"market_regime\": \"trending_strong\" | \"trending_moderate\" | \"ranging\" | \"volatile\",\n        \"pattern_type\": \"breakout_retest\" | \"pullback_entry\" | \"range_bounce\" | \"other\",\n    }\n\n    ### VALIDATION RULES\n    1. **Risk-Reward**: Must be ≥2.0:1; otherwise, recommend HOLD\n    2. **Take Profit**: Must be realistic based on timeframe and volatility\n    3. **Stop Loss**: Must account for normal volatility (use ATR-based placement)\n    4. **Confidence**: Must use fixed tier system (0.85, 0.65, 0.35, or 0.0)\n    5. **Market Regime**: Must identify and adapt strategy to current conditions\n\n    ### SUCCESS OPTIMIZATION GUIDELINES\n    - **Pattern Priority**: Focus on patterns with 75%+ historical success rates\n    - **Risk Management**: Ensure minimum 2.0:1 risk-reward ratio\n    - **Market Awareness**: Use funding rates and sentiment for additional confirmation\n    - **Consistency**: Prioritize setup quality over trade frequency\n    - **Adaptability**: Adjust strategy based on changing market regimes", "timestamp": "2025-09-25 12:00:00", "image_path": "trading_bot/data/charts/HYPERUSDT_1h_20250925_120000.png", "market_condition": "TRENDING", "market_direction": "DOWN"}}, "latest_recommendations": {"row_count": 0, "columns": {"id": {"type": "TEXT", "nullable": true, "primary_key": 1}, "symbol": {"type": "TEXT", "nullable": false, "primary_key": 0}, "timeframe": {"type": "TEXT", "nullable": false, "primary_key": 0}, "recommendation": {"type": "TEXT", "nullable": false, "primary_key": 0}, "confidence": {"type": "REAL", "nullable": false, "primary_key": 0}, "summary": {"type": "TEXT", "nullable": true, "primary_key": 0}, "support_level": {"type": "REAL", "nullable": true, "primary_key": 0}, "resistance_level": {"type": "REAL", "nullable": true, "primary_key": 0}, "entry_price": {"type": "REAL", "nullable": true, "primary_key": 0}, "stop_loss": {"type": "REAL", "nullable": true, "primary_key": 0}, "take_profit": {"type": "REAL", "nullable": true, "primary_key": 0}, "direction": {"type": "TEXT", "nullable": true, "primary_key": 0}, "rr": {"type": "REAL", "nullable": true, "primary_key": 0}, "risk_factors": {"type": "TEXT", "nullable": true, "primary_key": 0}, "analysis_data": {"type": "TEXT", "nullable": true, "primary_key": 0}, "timestamp": {"type": "DATETIME", "nullable": true, "primary_key": 0}, "image_path": {"type": "TEXT", "nullable": true, "primary_key": 0}, "market_condition": {"type": "TEXT", "nullable": true, "primary_key": 0}, "market_direction": {"type": "TEXT", "nullable": true, "primary_key": 0}}}, "trades": {"row_count": 60, "columns": {"id": {"type": "TEXT", "nullable": true, "primary_key": 1}, "recommendation_id": {"type": "TEXT", "nullable": true, "primary_key": 0}, "symbol": {"type": "TEXT", "nullable": false, "primary_key": 0}, "side": {"type": "TEXT", "nullable": false, "primary_key": 0}, "quantity": {"type": "REAL", "nullable": false, "primary_key": 0}, "entry_price": {"type": "REAL", "nullable": true, "primary_key": 0}, "take_profit": {"type": "REAL", "nullable": true, "primary_key": 0}, "stop_loss": {"type": "REAL", "nullable": true, "primary_key": 0}, "order_id": {"type": "TEXT", "nullable": true, "primary_key": 0}, "orderLinkId": {"type": "TEXT", "nullable": true, "primary_key": 0}, "pnl": {"type": "REAL", "nullable": true, "primary_key": 0}, "status": {"type": "TEXT", "nullable": true, "primary_key": 0}, "state": {"type": "TEXT", "nullable": true, "primary_key": 0}, "avg_exit_price": {"type": "REAL", "nullable": true, "primary_key": 0}, "closed_size": {"type": "REAL", "nullable": true, "primary_key": 0}, "created_at": {"type": "DATETIME", "nullable": true, "primary_key": 0}, "updated_at": {"type": "DATETIME", "nullable": true, "primary_key": 0}, "placed_by": {"type": "TEXT", "nullable": true, "primary_key": 0}, "alteration_details": {"type": "TEXT", "nullable": true, "primary_key": 0}}, "sample_data": {"id": "0e4f489c-718f-410f-bea4-d1ea6f25c9d9", "recommendation_id": "aa3deed2-66c5-4876-92c3-51ddedc1cf68", "symbol": "AVAXUSDT", "side": "<PERSON>ll", "quantity": 18.6, "entry_price": 30.47398387, "take_profit": 29.11, "stop_loss": 31.2, "order_id": null, "orderLinkId": null, "pnl": 24.04532909, "status": "closed", "state": "position", "avg_exit_price": 29.149, "closed_size": 18.6, "created_at": "2025-09-25 14:07:12", "updated_at": "2025-09-25 18:08:37", "placed_by": "BYBIT_POSITION", "alteration_details": null}}}, "data_summary": {"analysis_results": {"total_analyses": 1028, "unique_symbols": 19, "unique_timeframes": 1, "first_analysis": "2025-09-25 12:00:00", "latest_analysis": "2025-09-30 22:00:00"}, "trades": {"total_trades": 60, "unique_trade_symbols": 12, "closed_trades": 58, "open_trades": 2, "first_trade": "2025-09-25 13:12:44", "latest_trade": "2025-09-30 19:18:56"}}, "analysis_data_quality": {}}, "trading_performance": {"overall_statistics": {"total_trades": 58, "profitable_trades": 11, "loss_trades": 47, "win_rate": 18.96551724137931, "total_pnl": -551.68466341, "avg_pnl_per_trade": -9.511804541551724, "total_analyses": 1056, "trade_conversion_rate": 5.492424242424242}, "performance_by_symbol": {"AAVEUSDT": {"total_pnl": -29.9082, "avg_pnl": -4.9847, "trade_count": 6, "profitable_count": 2.0, "win_rate": 33.33}, "ATHUSDT": {"total_pnl": -88.5307, "avg_pnl": -14.7551, "trade_count": 6, "profitable_count": 0.0, "win_rate": 0.0}, "AVAXUSDT": {"total_pnl": 53.8624, "avg_pnl": 13.4656, "trade_count": 4, "profitable_count": 4.0, "win_rate": 100.0}, "DOGEUSDT": {"total_pnl": -88.4178, "avg_pnl": -14.7363, "trade_count": 6, "profitable_count": 0.0, "win_rate": 0.0}, "DYDXUSDT": {"total_pnl": -81.0356, "avg_pnl": -10.1294, "trade_count": 8, "profitable_count": 1.0, "win_rate": 12.5}, "HYPERUSDT": {"total_pnl": -29.9442, "avg_pnl": -14.9721, "trade_count": 2, "profitable_count": 0.0, "win_rate": 0.0}, "MUSDT": {"total_pnl": -28.438, "avg_pnl": -14.219, "trade_count": 2, "profitable_count": 0.0, "win_rate": 0.0}, "PENDLEUSDT": {"total_pnl": -170.7233, "avg_pnl": -13.1326, "trade_count": 13, "profitable_count": 0.0, "win_rate": 0.0}, "PUMPFUNUSDT": {"total_pnl": -27.3832, "avg_pnl": -13.6916, "trade_count": 2, "profitable_count": 0.0, "win_rate": 0.0}, "SUIUSDT": {"total_pnl": -14.0489, "avg_pnl": -14.0489, "trade_count": 1, "profitable_count": 0.0, "win_rate": 0.0}, "TRXUSDT": {"total_pnl": -22.3146, "avg_pnl": -5.5786, "trade_count": 4, "profitable_count": 2.0, "win_rate": 50.0}, "WAVESUSDT": {"total_pnl": -24.8026, "avg_pnl": -6.2006, "trade_count": 4, "profitable_count": 2.0, "win_rate": 50.0}}, "performance_by_timeframe": {"1h": {"total_pnl": -551.6847, "avg_pnl": -9.5118, "trade_count": 58, "profitable_count": 11.0, "win_rate": 18.97}}, "performance_by_recommendation": {"buy": {"total_pnl": -115.288, "avg_pnl": -11.5288, "trade_count": 10, "profitable_count": 1.0, "win_rate": 10.0}, "hold": {"total_pnl": -27.5611, "avg_pnl": -13.7806, "trade_count": 2, "profitable_count": 0.0, "win_rate": 0.0}, "sell": {"total_pnl": -408.8355, "avg_pnl": -8.8877, "trade_count": 46, "profitable_count": 10.0, "win_rate": 21.74}}, "performance_by_prompt": {"get_analyzer_prompt_improved_v28_short_fix": {"total_pnl": -41.8203, "avg_pnl": -13.9401, "trade_count": 3, "profitable_count": 0.0, "win_rate": 0.0}, "get_analyzer_prompt_improved_v28": {"total_pnl": -496.7319, "avg_pnl": -9.1987, "trade_count": 54, "profitable_count": 11.0, "win_rate": 20.37}}, "confidence_analysis": {"0-20%": {"trade_count": 0, "total_pnl": 0.0, "avg_pnl": NaN, "profitable_count": 0.0, "win_rate": NaN}, "20-40%": {"trade_count": 1, "total_pnl": -13.1326, "avg_pnl": -13.1326, "profitable_count": 0.0, "win_rate": 0.0}, "40-60%": {"trade_count": 0, "total_pnl": 0.0, "avg_pnl": NaN, "profitable_count": 0.0, "win_rate": NaN}, "60-80%": {"trade_count": 1, "total_pnl": -13.1326, "avg_pnl": -13.1326, "profitable_count": 0.0, "win_rate": 0.0}, "80-100%": {"trade_count": 55, "total_pnl": -510.991, "avg_pnl": -9.2907, "profitable_count": 11.0, "win_rate": 20.0}}, "risk_reward_metrics": {"average_win": 9.790532641818182, "average_loss": 14.02937281851064, "profit_factor": 0.6978596098679731, "max_profit": 24.04532909, "max_loss": -15.9686264, "largest_winning_streak": 6, "largest_losing_streak": 43}, "advanced_analytics": {"confidence_pnl_correlation": 0.08809359383586701, "avg_confidence_profitable": 0.85, "avg_confidence_loss": 0.8176595744680852, "best_performing_symbol": "AVAXUSDT", "best_performing_timeframe": "1h", "top_3_prompts": [{"prompt_name": "get_analyzer_prompt_improved_v28_short_fix", "total_pnl": -41.8203, "win_rate": 0.0, "trade_count": 3, "avg_pnl": -13.9401}, {"prompt_name": "get_analyzer_prompt_improved_v28", "total_pnl": -496.7319, "win_rate": 20.37, "trade_count": 54, "avg_pnl": -9.1987}], "worst_3_prompts": [{"prompt_name": "get_analyzer_prompt_improved_v28_short_fix", "total_pnl": -41.8203, "win_rate": 0.0, "trade_count": 3, "avg_pnl": -13.9401}, {"prompt_name": "get_analyzer_prompt_improved_v28", "total_pnl": -496.7319, "win_rate": 20.37, "trade_count": 54, "avg_pnl": -9.1987}]}}, "prompt_effectiveness": {"field_completion_rates": {"Missing risk factors": 1.0, "Missing trade parameters": 1.0, "Missing summary": 1.0, "Missing key levels": 1.0}, "prompt_gaps": [{"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing risk factors. Address Missing risk factors in prompt.", "affected_fields": ["risk factors"], "sample_improvements": ["Explicitly instruct AI to include 'risk factors' in its response."]}, {"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing trade parameters. Address Missing trade parameters in prompt.", "affected_fields": ["trade parameters"], "sample_improvements": ["Explicitly instruct AI to include 'trade parameters' in its response."]}, {"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing summary. Address Missing summary in prompt.", "affected_fields": ["summary"], "sample_improvements": ["Explicitly instruct AI to include 'summary' in its response."]}, {"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing key levels. Address Missing key levels in prompt.", "affected_fields": ["key levels"], "sample_improvements": ["Explicitly instruct AI to include 'key levels' in its response."]}], "improvement_suggestions": [{"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing risk factors. Address Missing risk factors in prompt.", "affected_fields": ["risk factors"], "sample_improvements": ["Explicitly instruct AI to include 'risk factors' in its response."]}, {"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing trade parameters. Address Missing trade parameters in prompt.", "affected_fields": ["trade parameters"], "sample_improvements": ["Explicitly instruct AI to include 'trade parameters' in its response."]}, {"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing summary. Address Missing summary in prompt.", "affected_fields": ["summary"], "sample_improvements": ["Explicitly instruct AI to include 'summary' in its response."]}, {"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing key levels. Address Missing key levels in prompt.", "affected_fields": ["key levels"], "sample_improvements": ["Explicitly instruct AI to include 'key levels' in its response."]}], "effectiveness_by_timeframe": {"1h": 0.7042613636363644}, "weak_analysis_areas": {"Missing summary": ["UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)"], "Missing key levels": ["UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)"], "Missing trade parameters": ["UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)"], "Missing risk factors": ["UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)"]}, "optimization_report": {"field_completion_rates": {"Missing risk factors": 1.0, "Missing trade parameters": 1.0, "Missing summary": 1.0, "Missing key levels": 1.0}, "identified_gaps": [{"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing risk factors. Address Missing risk factors in prompt.", "affected_fields": ["risk factors"], "sample_improvements": ["Explicitly instruct AI to include 'risk factors' in its response."]}, {"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing trade parameters. Address Missing trade parameters in prompt.", "affected_fields": ["trade parameters"], "sample_improvements": ["Explicitly instruct AI to include 'trade parameters' in its response."]}, {"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing summary. Address Missing summary in prompt.", "affected_fields": ["summary"], "sample_improvements": ["Explicitly instruct AI to include 'summary' in its response."]}, {"category": "Completeness/Consistency", "priority": "High", "description": "AI frequently misses: Missing key levels. Address Missing key levels in prompt.", "affected_fields": ["key levels"], "sample_improvements": ["Explicitly instruct AI to include 'key levels' in its response."]}], "template_suggestions": {"general_improvement": "Consider adding more explicit instructions and examples for fields that are frequently missed.", "improve_risk factors": "To address 'AI frequently misses: Missing risk factors. Address Missing risk factors in prompt.', ensure your prompt clearly defines the requirement for these fields and provides examples of expected output.", "improve_trade parameters": "To address 'AI frequently misses: Missing trade parameters. Address Missing trade parameters in prompt.', ensure your prompt clearly defines the requirement for these fields and provides examples of expected output.", "improve_summary": "To address 'AI frequently misses: Missing summary. Address Missing summary in prompt.', ensure your prompt clearly defines the requirement for these fields and provides examples of expected output.", "improve_key levels": "To address 'AI frequently misses: Missing key levels. Address Missing key levels in prompt.', ensure your prompt clearly defines the requirement for these fields and provides examples of expected output."}, "effectiveness_by_timeframe": {"1h": 0.7042613636363644}, "weak_analysis_areas": {"Missing summary": ["UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)"], "Missing key levels": ["UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)"], "Missing trade parameters": ["UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)"], "Missing risk factors": ["UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)", "UNKNOWN-UNKNOWN (UNKNOWN)"]}}}, "failure_patterns": {"overall_failure_stats": {"total_failed_trades": 47, "failure_rate": 4.450757575757576}, "failure_by_symbol": {"PENDLEUSDT": 13, "DYDXUSDT": 7, "ATHUSDT": 6, "DOGEUSDT": 6, "AAVEUSDT": 4, "HYPERUSDT": 2, "MUSDT": 2, "PUMPFUNUSDT": 2, "TRXUSDT": 2, "WAVESUSDT": 2, "SUIUSDT": 1}, "failure_by_timeframe": {"1h": 47}, "failure_by_confidence": {"0-20%": 0, "20-40%": 1, "40-60%": 0, "60-80%": 1, "80-100%": 44}, "common_missing_fields": {"Missing summary": 47, "Missing key levels": 47, "Missing trade parameters": 47, "Missing risk factors": 47}, "failure_patterns": {"overconfident_losses": 44, "high_confidence_losses": 44, "low_confidence_losses": 1}}, "key_insights": ["Win Rate: 19.0% across 58 trades", "Total PnL: $-551.68 (Avg: $-9.51 per trade)", "Trade Conversion Rate: 5.5% of analyses result in trades", "Best performing symbol: AVAXUSDT ($53.86)", "Worst performing symbol: PENDLEUSDT ($-170.72)", "Confidence-PnL Correlation: 0.088", "⚠️ Low correlation between confidence and actual outcomes - prompt needs improvement", "Most problematic field: Missing risk factors (missing in 100.0% of analyses)", "Failure Rate: 4.5% of all outcomes"], "recommendations": ["⚠️ Profit factor below 1.0 - review risk management and trade sizing", "⚠️ Long losing streaks detected - consider reducing position sizes or adding more filters", "🔧 Prompt improvements needed - see detailed suggestions in prompt_effectiveness section", "⚠️ Poor performance on: AAVEUSDT, ATHUSDT, DOGEUSDT - consider reducing exposure or improving analysis"]}