#!/usr/bin/env python3
"""
Streamlit Dashboard for Trade Entry Analysis - Interactive visualization of entry effectiveness.
Provides real-time insights and visualizations for improving entry handling strategies.

python3 Tools/data_collector.py

python3 -m streamlit run trade_entry_analysis/trade_entry_analysis_streamlit.py 

"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import os
from typing import Dict, List, Any

# Page configuration
st.set_page_config(
    page_title="Trade Entry Analysis Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin: 10px 0;
    }
    .metric-value {
        font-size: 2em;
        font-weight: bold;
        margin: 5px 0;
    }
    .metric-label {
        font-size: 0.9em;
        opacity: 0.9;
    }
    .warning-card {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #ff6b6b;
        margin: 10px 0;
    }
    .success-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #28a745;
        margin: 10px 0;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data(ttl=300)  # Cache for 5 minutes
def load_raw_data():
    """Load the raw execution data from the simple JSON file."""
    try:
        # PRIORITIZE raw data file for calculation
        raw_file = 'trade_entry_analysis/raw_trade_data_only.json'
        analysis_file = 'trade_entry_analysis/trade_entry_analysis_results.json'

        if os.path.exists(raw_file):
            with open(raw_file, 'r') as f:
                data = json.load(f)
                # Validate data structure
                if 'raw_executions' not in data:
                    st.error("❌ Invalid raw data format: 'raw_executions' field not found")
                    return None
                if not isinstance(data['raw_executions'], list):
                    st.error("❌ Invalid raw data format: 'raw_executions' should be a list")
                    return None
                if len(data['raw_executions']) == 0:
                    st.error("❌ Raw data file exists but contains no executions")
                    return None
                st.info(f"✅ Loaded {len(data['raw_executions'])} raw executions for calculation")
                return data

        # Only use analysis file if raw file doesn't exist
        elif os.path.exists(analysis_file):
            st.warning("⚠️ No raw execution data found, using pre-calculated analysis")
            with open(analysis_file, 'r') as f:
                return json.load(f)
        else:
            st.error("❌ No data files found. Please run the data collector first.")
            st.info("💡 Run: python3 trade_entry_analysis/simple_raw_data_fetcher.py")
            return None
    except json.JSONDecodeError as e:
        st.error(f"❌ Invalid JSON format in data file: {e}")
        return None
    except Exception as e:
        st.error(f"❌ Error loading data: {e}")
        return None

def create_metric_card(title: str, value: str, delta: str = ""):
    """Create a styled metric card."""
    delta_html = f"<div style='font-size: 1.2em; margin-bottom: 5px;'>{delta}</div>" if delta else ""
    return f"""
    <div class="metric-card">
        <div class="metric-label">{title}</div>
        {delta_html}
        <div class="metric-value">{value}</div>
    </div>
    """

def safe_float_to_str(value, precision: int = 2) -> str:
    """Safely convert float to formatted string with error handling."""
    try:
        if value is None or value == '':
            return "0.0"
        return f"{float(value):.{precision}f}"
    except (ValueError, TypeError):
        return "0.0"

def safe_timestamp_to_datetime(timestamp) -> str:
    """Safely convert timestamp to datetime string with error handling."""
    try:
        if timestamp is None or timestamp == '':
            return "N/A"
        # Handle both string and numeric timestamps
        if isinstance(timestamp, str):
            timestamp = int(timestamp)
        return datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
    except (ValueError, TypeError, OSError):
        return "N/A"

def safe_format_datetime(datetime_str: str) -> str:
    """Safely format datetime string with error handling."""
    try:
        if not datetime_str or datetime_str == '':
            return "N/A"
        # If it's already a string, just truncate it
        if isinstance(datetime_str, str):
            return datetime_str[:19] if len(datetime_str) > 19 else datetime_str
        return str(datetime_str)[:19]
    except (ValueError, TypeError):
        return "N/A"

def calculate_summary_metrics(raw_executions: List[Dict]) -> Dict[str, Any]:
    """Calculate summary metrics from raw execution data."""
    print(f"DEBUG: calculate_summary_metrics called with {len(raw_executions) if raw_executions else 0} executions")

    if not raw_executions:
        print("DEBUG: No raw executions provided")
        return {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'total_pnl': 0.0,
            'avg_entry_efficiency': 0.0,
            'avg_entry_timing_score': 0.0,
            'avg_return_winning': 0.0
        }

    # Group executions by order ID to calculate trade-level metrics
    trades_by_order = {}
    for exec in raw_executions:
        order_id = exec.get('orderId', '')
        if order_id and order_id.strip():
            if order_id not in trades_by_order:
                trades_by_order[order_id] = []
            trades_by_order[order_id].append(exec)

    print(f"DEBUG: Grouped into {len(trades_by_order)} unique orders")

    # Calculate trade metrics
    total_trades = 0
    winning_trades = 0
    losing_trades = 0
    total_pnl = 0.0
    total_entry_efficiency = 0.0
    total_timing_score = 0.0
    winning_pnl_values = []
    winning_returns = []

    print(f"DEBUG: Starting calculation loop with {len(trades_by_order)} orders")

    for i, (order_id, executions) in enumerate(trades_by_order.items()):
        if not executions:
            continue

        print(f"DEBUG: Processing order {i+1}/{len(trades_by_order)} - {order_id}")

        # Calculate trade-level metrics
        buy_execs = [e for e in executions if e.get('side') == 'Buy']
        sell_execs = [e for e in executions if e.get('side') == 'Sell']

        print(f"DEBUG: Order {order_id} - Buy execs: {len(buy_execs)}, Sell execs: {len(sell_execs)}")

        # Count this as a trade attempt
        total_trades += 1

        # Calculate P&L for any executions (don't require both buy and sell)
        try:
            # Calculate average buy and sell prices (volume-weighted)
            try:
                buy_qty = sum(float(e.get('execQty', 0)) for e in buy_execs)
                sell_qty = sum(float(e.get('execQty', 0)) for e in sell_execs)

                if buy_qty > 0 and sell_qty > 0:
                    avg_buy_price = sum(float(e.get('execPrice', 0)) * float(e.get('execQty', 0)) for e in buy_execs) / buy_qty
                    avg_sell_price = sum(float(e.get('execPrice', 0)) * float(e.get('execQty', 0)) for e in sell_execs) / sell_qty

                    # Calculate P&L using matched quantities
                    trade_qty = min(buy_qty, sell_qty)
                    gross_pnl = (avg_sell_price - avg_buy_price) * trade_qty

                    # Add fees to P&L calculation (fees are already negative in Bybit data)
                    total_fees = sum(float(e.get('execFee', 0)) for e in executions)
                    trade_pnl = gross_pnl + total_fees

                    total_pnl += trade_pnl

                    # Calculate return percentage
                    trade_return_pct = (trade_pnl / (avg_buy_price * trade_qty)) * 100 if avg_buy_price * trade_qty != 0 else 0

                    if trade_pnl > 0:
                        winning_trades += 1
                        winning_pnl_values.append(trade_pnl)
                        winning_returns.append(trade_return_pct)
                    elif trade_pnl < 0:
                        losing_trades += 1

                    # Calculate entry efficiency based on price improvement and market impact
                    # Use the order price vs execution price for entry efficiency
                    entry_efficiency = 100.0  # Start with perfect efficiency

                    # Improve efficiency calculation based on slippage
                    if buy_execs and sell_execs:
                        # For entry efficiency, compare order price vs execution price
                        first_buy = min(buy_execs, key=lambda x: float(x.get('execTime', 0)))
                        first_sell = min(sell_execs, key=lambda x: float(x.get('execTime', 0)))

                        # Calculate slippage as percentage of price difference
                        buy_slippage = 0.0
                        sell_slippage = 0.0

                        if avg_buy_price > 0:
                            buy_slippage = abs(float(first_buy.get('orderPrice', avg_buy_price)) - avg_buy_price) / avg_buy_price * 100
                        if avg_sell_price > 0:
                            sell_slippage = abs(float(first_sell.get('orderPrice', avg_sell_price)) - avg_sell_price) / avg_sell_price * 100

                        # Reduce efficiency based on slippage
                        avg_slippage = (buy_slippage + sell_slippage) / 2
                        entry_efficiency = max(0.0, 100.0 - avg_slippage)

                    total_entry_efficiency += entry_efficiency

                    # Calculate timing score based on execution quality and market conditions
                    # Use slippage and maker/taker status for timing calculation
                    maker_execs = [e for e in executions if e.get('isMaker', False)]
                    if executions:
                        maker_ratio = len(maker_execs) / len(executions)

                        # Calculate average slippage for timing assessment
                        total_slippage = 0.0
                        slippage_count = 0

                        for exec in executions:
                            order_price = float(exec.get('orderPrice', 0))
                            exec_price = float(exec.get('execPrice', 0))
                            if order_price > 0 and exec_price > 0:
                                slippage = abs(order_price - exec_price) / order_price
                                total_slippage += slippage
                                slippage_count += 1

                        avg_slippage = total_slippage / slippage_count if slippage_count > 0 else 0

                        # Better timing if more maker orders and less slippage
                        # Scale: 0-100 based on maker ratio and slippage
                        slippage_penalty = avg_slippage * 100  # Convert to percentage penalty
                        timing_score = max(0.0, min(100.0, (maker_ratio * 100) - slippage_penalty))
                    else:
                        timing_score = 0.0

                    total_timing_score += timing_score

            except (ZeroDivisionError, ValueError, TypeError) as e:
                # Skip trades with calculation errors but count as a trade
                losing_trades += 1
                continue
        except (ZeroDivisionError, ValueError, TypeError) as e:
            # Handle outer try block errors - don't count as losing trades
            # These are incomplete orders that can't be properly evaluated
            print(f"DEBUG: Skipping order {order_id} due to calculation error: {e}")
            continue
        else:
            # Single-sided executions (only buy or only sell) - don't count as losing trades
            # These are incomplete orders that can't be properly evaluated
            print(f"DEBUG: Skipping order {order_id} - incomplete trade (single-sided)")
            total_trades -= 1  # Don't count incomplete trades in total

    # Calculate averages only for trades that had complete calculations
    completed_trades = max(1, len([t for t in trades_by_order.values() if len([e for e in t if e.get('side') == 'Buy']) > 0 and len([e for e in t if e.get('side') == 'Sell']) > 0]))

    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0.0
    avg_entry_efficiency = (total_entry_efficiency / completed_trades) if completed_trades > 0 else 0.0
    avg_timing_score = (total_timing_score / completed_trades) if completed_trades > 0 else 0.0

    # Fix average return calculation for winning trades
    avg_return_winning = 0.0
    if winning_returns:
        avg_return_winning = sum(winning_returns) / len(winning_returns)
    elif winning_pnl_values:
        # Fallback to P&L-based calculation if return calculation failed
        total_winning_value = sum(float(pnl) for pnl in winning_pnl_values if pnl > 0)
        avg_return_winning = (total_winning_value / len(winning_pnl_values)) if winning_pnl_values else 0.0

    print(f"DEBUG: Calculation complete - Trades: {total_trades}, Wins: {winning_trades}, Losses: {losing_trades}")
    print(f"DEBUG: P&L: ${total_pnl}, Efficiency: {avg_entry_efficiency}, Timing: {avg_timing_score}")

    result = {
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'total_pnl': total_pnl,
        'avg_entry_efficiency': avg_entry_efficiency,
        'avg_entry_timing_score': avg_timing_score,
        'avg_return_winning': avg_return_winning
    }

    print(f"DEBUG: Returning result: {result}")
    return result

def main():
    st.title("📊 Trade Entry Analysis Dashboard")
    st.markdown("### Real-time Entry Effectiveness Analysis & Improvement Insights")

    # Load data
    st.info("🔄 Loading trade data...")
    data = load_raw_data()

    # Quick validation
    if data and "raw_executions" in data:
        st.success(f"✅ Raw data validation: {len(data['raw_executions'])} executions loaded")
        if len(data['raw_executions']) > 0:
            sample = data['raw_executions'][0]
            st.info(f"📝 Sample execution: {sample.get('symbol', 'N/A')} {sample.get('side', 'N/A')} {sample.get('execQty', 'N/A')} qty")

    if not data:
        st.error("❌ Failed to load any data!")
        st.stop()

    if "error" in data:
        st.error(f"❌ Analysis Error: {data['error']}")
        st.stop()

    # Debug information - Always show this
    st.success("✅ Data loaded successfully!")

    with st.expander("🔍 Data Loading Debug Info", expanded=True):
        col1, col2 = st.columns(2)
        with col1:
            st.write("**Data Source:**")
            if "raw_executions" in data:
                exec_count = len(data['raw_executions'])
                st.success(f"✅ Raw execution data loaded ({exec_count} executions)")
                if exec_count > 0:
                    st.info(f"Sample execution: {data['raw_executions'][0].get('symbol', 'N/A')} - {data['raw_executions'][0].get('side', 'N/A')}")
            else:
                st.warning("⚠️ Using pre-calculated analysis data")

        with col2:
            st.write("**Available Fields:**")
            fields = list(data.keys())
            st.info(", ".join(fields))

        # Show first few executions for debugging
        if "raw_executions" in data and data["raw_executions"]:
            with st.expander("🔍 Sample Raw Executions (First 3)"):
                for i, exec in enumerate(data["raw_executions"][:3]):
                    st.json(exec)

    # Sidebar configuration
    st.sidebar.header("⚙️ Configuration")
    refresh_rate = st.sidebar.slider("Auto-refresh (seconds)", 30, 300, 60)

    st.sidebar.markdown("---")
    st.sidebar.header("📊 Analysis Options")
    show_raw_data = st.sidebar.checkbox("Show Raw Data", value=False)
    show_detailed_metrics = st.sidebar.checkbox("Detailed Metrics", value=True)

    # Main content
    # Handle different data formats
    st.info("🔄 Processing trade data...")

    if "raw_executions" in data and data["raw_executions"]:
        # Raw execution data - ALWAYS calculate metrics from raw data
        raw_executions = data["raw_executions"]
        st.info(f"📊 Found {len(raw_executions)} raw executions, calculating metrics...")

        if len(raw_executions) > 0:
            with st.spinner("Calculating trade metrics..."):
                summary = calculate_summary_metrics(raw_executions)

            st.success(f"✅ Calculation complete!")

            # Debug the calculated summary
            with st.expander("🔍 Calculation Results Debug"):
                st.write("**Calculated Summary:**")
                st.json(summary)

                # Show key metrics
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Total Trades", summary.get('total_trades', 0))
                with col2:
                    st.metric("Winning Trades", summary.get('winning_trades', 0))
                with col3:
                    st.metric("Losing Trades", summary.get('losing_trades', 0))
                with col4:
                    st.metric("Win Rate", f"{summary.get('win_rate', 0):.1f}%")
        else:
            st.error("❌ Raw execution data is empty!")
            summary = {
                'total_trades': 0, 'winning_trades': 0, 'losing_trades': 0,
                'win_rate': 0.0, 'total_pnl': 0.0, 'avg_entry_efficiency': 0.0,
                'avg_entry_timing_score': 0.0, 'avg_return_winning': 0.0
            }
    elif "summary" in data:
        # Fallback to pre-calculated summary if no raw data
        st.warning("⚠️ Using pre-calculated metrics (no raw execution data found)")
        summary = data["summary"]
    else:
        st.error("❌ No data available!")
        summary = {
            'total_trades': 0, 'winning_trades': 0, 'losing_trades': 0,
            'win_rate': 0.0, 'total_pnl': 0.0, 'avg_entry_efficiency': 0.0,
            'avg_entry_timing_score': 0.0, 'avg_return_winning': 0.0
        }

    # Key Metrics Row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-label">Total Trades</div>
            <div class="metric-value">{summary.get('total_trades', 0)}</div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        win_rate = summary.get('win_rate', 0)
        delta = "🔴" if win_rate == 0 else "🟢"
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-label">Win Rate</div>
            <div style='font-size: 1.2em; margin-bottom: 5px;'>{delta}</div>
            <div class="metric-value">{win_rate:.1f}%</div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        pnl = summary.get('total_pnl', 0)
        pnl_color = "🟢" if pnl > 0 else "🔴"
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-label">Total P&L</div>
            <div class="metric-value">${pnl:.2f}</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        efficiency = summary.get('avg_entry_efficiency', 0)
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-label">Entry Efficiency</div>
            <div class="metric-value">{efficiency:.1f}/100</div>
        </div>
        """, unsafe_allow_html=True)

    # Second row of metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        timing_score = summary.get('avg_entry_timing_score', 0)
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-label">Timing Score</div>
            <div class="metric-value">{timing_score:.1f}/100</div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        winning_trades = summary.get('winning_trades', 0)
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-label">Winning Trades</div>
            <div class="metric-value">{winning_trades}</div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        losing_trades = summary.get('losing_trades', 0)
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-label">Losing Trades</div>
            <div class="metric-value">{losing_trades}</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        avg_return_winning = summary.get('avg_return_winning', 0)
        winning_trades = summary.get('winning_trades', 0)

        if winning_trades == 0:
            return_display = "N/A (No wins)"
        else:
            return_display = f"{avg_return_winning:.4f}%"

        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-label">Avg Win Return</div>
            <div class="metric-value">{return_display}</div>
        </div>
        """, unsafe_allow_html=True)

    # Analysis Tabs
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📈 Overview",
        "🎯 Entry Patterns",
        "⏰ Timing Analysis",
        "🏆 Symbol Performance",
        "💡 Recommendations",
        "🔍 Raw Data Inspection"
    ])

    with tab1:
        st.header("📈 Performance Overview")

        # Win/Loss Distribution
        col1, col2 = st.columns([2, 1])

        with col1:
            # Create pie chart for win/loss distribution
            win_loss_data = pd.DataFrame({
                'Type': ['Winning Trades', 'Losing Trades'],
                'Count': [summary.get('winning_trades', 0), summary.get('losing_trades', 0)]
            })

            fig_pie = px.pie(
                win_loss_data,
                values='Count',
                names='Type',
                title='Trade Distribution',
                color='Type',
                color_discrete_map={'Winning Trades': '#28a745', 'Losing Trades': '#dc3545'}
            )
            fig_pie.update_layout(height=400)
            st.plotly_chart(fig_pie, use_container_width=True)

        with col2:
            st.subheader("📊 Key Insights")

            total_trades = summary.get('total_trades', 0)
            win_rate = summary.get('win_rate', 0)
            timing_score = summary.get('avg_entry_timing_score', 0)
            efficiency = summary.get('avg_entry_efficiency', 0)

            if total_trades == 0:
                st.warning("⚠️ **No Trades**: No trade data available for analysis.")
            elif win_rate == 0:
                st.error("🚨 **CRITICAL**: 0% win rate detected! All trades are losing. Immediate strategy review needed.")
            elif win_rate < 30:
                st.warning(f"⚠️ **Low Win Rate**: Only {win_rate:.1f}% win rate. Strategy needs improvement.")
            elif win_rate > 70:
                st.success(f"✅ **Excellent Win Rate**: {win_rate:.1f}% win rate! Strategy performing well.")

            if timing_score < 30:
                st.error("🚨 **Poor Timing**: Entry timing score is critically low. Review market timing logic.")
            elif timing_score < 50:
                st.warning("⚠️ **Below Average Timing**: Entry timing needs improvement.")
            elif timing_score > 80:
                st.success("✅ **Excellent Timing**: Entry timing is very good.")

            if efficiency < 60:
                st.warning("⚠️ **Entry Efficiency**: Poor execution efficiency. High slippage detected.")
            elif efficiency > 85:
                st.success("✅ **Entry Efficiency**: Excellent spread execution and order placement.")

            # Market regime indicator
            st.info(f"📍 **Market Regime**: All trades in ranging conditions")

        # Performance timeline (if we had time series data)
        st.subheader("📅 Recent Performance")
        st.info("💡 **Note**: Consider implementing time-based analysis for trend identification")

    with tab2:
        st.header("🎯 Entry Patterns Analysis")

        patterns = data.get("entry_patterns", {})
        if patterns:
            # Create pattern performance table
            pattern_data = []
            for pattern_name, pattern_info in patterns.items():
                if isinstance(pattern_info, dict):
                    pattern_data.append({
                        'Pattern': pattern_name.replace('_', ' + ').title(),
                        'Trades': pattern_info.get('frequency', 0),
                        'Success Rate': f"{pattern_info.get('success_rate', 0):.1f}%",
                        'Avg Return': f"{pattern_info.get('avg_return', 0):.4f}%",
                        'Best Symbols': ', '.join(pattern_info.get('best_symbols', [])),
                        'Worst Symbols': ', '.join(pattern_info.get('worst_symbols', []))
                    })

            if pattern_data:
                df_patterns = pd.DataFrame(pattern_data)
                st.dataframe(df_patterns, use_container_width=True, height=min(400, len(pattern_data) * 35 + 35))

                # Pattern success rate chart
                fig_patterns = px.bar(
                    df_patterns,
                    x='Pattern',
                    y=[float(x.replace('%', '')) for x in df_patterns['Success Rate']],
                    title='Entry Pattern Success Rates',
                    labels={'y': 'Success Rate (%)'},
                    color=[float(x.replace('%', '')) for x in df_patterns['Success Rate']],
                    color_continuous_scale='RdYlGn'
                )
                fig_patterns.update_layout(height=400)
                st.plotly_chart(fig_patterns, use_container_width=True)
        else:
            st.info("No entry patterns identified. Need more data for pattern analysis.")

    with tab3:
        st.header("⏰ Entry Timing Analysis")

        timing_data = data.get("timing_analysis", {})
        if timing_data:
            col1, col2 = st.columns(2)

            with col1:
                st.subheader("📊 Duration Analysis")
                duration_analysis = timing_data.get("duration_analysis", {})

                if duration_analysis:
                    # Handle different data structures
                    quick_exits = duration_analysis.get('quick_exits', [])
                    normal_trades = duration_analysis.get('normal_trades', [])
                    long_trades = duration_analysis.get('long_trades', [])

                    quick_count = quick_exits.get('count', len(quick_exits)) if isinstance(quick_exits, dict) else len(quick_exits)
                    normal_count = normal_trades.get('count', len(normal_trades)) if isinstance(normal_trades, dict) else len(normal_trades)
                    long_count = long_trades.get('count', len(long_trades)) if isinstance(long_trades, dict) else len(long_trades)

                    duration_df = pd.DataFrame([
                        {'Duration': 'Quick Exits (< 5 min)', 'Count': quick_count},
                        {'Duration': 'Normal Trades (5-60 min)', 'Count': normal_count},
                        {'Duration': 'Long Trades (> 60 min)', 'Count': long_count}
                    ])

                    fig_duration = px.bar(
                        duration_df,
                        x='Duration',
                        y='Count',
                        title='Trade Duration Distribution',
                        color='Count',
                        color_continuous_scale='Blues'
                    )
                    fig_duration.update_layout(height=300)
                    st.plotly_chart(fig_duration, use_container_width=True)

            with col2:
                st.subheader("⏰ Hourly Performance")
                by_hour = timing_data.get("by_hour", {})

                if by_hour:
                    hour_data = []
                    for hour, data in by_hour.items():
                        try:
                            hour_int = int(hour)
                            hour_data.append({
                                'Hour': f'{hour_int:02d}:00',
                                'Avg Return': data.get('avg_return', 0),
                                'Trade Count': data.get('count', 0),
                                'Win Rate': data.get('win_rate', 0)
                            })
                        except (ValueError, TypeError):
                            continue

                    if hour_data:
                        df_hours = pd.DataFrame(hour_data)
                        fig_hours = px.scatter(
                            df_hours,
                            x='Hour',
                            y='Avg Return',
                            size='Trade Count',
                            color='Win Rate',
                            title='Performance by Hour',
                            color_continuous_scale='RdYlGn',
                            size_max=30
                        )
                        fig_hours.update_layout(height=300)
                        st.plotly_chart(fig_hours, use_container_width=True)

        else:
            st.info("No timing data available.")

    with tab4:
        st.header("🏆 Symbol Performance Analysis")

        symbol_perf = data.get("symbol_performance", {})
        symbol_details = symbol_perf.get("symbol_details", {})

        if symbol_details:
            # Top performers table
            top_symbols = list(symbol_details.keys())[:10]

            symbol_table_data = []
            for symbol in top_symbols:
                details = symbol_details[symbol]
                symbol_table_data.append({
                    'Symbol': symbol,
                    'Trades': details.get('trade_count', 0),
                    'Win Rate': f"{details.get('win_rate', 0):.1f}%",
                    'Total P&L': f"${details.get('total_pnl', 0):.2f}",
                    'Avg Return': f"{details.get('avg_return', 0):.4f}%",
                    'Entry Efficiency': f"{details.get('avg_entry_efficiency', 0):.1f}/100",
                    'Consistency': f"{details.get('consistency_score', 0):.1f}/100"
                })

            if symbol_table_data:
                df_symbols = pd.DataFrame(symbol_table_data)

                # Color coding for performance
                def color_win_rate(val):
                    try:
                        rate = float(val.replace('%', ''))
                        if rate > 50:
                            return 'background-color: #d4edda'
                        elif rate > 25:
                            return 'background-color: #fff3cd'
                        else:
                            return 'background-color: #f8d7da'
                    except:
                        return ''

                styled_df = df_symbols.style.map(color_win_rate, subset=['Win Rate'])
                st.dataframe(styled_df, use_container_width=True, height=400)

                # Symbol performance chart
                fig_symbols = px.scatter(
                    df_symbols,
                    x='Win Rate',
                    y='Avg Return',
                    size='Trades',
                    color='Total P&L',
                    hover_name='Symbol',
                    title='Symbol Performance Matrix',
                    color_continuous_scale='RdYlGn',
                    size_max=30
                )
                fig_symbols.update_layout(height=400)
                st.plotly_chart(fig_symbols, use_container_width=True)
        else:
            st.info("No symbol performance data available.")

    with tab5:
        st.header("💡 Improvement Recommendations")

        recommendations = data.get("improvement_recommendations", [])

        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                if "⚠️" in rec:
                    st.warning(f"**{i}.** {rec}")
                elif "✅" in rec:
                    st.success(f"**{i}.** {rec}")
                else:
                    st.info(f"**{i}.** {rec}")

            # Action items section
            st.subheader("🎯 Priority Action Items")

            priority_actions = [
                "1. **Review Entry Timing Logic** - Current timing score is critically low",
                "2. **Implement Market Regime Filter** - Avoid ranging conditions",
                "3. **Adjust Stop Loss Strategy** - Quick exits suggest stops are too tight",
                "4. **Focus on Better Performing Symbols** - DOGEUSDT, CAKEUSDT show relatively better performance",
                "5. **Add Entry Confirmation Signals** - Multiple timeframe or indicator confirmation needed"
            ]

            for action in priority_actions:
                st.markdown(f"• {action}")

        else:
            st.info("No specific recommendations available.")

    with tab6:
        st.header("🔍 Raw Execution Data Table")
        st.info("Complete table of raw execution data from Bybit API - no processing, just pure data.")

        # Check if we have simple raw data or analysis data
        if "raw_executions" in data:
            # Simple raw data format
            raw_executions = data.get("raw_executions", [])

            if raw_executions and len(raw_executions) > 0:
                # Convert raw executions to DataFrame with error handling
                df_raw = pd.DataFrame([{
                    'Symbol': exec.get('symbol', ''),
                    'Side': exec.get('side', ''),
                    'Order Type': exec.get('orderType', ''),
                    'Exec Type': exec.get('execType', ''),
                    'Order Price': safe_float_to_str(exec.get('orderPrice', 0), 6),
                    'Exec Price': safe_float_to_str(exec.get('execPrice', 0), 6),
                    'Order Qty': safe_float_to_str(exec.get('orderQty', 0), 8),
                    'Exec Qty': safe_float_to_str(exec.get('execQty', 0), 8),
                    'Exec Value': safe_float_to_str(exec.get('execValue', 0), 2),
                    'Fee': safe_float_to_str(exec.get('execFee', 0), 6),
                    'Is Maker': '✅' if exec.get('isMaker', False) else '❌',
                    'Exec Time': safe_timestamp_to_datetime(exec.get('execTime', 0)),
                    'Order ID': exec.get('orderId', '')[:12] + '...' if len(exec.get('orderId', '')) > 12 else exec.get('orderId', ''),
                    'Exec ID': exec.get('execId', '')[:12] + '...' if len(exec.get('execId', '')) > 12 else exec.get('execId', '')
                } for exec in raw_executions])

                st.dataframe(df_raw, use_container_width=True, height=600)

                # Summary metrics for raw executions
                st.subheader("📊 Raw Execution Summary")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Total Executions", len(raw_executions))

                with col2:
                    unique_orders = len(set(exec.get('orderId', '') for exec in raw_executions if exec.get('orderId')))
                    st.metric("Unique Orders", unique_orders)

                with col3:
                    unique_symbols = len(set(exec.get('symbol', '') for exec in raw_executions if exec.get('symbol')))
                    st.metric("Unique Symbols", unique_symbols)

                with col4:
                    total_volume = sum(float(exec.get('execValue', 0)) for exec in raw_executions)
                    st.metric("Total Volume", f"${total_volume:.2f}")

                # Show unique symbols
                st.subheader("🏷️ Symbols in Data")
                symbols = list(set(exec.get('symbol', '') for exec in raw_executions if exec.get('symbol')))
                st.write(", ".join(sorted(symbols)))

            else:
                st.error("❌ No raw execution data available!")
                st.info("💡 Run the simple raw data fetcher to generate data.")

        elif "raw_data" in data:
            # Analysis data format (fallback)
            raw_analyses = data.get("raw_data", [])

            if raw_analyses and len(raw_analyses) > 0:
                # Convert to DataFrame for better display with error handling
                df_raw = pd.DataFrame([{
                    'Symbol': analysis.get('symbol', ''),
                    'Side': analysis.get('side', ''),
                    'Entry Price': safe_float_to_str(analysis.get('entry_price', 0), 6),
                    'Exit Price': safe_float_to_str(analysis.get('exit_price', 0), 6),
                    'P&L ($)': safe_float_to_str(analysis.get('pnl', 0), 6),
                    'Return %': safe_float_to_str(analysis.get('return_pct', 0), 4) + '%',
                    'Duration (min)': safe_float_to_str(analysis.get('duration_minutes', 0), 2),
                    'Entry Efficiency': safe_float_to_str(analysis.get('entry_efficiency', 0), 1),
                    'Timing Score': safe_float_to_str(analysis.get('entry_timing_score', 0), 1),
                    'Market Regime': analysis.get('market_regime', ''),
                    'Entry Time': safe_format_datetime(analysis.get('entry_time', '')),
                    'Exit Time': safe_format_datetime(analysis.get('exit_time', ''))
                } for analysis in raw_analyses])

                # Add color coding for P&L
                def color_pnl(val):
                    try:
                        pnl = float(val)
                        if pnl > 0:
                            return 'background-color: #d4edda; color: #155724'
                        elif pnl < 0:
                            return 'background-color: #f8d7da; color: #721c24'
                        else:
                            return 'background-color: #fff3cd; color: #856404'
                    except:
                        return ''

                styled_df = df_raw.style.map(color_pnl, subset=['P&L ($)'])
                st.dataframe(styled_df, use_container_width=True, height=600)

                # Summary metrics
                st.subheader("📊 Analysis Summary")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Total Trades", len(raw_analyses))
                with col2:
                    winning = len([a for a in raw_analyses if a.get('pnl', 0) > 0])
                    st.metric("Winning Trades", winning)
                with col3:
                    losing = len([a for a in raw_analyses if a.get('pnl', 0) < 0])
                    st.metric("Losing Trades", losing)
                with col4:
                    total_pnl = sum(a.get('pnl', 0) for a in raw_analyses)
                    st.metric("Total P&L", f"${total_pnl:.2f}")
            else:
                st.error("❌ No analysis data available!")
        else:
            st.error("❌ No data format recognized!")
            st.info("💡 Run the data collector to generate data.")

    # Raw data section (collapsible)
    if show_raw_data:
        with st.expander("🔍 Raw Analysis Data"):
            st.json(data)

    # Footer
    st.markdown("---")
    col1, col2, col3 = st.columns(3)

    with col1:
        total_trades = summary.get('total_trades', 0)
        if total_trades == 0:
            st.caption("📊 No trade data available")
        else:
            st.caption(f"📊 Analysis based on {total_trades} trades")

    with col2:
        st.caption("🔄 Auto-refresh: 60 seconds")

    with col3:
        st.caption(f"📈 Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()