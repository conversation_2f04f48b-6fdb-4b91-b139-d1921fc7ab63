#!/usr/bin/env python3
"""
Comprehensive Trading Performance Analyzer

This script analyzes the trading database to generate insights on:
1. Trading performance statistics
2. Confidence calibration analysis
3. Prompt effectiveness analysis
4. Symbol and timeframe performance patterns
5. Risk-reward analytics
6. Failure pattern identification
7. Prompt optimization recommendations

Author: Trading Analysis System
"""

import sqlite3
import json
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import sys
import os
import logging

# Add the analysis_dashboard to the path so we can import its modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'analysis_dashboard'))

from analysis_dashboard.database_connector import DatabaseConnector
from analysis_dashboard.data_parser import AnalysisDataParser
from analysis_dashboard.outcome_tracker import OutcomeTracker, ConfidenceCalibrationAnalyzer
from analysis_dashboard.prompt_analyzer import PromptEffectivenessAnalyzer

class TradingPerformanceAnalyzer:
    """Comprehensive analyzer for trading performance and improvement insights"""

    # Configuration constants - these should be moved to config file in production
    DEFAULT_DB_PATH = '^^Server Backup/analysis_results.db'
    DEFAULT_QUERY_LIMIT = 10000
    PERFORMANCE_BATCH_SIZE = 1000  # Process data in smaller batches for memory efficiency
    PNL_PROFIT_THRESHOLD = 0.01
    PNL_LOSS_THRESHOLD = -0.01
    CONFIDENCE_BINS = [0, 0.2, 0.4, 0.6, 0.8, 1.0]
    CONFIDENCE_LABELS = ['0-20%', '20-40%', '40-60%', '60-80%', '80-100%']
    MIN_TRADES_FOR_ANALYSIS = 3
    MAX_INSIGHTS_DISPLAY = 10
    OVERCONFIDENT_THRESHOLD = 0.7
    HIGH_CONFIDENCE_THRESHOLD = 0.8
    LOW_CONFIDENCE_THRESHOLD = 0.3

    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path or self.DEFAULT_DB_PATH
        self.db_connector = DatabaseConnector(self.db_path)
        self.data_parser = AnalysisDataParser()
        self.outcome_tracker = OutcomeTracker(self.db_connector)
        self.confidence_analyzer = ConfidenceCalibrationAnalyzer(self.db_connector)
        self.prompt_analyzer = PromptEffectivenessAnalyzer(self.data_parser)
        self.logger = logging.getLogger(__name__)
        self._cached_raw_data = None  # Cache for raw data to avoid repeated DB queries

    def get_database_overview(self) -> Dict[str, Any]:
        """Get comprehensive overview of database structure and content"""
        conn = self.db_connector.get_connection()
        cursor = conn.cursor()

        overview = {
            'database_info': {},
            'table_structure': {},
            'data_summary': {},
            'analysis_data_quality': {}
        }

        try:
            # Database info
            cursor.execute("SELECT sqlite_version()")
            overview['database_info']['sqlite_version'] = cursor.fetchone()[0]

            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            overview['database_info']['tables'] = tables

            # Analyze each table
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]

                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                column_info = {col[1]: {'type': col[2], 'nullable': not col[3], 'primary_key': col[5]} for col in columns}

                overview['table_structure'][table] = {
                    'row_count': count,
                    'columns': column_info
                }

                # Get sample data for key tables
                if table in ['analysis_results', 'trades'] and count > 0:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 1")
                    sample_row = cursor.fetchone()
                    sample_data = dict(zip([col[1] for col in columns], sample_row))
                    overview['table_structure'][table]['sample_data'] = sample_data

            # Data summary
            if 'analysis_results' in tables:
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_analyses,
                        COUNT(DISTINCT symbol) as unique_symbols,
                        COUNT(DISTINCT timeframe) as unique_timeframes,
                        MIN(timestamp) as first_analysis,
                        MAX(timestamp) as latest_analysis
                    FROM analysis_results
                """)
                summary = cursor.fetchone()
                overview['data_summary']['analysis_results'] = dict(zip([
                    'total_analyses', 'unique_symbols', 'unique_timeframes',
                    'first_analysis', 'latest_analysis'
                ], summary))

            if 'trades' in tables:
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_trades,
                        COUNT(DISTINCT symbol) as unique_trade_symbols,
                        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_trades,
                        SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_trades,
                        MIN(created_at) as first_trade,
                        MAX(created_at) as latest_trade
                    FROM trades
                """)
                trade_summary = cursor.fetchone()
                overview['data_summary']['trades'] = dict(zip([
                    'total_trades', 'unique_trade_symbols', 'closed_trades',
                    'open_trades', 'first_trade', 'latest_trade'
                ], trade_summary))

        except Exception as e:
            overview['database_info']['error'] = str(e)
        finally:
            conn.close()

        return overview

    def _convert_raw_data_to_outcomes(self, raw_data: List[Dict]) -> List:
        """Convert raw database records to RecommendationOutcome objects"""
        outcomes = []
        failed_conversions = 0

        for i, record in enumerate(raw_data):
            try:
                # Convert raw database records to RecommendationOutcome objects
                analysis_data = record.get('analysis_data', {})
                trade_data = None
                if record.get('trade_status'):
                    trade_data = {
                        'pnl': record.get('pnl'),
                        'status': record.get('trade_status'),
                        'entry_price': record.get('trade_entry_price'),
                        'exit_price': record.get('trade_exit_price'),
                        'take_profit': record.get('trade_take_profit'),
                        'stop_loss': record.get('trade_stop_loss'),
                        'side': record.get('trade_side'),
                        'quantity': record.get('trade_quantity'),
                        'created_at': record.get('trade_created_at'),
                        'updated_at': record.get('trade_updated_at')
                    }

                # Determine outcome category
                outcome_category = 'no_trade'
                pnl = trade_data.get('pnl', 0.0) if trade_data else 0.0

                if trade_data and trade_data['status'] == 'closed':
                    if pnl is not None and pnl > self.PNL_PROFIT_THRESHOLD:
                        outcome_category = 'profitable'
                    elif pnl is not None and pnl < self.PNL_LOSS_THRESHOLD:
                        outcome_category = 'loss'
                    else:
                        outcome_category = 'breakeven'
                elif trade_data and trade_data['status'] == 'open':
                    outcome_category = 'pending'
                elif trade_data and trade_data['status'] == 'cancelled':
                    outcome_category = 'cancelled'

                success_metrics = {
                    "pnl": pnl,
                    "is_profitable": 1.0 if outcome_category == 'profitable' else 0.0,
                    "is_loss": 1.0 if outcome_category == 'loss' else 0.0,
                    "is_traded": 1.0 if trade_data else 0.0
                }

                from analysis_dashboard.outcome_tracker import RecommendationOutcome
                outcome = RecommendationOutcome(
                    recommendation_id=record.get('id') or 'unknown',
                    symbol=record.get('symbol', 'N/A'),
                    timeframe=record.get('timeframe', 'N/A'),
                    timestamp=record.get('timestamp', 'N/A'),
                    analysis_data=analysis_data,
                    trade_data=trade_data,
                    chart_path=record.get('image_path'),
                    success_metrics=success_metrics,
                    outcome_category=outcome_category
                )
                outcomes.append(outcome)

            except Exception as e:
                failed_conversions += 1
                self.logger.warning(f"Failed to convert record {i} to outcome: {e}")
                continue

        if failed_conversions > 0:
            self.logger.warning(f"Failed to convert {failed_conversions} out of {len(raw_data)} records")

        return outcomes

    def _get_raw_data(self) -> List[Dict]:
        """Get cached raw data or fetch from database if not cached"""
        if self._cached_raw_data is None:
            try:
                self.logger.info(f"Fetching raw data from database (limit: {self.DEFAULT_QUERY_LIMIT})")
                self._cached_raw_data = self.db_connector.get_recommendations_with_outcomes(limit=self.DEFAULT_QUERY_LIMIT)
                self.logger.info(f"Cached {len(self._cached_raw_data)} records for analysis")
            except Exception as e:
                self.logger.error(f"Failed to fetch data from database: {e}")
                raise
        else:
            self.logger.debug("Using cached raw data")
        return self._cached_raw_data

    def _clear_cache(self):
        """Clear cached data (useful for fresh analysis)"""
        self._cached_raw_data = None
        self.logger.info("Cache cleared")

    def analyze_trading_performance(self) -> Dict[str, Any]:
        """Generate comprehensive trading performance statistics"""
        raw_data = self._get_raw_data()
        outcomes = self._convert_raw_data_to_outcomes(raw_data)

        if not outcomes:
            return {'error': 'No trading data found'}

        # Convert to DataFrame for analysis
        df_data = []
        for outcome in outcomes:
            analysis_core_data = outcome.analysis_data.get('analysis', {})
            row = {
                'recommendation_id': outcome.recommendation_id,
                'symbol': outcome.symbol,
                'timeframe': outcome.timeframe,
                'timestamp': outcome.timestamp,
                'confidence_score': analysis_core_data.get('confidence', 0.0),
                'recommendation': analysis_core_data.get('recommendation', 'N/A'),
                'outcome_category': outcome.outcome_category,
                'pnl': outcome.success_metrics.get('pnl', 0.0),
                'is_profitable': outcome.success_metrics.get('is_profitable', 0.0),
                'is_loss': outcome.success_metrics.get('is_loss', 0.0),
                'is_traded': outcome.success_metrics.get('is_traded', 0.0),
                'trade_status': outcome.trade_data.get('status') if outcome.trade_data else None,
                'prompt_version': analysis_core_data.get('prompt_version', outcome.analysis_data.get('prompt_version', 'unknown')),
            }
            df_data.append(row)

        df = pd.DataFrame(df_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')

        # Filter for closed trades only for accurate performance metrics
        closed_trades_df = df[df['trade_status'] == 'closed'].copy()

        performance_metrics = {
            'overall_statistics': {},
            'performance_by_symbol': {},
            'performance_by_timeframe': {},
            'performance_by_recommendation': {},
            'performance_by_prompt': {},
            'confidence_analysis': {},
            'risk_reward_metrics': {},
            'advanced_analytics': {}
        }

        if closed_trades_df.empty:
            return {'error': 'No closed trades found for analysis'}

        # Overall statistics
        total_trades = len(closed_trades_df)
        profitable_trades = closed_trades_df['is_profitable'].sum()
        loss_trades = closed_trades_df['is_loss'].sum()
        total_pnl = closed_trades_df['pnl'].sum()

        # Handle potential division by zero
        win_rate = (profitable_trades / total_trades * 100) if total_trades > 0 else 0.0
        avg_pnl_per_trade = (total_pnl / total_trades) if total_trades > 0 else 0.0
        trade_conversion_rate = (total_trades / len(df) * 100) if len(df) > 0 else 0.0

        performance_metrics['overall_statistics'] = {
            'total_trades': int(total_trades),
            'profitable_trades': int(profitable_trades),
            'loss_trades': int(loss_trades),
            'win_rate': float(win_rate),
            'total_pnl': float(total_pnl),
            'avg_pnl_per_trade': float(avg_pnl_per_trade),
            'total_analyses': len(df),
            'trade_conversion_rate': float(trade_conversion_rate)
        }

        # Performance by symbol
        symbol_performance = closed_trades_df.groupby('symbol').agg({
            'pnl': ['sum', 'mean', 'count'],
            'is_profitable': 'sum'
        }).round(4)

        symbol_performance.columns = ['total_pnl', 'avg_pnl', 'trade_count', 'profitable_count']
        symbol_performance['win_rate'] = (symbol_performance['profitable_count'] /
                                        symbol_performance['trade_count'] * 100).round(2)
        # Handle division by zero for symbols with no trades
        symbol_performance['win_rate'] = symbol_performance['win_rate'].fillna(0)

        performance_metrics['performance_by_symbol'] = symbol_performance.to_dict('index')

        # Performance by timeframe
        timeframe_performance = closed_trades_df.groupby('timeframe').agg({
            'pnl': ['sum', 'mean', 'count'],
            'is_profitable': 'sum'
        }).round(4)

        timeframe_performance.columns = ['total_pnl', 'avg_pnl', 'trade_count', 'profitable_count']
        timeframe_performance['win_rate'] = (timeframe_performance['profitable_count'] /
                                           timeframe_performance['trade_count'] * 100).round(2)
        # Handle division by zero for timeframes with no trades
        timeframe_performance['win_rate'] = timeframe_performance['win_rate'].fillna(0)

        performance_metrics['performance_by_timeframe'] = timeframe_performance.to_dict('index')

        # Performance by recommendation type
        rec_performance = closed_trades_df.groupby('recommendation').agg({
            'pnl': ['sum', 'mean', 'count'],
            'is_profitable': 'sum'
        }).round(4)

        rec_performance.columns = ['total_pnl', 'avg_pnl', 'trade_count', 'profitable_count']
        rec_performance['win_rate'] = (rec_performance['profitable_count'] /
                                     rec_performance['trade_count'] * 100).round(2)
        # Handle division by zero for recommendations with no trades
        rec_performance['win_rate'] = rec_performance['win_rate'].fillna(0)

        performance_metrics['performance_by_recommendation'] = rec_performance.to_dict('index')

        # Performance by prompt version
        prompt_performance = closed_trades_df.groupby('prompt_version').agg({
            'pnl': ['sum', 'mean', 'count'],
            'is_profitable': 'sum'
        }).round(4)

        prompt_performance.columns = ['total_pnl', 'avg_pnl', 'trade_count', 'profitable_count']
        prompt_performance['win_rate'] = (prompt_performance['profitable_count'] /
                                        prompt_performance['trade_count'] * 100).round(2)
        # Handle division by zero for prompts with no trades
        prompt_performance['win_rate'] = prompt_performance['win_rate'].fillna(0)

        # Filter for prompts with minimum trades for meaningful analysis
        prompt_performance_filtered = prompt_performance[prompt_performance['trade_count'] >= self.MIN_TRADES_FOR_ANALYSIS].copy()
        prompt_performance_filtered = prompt_performance_filtered.sort_values('total_pnl', ascending=False)

        performance_metrics['performance_by_prompt'] = prompt_performance_filtered.to_dict('index')

        # Confidence analysis
        confidence_analysis = closed_trades_df.groupby(pd.cut(closed_trades_df['confidence_score'],
                                                           bins=self.CONFIDENCE_BINS,
                                                           labels=self.CONFIDENCE_LABELS)).agg({
            'pnl': ['count', 'sum', 'mean'],
            'is_profitable': 'sum'
        }).round(4)

        confidence_analysis.columns = ['trade_count', 'total_pnl', 'avg_pnl', 'profitable_count']
        confidence_analysis['win_rate'] = (confidence_analysis['profitable_count'] /
                                         confidence_analysis['trade_count'] * 100).round(2)
        # Handle division by zero for confidence levels with no trades
        confidence_analysis['win_rate'] = confidence_analysis['win_rate'].fillna(0)

        performance_metrics['confidence_analysis'] = confidence_analysis.to_dict('index')

        # Risk-reward metrics
        winning_trades = closed_trades_df[closed_trades_df['pnl'] > 0]
        losing_trades = closed_trades_df[closed_trades_df['pnl'] < 0]

        avg_win = winning_trades['pnl'].mean() if not winning_trades.empty else 0
        avg_loss = abs(losing_trades['pnl'].mean()) if not losing_trades.empty else 0
        profit_factor = avg_win / avg_loss if avg_loss != 0 else float('inf')

        # Handle edge cases for max/min calculations
        max_profit = float(closed_trades_df['pnl'].max()) if not closed_trades_df.empty else 0
        min_profit = float(closed_trades_df['pnl'].min()) if not closed_trades_df.empty else 0

        performance_metrics['risk_reward_metrics'] = {
            'average_win': float(avg_win),
            'average_loss': float(avg_loss),
            'profit_factor': float(profit_factor),
            'max_profit': max_profit,
            'max_loss': min_profit,
            'largest_winning_streak': self._calculate_streak_length(closed_trades_df, 'win'),
            'largest_losing_streak': self._calculate_streak_length(closed_trades_df, 'loss')
        }

        # Advanced analytics
        # Top 3 and worst 3 prompts
        if not prompt_performance_filtered.empty:
            top_3_prompts = prompt_performance_filtered.head(3)
            worst_3_prompts = prompt_performance_filtered.tail(3)

            top_3_list = []
            for prompt_name, data in top_3_prompts.iterrows():
                top_3_list.append({
                    'prompt_name': prompt_name,
                    'total_pnl': float(data['total_pnl']),
                    'win_rate': float(data['win_rate']),
                    'trade_count': int(data['trade_count']),
                    'avg_pnl': float(data['avg_pnl'])
                })

            worst_3_list = []
            for prompt_name, data in worst_3_prompts.iterrows():
                worst_3_list.append({
                    'prompt_name': prompt_name,
                    'total_pnl': float(data['total_pnl']),
                    'win_rate': float(data['win_rate']),
                    'trade_count': int(data['trade_count']),
                    'avg_pnl': float(data['avg_pnl'])
                })
        else:
            top_3_list = []
            worst_3_list = []

        performance_metrics['advanced_analytics'] = {
            'confidence_pnl_correlation': float(closed_trades_df['confidence_score'].corr(closed_trades_df['pnl'])),
            'avg_confidence_profitable': float(closed_trades_df[closed_trades_df['is_profitable'] == 1]['confidence_score'].mean()),
            'avg_confidence_loss': float(closed_trades_df[closed_trades_df['is_loss'] == 1]['confidence_score'].mean()),
            'best_performing_symbol': max(performance_metrics['performance_by_symbol'].items(),
                                       key=lambda x: x[1]['total_pnl'])[0] if performance_metrics['performance_by_symbol'] else None,
            'best_performing_timeframe': max(performance_metrics['performance_by_timeframe'].items(),
                                          key=lambda x: x[1]['total_pnl'])[0] if performance_metrics['performance_by_timeframe'] else None,
            'top_3_prompts': top_3_list,
            'worst_3_prompts': worst_3_list
        }

        return performance_metrics

    def _calculate_streak_length(self, df: pd.DataFrame, streak_type: str) -> int:
        """Calculate the longest winning or losing streak"""
        if streak_type == 'win':
            streak_series = (df['pnl'] > 0).astype(int)
        else:
            streak_series = (df['pnl'] < 0).astype(int)

        max_streak = 0
        current_streak = 0

        for value in streak_series:
            if value == 1:
                current_streak += 1
                max_streak = max(max_streak, current_streak)
            else:
                current_streak = 0

        return max_streak

    def analyze_prompt_effectiveness(self) -> Dict[str, Any]:
        """Analyze prompt effectiveness and identify improvement opportunities"""
        raw_data = self._get_raw_data()
        outcomes = self._convert_raw_data_to_outcomes(raw_data)

        if not outcomes:
            return {'error': 'No analysis data found for prompt analysis'}

        # Analyze field completion rates
        completion_rates = self.prompt_analyzer.analyze_field_completion_rates(outcomes)

        # Identify prompt gaps
        prompt_gaps = self.prompt_analyzer.identify_prompt_gaps(outcomes)

        # Generate improvement suggestions
        improvement_suggestions = self.prompt_analyzer.generate_prompt_improvement_suggestions(outcomes)

        # Analyze effectiveness by timeframe
        timeframe_effectiveness = self.prompt_analyzer.analyze_prompt_effectiveness_by_timeframe(outcomes)

        # Identify weak analysis areas
        weak_areas = self.prompt_analyzer.identify_weak_analysis_areas(outcomes)

        # Generate comprehensive report
        optimization_report = self.prompt_analyzer.generate_prompt_optimization_report(outcomes)

        return {
            'field_completion_rates': completion_rates,
            'prompt_gaps': [gap.__dict__ for gap in prompt_gaps],
            'improvement_suggestions': [suggestion.__dict__ for suggestion in improvement_suggestions],
            'effectiveness_by_timeframe': timeframe_effectiveness,
            'weak_analysis_areas': weak_areas,
            'optimization_report': optimization_report
        }

    def analyze_failure_patterns(self) -> Dict[str, Any]:
        """Identify patterns in failed trades for improvement insights"""
        raw_data = self._get_raw_data()
        outcomes = self._convert_raw_data_to_outcomes(raw_data)

        if not outcomes:
            return {'error': 'No data found for failure pattern analysis'}

        # Filter for failed outcomes
        failed_outcomes = [o for o in outcomes if o.outcome_category == 'loss']

        if not failed_outcomes:
            return {'message': 'No failed trades found for pattern analysis'}

        failure_analysis = {
            'overall_failure_stats': {},
            'failure_by_symbol': {},
            'failure_by_timeframe': {},
            'failure_by_confidence': {},
            'common_missing_fields': {},
            'failure_patterns': {}
        }

        # Overall failure statistics
        total_failed = len(failed_outcomes)
        total_outcomes = len(outcomes)
        failure_rate = (total_failed / total_outcomes * 100) if total_outcomes > 0 else 0.0

        failure_analysis['overall_failure_stats'] = {
            'total_failed_trades': total_failed,
            'failure_rate': failure_rate
        }

        # Convert to DataFrame for easier analysis
        df_data = []
        for outcome in failed_outcomes:
            analysis_core_data = outcome.analysis_data.get('analysis', {})
            row = {
                'symbol': outcome.symbol,
                'timeframe': outcome.timeframe,
                'confidence_score': analysis_core_data.get('confidence', 0.0),
                'recommendation': analysis_core_data.get('recommendation', 'N/A'),
                'pnl': outcome.success_metrics.get('pnl', 0.0),
                'analysis_data': outcome.analysis_data
            }
            df_data.append(row)

        failed_df = pd.DataFrame(df_data)

        # Failure by symbol
        symbol_failures = failed_df.groupby('symbol').size().sort_values(ascending=False)
        failure_analysis['failure_by_symbol'] = symbol_failures.to_dict()

        # Failure by timeframe
        timeframe_failures = failed_df.groupby('timeframe').size().sort_values(ascending=False)
        failure_analysis['failure_by_timeframe'] = timeframe_failures.to_dict()

        # Failure by confidence level
        confidence_failures = failed_df.groupby(pd.cut(failed_df['confidence_score'],
                                                     bins=TradingPerformanceAnalyzer.CONFIDENCE_BINS,
                                                     labels=TradingPerformanceAnalyzer.CONFIDENCE_LABELS)).size()
        failure_analysis['failure_by_confidence'] = confidence_failures.to_dict()

        # Common missing fields in failed analyses
        missing_fields = {}
        for outcome in failed_outcomes:
            issues = self.data_parser.extract_prompt_issues(outcome.analysis_data)
            for issue in issues:
                missing_fields[issue] = missing_fields.get(issue, 0) + 1

        failure_analysis['common_missing_fields'] = missing_fields

        # Failure patterns
        failure_patterns = {
            'overconfident_losses': len([o for o in failed_outcomes
                                       if o.analysis_data.get('analysis', {}).get('confidence', 0) > self.OVERCONFIDENT_THRESHOLD]),
            'high_confidence_losses': len([o for o in failed_outcomes
                                         if o.analysis_data.get('analysis', {}).get('confidence', 0) > self.HIGH_CONFIDENCE_THRESHOLD]),
            'low_confidence_losses': len([o for o in failed_outcomes
                                        if o.analysis_data.get('analysis', {}).get('confidence', 0) < self.LOW_CONFIDENCE_THRESHOLD])
        }

        failure_analysis['failure_patterns'] = failure_patterns

        return failure_analysis

    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate a comprehensive trading improvement report"""
        report = {
            'generated_at': datetime.now().isoformat(),
            'database_overview': self.get_database_overview(),
            'trading_performance': self.analyze_trading_performance(),
            'prompt_effectiveness': self.analyze_prompt_effectiveness(),
            'failure_patterns': self.analyze_failure_patterns(),
            'key_insights': [],
            'recommendations': []
        }

        # Generate key insights
        insights = []

        # Performance insights
        perf = report['trading_performance']
        if 'overall_statistics' in perf:
            stats = perf['overall_statistics']
            insights.append(f"Win Rate: {stats['win_rate']:.1f}% across {stats['total_trades']} trades")
            insights.append(f"Total PnL: ${stats['total_pnl']:.2f} (Avg: ${stats['avg_pnl_per_trade']:.2f} per trade)")
            insights.append(f"Trade Conversion Rate: {stats['trade_conversion_rate']:.1f}% of analyses result in trades")

        # Symbol insights
        if 'performance_by_symbol' in perf and perf['performance_by_symbol']:
            best_symbol = max(perf['performance_by_symbol'].items(), key=lambda x: x[1]['total_pnl'])
            worst_symbol = min(perf['performance_by_symbol'].items(), key=lambda x: x[1]['total_pnl'])
            insights.append(f"Best performing symbol: {best_symbol[0]} (${best_symbol[1]['total_pnl']:.2f})")
            insights.append(f"Worst performing symbol: {worst_symbol[0]} (${worst_symbol[1]['total_pnl']:.2f})")

        # Confidence insights
        if 'advanced_analytics' in perf:
            adv = perf['advanced_analytics']
            correlation = adv.get('confidence_pnl_correlation', 0)
            insights.append(f"Confidence-PnL Correlation: {correlation:.3f}")

            if correlation < 0.3:  # TODO: Make this configurable
                insights.append("⚠️ Low correlation between confidence and actual outcomes - prompt needs improvement")

        # Prompt effectiveness insights
        prompt_eff = report['prompt_effectiveness']
        if 'field_completion_rates' in prompt_eff:
            completion_rates = prompt_eff['field_completion_rates']
            if completion_rates:
                worst_field = max(completion_rates.items(), key=lambda x: x[1])
                insights.append(f"Most problematic field: {worst_field[0]} (missing in {worst_field[1]*100:.1f}% of analyses)")

        # Failure pattern insights
        failure = report['failure_patterns']
        if 'overall_failure_stats' in failure:
            fail_stats = failure['overall_failure_stats']
            insights.append(f"Failure Rate: {fail_stats['failure_rate']:.1f}% of all outcomes")

        report['key_insights'] = insights

        # Generate recommendations
        recommendations = []

        # Performance recommendations
        if 'risk_reward_metrics' in perf:
            rr = perf['risk_reward_metrics']
            if rr['profit_factor'] < 1.0:
                recommendations.append("⚠️ Profit factor below 1.0 - review risk management and trade sizing")
            if rr['largest_losing_streak'] > 5:
                recommendations.append("⚠️ Long losing streaks detected - consider reducing position sizes or adding more filters")

        # Prompt recommendations
        if 'prompt_gaps' in prompt_eff and prompt_eff['prompt_gaps']:
            recommendations.append("🔧 Prompt improvements needed - see detailed suggestions in prompt_effectiveness section")

        # Symbol recommendations
        if 'performance_by_symbol' in perf and perf['performance_by_symbol']:
            poor_performers = [symbol for symbol, data in perf['performance_by_symbol'].items()
                             if data['total_pnl'] < 0 and data['trade_count'] >= 3]
            if poor_performers:
                recommendations.append(f"⚠️ Poor performance on: {', '.join(poor_performers[:3])} - consider reducing exposure or improving analysis")

        report['recommendations'] = recommendations

        return report

def main():
    """Main function to run the comprehensive analysis"""
    logger = logging.getLogger(__name__)
    logger.info("🔍 Starting Comprehensive Trading Performance Analysis...")
    logger.info("=" * 60)

    analyzer = TradingPerformanceAnalyzer()

    try:
        # Generate comprehensive report
        report = analyzer.generate_comprehensive_report()

        if 'error' in report:
            logger.error(f"❌ Error: {report['error']}")
            return

        # Display results
        logger.info("📊 DATABASE OVERVIEW")
        logger.info("-" * 30)

        db_overview = report['database_overview']
        if 'data_summary' in db_overview:
            summary = db_overview['data_summary']

            if 'analysis_results' in summary:
                ar = summary['analysis_results']
                logger.info(f"Total Analyses: {ar.get('total_analyses', 0):,}")
                logger.info(f"Unique Symbols: {ar.get('unique_symbols', 0)}")
                logger.info(f"Unique Timeframes: {ar.get('unique_timeframes', 0)}")
                logger.info(f"Date Range: {ar.get('first_analysis', 'N/A')} to {ar.get('latest_analysis', 'N/A')}")

            if 'trades' in summary:
                trades = summary['trades']
                logger.info(f"Total Trades: {trades.get('total_trades', 0):,}")
                logger.info(f"Closed Trades: {trades.get('closed_trades', 0):,}")
                logger.info(f"Open Trades: {trades.get('open_trades', 0):,}")

        logger.info("\n📈 TRADING PERFORMANCE")
        logger.info("-" * 30)

        perf = report['trading_performance']
        if 'overall_statistics' in perf:
            stats = perf['overall_statistics']
            logger.info(f"Win Rate: {stats['win_rate']:.1f}%")
            logger.info(f"Total PnL: ${stats['total_pnl']:,.2f}")
            logger.info(f"Avg PnL/Trade: ${stats['avg_pnl_per_trade']:,.2f}")
            logger.info(f"Total Trades: {stats['total_trades']:,}")

        if 'risk_reward_metrics' in perf:
            rr = perf['risk_reward_metrics']
            logger.info(f"Profit Factor: {rr['profit_factor']:.2f}")
            logger.info(f"Max Profit: ${rr['max_profit']:.2f}")
            logger.info(f"Max Loss: ${rr['max_loss']:.2f}")

        # Display prompt performance ranking
        if 'advanced_analytics' in perf and 'top_3_prompts' in perf['advanced_analytics']:
            top_3 = perf['advanced_analytics']['top_3_prompts']
            worst_3 = perf['advanced_analytics']['worst_3_prompts']

            logger.info("\n🏆 PROMPT PERFORMANCE RANKING")
            logger.info("-" * 30)

            if top_3:
                logger.info("**Top 3 Best Performing Prompts (with actual trades):**")
                for i, prompt in enumerate(top_3, 1):
                    logger.info(f"{i}. **{prompt['prompt_name']}**")
                    logger.info(f"   • Total PnL: ${prompt['total_pnl']:.2f}")
                    logger.info(f"   • Win Rate: {prompt['win_rate']:.1f}%")
                    logger.info(f"   • Avg PnL: ${prompt['avg_pnl']:.2f}")
                    logger.info(f"   • Trades: {prompt['trade_count']}")

            # Show prompts with 0 trades dynamically
            if 'performance_by_prompt' in perf:
                all_prompts_with_data = list(perf['performance_by_prompt'].keys())
                prompts_with_trades = [tp['prompt_name'] for tp in top_3]
                zero_trade_prompts = [p for p in all_prompts_with_data if p not in prompts_with_trades]

                if zero_trade_prompts:
                    logger.info("\n**Prompts Generating Analyses (0 trades executed):**")
                    for prompt_name in zero_trade_prompts[:5]:  # Show top 5
                        prompt_data = perf['performance_by_prompt'][prompt_name]
                        logger.info(f"• {prompt_name}: {prompt_data['trade_count']} analyses, 0 trades")

            logger.info("\n💡 **KEY INSIGHT:** Only prompts that generate 'tradeable' recommendations")
            logger.info("   (passing trading system filters) result in actual trades.")

        logger.info("\n🎯 KEY INSIGHTS")
        logger.info("-" * 30)
        for insight in report['key_insights'][:TradingPerformanceAnalyzer.MAX_INSIGHTS_DISPLAY]:  # Show top insights
            logger.info(f"• {insight}")

        logger.info("\n💡 RECOMMENDATIONS")
        logger.info("-" * 30)
        for rec in report['recommendations']:
            logger.info(f"• {rec}")

        # Save detailed report to file
        report_file = f"trading_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            logger.info(f"\n💾 Detailed report saved to: {report_file}")
        except IOError as e:
            logger.error(f"Failed to save report to file {report_file}: {e}")
            logger.info("💾 Report analysis completed but could not be saved to file")

    except Exception as e:
        logger.error(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()