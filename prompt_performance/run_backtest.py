#!/usr/bin/env python3
"""
Prompt Performance Backtest Runner
Optimized backtest system for evaluating trading prompt performance.
"""

import logging
import argparse
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from prompt_performance.core.backtest_orchestrator import BacktestOrchestrator

def setup_logging(verbose=False):
    """Setup logging configuration."""
    # Get the directory where this script is located
    script_dir = Path(__file__).parent
    log_file = script_dir / 'backtest.log'

    level = logging.DEBUG if verbose else logging.INFO

    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file)
        ]
    )

def main():
    """Main entry point for the backtest system."""
    parser = argparse.ArgumentParser(description='Run prompt performance backtest')
    parser.add_argument('--limit', type=int, help='Limit number of analysis records to process')
    parser.add_argument('--testnet', action='store_true', help='Use Bybit testnet instead of mainnet')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    # Setup logging
    setup_logging(verbose=args.verbose)

    logger = logging.getLogger(__name__)
    logger.info("Starting prompt performance backtest system")

    try:
        # Initialize orchestrator
        orchestrator = BacktestOrchestrator(use_testnet=args.testnet)

        # Run backtest
        result = orchestrator.run_backtest(limit=args.limit)

        if result['success']:
            logger.info("Backtest completed successfully!")
            logger.info(f"Total trades simulated: {result['total_trades']}")
            logger.info(f"Win rate: {result['win_rate']:.2%}")
            logger.info(f"Duration: {result['duration_seconds']:.2f} seconds")
            logger.info("Results saved to prompt_performance/ directory")
        else:
            logger.error(f"Backtest failed: {result.get('error', 'Unknown error')}")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Backtest interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()