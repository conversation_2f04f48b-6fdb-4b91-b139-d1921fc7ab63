#!/usr/bin/env python3
"""
Historical Candle Data Population Script
Populates the database with maximum historical candle data for all symbols and timeframes.
"""

import logging
import argparse
import sys
from pathlib import Path
from typing import List, Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from prompt_performance.core.candle_fetcher import CandleFetcher

def setup_logging(verbose=False):
    """Setup logging configuration."""
    script_dir = Path(__file__).parent
    log_file = script_dir / 'populate_historical.log'

    level = logging.DEBUG if verbose else logging.INFO

    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file)
        ]
    )

def get_common_symbols() -> List[str]:
    """Get list of common trading symbols to populate."""
    return [
        # Major cryptocurrencies
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
        'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'LTCUSDT', 'TRXUSDT',
        'LINKUSDT', 'UNIUSDT', 'ALGOUSDT', 'ICPUSDT', 'XRPUSDT',
        'BCHUSDT', 'ETCUSDT', 'FILUSDT', 'VETUSDT', 'THETAUSDT'
    ]

def get_common_timeframes() -> List[str]:
    """Get list of common timeframes to populate."""
    return ['1h', '4h', '1d']

def main():
    """Main entry point for the historical data population script."""
    parser = argparse.ArgumentParser(description='Populate database with historical candle data')
    parser.add_argument('--symbols', nargs='*', help='Specific symbols to populate (default: all common)')
    parser.add_argument('--timeframes', nargs='*', help='Specific timeframes to populate (default: 1h,4h,1d)')
    parser.add_argument('--max-historical', action='store_true', help='Fetch maximum historical data')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    # Setup logging
    setup_logging(verbose=args.verbose)

    logger = logging.getLogger(__name__)
    logger.info("Starting historical data population script")

    try:
        # Initialize candle fetcher
        fetcher = CandleFetcher()

        # Get symbols and timeframes to process
        symbols = args.symbols if args.symbols else get_common_symbols()
        timeframes = args.timeframes if args.timeframes else get_common_timeframes()

        logger.info(f"Processing {len(symbols)} symbols with {len(timeframes)} timeframes")

        total_processed = 0
        total_success = 0

        for symbol in symbols:
            for timeframe in timeframes:
                try:
                    logger.info(f"Processing {symbol} {timeframe}")
                    total_processed += 1

                    # Fetch maximum historical data
                    success = fetcher.fetch_and_cache_candles(
                        symbol=symbol,
                        timeframe=timeframe,
                        earliest_timestamp=0,  # Will be ignored when max_historical=True
                        max_historical=True
                    )

                    if success:
                        total_success += 1
                        logger.info(f"✅ Successfully populated {symbol} {timeframe}")
                    else:
                        logger.warning(f"❌ Failed to populate {symbol} {timeframe}")

                except Exception as e:
                    logger.error(f"Error processing {symbol} {timeframe}: {e}")
                    continue

        logger.info(f"Population completed: {total_success}/{total_processed} successful")

    except KeyboardInterrupt:
        logger.info("Population interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()