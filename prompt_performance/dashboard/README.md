prompt_performance/launch_enhanced_dashboard.py




🚀 LAUNCHING ENHANCED PROMPT PERFORMANCE DASHBOARD
============================================================
✅ Data loaded successfully:
   📊 477 prompt combinations
   📈 5099 trades
   🎯 19 unique prompt hashes

🏆 Top 5 Performing Combinations:
   1. 69f94 (XRPUSDT 1h): 100.0% (1 trades)
   2. bf8bd (CETUSUSDT.P 1h): 100.0% (3 trades)
   3. c6d70 (ICPUSDT.P 1h): 100.0% (1 trades)
   4. c6d70 (DYDXUSDT.P 1h): 100.0% (2 trades)
   5. 69f94 (DASHUSDT.P 1h): 100.0% (10 trades)

🔬 Testing Enhanced Analytics Modules:
   ✅ Statistical Testing Framework
   ✅ Enhanced Metrics Aggregator
   ✅ A/B Testing Manager

📊 QUICK STATISTICAL ANALYSIS:
----------------------------------------
🧪 Comparing 83dad vs fbae8:
   Win Rate A: 26.9% (1339 trades)
   Win Rate B: 37.1% (896 trades)
   P-Value: 0.0000
   Significant: ✅ YES

💡 Gener

# Enhanced Prompt Performance Dashboard

A comprehensive statistical analysis and A/B testing platform for trading prompt optimization, built with advanced analytics capabilities and professional-grade statistical rigor.

## 🚀 New Features

### **Statistical Foundation**
- **Confidence Intervals**: 95% Wilson confidence intervals for all performance metrics
- **Statistical Significance Testing**: Two-proportion z-tests with multiple comparison corrections
- **Effect Size Analysis**: Cohen's h effect sizes with practical significance thresholds
- **Sample Size Analysis**: Power analysis and adequacy assessment for reliable conclusions

### **A/B Testing Framework**
- **Experiment Designer**: GUI for creating controlled prompt comparisons
- **Real-time Monitoring**: Live statistical significance tracking
- **Early Stopping**: Automatic experiment termination when significance is achieved
- **Power Calculations**: Determine required sample sizes before testing

### **Enhanced Analytics**
- **Performance Stability**: Time-series analysis of prompt performance degradation
- **Smart Insights**: Automated interpretation of statistical results
- **Executive Dashboard**: High-level overview with actionable recommendations
- **Bootstrap Confidence Intervals**: Robust uncertainty estimates for complex metrics

## 📊 Dashboard Modes

### 1. Executive Dashboard
- **Key Insights Panel**: Top 3 actionable insights with confidence levels
- **Performance Leaderboard**: Confidence interval visualization
- **Alert System**: Performance degradation and sample size warnings
- **Quick Actions**: One-click access to detailed analysis

### 2. Statistical Analysis
- **Confidence Intervals**: Forest plots with 95% confidence bounds
- **Significance Testing**: Heatmaps and comparison matrices
- **Sample Size Analysis**: Adequacy assessment and power calculations
- **Effect Sizes**: Practical significance evaluation

### 3. A/B Testing Lab
- **Experiment Designer**: Create controlled prompt comparisons
- **Active Tests**: Real-time monitoring with statistical significance
- **Results Archive**: Historical test results and learnings

### 4. Classic View
- **Backward Compatibility**: Original dashboard functionality preserved
- **Gradual Migration**: Use existing workflows while adopting new features

## 🛠 Installation & Setup

### Prerequisites
```bash
# Install core dependencies
pip install -r requirements.txt

# Optional: Install NLP dependencies for prompt optimization
pip install spacy textstat nltk
python -m spacy download en_core_web_sm
```

### Quick Start
```bash
# Launch enhanced dashboard
cd prompt_performance/dashboard
streamlit run enhanced_app.py

# Launch classic dashboard (existing)
streamlit run app.py
```

### Configuration
The enhanced dashboard automatically detects and imports available modules:
- **Full Features**: All modules available (statistical testing, A/B testing, etc.)
- **Basic Mode**: Falls back to original functionality if modules unavailable
- **Graceful Degradation**: Shows helpful messages for missing features

## 📈 Usage Examples

### Running Statistical Analysis
```python
from enhanced_metrics import EnhancedMetricsAggregator
from statistical_testing import StatisticalTestingFramework

# Load your trade results
trade_results = load_backtest_results()

# Generate enhanced report
aggregator = EnhancedMetricsAggregator()
enhanced_data = aggregator.generate_enhanced_report(trade_results, "hash")

# Access statistical insights
insights = enhanced_data['insights']
confidence_intervals = enhanced_data['confidence_intervals']
statistical_comparisons = enhanced_data['statistical_comparisons']
```

### Setting Up A/B Tests
```python
from ab_testing import ABTestManager, ABTestConfig, generate_test_id

# Create test manager
ab_manager = ABTestManager()

# Design experiment
config = ABTestConfig(
    test_id=generate_test_id(),
    test_name="Prompt V2 vs V3 Comparison",
    prompt_a_id="baseline_v2",
    prompt_b_id="enhanced_v3",
    symbols=["BTCUSDT", "ETHUSDT"],
    timeframes=["4h", "1d"],
    target_sample_size=200,
    max_duration_days=30,
    significance_level=0.05,
    minimum_effect_size=0.05,
    early_stopping_enabled=True
)

# Launch experiment
test_id = ab_manager.create_experiment(config)
ab_manager.start_experiment(test_id)
```

### Statistical Testing
```python
from statistical_testing import StatisticalTestingFramework

framework = StatisticalTestingFramework()

# Compare two prompts
prompt_a_trades = get_trades_for_prompt("prompt_a")
prompt_b_trades = get_trades_for_prompt("prompt_b")

result = framework.two_proportion_test(prompt_a_trades, prompt_b_trades)

print(f"P-value: {result.p_value:.4f}")
print(f"Effect size: {result.effect_size:.3f}")
print(f"Significant: {result.significant}")
print(f"Interpretation: {result.interpretation}")
```

## 📊 Data Requirements

### Input Data Format
The enhanced dashboard expects the same CSV format as the original:
- **Summary File**: `summary.csv` or `summary_prompt_hash.csv`
- **Trade Logs**: `trade_logs.csv` or `trade_logs_prompt_hash.csv`

### Required Columns
```
Trade Logs:
- prompt_version or prompt_hash
- symbol, timeframe, timestamp
- direction, entry_price, stop_loss, take_profit
- outcome, duration_candles, achieved_rr, confidence

Summary:
- prompt_version or prompt_hash
- symbol, timeframe
- total_trades, win_rate, avg_rr, profit_factor, expectancy
```

## 🎯 Key Improvements Over Original

### **Statistical Rigor**
- **Before**: Raw percentages without error bounds
- **After**: Confidence intervals and statistical significance testing
- **Impact**: Reliable identification of truly superior prompts

### **Decision Making**
- **Before**: Manual interpretation of results
- **After**: Automated insights with actionable recommendations
- **Impact**: Faster, more confident optimization decisions

### **Experimental Design**
- **Before**: Ad-hoc comparisons
- **After**: Structured A/B testing with proper controls
- **Impact**: Scientifically valid prompt optimization

### **User Experience**
- **Before**: Complex interface requiring expertise
- **After**: Executive dashboard with guided insights
- **Impact**: Accessible to non-technical stakeholders

## 🔧 Advanced Configuration

### Statistical Parameters
```python
# Configure significance testing
framework = StatisticalTestingFramework(
    alpha=0.05,                    # Significance level
    correction_method='fdr_bh'     # Multiple comparison correction
)

# Configure enhanced metrics
aggregator = EnhancedMetricsAggregator(
    confidence_level=0.95,         # Confidence interval level
    min_sample_size=30             # Minimum for reliable results
)
```

### A/B Testing Parameters
```python
config = ABTestConfig(
    significance_level=0.05,       # Type I error rate
    power=0.80,                    # Statistical power
    minimum_effect_size=0.05,      # Smallest meaningful difference
    early_stopping_enabled=True    # Stop when significance achieved
)
```

## 📈 Performance Metrics

### Enhanced Metrics Available
1. **Win Rate with Confidence Intervals**
2. **Profit Factor with Bootstrap CIs**
3. **Effect Sizes (Cohen's h)**
4. **Statistical Power Analysis**
5. **Performance Stability Over Time**
6. **Sample Size Adequacy Assessment**

### Statistical Tests Performed
1. **Two-Proportion Z-Test**: Compare win rates between prompts
2. **Multiple Comparison Correction**: Control false discovery rate
3. **Bootstrap Resampling**: Robust confidence intervals
4. **Wilson Score Intervals**: Accurate small-sample confidence bounds

## 🚨 Troubleshooting

### Common Issues

**1. "Enhanced features not available"**
```bash
# Install missing dependencies
pip install scipy statsmodels scikit-learn
```

**2. "No data available for analysis"**
```bash
# Run backtest to generate data
python run_backtest_prompt_hash.py
```

**3. "Database connection error"**
```bash
# Check database file exists
ls -la candle_cache.db
```

### Debugging Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 Further Reading

### Statistical Concepts
- **Confidence Intervals**: Understanding uncertainty in estimates
- **Statistical Significance**: When differences are meaningful
- **Effect Sizes**: Practical vs statistical significance
- **Multiple Comparisons**: Controlling false positive rates

### A/B Testing Best Practices
- **Power Analysis**: Determining sample size requirements
- **Early Stopping**: When to end experiments early
- **Randomization**: Ensuring valid experimental design
- **Practical Significance**: Beyond statistical significance

## 🤝 Contributing

### Development Setup
```bash
# Clone and install development dependencies
pip install -r requirements.txt
pip install pytest pytest-cov

# Run tests
python -m pytest tests/ -v --cov=prompt_performance
```

### Adding New Features
1. **Statistical Methods**: Add to `statistical_testing.py`
2. **Visualizations**: Add to `enhanced_app.py`
3. **Metrics**: Add to `enhanced_metrics.py`
4. **A/B Testing**: Add to `ab_testing.py`

## 📄 License

This enhanced dashboard builds upon the existing prompt performance system and follows the same licensing terms.

---

**🎯 Ready to optimize your trading prompts with statistical confidence?**

Launch the enhanced dashboard and start making data-driven decisions with professional-grade statistical analysis!