"""
Enhanced Prompt Performance Dashboard with Statistical Analysis and A/B Testing.
Built on top of the existing dashboard with advanced analytics capabilities.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add core modules to path - try multiple approaches
core_paths = [
    str(Path(__file__).parent.parent / "core"),
    str(Path(__file__).resolve().parent.parent / "core"),
    str(Path(__file__).parent / ".." / "core"),
    str(Path(__file__).parent.parent.parent),  # Project root
    str(Path(__file__).resolve().parent.parent.parent),  # Project root (resolved)
]

for core_path in core_paths:
    if core_path not in sys.path:
        sys.path.insert(0, core_path)

# Also add the current working directory and its parent
import os
current_dir = os.getcwd()
parent_dir = os.path.dirname(current_dir)

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import our enhanced modules with multiple fallback attempts
ENHANCED_FEATURES_AVAILABLE = False
StatisticalTestingFramework = None
EnhancedMetricsAggregator = None
ABTestManager = None
CandleCacheDatabase = None

# Import BacktestOrchestrator for backtest runner functionality
BacktestOrchestrator = None

def normalize_symbol(symbol: str) -> str:
    """Normalize symbol by removing .P suffix if present."""
    if symbol and symbol.endswith('.P'):
        return symbol[:-2]  # Remove .P suffix
    return symbol

# Initialize variables
ENHANCED_FEATURES_AVAILABLE = False
StatisticalTestingFramework = None
EnhancedMetricsAggregator = None
ABTestManager = None
CandleCacheDatabase = None
BacktestOrchestrator = None

# Try to import enhanced features
try:
    # Try direct imports with correct paths
    from prompt_performance.core.statistical_testing import StatisticalTestingFramework
    from prompt_performance.core.enhanced_metrics import EnhancedMetricsAggregator
    from prompt_performance.core.ab_testing import ABTestManager, ABTestConfig, generate_test_id
    from prompt_performance.core.database_utils import CandleCacheDatabase
    from prompt_performance.core.backtest_orchestrator import BacktestOrchestrator
    ENHANCED_FEATURES_AVAILABLE = True
    logger.info("✅ Enhanced features loaded successfully")
except ImportError as e:
    logger.warning(f"❌ Enhanced features not available via direct import: {e}")

    # Try importing with importlib as fallback
    try:
        import importlib.util
        import os

        core_dir = Path(__file__).parent.parent / "core"

        # Load statistical_testing
        try:
            spec = importlib.util.spec_from_file_location("statistical_testing", core_dir / "statistical_testing.py")
            if spec and spec.loader:
                stats_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(stats_module)
                StatisticalTestingFramework = stats_module.StatisticalTestingFramework
        except Exception as e:
            logger.warning(f"Failed to load statistical_testing: {e}")

        # Load enhanced_metrics
        try:
            spec = importlib.util.spec_from_file_location("enhanced_metrics", core_dir / "enhanced_metrics.py")
            if spec and spec.loader:
                metrics_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(metrics_module)
                EnhancedMetricsAggregator = metrics_module.EnhancedMetricsAggregator
        except Exception as e:
            logger.warning(f"Failed to load enhanced_metrics: {e}")

        # Load ab_testing
        try:
            spec = importlib.util.spec_from_file_location("ab_testing", core_dir / "ab_testing.py")
            if spec and spec.loader:
                ab_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(ab_module)
                ABTestManager = ab_module.ABTestManager
                ABTestConfig = ab_module.ABTestConfig
                generate_test_id = ab_module.generate_test_id
        except Exception as e:
            logger.warning(f"Failed to load ab_testing: {e}")

        # Load database_utils
        try:
            spec = importlib.util.spec_from_file_location("database_utils", core_dir / "database_utils.py")
            if spec and spec.loader:
                db_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(db_module)
                CandleCacheDatabase = db_module.CandleCacheDatabase
        except Exception as e:
            logger.warning(f"Failed to load database_utils: {e}")

        # Load backtest_orchestrator
        try:
            spec = importlib.util.spec_from_file_location("backtest_orchestrator", core_dir / "backtest_orchestrator.py")
            if spec and spec.loader:
                bt_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(bt_module)
                BacktestOrchestrator = bt_module.BacktestOrchestrator
                logger.info("✅ BacktestOrchestrator loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load backtest_orchestrator: {e}")

        # Check what features are available
        if StatisticalTestingFramework and EnhancedMetricsAggregator:
            ENHANCED_FEATURES_AVAILABLE = True
            logger.info("✅ Enhanced features loaded via importlib fallback")
        else:
            logger.warning("❌ Enhanced features partially available")

    except Exception as fallback_error:
        logger.error(f"❌ Fallback import failed: {fallback_error}")

# Set page config
st.set_page_config(
    page_title="Enhanced Prompt Performance Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .insight-card {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 0.25rem solid #1f77b4;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 0.25rem solid #1f77b4;
    }
    .significance-high {
        color: #28a745;
        font-weight: bold;
    }
    .significance-medium {
        color: #ffc107;
        font-weight: bold;
    }
    .significance-low {
        color: #dc3545;
        font-weight: bold;
    }
    .confidence-interval {
        font-size: 0.9em;
        color: #6c757d;
        font-style: italic;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_enhanced_data(analysis_type="version"):
    """Load and enhance data with statistical analysis."""
    # Load basic data
    script_dir = Path(__file__).resolve().parent
    base_path = script_dir.parent

    if analysis_type == "hash":
        summary_file = base_path / "summary_prompt_hash.csv"
        trades_file = base_path / "trade_logs_prompt_hash.csv"
    else:
        summary_file = base_path / "summary.csv"
        trades_file = base_path / "trade_logs.csv"

    if not summary_file.exists() or not trades_file.exists():
        return None, None, None

    try:
        summary_df = pd.read_csv(summary_file)
        trades_df = pd.read_csv(trades_file)

        # Normalize symbols in dataframes
        if 'symbol' in summary_df.columns:
            summary_df['symbol'] = summary_df['symbol'].apply(normalize_symbol)
        if 'symbol' in trades_df.columns:
            trades_df['symbol'] = trades_df['symbol'].apply(normalize_symbol)

        # Convert timestamp strings to datetime
        if 'timestamp' in trades_df.columns:
            trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'], errors='coerce')

        # Generate enhanced analytics if available
        enhanced_data = None
        if ENHANCED_FEATURES_AVAILABLE and EnhancedMetricsAggregator is not None:
            try:
                aggregator = EnhancedMetricsAggregator()
                if aggregator is not None:
                    trade_records = trades_df.to_dict('records')
                    # Type cast to match expected signature - use cast function
                    from typing import cast, List, Dict, Any
                    trade_records_typed = cast(List[Dict[str, Any]], trade_records)
                    enhanced_data = aggregator.generate_enhanced_report(trade_records_typed, analysis_type)
            except Exception as e:
                logger.error(f"Error generating enhanced analytics: {e}")
                enhanced_data = None

        return summary_df, trades_df, enhanced_data

    except Exception as e:
        st.error(f"❌ Error loading data: {e}")
        return None, None, None

def aggregate_prompt_performance(summary_df):
    """Aggregate performance data by prompt hash for overall ranking."""
    if summary_df is None or summary_df.empty:
        return None

    # Group by prompt hash and calculate aggregated metrics
    prompt_col = 'prompt_hash' if 'prompt_hash' in summary_df.columns else 'prompt_version'

    aggregated = summary_df.groupby(prompt_col).agg({
        'win_rate': ['mean', 'count'],
        'total_trades': 'sum',
        'avg_rr': 'mean',
        'profit_factor': 'mean'
    }).round(4)

    # Flatten column names
    aggregated.columns = ['avg_win_rate', 'combination_count', 'total_trades', 'avg_rr', 'avg_profit_factor']
    aggregated = aggregated.reset_index().rename(columns={prompt_col: 'prompt_id'})

    # Calculate weighted win rate (weighted by total_trades)
    aggregated['weighted_win_rate'] = (
        aggregated['avg_win_rate'] * aggregated['total_trades']
    ) / aggregated['total_trades']

    # Sort by weighted win rate
    aggregated = aggregated.sort_values('weighted_win_rate', ascending=False).reset_index(drop=True)

    return aggregated

def find_best_performers_by_combination(summary_df):
    """Find best prompt for each symbol-timeframe combination."""
    if summary_df is None or summary_df.empty:
        return None

    # Group by symbol and timeframe, find best prompt for each combination
    combinations = []
    prompt_col = 'prompt_hash' if 'prompt_hash' in summary_df.columns else 'prompt_version'

    for (symbol, timeframe), group in summary_df.groupby(['symbol', 'timeframe']):
        if not group.empty:
            # Find the best performing prompt for this combination
            best_prompt = group.loc[group['win_rate'].idxmax()]

            combinations.append({
                'symbol': symbol,
                'timeframe': timeframe,
                'best_prompt': best_prompt[prompt_col],
                'win_rate': best_prompt['win_rate'],
                'total_trades': best_prompt['total_trades'],
                'avg_rr': best_prompt['avg_rr'],
                'profit_factor': best_prompt['profit_factor']
            })

    # Convert to DataFrame and sort
    result_df = pd.DataFrame(combinations)
    result_df = result_df.sort_values(['symbol', 'timeframe'])

    return result_df

def get_performance_tier(win_rate):
    """Get performance tier based on win rate."""
    if win_rate >= 0.7:
        return "🏆 Excellent", "#28a745"
    elif win_rate >= 0.6:
        return "✅ Good", "#17a2b8"
    elif win_rate >= 0.5:
        return "⚠️ Moderate", "#ffc107"
    else:
        return "❌ Poor", "#dc3545"

def create_executive_dashboard(summary_df, trades_df, enhanced_data):
    """Create executive-level dashboard with key insights."""

    st.markdown('<h1 class="main-header">🎯 Executive Dashboard</h1>', unsafe_allow_html=True)
    
    if summary_df is None or trades_df is None:
        st.error("No data available for executive dashboard.")
        return

    # Smart Insights Panel
    st.markdown("## 💡 Key Insights")
    
    if enhanced_data and 'insights' in enhanced_data:
        insights = enhanced_data['insights']
        
        for insight in insights[:3]:  # Top 3 insights
            with st.container():
                if insight['type'] == 'best_performer':
                    st.success(f"🏆 {insight['title']}: {insight['description']}")
                elif insight['type'] == 'sample_size_warning':
                    st.warning(f"⚠️ {insight['title']}: {insight['description']}")
                elif insight['type'] == 'performance_decline':
                    st.error(f"📉 {insight['title']}: {insight['description']}")
                elif insight['type'] == 'significant_differences':
                    # Make significant differences more prominent
                    st.markdown("""
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                               padding: 1rem; border-radius: 0.5rem; border-left: 0.25rem solid #ff6b6b;
                               color: white; margin-bottom: 1rem;">
                        <h4 style="margin: 0; color: white;">🎯 {}</h4>
                        <p style="margin: 0.5rem 0 0 0; color: #f8f9fa;">{}</p>
                    </div>
                    """.format(insight['title'], insight['description']), unsafe_allow_html=True)
                else:
                    st.info(f"💡 {insight['title']}: {insight['description']}")

                with st.expander("📋 Recommended Action"):
                    st.write(insight['actionable'])
    else:
        # Fallback basic insights
        total_trades = len(trades_df)
        win_rate = len(trades_df[trades_df['outcome'] == 'win']) / len(trades_df[trades_df['outcome'].isin(['win', 'loss'])])
        
        if win_rate > 0.6:
            st.success(f"🏆 Strong Performance: Overall win rate of {win_rate:.1%} across {total_trades:,} trades")
        elif win_rate > 0.5:
            st.info(f"📊 Moderate Performance: Win rate of {win_rate:.1%} with room for improvement")
        else:
            st.warning(f"⚠️ Performance Concern: Win rate of {win_rate:.1%} needs attention")

    # Performance Leaderboards - Two Versions
    st.markdown("## 📊 Performance Leaderboards")

    # Create tabs for different leaderboard versions
    leaderboard_tab1, leaderboard_tab2, leaderboard_tab3, leaderboard_tab4 = st.tabs([
        "🎯 Win Rate Focus (Original)",
        "📏 Sample-Weighted Performance",
        "🏆 Best Overall Prompts",
        "📊 Top Performers by Symbol/Timeframe"
    ])

    with leaderboard_tab1:
        st.markdown("### Traditional Win Rate Ranking")
        st.markdown("*Ranked by win rate with confidence intervals*")

        if enhanced_data and 'confidence_intervals' in enhanced_data:
            # Create leaderboard with confidence intervals
            leaderboard_data = []
            basic_metrics = enhanced_data.get('basic_metrics', {})
            ci_data = enhanced_data.get('confidence_intervals', {})

            for prompt_id, metrics in basic_metrics.items():
                ci = ci_data.get(prompt_id, {})
                win_rate_ci = ci.get('win_rate', {})

                leaderboard_data.append({
                    'prompt_id': prompt_id,
                    'win_rate': metrics['win_rate'],
                    'ci_lower': win_rate_ci.get('ci_lower', metrics['win_rate']),
                    'ci_upper': win_rate_ci.get('ci_upper', metrics['win_rate']),
                    'sample_size': metrics['decisive_trades'],
                    'reliable': metrics['decisive_trades'] >= 30
                })

            if leaderboard_data:
                df = pd.DataFrame(leaderboard_data)
                df = df.sort_values('win_rate', ascending=False)

                # Create confidence interval chart
                fig = go.Figure()

                colors = ['green' if reliable else 'orange' for reliable in df['reliable']]

                # Add error bars for confidence intervals
                fig.add_trace(go.Scatter(
                    x=df['prompt_id'],
                    y=df['win_rate'],
                    error_y=dict(
                        type='data',
                        symmetric=False,
                        array=df['ci_upper'] - df['win_rate'],
                        arrayminus=df['win_rate'] - df['ci_lower'],
                        visible=True
                    ),
                    mode='markers',
                    marker=dict(
                        size=10,
                        color=colors,
                        symbol='diamond'
                    ),
                    name='Win Rate (95% CI)',
                    hovertemplate='<b>%{x}</b><br>Win Rate: %{y:.1%}<br>Sample Size: %{customdata}<extra></extra>',
                    customdata=df['sample_size']
                ))

                fig.update_layout(
                    title='Prompt Performance with 95% Confidence Intervals',
                    xaxis_title='Prompt ID',
                    yaxis_title='Win Rate',
                    yaxis_tickformat='.1%',
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

        else:
            # Fallback to basic leaderboard
            if not summary_df.empty:
                fig = px.bar(
                    summary_df.head(10),
                    x='prompt_hash' if 'prompt_hash' in summary_df.columns else 'prompt_version',
                    y='win_rate',
                    title='Top 10 Performing Prompts',
                    color='win_rate',
                    color_continuous_scale='RdYlGn'
                )
                fig.update_layout(xaxis_tickangle=-45)
                st.plotly_chart(fig, use_container_width=True)

    with leaderboard_tab2:
        st.markdown("### Sample-Weighted Performance Ranking")
        st.markdown("*Relative ranking that balances win rate with sample size reliability*")

        if enhanced_data and 'basic_metrics' in enhanced_data:
            # Create sample-weighted leaderboard with relative scoring
            weighted_data = []
            basic_metrics = enhanced_data.get('basic_metrics', {})

            # Calculate global statistics for relative scoring
            all_win_rates = [metrics['win_rate'] for metrics in basic_metrics.values()]
            all_sample_sizes = [metrics['decisive_trades'] for metrics in basic_metrics.values()]

            if all_win_rates and all_sample_sizes:
                avg_win_rate = np.mean(all_win_rates)
                std_win_rate = np.std(all_win_rates) if len(all_win_rates) > 1 else 0.01
                avg_sample_size = np.mean(all_sample_sizes)
                max_sample_size = max(all_sample_sizes)

                for prompt_id, metrics in basic_metrics.items():
                    win_rate = metrics['win_rate']
                    sample_size = metrics['decisive_trades']

                    # Relative win rate score (standardized)
                    if std_win_rate > 0:
                        win_rate_z_score = (win_rate - avg_win_rate) / std_win_rate
                    else:
                        win_rate_z_score = 0

                    # Sample size reliability factor (0-1 scale)
                    if max_sample_size > 0:
                        sample_reliability = min(1.0, sample_size / max_sample_size)
                    else:
                        sample_reliability = 0

                    # Combined relative score
                    # Weight: 70% win rate performance, 30% sample reliability
                    relative_score = (0.7 * win_rate_z_score) + (0.3 * (sample_reliability - 0.5) * 2)

                    # Determine reliability tier
                    if sample_size >= 100:
                        reliability = "Excellent"
                        color = "green"
                    elif sample_size >= 50:
                        reliability = "Good"
                        color = "blue"
                    elif sample_size >= 20:
                        reliability = "Moderate"
                        color = "orange"
                    else:
                        reliability = "Limited"
                        color = "red"

                    weighted_data.append({
                        'prompt_id': prompt_id,
                        'win_rate': win_rate,
                        'sample_size': sample_size,
                        'relative_score': relative_score,
                        'win_rate_z': win_rate_z_score,
                        'sample_reliability': sample_reliability,
                        'reliability': reliability,
                        'color': color
                    })

                if weighted_data:
                    df_weighted = pd.DataFrame(weighted_data)
                    df_weighted = df_weighted.sort_values('relative_score', ascending=False)

                    # Create sample-weighted performance chart
                    fig = go.Figure()

                    # Add scatter plot with size based on sample size
                    fig.add_trace(go.Scatter(
                        x=df_weighted['prompt_id'],
                        y=df_weighted['relative_score'],
                        mode='markers',
                        marker=dict(
                            size=df_weighted['sample_size'].apply(lambda x: max(8, min(25, 8 + (x/10)))),  # Scale size
                            color=df_weighted['color'],
                            symbol='circle',
                            sizemode='diameter'
                        ),
                        name='Relative Performance Score',
                        hovertemplate='<b>%{x}</b><br>Win Rate: %{customdata:.1%}<br>Sample Size: %{marker.size}<br>Relative Score: %{y:.3f}<br>Reliability: %{text}<extra></extra>',
                        customdata=df_weighted['win_rate'],
                        text=df_weighted['reliability']
                    ))

                    fig.update_layout(
                        title='Sample-Weighted Relative Performance Ranking',
                        xaxis_title='Prompt ID',
                        yaxis_title='Relative Performance Score',
                        yaxis_tickformat='.3f',
                        height=400,
                        showlegend=False
                    )

                    st.plotly_chart(fig, use_container_width=True)

                    # Add explanation
                    st.markdown("""
                    **📖 How the Relative Sample-Weighted Score Works:**
                    - **Win Rate Component (70%)**: Standardized z-score relative to other prompts
                    - **Sample Reliability (30%)**: Based on sample size compared to the largest sample
                    - **Combined Score**: Balances performance with statistical reliability
                    - **Bubble size**: Represents absolute sample size magnitude
                    - **Color coding**: Reliability tiers (Green=Excellent ≥100, Blue=Good ≥50, Orange=Moderate ≥20, Red=Limited <20)
                    """)

                    # Show top performers table
                    st.markdown("### 🏆 Top Relative Performers")
                    top_performers = df_weighted.head(10)[['prompt_id', 'win_rate', 'sample_size', 'relative_score', 'reliability']]
                    top_performers['win_rate'] = top_performers['win_rate'].apply(lambda x: f"{x:.1%}")
                    top_performers['relative_score'] = top_performers['relative_score'].apply(lambda x: f"{x:.3f}")
                    top_performers.columns = ['Prompt ID', 'Win Rate', 'Sample Size', 'Relative Score', 'Reliability']

                    st.dataframe(top_performers, use_container_width=True)

        else:
            st.info("No enhanced data available for sample-weighted analysis.")

    with leaderboard_tab3:
        st.markdown("### 🏆 Best Overall Prompts")
        st.markdown("*Aggregated performance across all symbol-timeframe combinations*")

        # Aggregate prompt performance data
        aggregated_df = aggregate_prompt_performance(summary_df)

        if aggregated_df is not None and not aggregated_df.empty:
            # Quick stats cards
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                best_prompt = aggregated_df.iloc[0]
                tier, color = get_performance_tier(best_prompt['weighted_win_rate'])
                st.metric("🏆 Best Prompt", f"{tier}", f"{best_prompt['weighted_win_rate']:.1%}")

            with col2:
                avg_win_rate = aggregated_df['weighted_win_rate'].mean()
                st.metric("📊 Average Win Rate", f"{avg_win_rate:.1%}")

            with col3:
                total_prompts = len(aggregated_df)
                st.metric("📋 Total Prompts", f"{total_prompts:,}")

            with col4:
                excellent_count = sum(1 for _, row in aggregated_df.iterrows() if row['weighted_win_rate'] >= 0.7)
                st.metric("⭐ Excellent Prompts", f"{excellent_count}")

            # Performance distribution chart
            st.markdown("#### 📈 Performance Distribution")
            fig_dist = px.histogram(
                aggregated_df,
                x='weighted_win_rate',
                title='Distribution of Prompt Performance',
                labels={'weighted_win_rate': 'Weighted Win Rate'},
                color_discrete_sequence=['#1f77b4'],
                nbins=20
            )
            fig_dist.update_layout(height=300)
            st.plotly_chart(fig_dist, use_container_width=True)

            # Top 10 prompts table
            st.markdown("#### 🏆 Top 10 Best Performing Prompts")
            top_10 = aggregated_df.head(10).copy()

            # Add performance tier column
            top_10['tier'] = top_10['weighted_win_rate'].apply(lambda x: get_performance_tier(x)[0])

            # Format for display
            display_df = top_10[['prompt_id', 'weighted_win_rate', 'total_trades', 'combination_count', 'tier']].copy()
            display_df['weighted_win_rate'] = display_df['weighted_win_rate'].apply(lambda x: f"{x:.1%}")
            display_df.columns = ['Prompt ID', 'Weighted Win Rate', 'Total Trades', 'Combinations', 'Performance Tier']

            st.dataframe(display_df, use_container_width=True)

            # Performance tier breakdown
            st.markdown("#### 🎯 Performance Tier Breakdown")
            tier_counts = aggregated_df['weighted_win_rate'].apply(lambda x: get_performance_tier(x)[0]).value_counts()

            tier_df = pd.DataFrame({
                'Tier': tier_counts.index,
                'Count': tier_counts.values,
                'Percentage': (tier_counts.values / len(aggregated_df) * 100).round(1)
            })

            st.dataframe(tier_df, use_container_width=True)

        else:
            st.info("No data available for overall prompt analysis.")

    with leaderboard_tab4:
        st.markdown("### 📊 Top Performers by Symbol/Timeframe")
        st.markdown("*Best prompt for each symbol-timeframe combination*")

        # Find best performers by combination
        best_performers_df = find_best_performers_by_combination(summary_df)

        if best_performers_df is not None and not best_performers_df.empty:
            # Overview metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                total_combinations = len(best_performers_df)
                st.metric("🎯 Total Combinations", f"{total_combinations:,}")

            with col2:
                avg_win_rate = best_performers_df['win_rate'].mean()
                st.metric("📊 Average Win Rate", f"{avg_win_rate:.1%}")

            with col3:
                unique_symbols = best_performers_df['symbol'].nunique()
                st.metric("🪙 Unique Symbols", f"{unique_symbols}")

            with col4:
                unique_timeframes = best_performers_df['timeframe'].nunique()
                st.metric("⏰ Unique Timeframes", f"{unique_timeframes}")

            # Heatmap visualization
            st.markdown("#### 🔥 Performance Heatmap")
            heatmap_data = best_performers_df.pivot(
                index='symbol',
                columns='timeframe',
                values='win_rate'
            )

            if not heatmap_data.empty:
                fig_heatmap = px.imshow(
                    heatmap_data,
                    title='Win Rate Heatmap by Symbol and Timeframe',
                    labels=dict(x="Timeframe", y="Symbol", color="Win Rate"),
                    color_continuous_scale='RdYlGn',
                    aspect="auto"
                )
                fig_heatmap.update_layout(height=400)
                st.plotly_chart(fig_heatmap, use_container_width=True)

            # Detailed table
            st.markdown("#### 📋 Best Prompt for Each Combination")
            display_df = best_performers_df.copy()

            # Add performance tier
            display_df['tier'] = display_df['win_rate'].apply(lambda x: get_performance_tier(x)[0])

            # Format for display
            display_df['win_rate'] = display_df['win_rate'].apply(lambda x: f"{x:.1%}")
            display_df['avg_rr'] = display_df['avg_rr'].apply(lambda x: f"{x:.2f}")
            display_df['profit_factor'] = display_df['profit_factor'].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "∞")

            display_df = display_df[['symbol', 'timeframe', 'best_prompt', 'win_rate', 'total_trades', 'avg_rr', 'profit_factor', 'tier']]
            display_df.columns = ['Symbol', 'Timeframe', 'Best Prompt', 'Win Rate', 'Total Trades', 'Avg R:R', 'Profit Factor', 'Performance Tier']

            st.dataframe(display_df, use_container_width=True)

            # Filter options
            st.markdown("#### 🔍 Filter by Symbol or Timeframe")

            col1, col2 = st.columns(2)

            with col1:
                selected_symbol = st.selectbox(
                    "Filter by Symbol:",
                    options=['All'] + sorted(best_performers_df['symbol'].unique().tolist()),
                    key="symbol_filter"
                )

            with col2:
                selected_timeframe = st.selectbox(
                    "Filter by Timeframe:",
                    options=['All'] + sorted(best_performers_df['timeframe'].unique().tolist()),
                    key="timeframe_filter"
                )

            # Apply filters
            filtered_df = best_performers_df.copy()

            if selected_symbol != 'All':
                filtered_df = filtered_df[filtered_df['symbol'] == selected_symbol]

            if selected_timeframe != 'All':
                filtered_df = filtered_df[filtered_df['timeframe'] == selected_timeframe]

            if len(filtered_df) != len(best_performers_df):
                st.markdown(f"#### 📊 Filtered Results ({len(filtered_df)} combinations)")

                if not filtered_df.empty:
                    # Format filtered results
                    filtered_display = filtered_df.copy()
                    filtered_display['win_rate'] = filtered_display['win_rate'].apply(lambda x: f"{x:.1%}")
                    filtered_display['avg_rr'] = filtered_display['avg_rr'].apply(lambda x: f"{x:.2f}")
                    filtered_display['profit_factor'] = filtered_display['profit_factor'].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "∞")

                    filtered_display = filtered_display[['symbol', 'timeframe', 'best_prompt', 'win_rate', 'total_trades', 'avg_rr', 'profit_factor']]
                    filtered_display.columns = ['Symbol', 'Timeframe', 'Best Prompt', 'Win Rate', 'Total Trades', 'Avg R:R', 'Profit Factor']

                    st.dataframe(filtered_display, use_container_width=True)

                    # Summary stats for filtered data
                    if len(filtered_df) > 1:
                        avg_filtered_win_rate = filtered_df['win_rate'].mean()
                        best_filtered = filtered_df.loc[filtered_df['win_rate'].idxmax()]

                        st.success(f"🏆 Best in filtered set: {best_filtered['symbol']} {best_filtered['timeframe']} "
                                  f"with {best_filtered['win_rate']:.1%} win rate")
                        st.info(f"📊 Average win rate in filtered set: {avg_filtered_win_rate:.1%}")

        else:
            st.info("No data available for symbol-timeframe analysis.")

    # Alerts & Notifications section
    st.markdown("## 🚨 Alerts & Notifications")

    # Check for insufficient sample sizes
    insufficient_sample_alerts = []
    if enhanced_data and 'basic_metrics' in enhanced_data:
        basic_metrics = enhanced_data.get('basic_metrics', {})
        for prompt_id, metrics in basic_metrics.items():
            sample_size = metrics.get('decisive_trades', 0)
            if sample_size < 20:  # Threshold for insufficient sample size
                insufficient_sample_alerts.append({
                    'prompt_id': prompt_id,
                    'sample_size': sample_size,
                    'win_rate': metrics.get('win_rate', 0)
                })

    if enhanced_data and 'insights' in enhanced_data:
        alerts = [insight for insight in enhanced_data['insights'] if insight['type'] in ['sample_size_warning', 'performance_decline']]

        # Add insufficient sample size alerts
        if insufficient_sample_alerts:
            st.warning("📏 **Insufficient Sample Sizes Detected**")
            st.markdown("The following prompts have fewer than 20 decisive trades and may have unreliable performance estimates:")

            # Create a table of insufficient sample prompts
            insufficient_df = pd.DataFrame(insufficient_sample_alerts)
            insufficient_df = insufficient_df.sort_values('sample_size')
            insufficient_df['win_rate'] = insufficient_df['win_rate'].apply(lambda x: f"{x:.1%}")
            insufficient_df.columns = ['Prompt ID', 'Sample Size', 'Win Rate']

            st.dataframe(insufficient_df, use_container_width=True)

            st.markdown("**💡 Recommendation:** Run more backtests or collect more trading data for these prompts to improve statistical reliability.")

        if alerts:
            for alert in alerts[:5]:  # Show up to 5 alerts
                if alert['type'] == 'sample_size_warning':
                    st.warning(f"📏 {alert['title']}")
                elif alert['type'] == 'performance_decline':
                    st.error(f"📉 {alert['title']}")

                st.caption(alert['description'])
        else:
            st.success("✅ No critical alerts")
    else:
        st.info("🔄 Enhanced alerts require statistical analysis")

    # Quick Action Buttons
    st.markdown("## 🎯 Quick Actions")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔍 Statistical Analysis", key="goto_stats"):
            st.session_state.dashboard_mode = "Statistical Analysis"
            st.rerun()
    
    with col2:
        if st.button("🧪 A/B Testing Lab", key="goto_ab"):
            st.session_state.dashboard_mode = "A/B Testing Lab"
            st.rerun()
    
    with col3:
        if st.button("📈 Market Analysis", key="goto_market"):
            st.session_state.dashboard_mode = "Market Intelligence"
            st.rerun()
    
    with col4:
        if st.button("🎯 Optimization", key="goto_opt"):
            st.session_state.dashboard_mode = "Optimization"
            st.rerun()

def create_statistical_analysis_tab(enhanced_data):
    """Advanced statistical analysis visualization."""
    
    st.markdown("# 📊 Statistical Analysis")
    
    if not ENHANCED_FEATURES_AVAILABLE:
        st.error("❌ Enhanced statistical features not available. Please check module imports.")
        return
    
    if not enhanced_data:
        st.warning("⚠️ No enhanced data available. Run backtest with enhanced analytics first.")
        return

    tab1, tab2, tab3, tab4 = st.tabs([
        "📊 Confidence Intervals", 
        "🔬 Significance Testing", 
        "📏 Sample Size Analysis", 
        "📈 Effect Sizes"
    ])
    
    with tab1:
        st.markdown("### 🎯 95% Confidence Intervals for Win Rates")
        
        confidence_intervals = enhanced_data.get('confidence_intervals', {})
        
        if confidence_intervals:
            # Create forest plot
            plot_data = []
            for prompt_id, ci_data in confidence_intervals.items():
                win_rate_ci = ci_data.get('win_rate', {})
                plot_data.append({
                    'prompt_id': prompt_id,
                    'point_estimate': win_rate_ci.get('point_estimate', 0),
                    'ci_lower': win_rate_ci.get('ci_lower', 0),
                    'ci_upper': win_rate_ci.get('ci_upper', 0),
                    'sample_size': ci_data.get('sample_size', 0)
                })
            
            if plot_data:
                df = pd.DataFrame(plot_data)
                df = df.sort_values('point_estimate', ascending=True)
                
                fig = go.Figure()
                
                # Add confidence interval lines
                for i, row in df.iterrows():
                    fig.add_trace(go.Scatter(
                        x=[row['ci_lower'], row['ci_upper']],
                        y=[i, i],
                        mode='lines',
                        line=dict(width=3, color='blue'),
                        showlegend=False,
                        hoverinfo='skip'
                    ))
                    
                    # Add point estimate
                    fig.add_trace(go.Scatter(
                        x=[row['point_estimate']],
                        y=[i],
                        mode='markers',
                        marker=dict(size=8, color='red', symbol='diamond'),
                        showlegend=False,
                        hovertemplate=f"<b>{row['prompt_id']}</b><br>Win Rate: {row['point_estimate']:.1%}<br>95% CI: [{row['ci_lower']:.1%}, {row['ci_upper']:.1%}]<br>Sample Size: {row['sample_size']}<extra></extra>"
                    ))
                
                fig.update_layout(
                    title="95% Confidence Intervals for Win Rates (Forest Plot)",
                    xaxis_title="Win Rate",
                    yaxis=dict(
                        tickmode='array',
                        tickvals=list(range(len(df))),
                        ticktext=df['prompt_id'].tolist()
                    ),
                    height=max(400, len(df) * 40),
                    xaxis_tickformat='.1%'
                )
                
                st.plotly_chart(fig, use_container_width=True)
                
                # Explanation
                st.markdown("""
                **📖 How to Read This Chart:**
                - **Red Diamonds**: Point estimates (actual win rates)
                - **Blue Lines**: 95% confidence intervals
                - **Overlapping intervals**: No significant difference
                - **Longer lines**: Less certain estimates (smaller sample sizes)
                """)
        else:
            st.info("No confidence interval data available.")
    
    with tab2:
        st.markdown("### 🧪 Statistical Significance Testing")
        
        statistical_comparisons = enhanced_data.get('statistical_comparisons', {})
        pairwise_comparisons = statistical_comparisons.get('pairwise_comparisons', {})
        
        if pairwise_comparisons:
            # Extract prompt IDs from comparison keys (format: "promptA_vs_promptB")
            all_prompts = set()
            for comp_key in pairwise_comparisons.keys():
                prompt_a, prompt_b = comp_key.split('_vs_')
                all_prompts.add(prompt_a)
                all_prompts.add(prompt_b)
            
            prompts = sorted(list(all_prompts))
            
            n_prompts = len(prompts)
            significance_matrix = np.ones((n_prompts, n_prompts))
            p_value_matrix = np.ones((n_prompts, n_prompts))
            
            for comp_key, comp_result in pairwise_comparisons.items():
                prompt_a, prompt_b = comp_key.split('_vs_')
                i = prompts.index(prompt_a)
                j = prompts.index(prompt_b)
                
                significance_matrix[i][j] = 0 if comp_result.significant else 1
                significance_matrix[j][i] = significance_matrix[i][j]
                p_value_matrix[i][j] = comp_result.p_value
                p_value_matrix[j][i] = comp_result.p_value
            
            # Create heatmap
            fig = px.imshow(
                significance_matrix,
                x=prompts,
                y=prompts,
                color_continuous_scale=['red', 'green'],  # Red = significant difference
                title="Statistical Significance Matrix (Red = Significant Difference)",
                text_auto=True
            )
            
            # Add p-values as hover text
            fig.update_traces(
                hovertemplate="<b>%{y} vs %{x}</b><br>P-value: %{customdata:.4f}<extra></extra>",
                customdata=p_value_matrix
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Significance table
            st.markdown("### 📋 Pairwise Comparison Results")
            
            comparison_data = []
            for comp_key, comp_result in pairwise_comparisons.items():
                comparison_data.append({
                    'Comparison': comp_key.replace('_vs_', ' vs '),
                    'P-Value': f"{comp_result.p_value:.4f}",
                    'Effect Size': f"{comp_result.effect_size:.3f}",
                    'Significant': "✅ Yes" if comp_result.significant else "❌ No",
                    'Interpretation': comp_result.interpretation[:100] + "..." if len(comp_result.interpretation) > 100 else comp_result.interpretation
                })
            
            if comparison_data:
                comparison_df = pd.DataFrame(comparison_data)
                st.dataframe(comparison_df, use_container_width=True)
        else:
            st.info("No statistical comparison data available.")
    
    with tab3:
        st.markdown("### 📏 Sample Size Adequacy Analysis")
        
        sample_analysis = enhanced_data.get('sample_analysis', {})
        
        if sample_analysis:
            adequacy_data = []
            for prompt_id, analysis in sample_analysis.items():
                adequacy_data.append({
                    'Prompt ID': prompt_id,
                    'Current Sample': analysis['current_sample_size'],
                    'Adequacy Status': analysis['adequacy_status'].title(),
                    'Confidence Level': analysis['confidence_level'].title(),
                    'Required for 5% Effect': analysis['required_samples'].get('5%_effect', 'N/A')
                })
            
            if adequacy_data:
                adequacy_df = pd.DataFrame(adequacy_data)
                
                # Color coding for adequacy status
                def color_adequacy(val):
                    if val == 'Excellent':
                        return 'background-color: #d4edda; color: #155724'
                    elif val == 'Good':
                        return 'background-color: #d1ecf1; color: #0c5460'
                    elif val == 'Moderate':
                        return 'background-color: #fff3cd; color: #856404'
                    elif val == 'Minimal':
                        return 'background-color: #f8d7da; color: #721c24'
                    else:
                        return 'background-color: #f8d7da; color: #721c24'
                
                styled_df = adequacy_df.style.map(color_adequacy, subset=['Adequacy Status'])
                st.dataframe(styled_df, use_container_width=True)
                
                # Sample size visualization
                fig = px.scatter(
                    adequacy_df,
                    x='Current Sample',
                    y='Prompt ID',
                    color='Adequacy Status',
                    size='Current Sample',
                    title='Sample Size Adequacy by Prompt',
                    color_discrete_map={
                        'Excellent': 'green',
                        'Good': 'blue',
                        'Moderate': 'orange',
                        'Minimal': 'red',
                        'Insufficient': 'darkred'
                    }
                )
                fig.update_layout(height=400)
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No sample size analysis data available.")
    
    with tab4:
        st.markdown("### 📈 Effect Size Analysis")
        
        statistical_comparisons = enhanced_data.get('statistical_comparisons', {})
        pairwise_comparisons = statistical_comparisons.get('pairwise_comparisons', {})
        
        if pairwise_comparisons:
            effect_data = []
            for comp_key, comp_result in pairwise_comparisons.items():
                magnitude = "Large" if abs(comp_result.effect_size) > 0.5 else \
                           "Medium" if abs(comp_result.effect_size) > 0.2 else "Small"
                
                effect_data.append({
                    'Comparison': comp_key.replace('_vs_', ' vs '),
                    'Effect Size': comp_result.effect_size,
                    'Magnitude': magnitude,
                    'Practical Significance': "Yes" if abs(comp_result.effect_size) > 0.1 else "No"
                })
            
            if effect_data:
                effect_df = pd.DataFrame(effect_data)
                
                # Effect size visualization
                fig = px.bar(
                    effect_df,
                    x='Comparison',
                    y='Effect Size',
                    color='Magnitude',
                    title='Effect Sizes for Pairwise Comparisons',
                    color_discrete_map={'Large': 'red', 'Medium': 'orange', 'Small': 'green'}
                )
                fig.update_layout(xaxis_tickangle=-45)
                st.plotly_chart(fig, use_container_width=True)
                
                st.dataframe(effect_df, use_container_width=True)
        else:
            st.info("No effect size data available.")

def create_prompt_viewer():
    """Create prompt hash to text viewer."""
    st.markdown("# 📝 Prompt Hash Viewer")
    st.markdown("*Explore the actual prompt text behind each hash*")

    if CandleCacheDatabase is None:
        st.error("❌ Database not available. Cannot load prompt mappings.")
        st.info("💡 Ensure database_utils.py is properly configured and the database contains prompt mappings.")
        return

    try:
        db = CandleCacheDatabase()
        all_mappings = db.get_all_prompt_mappings()

        if not all_mappings:
            st.warning("No prompt mappings found in database.")
            st.info("💡 Prompt mappings are created when running backtest with prompt hash analysis.")
            return

        # Create summary of all prompts
        prompt_data = []
        for prompt_hash, prompt_text in all_mappings.items():
            metadata = db.get_prompt_metadata(prompt_hash)
            prompt_data.append({
                'hash': prompt_hash,
                'length': len(prompt_text),
                'words': len(prompt_text.split()),
                'timeframe': metadata.get('timeframe', 'N/A') if metadata else 'N/A',
                'symbol': metadata.get('symbol', 'N/A') if metadata else 'N/A',
                'preview': prompt_text[:150] + '...' if len(prompt_text) > 150 else prompt_text
            })

        df = pd.DataFrame(prompt_data)

        # Overview metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Prompts", len(df))
        with col2:
            st.metric("Avg Length", f"{df['length'].mean():.0f} chars")
        with col3:
            st.metric("Avg Words", f"{df['words'].mean():.0f}")
        with col4:
            st.metric("Unique Timeframes", df['timeframe'].nunique())

        # Load performance data for context
        summary_df, _, _ = load_enhanced_data("hash")
        performance_map = {}
        if summary_df is not None:
            for _, row in summary_df.iterrows():
                hash_key = row['prompt_hash']
                if hash_key not in performance_map:
                    performance_map[hash_key] = []
                performance_map[hash_key].append({
                    'symbol': row['symbol'],
                    'timeframe': row['timeframe'],
                    'win_rate': row['win_rate'],
                    'total_trades': row['total_trades']
                })

        # Enhanced hash selector with performance info
        def format_hash_option(hash_val):
            perf_info = performance_map.get(hash_val, [])
            if perf_info:
                avg_win_rate = np.mean([p['win_rate'] for p in perf_info])
                total_trades = sum([p['total_trades'] for p in perf_info])
                return f"{hash_val} | Win Rate: {avg_win_rate:.1%} | Trades: {total_trades}"
            else:
                length = df[df['hash']==hash_val]['length'].iloc[0]
                return f"{hash_val} | Length: {length} chars"

        selected_hash = st.selectbox(
            "Select Prompt Hash to View:",
            options=df['hash'].tolist(),
            format_func=format_hash_option
        )

        if selected_hash:
            # Display prompt details
            full_prompt = all_mappings[selected_hash]
            metadata = db.get_prompt_metadata(selected_hash)
            prompt_info = df[df['hash'] == selected_hash].iloc[0]

            # Performance data for this hash
            hash_performance = performance_map.get(selected_hash, [])

            # Layout: Prompt text + metadata + performance
            col1, col2 = st.columns([2, 1])

            with col1:
                st.markdown("### 📄 Full Prompt Text")
                st.text_area(
                    "Prompt Content:",
                    value=full_prompt,
                    height=400,
                    key=f"prompt_view_{selected_hash}",
                    help="This is the actual prompt text that generated this hash"
                )

                # Copy prompt section
                st.markdown("### 📋 Copy Prompt")
                st.code(full_prompt, language=None)

            with col2:
                st.markdown("### ℹ️ Prompt Information")

                # Basic info
                st.markdown(f"**Hash:** `{selected_hash}`")
                st.markdown(f"**Length:** {prompt_info['length']:,} characters")
                st.markdown(f"**Word Count:** {prompt_info['words']:,} words")

                if metadata:
                    st.markdown(f"**Timeframe:** {metadata.get('timeframe', 'N/A')}")
                    st.markdown(f"**Symbol:** {metadata.get('symbol', 'N/A')}")

                # Performance summary
                if hash_performance:
                    st.markdown("### 📊 Performance Summary")

                    avg_win_rate = np.mean([p['win_rate'] for p in hash_performance])
                    total_combinations = len(hash_performance)
                    total_trades = sum([p['total_trades'] for p in hash_performance])

                    st.metric("Average Win Rate", f"{avg_win_rate:.1%}")
                    st.metric("Total Trades", f"{total_trades:,}")
                    st.metric("Combinations", total_combinations)

                    # Performance by symbol/timeframe
                    st.markdown("### 📈 Detailed Performance")
                    perf_df = pd.DataFrame(hash_performance)
                    perf_df['win_rate'] = perf_df['win_rate'].apply(lambda x: f"{x:.1%}")
                    st.dataframe(perf_df, use_container_width=True)

                    # Best performing combination
                    best_combo = max(hash_performance, key=lambda x: x['win_rate'])
                    st.success(f"🏆 Best: {best_combo['symbol']} {best_combo['timeframe']} "
                              f"({best_combo['win_rate']:.1%} win rate)")
                else:
                    st.info("No performance data available for this prompt hash.")

        # Prompt comparison table
        st.markdown("### 📋 All Prompts Overview")

        # Add performance data to overview
        overview_df = df.copy()

        def get_avg_win_rate(hash_val):
            perf_data = performance_map.get(hash_val, [])
            if perf_data:
                win_rates = [p['win_rate'] for p in perf_data]
                return f"{np.mean(win_rates):.1%}"
            return "0.0%"

        def get_total_trades(hash_val):
            perf_data = performance_map.get(hash_val, [])
            return sum([p['total_trades'] for p in perf_data])

        overview_df['avg_win_rate'] = overview_df['hash'].map(get_avg_win_rate)
        overview_df['total_trades'] = overview_df['hash'].map(get_total_trades)

        # Reorder columns for better display
        display_cols = ['hash', 'avg_win_rate', 'total_trades', 'length', 'words', 'timeframe', 'symbol', 'preview']
        overview_df = overview_df[display_cols]
        overview_df.columns = ['Hash', 'Avg Win Rate', 'Total Trades', 'Length', 'Words', 'Timeframe', 'Symbol', 'Preview']

        st.dataframe(overview_df, use_container_width=True)

    except Exception as e:
        st.error(f"Error loading prompt data: {e}")
        st.info("Make sure the database is properly set up and contains prompt mappings.")

def create_backtest_runner():
    """Create interactive backtest runner interface."""

    # Declare global variable at the beginning of the function
    global BacktestOrchestrator

    st.markdown("# 🚀 Backtest Runner")
    st.markdown("*Run prompt performance backtests directly from the dashboard*")

    # Debug import status
    with st.expander("🔧 Debug Information"):
        st.write("**Current Working Directory:**", os.getcwd())
        st.write("**Python Path:**")
        for i, path in enumerate(sys.path):
            st.write(f"{i}: {path}")

        st.write("**BacktestOrchestrator Status:**", "Available" if BacktestOrchestrator else "Not Available")

        # Try to import directly here
        if BacktestOrchestrator is None:
            st.write("**Attempting direct import...**")
            try:
                from prompt_performance.core.backtest_orchestrator import BacktestOrchestrator as DirectImport
                st.success("✅ Direct import successful!")
                BacktestOrchestrator = DirectImport
            except Exception as e:
                st.error(f"❌ Direct import failed: {e}")

    if BacktestOrchestrator is None:
        st.error("❌ BacktestOrchestrator not available. Please check module imports.")
        st.info("💡 Ensure backtest_orchestrator.py is properly configured.")
        st.info("💡 Try running: `cd /home/<USER>/projects/^^Python/Analyse_Chart_Screenshot && python -c \"from prompt_performance.core.backtest_orchestrator import BacktestOrchestrator; print('Import successful')\"`")
        return

    # Initialize session state for backtest progress
    if 'backtest_running' not in st.session_state:
        st.session_state.backtest_running = False
    if 'backtest_results' not in st.session_state:
        st.session_state.backtest_results = None
    if 'backtest_progress' not in st.session_state:
        st.session_state.backtest_progress = 0
    if 'backtest_status' not in st.session_state:
        st.session_state.backtest_status = ""

    # Configuration section
    st.markdown("## ⚙️ Backtest Configuration")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 📊 Analysis Parameters")
        limit = st.number_input(
            "Record Limit (optional)",
            min_value=1,
            value=None,
            help="Limit the number of analysis records to process. Leave empty for all records."
        )

        use_testnet = st.checkbox(
            "Use Testnet",
            value=False,
            help="Use Bybit testnet instead of mainnet for backtesting"
        )

    with col2:
        st.markdown("### 🎯 Analysis Type")
        analysis_type = st.radio(
            "Select Analysis Type",
            ["Prompt Hash Analysis", "Version Analysis"],
            index=0,
            help="Choose between prompt hash analysis (recommended) or version analysis"
        )

        verbose_logging = st.checkbox(
            "Verbose Logging",
            value=False,
            help="Enable detailed logging output during backtest execution"
        )

    # Quick start info
    with st.expander("ℹ️ Quick Start Guide"):
        st.markdown("""
        **Getting Started:**
        1. **Choose Analysis Type**: Prompt Hash Analysis is recommended for detailed prompt-level insights
        2. **Set Record Limit**: Start with a small limit (e.g., 100) for faster testing
        3. **Configure Environment**: Use testnet for safe testing, mainnet for production data
        4. **Run Backtest**: Click the run button and monitor progress in real-time

        **Expected Results:**
        - **Prompt Hash Analysis**: Detailed performance by unique prompt content
        - **Version Analysis**: Performance grouped by prompt versions
        - **Output Files**: CSV files saved to the prompt_performance directory
        """)

    # Run/Cancel buttons
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        run_backtest = st.button(
            "🚀 Run Backtest",
            type="primary",
            disabled=st.session_state.backtest_running,
            use_container_width=True
        )

    with col2:
        cancel_backtest = st.button(
            "⏹️ Cancel",
            disabled=not st.session_state.backtest_running,
            use_container_width=True
        )

    with col3:
        if st.session_state.backtest_results:
            st.success("✅ Backtest completed successfully!")

    # Progress section
    if st.session_state.backtest_running or st.session_state.backtest_results:
        st.markdown("## 📈 Progress & Status")

        # Progress bar
        progress_bar = st.progress(st.session_state.backtest_progress)

        # Status messages
        if st.session_state.backtest_status:
            if "error" in st.session_state.backtest_status.lower():
                st.error(f"❌ {st.session_state.backtest_status}")
            elif "completed" in st.session_state.backtest_status.lower():
                st.success(f"✅ {st.session_state.backtest_status}")
            else:
                st.info(f"🔄 {st.session_state.backtest_status}")

        # Live log output
        if hasattr(st.session_state, 'backtest_logs') and st.session_state.backtest_logs:
            st.markdown("### 📋 Live Backtest Logs")

            # Create a scrollable container for logs
            log_container = st.container()
            with log_container:
                # Display logs in reverse order (most recent first)
                for log_entry in reversed(st.session_state.backtest_logs[-10:]):  # Show last 10 logs
                    # Color code based on log level
                    if "ERROR" in log_entry.upper():
                        st.error(f"🔴 {log_entry}")
                    elif "WARNING" in log_entry.upper():
                        st.warning(f"🟡 {log_entry}")
                    elif "INFO" in log_entry.upper():
                        st.info(f"🔵 {log_entry}")
                    else:
                        st.write(f"⚪ {log_entry}")

            # Show log count
            total_logs = len(st.session_state.backtest_logs)
            st.caption(f"Showing last 10 of {total_logs} log entries")

            # Option to view all logs
            if total_logs > 10:
                with st.expander("📄 View All Logs"):
                    for i, log_entry in enumerate(st.session_state.backtest_logs):
                        st.code(f"{i+1}: {log_entry}", language=None)

    # Independent Chart Viewer Section
    st.markdown("## 📊 Independent Chart Viewer")
    st.markdown("*Load and view candlestick charts from existing backtest data*")

    with st.expander("📈 Load Existing Chart", expanded=False):
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            # Load available summary data
            summary_file = Path(__file__).parent.parent / "summary_prompt_hash.csv"
            if summary_file.exists():
                try:
                    existing_summary_df = pd.read_csv(summary_file)
                    available_hashes = existing_summary_df['prompt_hash'].unique().tolist()
                    selected_chart_hash = st.selectbox(
                        "Select Prompt Hash:",
                        options=available_hashes,
                        key="independent_hash_selector"
                    )
                except Exception as e:
                    st.error(f"Error loading summary data: {e}")
                    selected_chart_hash = None
            else:
                st.warning("No existing summary data found. Run a backtest first.")
                selected_chart_hash = None

        with col2:
            if selected_chart_hash and 'existing_summary_df' in locals():
                # Filter symbols for selected hash
                hash_data = existing_summary_df[existing_summary_df['prompt_hash'] == selected_chart_hash]
                available_symbols = hash_data['symbol'].unique().tolist()
                selected_chart_symbol = st.selectbox(
                    "Select Symbol:",
                    options=available_symbols,
                    key="independent_symbol_selector"
                )
            else:
                selected_chart_symbol = None

        with col3:
            if selected_chart_hash and selected_chart_symbol:
                # Get all available timeframes from candle database for this symbol
                if CandleCacheDatabase:
                    try:
                        db = CandleCacheDatabase()
                        available_timeframes = db.get_available_timeframes(selected_chart_symbol)
                        if not available_timeframes:
                            available_timeframes = ["1h", "4h", "1d"]  # Fallback
                    except Exception:
                        available_timeframes = ["1h", "4h", "1d"]  # Fallback
                else:
                    available_timeframes = ["1h", "4h", "1d"]  # Fallback

                selected_chart_timeframe = st.selectbox(
                    "Select Timeframe:",
                    options=available_timeframes,
                    key="independent_timeframe_selector"
                )

                # Show data availability info
                if 'existing_summary_df' in locals() and existing_summary_df is not None:
                    hash_symbol_data = existing_summary_df[
                        (existing_summary_df['prompt_hash'] == selected_chart_hash) &
                        (existing_summary_df['symbol'] == selected_chart_symbol)
                    ]

                    if hash_symbol_data is not None and not hash_symbol_data.empty:
                        available_in_summary = hash_symbol_data['timeframe'].unique().tolist()

                        if selected_chart_timeframe not in available_in_summary:
                            st.warning(f"⚠️ No trade data available for {selected_chart_symbol} {selected_chart_timeframe} with this prompt. Chart will show candles only.")
                        else:
                            trade_count = hash_symbol_data[hash_symbol_data['timeframe'] == selected_chart_timeframe]['total_trades'].sum()
                            st.info(f"📊 Found {trade_count} trades for {selected_chart_symbol} {selected_chart_timeframe}")
                    else:
                        st.warning(f"⚠️ No trade data available for {selected_chart_symbol} with this prompt. Chart will show candles only.")
                else:
                    selected_chart_timeframe = None

        with col4:
            load_chart_button = st.button(
                "📊 Load Chart",
                use_container_width=True,
                disabled=not (selected_chart_hash and selected_chart_symbol and selected_chart_timeframe)
            )

        # Load and display chart when button is clicked
        if load_chart_button and selected_chart_hash and selected_chart_symbol and selected_chart_timeframe:
            with st.spinner("Loading chart data..."):
                try:
                    # Load trade data
                    trades_file = Path(__file__).parent.parent / "trade_logs_prompt_hash.csv"
                    if trades_file.exists():
                        trades_df = pd.read_csv(trades_file)

                        # Filter trades for selected combination
                        combination_trades = trades_df[
                            (trades_df['prompt_hash'] == selected_chart_hash) &
                            (trades_df['symbol'] == selected_chart_symbol) &
                            (trades_df['timeframe'] == selected_chart_timeframe)
                        ]

                        if not combination_trades.empty:
                            st.markdown(f"### 📈 Chart: {selected_chart_symbol} {selected_chart_timeframe} - Prompt {selected_chart_hash[:8]}...")

                            # Load candle data
                            if CandleCacheDatabase:
                                try:
                                    db = CandleCacheDatabase()

                                    # Get date range from trades to limit candle data
                                    if 'timestamp' in combination_trades.columns:
                                        # Convert timestamps and get range
                                        trade_timestamps = []
                                        for ts in combination_trades['timestamp'].dropna():
                                            if isinstance(ts, str):
                                                try:
                                                    # Try parsing ISO string
                                                    dt = datetime.fromisoformat(ts.replace('Z', '+00:00'))
                                                    trade_timestamps.append(dt.timestamp() * 1000)
                                                except:
                                                    continue
                                            elif isinstance(ts, (int, float)):
                                                if ts > 1e10:  # milliseconds
                                                    trade_timestamps.append(ts)
                                                else:  # seconds
                                                    trade_timestamps.append(ts * 1000)

                                        if trade_timestamps:
                                            min_ts = min(trade_timestamps)
                                            max_ts = max(trade_timestamps)

                                            # Add buffer (1 day before and after)
                                            buffer_ms = 24 * 60 * 60 * 1000  # 1 day in ms
                                            start_ts = max(0, min_ts - buffer_ms)
                                            end_ts = max_ts + buffer_ms

                                            # Load candles for the period
                                            candles = db.get_candles_between_timestamps(
                                                selected_chart_symbol,
                                                selected_chart_timeframe,
                                                start_ts,
                                                end_ts,
                                                limit=10000
                                            )

                                            if candles and len(candles) > 0:
                                                # Convert to DataFrame
                                                candles_df = pd.DataFrame(candles)

                                                # Convert timestamps
                                                if candles_df['start_time'].iloc[0] > 1e10:  # milliseconds
                                                    candles_df['datetime'] = pd.to_datetime(candles_df['start_time'], unit='ms')
                                                else:  # seconds
                                                    candles_df['datetime'] = pd.to_datetime(candles_df['start_time'], unit='s')

                                                candles_df = candles_df.sort_values('datetime')

                                                # Create candlestick chart
                                                fig = go.Figure()

                                                # Add candlestick
                                                fig.add_trace(go.Candlestick(
                                                    x=candles_df['datetime'],
                                                    open=candles_df['open_price'],
                                                    high=candles_df['high_price'],
                                                    low=candles_df['low_price'],
                                                    close=candles_df['close_price'],
                                                    name='Candles',
                                                    increasing_line_color='green',
                                                    decreasing_line_color='red'
                                                ))

                                                # Add entry and exit points with improved logic
                                                entry_times = []
                                                exit_times = []
                                                entry_prices = []
                                                exit_prices = []
                                                trade_pairs = []  # Store trade pair information for connecting lines

                                                # Debug: Show trade data info
                                                st.write(f"Total markers to add: {len(combination_trades)} trades for this combination")

                                                for idx, trade in combination_trades.iterrows():
                                                    try:
                                                        # Handle different timestamp formats for entry
                                                        timestamp = trade.get('timestamp')
                                                        if pd.isna(timestamp):
                                                            continue

                                                        # Convert entry timestamp to datetime
                                                        if isinstance(timestamp, str):
                                                            # Handle ISO format strings
                                                            if 'T' in timestamp:
                                                                entry_dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                                            else:
                                                                # Handle other string formats
                                                                entry_dt = pd.to_datetime(timestamp)
                                                        elif isinstance(timestamp, (int, float)):
                                                            # Handle unix timestamps
                                                            if timestamp > 1e10:  # milliseconds
                                                                entry_dt = datetime.fromtimestamp(timestamp / 1000)
                                                            else:  # seconds
                                                                entry_dt = datetime.fromtimestamp(timestamp)
                                                        else:
                                                            continue

                                                        # Find the entry candle using timestamp matching
                                                        entry_time = pd.Timestamp(entry_dt)
                                                        time_diff = abs((candles_df['datetime'] - entry_time).dt.total_seconds())
                                                        entry_candle_idx = time_diff.idxmin()

                                                        if pd.notna(entry_candle_idx):
                                                            entry_candle = candles_df.loc[entry_candle_idx]

                                                            # Entry point (green triangle up below entry bar)
                                                            entry_times.append(entry_candle['datetime'])
                                                            entry_prices.append(entry_candle['low_price'] * 0.998)  # Slightly below low

                                                            # Calculate exit position using timestamp-based matching instead of absolute indices
                                                            exit_candle = None
                                                            exit_datetime = None

                                                            # Strategy 1: Calculate exit time based on trade duration from entry time
                                                            duration_candles = trade.get('duration_candles', 1)

                                                            # Ensure duration_candles is a valid number
                                                            if isinstance(duration_candles, str):
                                                                try:
                                                                    duration_candles = int(duration_candles)
                                                                except ValueError:
                                                                    duration_candles = 1
                                                            elif not isinstance(duration_candles, (int, float)) or duration_candles is None:
                                                                duration_candles = 1

                                                            # Ensure duration is at least 1 candle
                                                            duration_candles = max(1, duration_candles)

                                                            # Calculate exit time by adding duration to entry time
                                                            # For hourly candles, each candle represents 1 hour
                                                            timeframe_hours = 1  # Default to 1 hour
                                                            if selected_chart_timeframe == '4h':
                                                                timeframe_hours = 4
                                                            elif selected_chart_timeframe == '1d':
                                                                timeframe_hours = 24
                                                            elif selected_chart_timeframe == '1h':
                                                                timeframe_hours = 1

                                                            exit_time = entry_time + pd.Timedelta(hours=timeframe_hours * duration_candles)

                                                            # Find the closest candle to the calculated exit time
                                                            exit_time_diff = abs((candles_df['datetime'] - exit_time).dt.total_seconds())
                                                            exit_candle_idx = exit_time_diff.idxmin()

                                                            if pd.notna(exit_candle_idx) and isinstance(exit_candle_idx, int) and 0 <= exit_candle_idx < len(candles_df):
                                                                exit_candle = candles_df.iloc[exit_candle_idx]
                                                                exit_datetime = exit_candle['datetime']

                                                            # Strategy 2: Use the last available candle as fallback
                                                            if exit_candle is None:
                                                                exit_candle = candles_df.iloc[-1]
                                                                exit_datetime = exit_candle['datetime']

                                                            # Add exit point if we found a valid position
                                                            if exit_candle is not None:
                                                                exit_times.append(exit_datetime)
                                                                exit_prices.append(exit_candle['high_price'] * 1.002)  # Slightly above high

                                                                # Store trade pair information for connecting lines
                                                                trade_pairs.append({
                                                                    'entry_time': entry_candle['datetime'],
                                                                    'entry_price': entry_candle['low_price'] * 0.998,
                                                                    'exit_time': exit_datetime,
                                                                    'exit_price': exit_candle['high_price'] * 1.002,
                                                                    'outcome': trade.get('outcome', 'unknown'),
                                                                    'trade_id': idx
                                                                })

                                                    except Exception as e:
                                                        # Skip problematic trades but continue processing others
                                                        continue

                                                st.write(f"Total markers to add: {len(entry_times)} entries, {len(exit_times)} exits, {len(trade_pairs)} trade pairs")

                                                # Add entry markers with enhanced tooltips (green triangles pointing up)
                                                if entry_times:
                                                    # Create enhanced hover text for entries
                                                    entry_hover_texts = []
                                                    for i, (time, price) in enumerate(zip(entry_times, entry_prices)):
                                                        if trade_pairs and i < len(trade_pairs):
                                                            trade = trade_pairs[i]
                                                            entry_hover_texts.append(
                                                                f"Entry - {trade['outcome'].upper()}<br>" +
                                                                f"Symbol: {selected_chart_symbol}<br>" +
                                                                f"Time: {time}<br>" +
                                                                f"Entry: {price:.6f}<br>" +
                                                                f"Outcome: {trade['outcome']}<br>" +
                                                                f"Duration: {trade['trade_id']} candles"
                                                            )
                                                        else:
                                                            entry_hover_texts.append(f"Entry<br>Time: {time}<br>Price: {price:.6f}")

                                                    fig.add_trace(go.Scatter(
                                                        x=entry_times,
                                                        y=entry_prices,
                                                        mode='markers',
                                                        marker=dict(
                                                            symbol='triangle-up',
                                                            size=12,
                                                            color='green',
                                                            line=dict(width=2, color='darkgreen')
                                                        ),
                                                        name='Entry Points',
                                                        text=entry_hover_texts,
                                                        hovertemplate='%{text}<extra></extra>'
                                                    ))

                                                # Add exit markers with enhanced tooltips (red triangles pointing down)
                                                if exit_times:
                                                    # Create enhanced hover text for exits
                                                    exit_hover_texts = []
                                                    for i, (time, price) in enumerate(zip(exit_times, exit_prices)):
                                                        if trade_pairs and i < len(trade_pairs):
                                                            trade = trade_pairs[i]
                                                            exit_hover_texts.append(
                                                                f"Exit - {trade['outcome'].upper()}<br>" +
                                                                f"Symbol: {selected_chart_symbol}<br>" +
                                                                f"Time: {time}<br>" +
                                                                f"Exit: {price:.6f}<br>" +
                                                                f"Outcome: {trade['outcome']}<br>" +
                                                                f"P&L: {trade['exit_price'] - trade['entry_price']:.6f}"
                                                            )
                                                        else:
                                                            exit_hover_texts.append(f"Exit<br>Time: {time}<br>Price: {price:.6f}")

                                                    fig.add_trace(go.Scatter(
                                                        x=exit_times,
                                                        y=exit_prices,
                                                        mode='markers',
                                                        marker=dict(
                                                            symbol='triangle-down',
                                                            size=12,
                                                            color='red',
                                                            line=dict(width=2, color='darkred')
                                                        ),
                                                        name='Exit Points',
                                                        text=exit_hover_texts,
                                                        hovertemplate='%{text}<extra></extra>'
                                                    ))

                                                # Add connecting lines between entry and exit points
                                                # Use a set to track unique trade pairs and avoid duplicates
                                                unique_pairs = set()
                                                duplicate_count = 0
                                                processed_count = 0

                                                if trade_pairs:
                                                    st.write(f"🔍 **Debug Info:** Processing {len(trade_pairs)} trade pairs")

                                                    # Check for duplicate entries in the data
                                                    entry_times = [pair['entry_time'] for pair in trade_pairs]
                                                    if len(entry_times) != len(set(entry_times)):
                                                        st.warning("⚠️ **Data Issue Detected:** Multiple trades have the same entry time!")
                                                        from collections import Counter
                                                        entry_counts = Counter(entry_times)
                                                        duplicates = {time: count for time, count in entry_counts.items() if count > 1}
                                                        st.write(f"Duplicate entry times: {duplicates}")

                                                    for pair in trade_pairs:
                                                        # Create a unique identifier for each trade pair
                                                        pair_key = f"{pair['entry_time']}_{pair['exit_time']}_{pair['trade_id']}"

                                                        # Skip if we've already processed this pair
                                                        if pair_key in unique_pairs:
                                                            duplicate_count += 1
                                                            continue

                                                        unique_pairs.add(pair_key)
                                                        processed_count += 1

                                                        # Determine line color based on outcome
                                                        line_color = 'green' if pair['outcome'] == 'win' else 'red' if pair['outcome'] == 'loss' else 'orange'

                                                        # Add line connecting entry to exit
                                                        fig.add_trace(go.Scatter(
                                                            x=[pair['entry_time'], pair['exit_time']],
                                                            y=[pair['entry_price'], pair['exit_price']],
                                                            mode='lines',
                                                            line=dict(
                                                                color=line_color,
                                                                width=2,
                                                                dash='solid'
                                                            ),
                                                            name=f"Trade {pair['trade_id']} ({pair['outcome']})",
                                                            showlegend=False,  # Hide from legend to avoid clutter
                                                            hovertemplate=f"Trade {pair['trade_id']}<br>Entry: {pair['entry_time']}<br>Exit: {pair['exit_time']}<br>Outcome: {pair['outcome']}<extra></extra>"
                                                        ))

                                                    st.write(f"✅ **Debug Info:** Processed {processed_count} unique trade pairs, skipped {duplicate_count} duplicates")

                                                # Update layout
                                                fig.update_layout(
                                                    title=f'{selected_chart_symbol} {selected_chart_timeframe} - Trades for Prompt {selected_chart_hash[:8]}...',
                                                    yaxis_title='Price',
                                                    xaxis_title='Time',
                                                    height=600,
                                                    xaxis_rangeslider_visible=True,
                                                    showlegend=True
                                                )

                                                st.plotly_chart(fig, use_container_width=True)

                                                # Trade summary for this combination
                                                st.markdown("### 📋 Trade Summary")
                                                col1, col2, col3, col4 = st.columns(4)

                                                wins = len(combination_trades[combination_trades['outcome'] == 'win'])
                                                losses = len(combination_trades[combination_trades['outcome'] == 'loss'])
                                                expired = len(combination_trades[combination_trades['outcome'] == 'expired'])
                                                win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0

                                                with col1:
                                                    st.metric("Total Trades", len(combination_trades))
                                                with col2:
                                                    st.metric("Wins", wins)
                                                with col3:
                                                    st.metric("Losses", losses)
                                                with col4:
                                                    st.metric("Win Rate", f"{win_rate:.1%}")

                                                # Performance details for this combination
                                                combo_data = existing_summary_df[
                                                    (existing_summary_df['prompt_hash'] == selected_chart_hash) &
                                                    (existing_summary_df['symbol'] == selected_chart_symbol) &
                                                    (existing_summary_df['timeframe'] == selected_chart_timeframe)
                                                ]

                                                if not combo_data.empty:
                                                    combo = combo_data.iloc[0]
                                                    st.markdown("### 📊 Performance Details")
                                                    perf_col1, perf_col2, perf_col3, perf_col4 = st.columns(4)

                                                    with perf_col1:
                                                        st.metric("Win Rate", f"{combo['win_rate']:.1%}")
                                                    with perf_col2:
                                                        st.metric("Total Trades", f"{combo['total_trades']:,}")
                                                    with perf_col3:
                                                        st.metric("Avg R:R", f"{combo['avg_rr']:.2f}")
                                                    with perf_col4:
                                                        profit_factor = combo['profit_factor']
                                                        st.metric("Profit Factor", f"{profit_factor:.2f}" if pd.notna(profit_factor) else "∞")

                                            else:
                                                st.warning("No candle data available for the selected period.")
                                        else:
                                            st.warning("Could not determine trade date range.")
                                    else:
                                        st.warning("Candle database not available.")
                                except Exception as e:
                                    st.error(f"Error loading chart data: {e}")
                            else:
                                st.warning("No trades found for the selected combination.")
                        else:
                            st.warning("Trade logs file not found.")
                    else:
                        st.warning("No trade data found for the selected combination.")

                except Exception as e:
                    st.error(f"Error loading chart: {e}")

    # Results section
    if st.session_state.backtest_results:
        st.markdown("## 📊 Backtest Results")

        results = st.session_state.backtest_results

        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Total Trades", f"{results.get('total_trades', 0):,}")

        with col2:
            win_rate = results.get('win_rate', 0)
            st.metric("Win Rate", f"{win_rate:.1%}")

        with col3:
            duration = results.get('duration_seconds', 0)
            st.metric("Duration", f"{duration:.1f}s")

        with col4:
            groups = results.get('groups_processed', 0)
            st.metric("Groups Processed", groups)

        # Detailed breakdown
        st.markdown("### 📋 Trade Breakdown")

        wins = results.get('wins', 0)
        losses = results.get('losses', 0)
        expired = results.get('expired', 0)

        breakdown_col1, breakdown_col2, breakdown_col3 = st.columns(3)

        with breakdown_col1:
            st.metric("Wins", f"{wins:,}", delta=f"{wins/(wins+losses)*100:.1f}%" if (wins+losses) > 0 else "0.0%")

        with breakdown_col2:
            st.metric("Losses", f"{losses:,}", delta=f"{losses/(wins+losses)*100:.1f}%" if (wins+losses) > 0 else "0.0%")

        with breakdown_col3:
            st.metric("Expired", f"{expired:,}")

        # Performance visualization
        if wins > 0 or losses > 0:
            st.markdown("### 📈 Performance Visualization")

            # Create pie chart for win/loss distribution
            labels = ['Wins', 'Losses', 'Expired']
            values = [wins, losses, expired]
            colors = ['#28a745', '#dc3545', '#ffc107']

            fig = go.Figure(data=[go.Pie(
                labels=labels,
                values=values,
                marker_colors=colors,
                title="Trade Outcomes Distribution"
            )])

            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)

        # Export options
        st.markdown("### 💾 Export Results")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("📊 View Summary CSV", use_container_width=True):
                st.info("📁 Summary CSV saved to: prompt_performance/summary_prompt_hash.csv")

        with col2:
            if st.button("📋 View Trade Logs CSV", use_container_width=True):
                st.info("📁 Trade logs saved to: prompt_performance/trade_logs_prompt_hash.csv")

        # Prompt Hash Performance Stats Section
        if results.get('analysis_type') == 'prompt_hash':
            st.markdown("## 📊 Prompt Hash Performance Stats")

            try:
                # Load the summary data generated by the backtest
                summary_file = Path(__file__).parent.parent / "summary_prompt_hash.csv"

                if summary_file.exists():
                    summary_df = pd.read_csv(summary_file)

                    if not summary_df.empty:
                        # Overview metrics for prompt hash analysis
                        col1, col2, col3, col4 = st.columns(4)

                        with col1:
                            total_prompts = len(summary_df['prompt_hash'].unique())
                            st.metric("Total Unique Prompts", f"{total_prompts:,}")

                        with col2:
                            avg_win_rate = summary_df['win_rate'].mean()
                            st.metric("Average Win Rate", f"{avg_win_rate:.1%}")

                        with col3:
                            total_combinations = len(summary_df)
                            st.metric("Total Combinations", f"{total_combinations:,}")

                        with col4:
                            best_win_rate = summary_df['win_rate'].max()
                            st.metric("Best Win Rate", f"{best_win_rate:.1%}")

                        # Performance table
                        st.markdown("### 📋 Performance by Prompt Hash")

                        # Prepare data for display
                        display_df = summary_df.copy()

                        # Format numeric columns
                        display_df['win_rate'] = display_df['win_rate'].apply(lambda x: f"{x:.1%}")
                        display_df['avg_rr'] = display_df['avg_rr'].apply(lambda x: f"{x:.2f}")
                        display_df['profit_factor'] = display_df['profit_factor'].apply(
                            lambda x: f"{x:.2f}" if pd.notna(x) and x != float('inf') else "∞"
                        )
                        display_df['expectancy'] = display_df['expectancy'].apply(lambda x: f"{x:.4f}")

                        # Add performance tier column
                        def get_performance_tier(win_rate_str):
                            win_rate = float(win_rate_str.strip('%')) / 100
                            if win_rate >= 0.7:
                                return "🏆 Excellent"
                            elif win_rate >= 0.6:
                                return "✅ Good"
                            elif win_rate >= 0.5:
                                return "⚠️ Moderate"
                            else:
                                return "❌ Poor"

                        display_df['Performance Tier'] = display_df['win_rate'].apply(get_performance_tier)

                        # Reorder columns for better display
                        cols_order = ['prompt_hash', 'symbol', 'timeframe', 'total_trades', 'win_rate',
                                    'avg_rr', 'profit_factor', 'expectancy', 'Performance Tier']
                        display_df = display_df[cols_order]

                        # Rename columns for display
                        display_df.columns = ['Prompt Hash', 'Symbol', 'Timeframe', 'Total Trades',
                                            'Win Rate', 'Avg R:R', 'Profit Factor', 'Expectancy', 'Performance Tier']

                        # Display the table with sorting
                        st.dataframe(
                            display_df,
                            use_container_width=True,
                            column_config={
                                "Prompt Hash": st.column_config.TextColumn("Prompt Hash", width="medium"),
                                "Symbol": st.column_config.TextColumn("Symbol", width="small"),
                                "Timeframe": st.column_config.TextColumn("Timeframe", width="small"),
                                "Total Trades": st.column_config.NumberColumn("Total Trades", width="small"),
                                "Win Rate": st.column_config.TextColumn("Win Rate", width="small"),
                                "Avg R:R": st.column_config.TextColumn("Avg R:R", width="small"),
                                "Profit Factor": st.column_config.TextColumn("Profit Factor", width="small"),
                                "Expectancy": st.column_config.TextColumn("Expectancy", width="small"),
                                "Performance Tier": st.column_config.TextColumn("Performance Tier", width="medium")
                            }
                        )

                        # Top performers highlight
                        st.markdown("### 🏆 Top Performing Prompt Hashes")

                        # Sort by win rate and show top 5
                        top_performers = summary_df.nlargest(5, 'win_rate').copy()
                        top_performers['win_rate'] = top_performers['win_rate'].apply(lambda x: f"{x:.1%}")
                        top_performers['avg_rr'] = top_performers['avg_rr'].apply(lambda x: f"{x:.2f}")

                        top_display = top_performers[['prompt_hash', 'symbol', 'timeframe', 'total_trades', 'win_rate', 'avg_rr']]
                        top_display.columns = ['Prompt Hash', 'Symbol', 'Timeframe', 'Trades', 'Win Rate', 'Avg R:R']

                        st.dataframe(top_display, use_container_width=True)

                        # Interactive Chart Section
                        st.markdown("## 📊 Interactive Chart Analysis")

                        # Controls for chart selection
                        chart_col1, chart_col2, chart_col3 = st.columns(3)

                        with chart_col1:
                            # Get available prompt hashes from the summary data
                            available_hashes = summary_df['prompt_hash'].unique().tolist()
                            selected_chart_hash = st.selectbox(
                                "Select Prompt Hash:",
                                options=available_hashes,
                                key="chart_hash_selector"
                            )

                        with chart_col2:
                            # Filter symbols available for selected hash
                            if selected_chart_hash:
                                hash_data = summary_df[summary_df['prompt_hash'] == selected_chart_hash]
                                available_symbols = hash_data['symbol'].unique().tolist()
                                selected_chart_symbol = st.selectbox(
                                    "Select Symbol:",
                                    options=available_symbols,
                                    key="chart_symbol_selector"
                                )
                            else:
                                selected_chart_symbol = None

                        with chart_col3:
                            # Filter timeframes available for selected hash and symbol
                            if selected_chart_hash and selected_chart_symbol:
                                hash_symbol_data = summary_df[
                                    (summary_df['prompt_hash'] == selected_chart_hash) &
                                    (summary_df['symbol'] == selected_chart_symbol)
                                ]
                                available_timeframes = hash_symbol_data['timeframe'].unique().tolist()
                                selected_chart_timeframe = st.selectbox(
                                    "Select Timeframe:",
                                    options=available_timeframes,
                                    key="chart_timeframe_selector"
                                )
                            else:
                                selected_chart_timeframe = None

                        # Display chart when all selections are made
                        if selected_chart_hash and selected_chart_symbol and selected_chart_timeframe:
                            try:
                                # Load trade data for this specific combination
                                trades_file = Path(__file__).parent.parent / "trade_logs_prompt_hash.csv"

                                if trades_file.exists():
                                    trades_df = pd.read_csv(trades_file)

                                    # Filter trades for selected combination
                                    combination_trades = trades_df[
                                        (trades_df['prompt_hash'] == selected_chart_hash) &
                                        (trades_df['symbol'] == selected_chart_symbol) &
                                        (trades_df['timeframe'] == selected_chart_timeframe)
                                    ]

                                    if not combination_trades.empty:
                                        st.markdown(f"### 📈 Chart: {selected_chart_symbol} {selected_chart_timeframe} - Prompt {selected_chart_hash[:8]}...")

                                        # Load candle data
                                        if CandleCacheDatabase:
                                            try:
                                                db = CandleCacheDatabase()

                                                # Get date range from trades to limit candle data
                                                if 'timestamp' in combination_trades.columns:
                                                    # Convert timestamps and get range
                                                    trade_timestamps = []
                                                    for ts in combination_trades['timestamp'].dropna():
                                                        if isinstance(ts, str):
                                                            try:
                                                                # Try parsing ISO string
                                                                dt = datetime.fromisoformat(ts.replace('Z', '+00:00'))
                                                                trade_timestamps.append(dt.timestamp() * 1000)
                                                            except:
                                                                continue
                                                        elif isinstance(ts, (int, float)):
                                                            if ts > 1e10:  # milliseconds
                                                                trade_timestamps.append(ts)
                                                            else:  # seconds
                                                                trade_timestamps.append(ts * 1000)

                                                    if trade_timestamps:
                                                        min_ts = min(trade_timestamps)
                                                        max_ts = max(trade_timestamps)

                                                        # Add buffer (1 day before and after)
                                                        buffer_ms = 24 * 60 * 60 * 1000  # 1 day in ms
                                                        start_ts = max(0, min_ts - buffer_ms)
                                                        end_ts = max_ts + buffer_ms

                                                        # Load candles for the period
                                                        candles = db.get_candles_between_timestamps(
                                                            selected_chart_symbol,
                                                            selected_chart_timeframe,
                                                            start_ts,
                                                            end_ts,
                                                            limit=10000
                                                        )

                                                        if candles and len(candles) > 0:
                                                            # Convert to DataFrame
                                                            candles_df = pd.DataFrame(candles)

                                                            # Convert timestamps
                                                            if candles_df['start_time'].iloc[0] > 1e10:  # milliseconds
                                                                candles_df['datetime'] = pd.to_datetime(candles_df['start_time'], unit='ms')
                                                            else:  # seconds
                                                                candles_df['datetime'] = pd.to_datetime(candles_df['start_time'], unit='s')

                                                            candles_df = candles_df.sort_values('datetime')

                                                            # Create candlestick chart
                                                            fig = go.Figure()

                                                            # Add candlestick
                                                            fig.add_trace(go.Candlestick(
                                                                x=candles_df['datetime'],
                                                                open=candles_df['open_price'],
                                                                high=candles_df['high_price'],
                                                                low=candles_df['low_price'],
                                                                close=candles_df['close_price'],
                                                                name='Candles',
                                                                increasing_line_color='green',
                                                                decreasing_line_color='red'
                                                            ))

                                                            # Add entry and exit points with improved logic
                                                            entry_times = []
                                                            exit_times = []
                                                            entry_prices = []
                                                            exit_prices = []
                                                            trade_pairs = []  # Store trade pair information for connecting lines

                                                            # Debug: Show trade data info
                                                            st.write(f"Total markers to add: {len(combination_trades)} trades for this combination")

                                                            for idx, trade in combination_trades.iterrows():
                                                                try:
                                                                    # Handle different timestamp formats for entry
                                                                    timestamp = trade.get('timestamp')
                                                                    if pd.isna(timestamp):
                                                                        continue

                                                                    # Convert entry timestamp to datetime
                                                                    if isinstance(timestamp, str):
                                                                        # Handle ISO format strings
                                                                        if 'T' in timestamp:
                                                                            entry_dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                                                        else:
                                                                            # Handle other string formats
                                                                            entry_dt = pd.to_datetime(timestamp)
                                                                    elif isinstance(timestamp, (int, float)):
                                                                        # Handle unix timestamps
                                                                        if timestamp > 1e10:  # milliseconds
                                                                            entry_dt = datetime.fromtimestamp(timestamp / 1000)
                                                                        else:  # seconds
                                                                            entry_dt = datetime.fromtimestamp(timestamp)
                                                                    else:
                                                                        continue

                                                                    # Find the entry candle using timestamp matching
                                                                    entry_time = pd.Timestamp(entry_dt)
                                                                    time_diff = abs((candles_df['datetime'] - entry_time).dt.total_seconds())
                                                                    entry_candle_idx = time_diff.idxmin()

                                                                    if pd.notna(entry_candle_idx):
                                                                        entry_candle = candles_df.loc[entry_candle_idx]

                                                                        # Entry point (green triangle up below entry bar)
                                                                        entry_times.append(entry_candle['datetime'])
                                                                        entry_prices.append(entry_candle['low_price'] * 0.998)  # Slightly below low

                                                                        # Calculate exit position using multiple fallback strategies
                                                                        exit_candle = None
                                                                        exit_datetime = None

                                                                        # Strategy 1: Use exit_candle_index if valid and within our loaded candle range
                                                                        exit_candle_idx = trade.get('exit_candle_index')
                                                                        if exit_candle_idx is not None and 0 <= exit_candle_idx < len(candles_df):
                                                                            exit_candle = candles_df.iloc[exit_candle_idx]
                                                                            exit_datetime = exit_candle['datetime']

                                                                        # Strategy 2: Estimate based on trade duration from entry candle
                                                                        if exit_candle is None:
                                                                            duration_candles = trade.get('duration_candles', 1)

                                                                            # Ensure duration_candles is a valid number
                                                                            if isinstance(duration_candles, str):
                                                                                try:
                                                                                    duration_candles = int(duration_candles)
                                                                                except ValueError:
                                                                                    duration_candles = 1
                                                                            elif not isinstance(duration_candles, (int, float)) or duration_candles is None:
                                                                                duration_candles = 1

                                                                            # Ensure duration is at least 1 candle
                                                                            duration_candles = max(1, duration_candles)

                                                                            # Calculate exit index from entry position
                                                                            estimated_exit_idx = min(int(entry_candle_idx) + int(duration_candles), len(candles_df) - 1)

                                                                            # Only use estimated position if it's different from entry
                                                                            if estimated_exit_idx != entry_candle_idx:
                                                                                exit_candle = candles_df.iloc[estimated_exit_idx]
                                                                                exit_datetime = exit_candle['datetime']

                                                                        # Strategy 3: Use the last available candle as fallback
                                                                        if exit_candle is None:
                                                                            exit_candle = candles_df.iloc[-1]
                                                                            exit_datetime = exit_candle['datetime']

                                                                        # Add exit point if we found a valid position
                                                                        if exit_candle is not None:
                                                                            exit_times.append(exit_datetime)
                                                                            exit_prices.append(exit_candle['high_price'] * 1.002)  # Slightly above high

                                                                            # Store trade pair information for connecting lines
                                                                            trade_pairs.append({
                                                                                'entry_time': entry_candle['datetime'],
                                                                                'entry_price': entry_candle['low_price'] * 0.998,
                                                                                'exit_time': exit_datetime,
                                                                                'exit_price': exit_candle['high_price'] * 1.002,
                                                                                'outcome': trade.get('outcome', 'unknown'),
                                                                                'trade_id': idx
                                                                            })

                                                                except Exception as e:
                                                                    # Skip problematic trades but continue processing others
                                                                    continue

                                                            st.write(f"Total markers to add: {len(entry_times)} entries, {len(exit_times)} exits, {len(trade_pairs)} trade pairs")

                                                            # Add entry markers (green triangles pointing up)
                                                            if entry_times:
                                                                fig.add_trace(go.Scatter(
                                                                    x=entry_times,
                                                                    y=entry_prices,
                                                                    mode='markers',
                                                                    marker=dict(
                                                                        symbol='triangle-up',
                                                                        size=12,
                                                                        color='green',
                                                                        line=dict(width=2, color='darkgreen')
                                                                    ),
                                                                    name='Entry Points',
                                                                    hovertemplate='Entry<br>%{x}<extra></extra>'
                                                                ))

                                                            # Add exit markers (red triangles pointing down)
                                                            if exit_times:
                                                                fig.add_trace(go.Scatter(
                                                                    x=exit_times,
                                                                    y=exit_prices,
                                                                    mode='markers',
                                                                    marker=dict(
                                                                        symbol='triangle-down',
                                                                        size=12,
                                                                        color='red',
                                                                        line=dict(width=2, color='darkred')
                                                                    ),
                                                                    name='Exit Points',
                                                                    hovertemplate='Exit<br>%{x}<extra></extra>'
                                                                ))

                                                            # Add connecting lines between entry and exit points
                                                            # Use a set to track unique trade pairs and avoid duplicates
                                                            unique_pairs = set()
                                                            duplicate_count = 0
                                                            processed_count = 0

                                                            if trade_pairs:
                                                                st.write(f"🔍 **Debug Info:** Processing {len(trade_pairs)} trade pairs")

                                                                # Check for duplicate entries in the data
                                                                entry_times = [pair['entry_time'] for pair in trade_pairs]
                                                                if len(entry_times) != len(set(entry_times)):
                                                                    st.warning("⚠️ **Data Issue Detected:** Multiple trades have the same entry time!")
                                                                    from collections import Counter
                                                                    entry_counts = Counter(entry_times)
                                                                    duplicates = {time: count for time, count in entry_counts.items() if count > 1}
                                                                    st.write(f"Duplicate entry times: {duplicates}")

                                                                for pair in trade_pairs:
                                                                    # Create a unique identifier for each trade pair
                                                                    pair_key = f"{pair['entry_time']}_{pair['exit_time']}_{pair['trade_id']}"

                                                                    # Skip if we've already processed this pair
                                                                    if pair_key in unique_pairs:
                                                                        duplicate_count += 1
                                                                        continue

                                                                    unique_pairs.add(pair_key)
                                                                    processed_count += 1

                                                                    # Determine line color based on outcome
                                                                    line_color = 'green' if pair['outcome'] == 'win' else 'red' if pair['outcome'] == 'loss' else 'orange'

                                                                    # Add line connecting entry to exit
                                                                    fig.add_trace(go.Scatter(
                                                                        x=[pair['entry_time'], pair['exit_time']],
                                                                        y=[pair['entry_price'], pair['exit_price']],
                                                                        mode='lines',
                                                                        line=dict(
                                                                            color=line_color,
                                                                            width=2,
                                                                            dash='solid'
                                                                        ),
                                                                        name=f"Trade {pair['trade_id']} ({pair['outcome']})",
                                                                        showlegend=False,  # Hide from legend to avoid clutter
                                                                        hovertemplate=f"Trade {pair['trade_id']}<br>Entry: {pair['entry_time']}<br>Exit: {pair['exit_time']}<br>Outcome: {pair['outcome']}<extra></extra>"
                                                                    ))

                                                                st.write(f"✅ **Debug Info:** Processed {processed_count} unique trade pairs, skipped {duplicate_count} duplicates")

                                                            # Update layout
                                                            fig.update_layout(
                                                                title=f'{selected_chart_symbol} {selected_chart_timeframe} - Trades for Prompt {selected_chart_hash[:8]}...',
                                                                yaxis_title='Price',
                                                                xaxis_title='Time',
                                                                height=600,
                                                                xaxis_rangeslider_visible=True,
                                                                showlegend=True
                                                            )

                                                            st.plotly_chart(fig, use_container_width=True)

                                                            # Trade summary for this combination
                                                            st.markdown("### 📋 Trade Summary")
                                                            col1, col2, col3, col4 = st.columns(4)

                                                            wins = len(combination_trades[combination_trades['outcome'] == 'win'])
                                                            losses = len(combination_trades[combination_trades['outcome'] == 'loss'])
                                                            expired = len(combination_trades[combination_trades['outcome'] == 'expired'])
                                                            win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0

                                                            with col1:
                                                                st.metric("Total Trades", len(combination_trades))
                                                            with col2:
                                                                st.metric("Wins", wins)
                                                            with col3:
                                                                st.metric("Losses", losses)
                                                            with col4:
                                                                st.metric("Win Rate", f"{win_rate:.1%}")

                                                        else:
                                                            st.warning("No candle data available for the selected period.")
                                                    else:
                                                        st.warning("Could not determine trade date range.")
                                                else:
                                                    st.warning("Candle database not available.")
                                            except Exception as e:
                                                st.error(f"Error loading chart data: {e}")
                                        else:
                                            st.warning("No trades found for the selected combination.")
                                    else:
                                        st.warning("No trades found for the selected combination.")
                                else:
                                    st.warning("Trade logs file not found.")
                            except Exception as e:
                                st.error(f"Error creating chart: {e}")

                    else:
                        st.info("No prompt hash summary data available.")
                else:
                    st.info("Summary file not found. Run a prompt hash backtest first.")

            except Exception as e:
                st.error(f"Error loading prompt hash stats: {e}")
                st.info("Make sure the backtest completed successfully and generated summary files.")

        # Raw results
        with st.expander("🔍 Raw Results Data"):
            st.json(results)

    # Handle button actions
    if run_backtest:
        # Reset session state
        st.session_state.backtest_running = True
        st.session_state.backtest_results = None
        st.session_state.backtest_progress = 0
        st.session_state.backtest_status = "Initializing backtest..."
        st.session_state.backtest_logs = []

        # Create a custom logger to capture backtest output
        import io
        import contextlib
        from logging import StreamHandler

        class StreamlitLogHandler(StreamHandler):
            def __init__(self, session_key):
                super().__init__()
                self.session_key = session_key

            def emit(self, record):
                if not hasattr(st.session_state, self.session_key):
                    setattr(st.session_state, self.session_key, [])
                log_list = getattr(st.session_state, self.session_key)
                log_list.append(self.format(record))
                # Keep only last 20 log messages to avoid memory issues
                if len(log_list) > 20:
                    log_list.pop(0)

        # Set up logging to capture output
        log_capture_handler = StreamlitLogHandler('backtest_logs')
        log_capture_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        log_capture_handler.setFormatter(formatter)

        # Add handler to the logger
        logger.addHandler(log_capture_handler)

        try:
            # Initialize orchestrator
            use_testnet_flag = use_testnet
            if BacktestOrchestrator is None:
                raise ImportError("BacktestOrchestrator not available")

            orchestrator = BacktestOrchestrator(use_testnet=use_testnet_flag)

            # Update status
            st.session_state.backtest_status = "Loading analysis records..."
            st.session_state.backtest_progress = 0.1

            # Run the appropriate backtest method
            if analysis_type == "Prompt Hash Analysis":
                st.session_state.backtest_status = "Running prompt hash analysis backtest..."
                st.session_state.backtest_progress = 0.3

                if orchestrator is not None and hasattr(orchestrator, 'run_backtest_with_prompt_hash'):
                    results = orchestrator.run_backtest_with_prompt_hash(limit=limit)
                else:
                    raise AttributeError("BacktestOrchestrator does not have run_backtest_with_prompt_hash method")
            else:
                st.session_state.backtest_status = "Running version analysis backtest..."
                st.session_state.backtest_progress = 0.3

                if orchestrator is not None and hasattr(orchestrator, 'run_backtest'):
                    results = orchestrator.run_backtest(limit=limit)
                else:
                    raise AttributeError("BacktestOrchestrator does not have run_backtest method")

            # Update progress and status
            st.session_state.backtest_progress = 0.9
            st.session_state.backtest_status = "Generating reports..."

            if results.get('success'):
                st.session_state.backtest_progress = 1.0
                st.session_state.backtest_status = f"Backtest completed successfully! Processed {results.get('total_trades', 0)} trades in {results.get('duration_seconds', 0):.1f} seconds."
                st.session_state.backtest_results = results
            else:
                st.session_state.backtest_status = f"Backtest failed: {results.get('error', 'Unknown error')}"
                st.session_state.backtest_progress = 0

        except Exception as e:
            st.session_state.backtest_status = f"Error running backtest: {str(e)}"
            st.session_state.backtest_progress = 0
            logger.error(f"Backtest execution error: {e}")

        finally:
            st.session_state.backtest_running = False
            # Remove the log handler
            logger.removeHandler(log_capture_handler)

        # Rerun to update UI
        st.rerun()

    if cancel_backtest:
        st.session_state.backtest_running = False
        st.session_state.backtest_status = "Backtest cancelled by user"
        st.session_state.backtest_progress = 0
        st.rerun()

def create_candle_explorer():
    """Create interactive candle explorer with chart visualization."""

    st.markdown("# 📊 Candle Explorer")
    st.markdown("*Explore historical candlestick data from the candle cache database*")

    if CandleCacheDatabase is None:
        st.error("❌ Database not available. Cannot load candle data.")
        st.info("💡 Ensure database_utils.py is properly configured and the candle cache contains data.")
        return

    # Debug information
    with st.expander("🔧 Debug Information"):
        st.write("**CandleCacheDatabase Status:**", "Available" if CandleCacheDatabase else "Not Available")
        st.write("**Database Path:**", str(Path(__file__).parent.parent / "candle_cache.db"))

        # Test database connection
        try:
            test_db = CandleCacheDatabase()
            st.success("✅ Database connection successful")
            # Test if methods exist
            if hasattr(test_db, 'get_available_symbols'):
                st.success("✅ get_available_symbols method exists")
            else:
                st.error("❌ get_available_symbols method missing")

            if hasattr(test_db, 'get_available_timeframes'):
                st.success("✅ get_available_timeframes method exists")
            else:
                st.error("❌ get_available_timeframes method missing")

        except Exception as e:
            st.error(f"❌ Database connection failed: {e}")

    try:
        db = CandleCacheDatabase()

        # Get available symbols and timeframes
        available_symbols = db.get_available_symbols()
        available_timeframes = db.get_available_timeframes()

        if not available_symbols:
            st.warning("No symbols found in candle cache database.")
            st.info("💡 Populate the candle cache first by running historical data collection.")
            return

        if not available_timeframes:
            st.warning("No timeframes found in candle cache database.")
            return

        # Controls section
        st.markdown("## 🎛️ Chart Controls")

        col1, col2, col3 = st.columns(3)

        with col1:
            selected_symbol = st.selectbox(
                "Select Symbol:",
                options=available_symbols,
                index=0 if available_symbols else None,
                key="symbol_selector"
            )

        with col2:
            # Filter timeframes by selected symbol
            symbol_timeframes = db.get_available_timeframes(selected_symbol) if selected_symbol else available_timeframes
            selected_timeframe = st.selectbox(
                "Select Timeframe:",
                options=symbol_timeframes,
                index=0 if symbol_timeframes else None,
                key="timeframe_selector"
            )

        with col3:
            # Show date range information for selected symbol/timeframe
            if selected_symbol and selected_timeframe:
                date_range = db.get_candle_date_range(selected_symbol, selected_timeframe)

                if date_range:
                    min_date = datetime.fromtimestamp(date_range['earliest'] / 1000)
                    max_date = datetime.fromtimestamp(date_range['latest'] / 1000)

                    st.info(f"📅 Available data: {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}")
                    start_date = min_date.date()
                    end_date = max_date.date()
                else:
                    st.warning("No date range available for selected symbol/timeframe")
                    start_date = end_date = None
            else:
                st.info("Select symbol and timeframe to see available data range")
                start_date = end_date = None

        # Load and display data
        if selected_symbol and selected_timeframe:
            # Debug information
            st.markdown("### 🔍 Debug Information")
            debug_col1, debug_col2, debug_col3 = st.columns(3)
            with debug_col1:
                st.info(f"Symbol: {selected_symbol}")
            with debug_col2:
                st.info(f"Timeframe: {selected_timeframe}")
            with debug_col3:
                count = db.get_candle_count(selected_symbol, selected_timeframe)
                st.info(f"Available candles: {count}")

            # Load ALL available candle data for the selected symbol/timeframe (no date filtering)
            with st.spinner("Loading candle data..."):
                candles = None
                try:
                    # First check if data exists
                    candle_count = db.get_candle_count(selected_symbol, selected_timeframe)
                    st.info(f"Found {candle_count} candles in database for {selected_symbol} {selected_timeframe}")

                    if candle_count > 0:
                        # Get all available candles using the correct database method
                        # Use a very wide date range to ensure we get all available data
                        past_timestamp = int((datetime.now() - timedelta(days=365*10)).timestamp())  # 10 years ago
                        future_timestamp = int((datetime.now() + timedelta(days=365*5)).timestamp())  # 5 years in future

                        candles = db.get_candles_between_timestamps(
                            selected_symbol,
                            selected_timeframe,
                            past_timestamp,
                            future_timestamp,
                            limit=100000  # Very large limit to get all available data
                        )

                        if not candles or len(candles) == 0:
                            # Try with a different approach - get data from earliest available
                            earliest = db.get_earliest_candle_timestamp(selected_symbol, selected_timeframe)
                            if earliest:
                                # Convert milliseconds to seconds for datetime conversion
                                if earliest > 1e10:  # If timestamp is in milliseconds
                                    earliest_seconds = earliest / 1000
                                else:  # Already in seconds
                                    earliest_seconds = earliest
                                st.info(f"Earliest timestamp: {earliest} ({datetime.fromtimestamp(earliest_seconds)})")
                                candles = db.get_candles_after_timestamp(
                                    selected_symbol,
                                    selected_timeframe,
                                    earliest,
                                    limit=100000
                                )

                        if candles:
                            st.success(f"Successfully loaded {len(candles)} candles")
                        else:
                            st.warning("No candles loaded despite count > 0")
                    else:
                        st.warning(f"No candle data found in database for {selected_symbol} {selected_timeframe}")

                except Exception as e:
                    st.error(f"Error loading candle data: {e}")
                    st.error(f"Exception type: {type(e).__name__}")
                    import traceback
                    st.code(traceback.format_exc())
                    candles = None

            if candles and len(candles) > 0:
                # Convert to DataFrame for easier manipulation
                df = pd.DataFrame(candles)

                # Convert timestamps to datetime - handle both seconds and milliseconds
                # Check if timestamps are in milliseconds (common in trading APIs)
                sample_timestamp = df['start_time'].iloc[0]
                if sample_timestamp > 1e10:  # If timestamp is > 10 billion, it's likely milliseconds
                    df['datetime'] = pd.to_datetime(df['start_time'], unit='ms')
                else:  # Otherwise treat as seconds
                    df['datetime'] = pd.to_datetime(df['start_time'], unit='s')

                # Sort by datetime to ensure proper chronological order
                df = df.sort_values('datetime')

                # Overview metrics
                st.markdown("## 📈 Chart Overview")

                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Total Candles", f"{len(df):,}")

                with col2:
                    if len(df) > 0:
                        price_range = df['high_price'].max() - df['low_price'].min()
                        st.metric("Price Range", f"{price_range:.4f}")
                    else:
                        st.metric("Price Range", "N/A")

                with col3:
                    if len(df) > 0:
                        avg_volume = df['volume'].mean()
                        st.metric("Avg Volume", f"{avg_volume:.2f}")
                    else:
                        st.metric("Avg Volume", "N/A")

                with col4:
                    if len(df) > 0:
                        total_volume = df['volume'].sum()
                        st.metric("Total Volume", f"{total_volume:.2f}")
                    else:
                        st.metric("Total Volume", "N/A")

                # Create candlestick chart with volume
                st.markdown("## 📊 Candlestick Chart")

                # Create subplots for candlestick and volume
                fig = make_subplots(
                    rows=2, cols=1,
                    shared_xaxes=True,
                    vertical_spacing=0.03,
                    subplot_titles=('Candlestick Chart', 'Volume'),
                    row_width=[0.2, 0.8]
                )

                # Add candlestick trace
                fig.add_trace(
                    go.Candlestick(
                        x=df['datetime'],
                        open=df['open_price'],
                        high=df['high_price'],
                        low=df['low_price'],
                        close=df['close_price'],
                        name='Candles',
                        increasing_line_color='green',
                        decreasing_line_color='red'
                    ),
                    row=1, col=1
                )

                # Add volume bar chart
                if len(df) > 0:
                    colors = ['red' if close < open else 'green' for close, open in zip(df['close_price'], df['open_price'])]

                    fig.add_trace(
                        go.Bar(
                            x=df['datetime'],
                            y=df['volume'],
                            name='Volume',
                            marker_color=colors,
                            showlegend=False
                        ),
                        row=2, col=1
                    )

                # Update layout
                date_range_text = f"{df['datetime'].min().strftime('%Y-%m-%d')} to {df['datetime'].max().strftime('%Y-%m-%d')}" if len(df) > 0 else "No data available"
                fig.update_layout(
                    height=800,
                    title=f'{selected_symbol} {selected_timeframe} - {date_range_text}',
                    xaxis_rangeslider_visible=True,  # Enable range slider for interactivity
                    yaxis_title='Price',
                    yaxis2_title='Volume'
                )

                # Update x-axes
                fig.update_xaxes(
                    type='date',
                    row=1, col=1
                )
                fig.update_xaxes(
                    type='date',
                    row=2, col=1
                )

                st.plotly_chart(fig, use_container_width=True)

                # Interactive features
                st.markdown("### 🎛️ Interactive Features")
                st.info("💡 **Interactive Chart Features:**\n"
                       "- Use the range slider below the chart to zoom in/out\n"
                       "- Hover over candles to see detailed OHLC data\n"
                       "- Click and drag to pan across the timeline\n"
                       "- Double-click to reset zoom")

                # Data table
                st.markdown("## 📋 Raw Data")

                # Format data for display
                display_df = df.copy()
                display_df['datetime'] = display_df['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
                display_df = display_df[['datetime', 'open_price', 'high_price', 'low_price', 'close_price', 'volume', 'turnover']]

                # Rename columns for better display
                display_df.columns = ['DateTime', 'Open', 'High', 'Low', 'Close', 'Volume', 'Turnover']

                # Format numeric columns
                for col in ['Open', 'High', 'Low', 'Close', 'Volume', 'Turnover']:
                    if col == 'Volume':
                        display_df[col] = display_df[col].apply(lambda x: f"{x:.0f}")
                    else:
                        display_df[col] = display_df[col].apply(lambda x: f"{x:.6f}")

                st.dataframe(display_df, use_container_width=True)

                # Export options
                st.markdown("## 💾 Export Data")

                col1, col2 = st.columns(2)

                with col1:
                    csv_data = display_df.to_csv(index=False)
                    st.download_button(
                        label="📊 Download CSV",
                        data=csv_data,
                        file_name=f"candles_{selected_symbol}_{selected_timeframe}_all_data.csv",
                        mime="text/csv",
                        use_container_width=True
                    )

                with col2:
                    if st.button("📈 View Statistics", use_container_width=True):
                        with st.expander("📊 Detailed Statistics"):
                            st.markdown("### Price Statistics")
                            price_stats = df[['open_price', 'high_price', 'low_price', 'close_price']].describe()
                            st.dataframe(price_stats)

                            st.markdown("### Volume Statistics")
                            volume_stats = df[['volume', 'turnover']].describe()
                            st.dataframe(volume_stats)

                            st.markdown("### Time Series Analysis")
                            st.markdown(f"**Data Range:** {df['datetime'].min()} to {df['datetime'].max()}")
                            st.markdown(f"**Total Duration:** {(df['datetime'].max() - df['datetime'].min()).days} days")

            else:
                st.warning(f"No candle data found for {selected_symbol} {selected_timeframe}")
                st.info("💡 This could mean:\n"
                       "- The symbol/timeframe combination has no data in the database\n"
                       "- The database connection failed\n"
                       "- The data format is incompatible\n\n"
                       "**Suggestions:**\n"
                       "- Check if the database contains data for this symbol/timeframe\n"
                       "- Try running the historical data population script\n"
                       "- Verify the database connection and schema")

        # Database info
        with st.expander("ℹ️ Database Information"):
            st.markdown("### Available Data Summary")

            col1, col2 = st.columns(2)

            with col1:
                st.metric("Total Symbols", len(available_symbols))
                st.write("**Symbols:**", ", ".join(available_symbols[:10]) + ("..." if len(available_symbols) > 10 else ""))

            with col2:
                st.metric("Total Timeframes", len(available_timeframes))
                st.write("**Timeframes:**", ", ".join(available_timeframes))

            # Show data counts per symbol/timeframe
            st.markdown("### Data Counts by Symbol/Timeframe")

            data_counts = []
            for symbol in available_symbols[:10]:  # Limit to first 10 symbols for performance
                for timeframe in available_timeframes:
                    count = db.get_candle_count(symbol, timeframe)
                    if count > 0:
                        data_counts.append({
                            'Symbol': symbol,
                            'Timeframe': timeframe,
                            'Count': count
                        })

            if data_counts:
                counts_df = pd.DataFrame(data_counts)
                counts_df = counts_df.sort_values('Count', ascending=False)
                st.dataframe(counts_df, use_container_width=True)
            else:
                st.info("No detailed count data available.")

    except Exception as e:
        st.error(f"Error loading candle explorer: {e}")
        st.info("Make sure the database is properly set up and contains candle data.")

def create_ab_testing_lab():
    """Interactive A/B testing interface for prompt comparison."""

    st.markdown("# 🧪 A/B Testing Lab")

    if not ENHANCED_FEATURES_AVAILABLE:
        st.error("❌ A/B Testing features not available. Please check module imports.")
        return

    try:
        ab_manager = ABTestManager()
    except Exception as e:
        st.error(f"❌ Could not initialize A/B Test Manager: {e}")
        return

    tab1, tab2, tab3 = st.tabs(["🎯 Experiment Designer", "📊 Active Tests", "📈 Results Archive"])

    with tab1:
        st.markdown("## 🎯 Design New Experiment")

        with st.form("experiment_designer"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("### Prompt A (Control)")
                prompt_a = st.text_input("Control Prompt ID", value="baseline_v1")
                expected_win_rate_a = st.number_input("Expected Win Rate A", 0.0, 1.0, 0.65, format="%.2f")

            with col2:
                st.markdown("### Prompt B (Variant)")
                prompt_b = st.text_input("Variant Prompt ID", value="enhanced_v2")
                expected_win_rate_b = st.number_input("Expected Win Rate B", 0.0, 1.0, 0.70, format="%.2f")

            # Power analysis
            st.markdown("### 📊 Statistical Design")
            col1, col2, col3 = st.columns(3)

            with col1:
                effect_size = st.number_input("Minimum Detectable Effect", 0.01, 0.20, 0.05, format="%.2f")
            with col2:
                power = st.slider("Statistical Power", 0.70, 0.95, 0.80, format="%.2f")
            with col3:
                alpha = st.slider("Significance Level", 0.01, 0.10, 0.05, format="%.2f")

            # Calculate required sample size
            if ENHANCED_FEATURES_AVAILABLE:
                try:
                    framework = StatisticalTestingFramework()
                    required_sample_size = framework.calculate_minimum_sample_size(
                        expected_win_rate_a, expected_win_rate_b, power, alpha
                    )
                except Exception as e:
                    required_sample_size = 200  # Fallback
            else:
                required_sample_size = 200  # Fallback

            st.info(f"📏 Required sample size: {required_sample_size} trades per group")

            # Experiment settings
            st.markdown("### ⚙️ Experiment Settings")
            col1, col2 = st.columns(2)

            with col1:
                test_name = st.text_input("Experiment Name", value=f"Test_{datetime.now().strftime('%Y%m%d_%H%M')}")
                symbols = st.multiselect("Symbols", ["BTCUSDT", "ETHUSDT", "ADAUSDT"], default=["BTCUSDT"])

            with col2:
                timeframes = st.multiselect("Timeframes", ["30m","1h", "4h", "1d"], default=["4h"])
                max_duration = st.number_input("Max Duration (days)", 1, 90, 30)
                early_stopping = st.checkbox("Enable Early Stopping", True)

            submit_experiment = st.form_submit_button("🚀 Launch Experiment")

            if submit_experiment:
                if not prompt_a or not prompt_b:
                    st.error("Both prompt IDs are required")
                elif prompt_a == prompt_b:
                    st.error("Prompt A and B must be different")
                elif not symbols or not timeframes:
                    st.error("At least one symbol and timeframe must be selected")
                else:
                    try:
                        config = ABTestConfig(
                            test_id=generate_test_id(),
                            test_name=test_name,
                            prompt_a_id=prompt_a,
                            prompt_b_id=prompt_b,
                            symbols=symbols,
                            timeframes=timeframes,
                            target_sample_size=required_sample_size,
                            max_duration_days=max_duration,
                            significance_level=alpha,
                            minimum_effect_size=effect_size,
                            power=power,
                            early_stopping_enabled=early_stopping
                        )

                        test_id = ab_manager.create_experiment(config)
                        ab_manager.start_experiment(test_id)

                        st.success(f"🎉 Experiment '{test_name}' launched successfully!")
                        st.info(f"Test ID: {test_id}")

                    except Exception as e:
                        st.error(f"❌ Failed to create experiment: {e}")

    with tab2:
        st.markdown("## 📈 Active Experiments")

        try:
            active_tests = ab_manager.get_active_experiments()

            if active_tests:
                for test in active_tests:
                    with st.expander(f"📊 {test['test_name']} - {test['status'].title()}"):
                        col1, col2, col3 = st.columns(3)

                        try:
                            results = ab_manager.get_test_results(test['test_id'])

                            with col1:
                                st.metric("Progress", f"{(results.current_sample_size_a + results.current_sample_size_b) / test['config']['target_sample_size']:.1%}")
                                st.metric("Days Running", results.days_running)

                            with col2:
                                st.metric("Current P-value", f"{results.current_p_value:.4f}")
                                st.metric("Effect Size", f"{results.current_effect_size:.3f}")

                            with col3:
                                if results.current_p_value < test['config']['significance_level']:
                                    st.success("✅ Significant")
                                else:
                                    st.warning("⏳ Not Yet Significant")

                                if st.button(f"Stop Test", key=f"stop_{test['test_id']}"):
                                    ab_manager.stop_experiment(test['test_id'])
                                    st.rerun()

                            # Real-time results visualization
                            if results.current_sample_size_a > 0 and results.current_sample_size_b > 0:
                                fig = go.Figure()

                                fig.add_trace(go.Bar(
                                    name='Group A',
                                    x=['Win Rate'],
                                    y=[results.current_wins_a / results.current_sample_size_a],
                                    error_y=dict(
                                        type='data',
                                        array=[0.05],  # Approximate error
                                        visible=True
                                    )
                                ))

                                fig.add_trace(go.Bar(
                                    name='Group B',
                                    x=['Win Rate'],
                                    y=[results.current_wins_b / results.current_sample_size_b],
                                    error_y=dict(
                                        type='data',
                                        array=[0.05],  # Approximate error
                                        visible=True
                                    )
                                ))

                                fig.update_layout(
                                    title=f'Current Results: {test["test_name"]}',
                                    yaxis_title='Win Rate',
                                    yaxis_tickformat='.1%',
                                    height=300
                                )

                                st.plotly_chart(fig, use_container_width=True)

                        except Exception as e:
                            st.error(f"Error getting test results: {e}")
            else:
                st.info("No active experiments. Create one in the Experiment Designer tab.")

        except Exception as e:
            st.error(f"Error loading active experiments: {e}")

    with tab3:
        st.markdown("## 📈 Completed Experiments")
        st.info("Results archive feature coming soon!")

def main():
    """Main enhanced dashboard application."""
    
    # Sidebar configuration
    st.sidebar.markdown("## 🎛️ Enhanced Dashboard Controls")
    
    # Analysis type selector
    analysis_type = st.sidebar.radio(
        "Analysis Type",
        ["Version Analysis", "Prompt Hash Analysis"],
        help="Choose between prompt version analysis (existing) or prompt hash analysis (new)"
    )
    
    internal_type = "hash" if analysis_type == "Prompt Hash Analysis" else "version"
    
    # Initialize session state for dashboard mode
    if 'dashboard_mode' not in st.session_state:
        st.session_state.dashboard_mode = "Executive Dashboard"
    
    # Dashboard mode selector
    dashboard_mode = st.sidebar.selectbox(
        "Dashboard Mode",
        ["Backtest Runner", "Executive Dashboard", "Statistical Analysis", "A/B Testing Lab", "Prompt Hash Viewer", "Candle Explorer", "Classic View"],
        index=["Backtest Runner", "Executive Dashboard", "Statistical Analysis", "A/B Testing Lab", "Prompt Hash Viewer", "Candle Explorer", "Classic View"].index(st.session_state.dashboard_mode),
        help="Choose the dashboard mode for your analysis needs",
        key="mode_selector"
    )
    
    # Update session state when selectbox changes
    if dashboard_mode != st.session_state.dashboard_mode:
        st.session_state.dashboard_mode = dashboard_mode
    
    # Load enhanced data
    with st.spinner("Loading and analyzing data..."):
        summary_df, trades_df, enhanced_data = load_enhanced_data(internal_type)
    
    # Refresh button
    if st.sidebar.button("🔄 Refresh Data"):
        st.cache_data.clear()
        st.rerun()
    
    # Use session state for mode to maintain consistency
    current_mode = st.session_state.dashboard_mode
    
    # Display appropriate dashboard based on mode
    if current_mode == "Backtest Runner":
        create_backtest_runner()
    elif current_mode == "Executive Dashboard":
        create_executive_dashboard(summary_df, trades_df, enhanced_data)
    elif current_mode == "Statistical Analysis":
        create_statistical_analysis_tab(enhanced_data)
    elif current_mode == "A/B Testing Lab":
        create_ab_testing_lab()
    elif current_mode == "Prompt Hash Viewer":
        create_prompt_viewer()
    elif current_mode == "Candle Explorer":
        create_candle_explorer()
    elif current_mode == "Classic View":
        # Fall back to basic dashboard
        st.info("Classic view would load the original dashboard here")
        if summary_df is not None and trades_df is not None:
            # Show basic metrics from the original dashboard
            st.markdown("## 📊 Basic Performance Overview")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Total Trades", len(trades_df))
            with col2:
                wins = len(trades_df[trades_df['outcome'] == 'win'])
                losses = len(trades_df[trades_df['outcome'] == 'loss'])
                win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0
                st.metric("Win Rate", f"{win_rate:.1%}")
            with col3:
                st.metric("Unique Prompts", len(summary_df) if summary_df is not None else 0)
    
    # Enhanced sidebar info
    st.sidebar.markdown("---")
    st.sidebar.markdown("## 🚀 Enhanced Features")
    
    if ENHANCED_FEATURES_AVAILABLE:
        st.sidebar.success("✅ Statistical Testing")
        st.sidebar.success("✅ A/B Testing")
        st.sidebar.success("✅ Enhanced Metrics")
        st.sidebar.success("✅ Confidence Intervals")
    else:
        st.sidebar.error("❌ Enhanced features unavailable")
        st.sidebar.info("Install required modules for full functionality")
    
    # Footer
    st.sidebar.markdown("---")
    st.sidebar.markdown(f"**Last Updated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
