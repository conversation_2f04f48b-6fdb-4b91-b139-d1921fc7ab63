import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
from pathlib import Path
import numpy as np
from datetime import datetime
import sys

# Add core module to path for database access
sys.path.append(str(Path(__file__).parent.parent / "core"))
try:
    from database_utils import CandleCacheDatabase
except ImportError:
    try:
        from ..core.database_utils import CandleCacheDatabase
    except ImportError:
        CandleCacheDatabase = None

# Set page config
st.set_page_config(
    page_title="Prompt Performance Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 0.25rem solid #1f77b4;
    }
    .win-rate-high {
        color: #28a745;
        font-weight: bold;
    }
    .win-rate-medium {
        color: #ffc107;
        font-weight: bold;
    }
    .win-rate-low {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def load_data(analysis_type="version"):
    """Load CSV data from the results directory."""
    # Get the absolute path to ensure we're looking in the right place
    script_dir = Path(__file__).resolve().parent  # dashboard/
    base_path = script_dir.parent  # prompt_performance/

    # Debug: print the paths we're looking at
    print(f"Dashboard script location: {Path(__file__).resolve()}")
    print(f"Looking for files in: {base_path}")
    print(f"Expected summary file: {base_path / ('summary_prompt_hash.csv' if analysis_type == 'hash' else 'summary.csv')}")

    if analysis_type == "hash":
        summary_file = base_path / "summary_prompt_hash.csv"
        trades_file = base_path / "trade_logs_prompt_hash.csv"
        error_prefix = "prompt hash"
    else:
        summary_file = base_path / "summary.csv"
        trades_file = base_path / "trade_logs.csv"
        error_prefix = "standard"

    if not summary_file.exists():
        st.error(f"❌ {summary_file.name} not found. Please run the {error_prefix} backtest first.")
        return None, None

    if not trades_file.exists():
        st.error(f"❌ {trades_file.name} not found. Please run the {error_prefix} backtest first.")
        return None, None

    try:
        summary_df = pd.read_csv(summary_file)
        trades_df = pd.read_csv(trades_file)

        # Convert timestamp strings to datetime
        if 'timestamp' in trades_df.columns:
            trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'], errors='coerce')

        return summary_df, trades_df
    except Exception as e:
        st.error(f"❌ Error loading {error_prefix} data: {e}")
        return None, None

def get_win_rate_class(win_rate):
    """Get CSS class based on win rate."""
    if win_rate >= 0.6:
        return "win-rate-high"
    elif win_rate >= 0.4:
        return "win-rate-medium"
    else:
        return "win-rate-low"

def create_overview_metrics(summary_df, trades_df, analysis_type="version"):
    """Create overview metrics section."""
    title = "📊 Prompt Hash Performance Dashboard" if analysis_type == "hash" else "📊 Prompt Performance Dashboard"
    st.markdown(f'<h1 class="main-header">{title}</h1>', unsafe_allow_html=True)

    if summary_df is None or trades_df is None:
        return

    # Calculate overall metrics
    total_trades = len(trades_df)
    total_wins = len(trades_df[trades_df['outcome'] == 'win'])
    total_losses = len(trades_df[trades_df['outcome'] == 'loss'])
    total_expired = len(trades_df[trades_df['outcome'] == 'expired'])

    win_rate = total_wins / (total_wins + total_losses) if (total_wins + total_losses) > 0 else 0

    # Overall metrics
    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        st.metric("Total Trades", f"{total_trades:,}")

    with col2:
        st.metric("Wins", f"{total_wins:,}")

    with col3:
        st.metric("Losses", f"{total_losses:,}")

    with col4:
        st.metric("Expired", f"{total_expired:,}")

    with col5:
        win_rate_class = get_win_rate_class(win_rate)
        st.metric("Win Rate", f"{win_rate:.1%}")

def create_performance_charts(summary_df, trades_df, analysis_type="version"):
    """Create performance visualization charts."""
    if summary_df is None or trades_df is None:
        return

    st.markdown("---")
    st.markdown("## 📈 Performance Analysis")

    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs(["📊 Win Rate Analysis", "🎯 Risk-Reward", "📅 Time Series", "🔍 Detailed Breakdown", "🏆 Top Combinations", "🎯 Interactive Backtest"])

    with tab1:
        col1, col2 = st.columns(2)

        with col1:
            # Win Rate by Prompt Version/Hash
            if not summary_df.empty:
                x_col = 'prompt_hash' if analysis_type == "hash" else 'prompt_version'
                x_label = 'Prompt Hash' if analysis_type == "hash" else 'Prompt Version'
                title = f'Win Rate by {x_label}'

                # Create custom hover text explaining the combination structure
                def format_profit_factor(pf):
                    return f"{pf:.2f}" if pd.notna(pf) else "∞"

                if analysis_type == "hash":
                    hover_text = summary_df.apply(
                        lambda row: f"<span style='color: black;'><b>Prompt Hash:</b> {row['prompt_hash']}<br>" +
                                   f"<b>Symbol:</b> {row['symbol']}<br>" +
                                   f"<b>Timeframe:</b> {row['timeframe']}<br>" +
                                   f"<b>Win Rate:</b> {row['win_rate']:.1%}<br>" +
                                   f"<b>Total Trades:</b> {row['total_trades']}<br>" +
                                   f"<b>Avg Risk-Reward:</b> {row['avg_rr']:.2f}<br>" +
                                   f"<b>Profit Factor:</b> {format_profit_factor(row['profit_factor'])}<br>" +
                                   f"<b>Expectancy:</b> ${row['expectancy']:.4f}<br><br>" +
                                   "💡 <i>Each bar represents one prompt hash + symbol + timeframe combination</i></span>",
                        axis=1
                    )
                else:
                    hover_text = summary_df.apply(
                        lambda row: f"<span style='color: black;'><b>Prompt Version:</b> {row['prompt_version']}<br>" +
                                   f"<b>Symbol:</b> {row['symbol']}<br>" +
                                   f"<b>Timeframe:</b> {row['timeframe']}<br>" +
                                   f"<b>Win Rate:</b> {row['win_rate']:.1%}<br>" +
                                   f"<b>Total Trades:</b> {row['total_trades']}<br>" +
                                   f"<b>Avg Risk-Reward:</b> {row['avg_rr']:.2f}<br>" +
                                   f"<b>Profit Factor:</b> {format_profit_factor(row['profit_factor'])}<br>" +
                                   f"<b>Expectancy:</b> ${row['expectancy']:.4f}<br><br>" +
                                   "💡 <i>Each bar represents one prompt version + symbol + timeframe combination</i></span>",
                        axis=1
                    )

                fig_win_rate = px.bar(
                    summary_df,
                    x=x_col,
                    y='win_rate',
                    title=title,
                    labels={'win_rate': 'Win Rate', x_col: x_label},
                    color='win_rate',
                    color_continuous_scale='RdYlGn',
                    hover_data={
                        'win_rate': ':.1%',
                        'total_trades': True,
                        'avg_rr': ':.2f',
                        'profit_factor': ':.2f',
                        'expectancy': ':.4f'
                    }
                )

                # Update hover template to use our custom text
                fig_win_rate.update_traces(
                    hovertemplate=hover_text,
                    hoverlabel=dict(bgcolor="white", bordercolor="black", font_size=12)
                )

                fig_win_rate.update_layout(xaxis_tickangle=-45)
                st.plotly_chart(fig_win_rate, use_container_width=True)

        with col2:
            # Win Rate Distribution
            win_rates = summary_df['win_rate'].dropna()
            if not win_rates.empty:
                fig_dist = px.histogram(
                    win_rates,
                    title='Win Rate Distribution',
                    labels={'value': 'Win Rate'},
                    color_discrete_sequence=['#1f77b4']
                )
                st.plotly_chart(fig_dist, use_container_width=True)

    with tab2:
        col1, col2 = st.columns(2)

        with col1:
            # Average RR by Prompt Version/Hash
            if not summary_df.empty:
                x_col = 'prompt_hash' if analysis_type == "hash" else 'prompt_version'
                x_label = 'Prompt Hash' if analysis_type == "hash" else 'Prompt Version'
                title = f'Average Risk-Reward by {x_label}'

                fig_rr = px.bar(
                    summary_df,
                    x=x_col,
                    y='avg_rr',
                    title=title,
                    labels={'avg_rr': 'Avg R:R', x_col: x_label},
                    color='avg_rr',
                    color_continuous_scale='Blues'
                )
                fig_rr.update_layout(xaxis_tickangle=-45)
                st.plotly_chart(fig_rr, use_container_width=True)

        with col2:
            # Profit Factor by Prompt Version/Hash
            if not summary_df.empty:
                x_col = 'prompt_hash' if analysis_type == "hash" else 'prompt_version'
                x_label = 'Prompt Hash' if analysis_type == "hash" else 'Prompt Version'
                title = f'Profit Factor by {x_label}'

                fig_pf = px.bar(
                    summary_df,
                    x=x_col,
                    y='profit_factor',
                    title=title,
                    labels={'profit_factor': 'Profit Factor', x_col: x_label},
                    color='profit_factor',
                    color_continuous_scale='Greens'
                )
                fig_pf.update_layout(xaxis_tickangle=-45)
                st.plotly_chart(fig_pf, use_container_width=True)

    with tab3:
        # Time series analysis
        if 'timestamp' in trades_df.columns and not trades_df['timestamp'].isna().all():
            # Trade outcomes over time
            trades_over_time = trades_df.groupby(trades_df['timestamp'].dt.date)['outcome'].value_counts().unstack().fillna(0)

            if not trades_over_time.empty:
                fig_time = px.line(
                    trades_over_time,
                    title='Trade Outcomes Over Time',
                    labels={'value': 'Number of Trades', 'timestamp': 'Date'},
                    color_discrete_map={'win': 'green', 'loss': 'red', 'expired': 'orange'}
                )
                st.plotly_chart(fig_time, use_container_width=True)

    with tab4:
        # Detailed breakdown table
        label = 'Prompt Hash' if analysis_type == "hash" else 'Prompt Version'
        st.markdown(f"### 📋 Detailed Performance by {label}")

        if not summary_df.empty:
            # Format the dataframe for display
            display_df = summary_df.copy()
            display_df['win_rate'] = display_df['win_rate'].apply(lambda x: f"{x:.1%}")
            display_df['avg_rr'] = display_df['avg_rr'].apply(lambda x: f"{x:.2f}")
            display_df['profit_factor'] = display_df['profit_factor'].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "∞")
            display_df['expectancy'] = display_df['expectancy'].apply(lambda x: f"{x:.4f}")

            st.dataframe(display_df, use_container_width=True)

    with tab5:
        # Top performing combinations analysis
        if not trades_df.empty:
            # Try to import metrics aggregator for combination analysis
            aggregator = None
            top_combinations = None

            try:
                import sys
                import importlib.util

                # Try multiple possible paths for the core module
                possible_paths = [
                    Path(__file__).parent.parent / "core" / "metrics_aggregator.py",
                    Path(__file__).parent / ".." / "core" / "metrics_aggregator.py",
                    Path.cwd() / "core" / "metrics_aggregator.py",
                    Path.cwd().parent / "core" / "metrics_aggregator.py"
                ]

                core_path = None
                for path in possible_paths:
                    if path.exists():
                        core_path = path
                        break

                if core_path:
                    spec = importlib.util.spec_from_file_location("metrics_aggregator", core_path)
                    if spec and spec.loader:
                        metrics_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(metrics_module)
                        aggregator = metrics_module.MetricsAggregator()

                        # Get top performing combinations
                        top_combinations = aggregator.get_top_performing_combinations(trades_df.to_dict('records'), top_n=20)
                else:
                    raise FileNotFoundError("Could not find metrics_aggregator.py in any expected location")

            except Exception as e:
                st.warning(f"⚠️ Could not load metrics aggregator: {e}")
                st.info("💡 Combination analysis is still generated automatically during backtests and saved to `combination_analysis.csv`")
                st.markdown("**To view combination analysis:**")
                st.code("python run_backtest_prompt_hash.py")
                st.markdown("**Then check:** `combination_analysis.csv`")

                if aggregator and top_combinations is not None and len(top_combinations) > 0:
                    st.markdown("### 🏆 Top Performing Combinations")
                    st.markdown("*Prompt Hash + Symbol + Timeframe combinations with highest win rates*")

                    # Show summary of combination analysis
                    st.markdown(f"**📊 Analysis based on {len(top_combinations)} unique combinations**")

                    # Summary metrics
                    avg_win_rate = sum(c['win_rate'] for c in top_combinations[:10]) / min(10, len(top_combinations))
                    total_trades_top10 = sum(c['total_trades'] for c in top_combinations[:10])

                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Top 10 Avg Win Rate", f"{avg_win_rate:.1%}")
                    with col2:
                        st.metric("Top 10 Total Trades", f"{total_trades_top10:,}")
                    with col3:
                        st.metric("Total Combinations", f"{len(top_combinations):,}")

                    # Top combinations table
                    combinations_df = pd.DataFrame(top_combinations)

                    # Format for display
                    display_df = combinations_df.copy()
                    display_df['win_rate'] = display_df['win_rate'].apply(lambda x: f"{x:.1%}")
                    display_df['avg_rr'] = display_df['avg_rr'].apply(lambda x: f"{x:.2f}")
                    display_df['profit_factor'] = display_df['profit_factor'].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "∞")
                    display_df['expectancy'] = display_df['expectancy'].apply(lambda x: f"{x:.4f}")

                    # Add rank column
                    display_df.insert(0, 'Rank', range(1, len(display_df) + 1))

                    st.dataframe(display_df, use_container_width=True)

                    # Visualization of top combinations
                    st.markdown("### 📊 Top 10 Win Rates")
                    top_10 = combinations_df.head(10).copy()
                    top_10['short_combo'] = top_10.apply(
                        lambda row: f"{row['prompt_hash'][:5]}<br>{row['symbol']}<br>{row['timeframe']}",
                        axis=1
                    )

                    fig_combinations = px.bar(
                        top_10,
                        x='short_combo',
                        y='win_rate',
                        title='Top 10 Performing Combinations',
                        labels={'win_rate': 'Win Rate', 'short_combo': 'Combination'},
                        color='win_rate',
                        color_continuous_scale='RdYlGn'
                    )
                    fig_combinations.update_layout(xaxis_tickangle=-45)
                    st.plotly_chart(fig_combinations, use_container_width=True)

                    # Export button
                    if st.button("📥 Export Combination Analysis"):
                        aggregator.write_combination_analysis_csv(top_combinations)
                        st.success("✅ Combination analysis exported to `combination_analysis.csv`")

                elif aggregator:
                    st.warning("No combination data available. Run a backtest first.")
                else:
                    st.info("💡 Combination analysis is generated automatically during backtests.")
                    st.markdown("**To view combination analysis:**")
                    st.code("python run_backtest_prompt_hash.py")
                    st.markdown("**Then check:** `combination_analysis.csv`")
        else:
            st.warning("No trade data available for combination analysis.")

    with tab6:
        # Interactive Backtest functionality
        st.markdown("### 🎯 Interactive Backtest")

        # Symbol and timeframe selection
        col1, col2 = st.columns(2)

        with col1:
            if summary_df is not None and not summary_df.empty:
                available_symbols = sorted(summary_df['symbol'].unique())
                selected_symbol = st.selectbox("Select Symbol", available_symbols, key="backtest_symbol")
            else:
                selected_symbol = st.selectbox("Select Symbol", ["BTCUSDT", "ETHUSDT"], key="backtest_symbol")

        with col2:
            if summary_df is not None and not summary_df.empty:
                available_timeframes = sorted(summary_df['timeframe'].unique())
                selected_timeframe = st.selectbox("Select Timeframe", available_timeframes, key="backtest_timeframe")
            else:
                selected_timeframe = st.selectbox("Select Timeframe", ["1h", "4h", "1d"], key="backtest_timeframe")

        # Candlestick chart display
        st.markdown("### 📊 Candlestick Chart")

        # Chart configuration controls
        col1, col2, col3 = st.columns(3)

        with col1:
            candle_limit_options = [100, 500, 1000, 2000, 5000, 10000]
            selected_limit = st.selectbox(
                "Number of Candles",
                candle_limit_options,
                index=1,  # Default to 500
                key="candle_limit"
            )

        with col2:
            show_volume = st.checkbox("Show Volume", value=False, key="show_volume")

        with col3:
            chart_height = st.slider("Chart Height", min_value=300, max_value=800, value=500, step=50, key="chart_height")

        # Fetch and display candles
        if CandleCacheDatabase and selected_symbol and selected_timeframe:
            try:
                db = CandleCacheDatabase()

                # Get candles with user-selected limit
                candles = db.get_candles_after_timestamp(selected_symbol, selected_timeframe, 0, limit=selected_limit)

                if candles:
                    # Convert to DataFrame for plotting
                    candles_df = pd.DataFrame(candles)

                    # Convert timestamp to datetime
                    candles_df['datetime'] = pd.to_datetime(candles_df['start_time'], unit='ms')

                    # Sort by datetime to ensure proper order
                    candles_df = candles_df.sort_values('datetime')

                    # Create candlestick chart
                    if show_volume and 'volume' in candles_df.columns:
                        # Create subplot with volume
                        fig = make_subplots(
                            rows=2, cols=1,
                            shared_xaxes=True,
                            vertical_spacing=0.03,
                            subplot_titles=(f'{selected_symbol} {selected_timeframe}', 'Volume'),
                            row_width=[0.7, 0.3]
                        )

                        # Add candlestick
                        fig.add_trace(go.Candlestick(
                            x=candles_df['datetime'],
                            open=candles_df['open_price'],
                            high=candles_df['high_price'],
                            low=candles_df['low_price'],
                            close=candles_df['close_price'],
                            name=f'{selected_symbol} {selected_timeframe}'
                        ), row=1, col=1)

                        # Add volume bar chart
                        fig.add_trace(go.Bar(
                            x=candles_df['datetime'],
                            y=candles_df['volume'],
                            name='Volume',
                            marker_color='rgba(158,158,158,0.8)'
                        ), row=2, col=1)

                        # Update layout
                        fig.update_layout(
                            height=chart_height,
                            xaxis_rangeslider_visible=False,
                            showlegend=False
                        )

                        # Update y-axes
                        fig.update_yaxes(title_text="Price", row=1, col=1)
                        fig.update_yaxes(title_text="Volume", row=2, col=1)
                        fig.update_xaxes(title_text="Time", row=2, col=1)

                    else:
                        # Simple candlestick chart
                        fig = go.Figure(data=[go.Candlestick(
                            x=candles_df['datetime'],
                            open=candles_df['open_price'],
                            high=candles_df['high_price'],
                            low=candles_df['low_price'],
                            close=candles_df['close_price'],
                            name=f'{selected_symbol} {selected_timeframe}'
                        )])

                        fig.update_layout(
                            title=f'{selected_symbol} {selected_timeframe} Candlestick Chart ({len(candles_df)} candles)',
                            yaxis_title='Price',
                            xaxis_title='Time',
                            height=chart_height,
                            xaxis_rangeslider_visible=True
                        )

                    st.plotly_chart(fig, use_container_width=True)

                    # Show data range info
                    if len(candles_df) > 0:
                        start_date = candles_df['datetime'].min().strftime('%Y-%m-%d %H:%M')
                        end_date = candles_df['datetime'].max().strftime('%Y-%m-%d %H:%M')
                        st.info(f"📅 Showing data from {start_date} to {end_date} ({len(candles_df)} candles)")

                else:
                    st.info(f"No candle data available for {selected_symbol} {selected_timeframe}. Data will be fetched when backtest runs.")

            except Exception as e:
                st.error(f"Error loading candle data: {e}")
                st.info("Candlestick chart requires candle data from backtests.")
        else:
            st.warning("Candle database not available. Please ensure database_utils.py is properly configured.")

        # Backtest functionality
        st.markdown("### 🔄 Backtest Controls")

        # Import backtest orchestrator
        backtest_orchestrator = None
        try:
            # Try to import backtest orchestrator using multiple methods
            backtest_orchestrator = None

            # Method 1: Try direct import from core
            try:
                from prompt_performance.core.backtest_orchestrator import BacktestOrchestrator
                backtest_orchestrator = BacktestOrchestrator()
            except ImportError:
                pass

            # Method 2: Try relative import
            if backtest_orchestrator is None:
                try:
                    from ..core.backtest_orchestrator import BacktestOrchestrator
                    backtest_orchestrator = BacktestOrchestrator()
                except ImportError:
                    pass

            # Method 3: Try importlib with absolute path
            if backtest_orchestrator is None:
                try:
                    import importlib.util
                    import os

                    # Find the backtest_orchestrator.py file
                    script_dir = Path(__file__).resolve().parent
                    core_dir = script_dir.parent / "core"
                    orchestrator_path = core_dir / "backtest_orchestrator.py"

                    if orchestrator_path.exists():
                        spec = importlib.util.spec_from_file_location("backtest_orchestrator", orchestrator_path)
                        if spec and spec.loader:
                            module = importlib.util.module_from_spec(spec)
                            spec.loader.exec_module(module)
                            backtest_orchestrator = module.BacktestOrchestrator()
                except Exception:
                    pass

        except Exception as e:
            st.warning(f"Could not load backtest orchestrator: {e}")
            backtest_orchestrator = None
        except ImportError:
            st.warning("Backtest orchestrator not available. Please ensure core modules are properly configured.")

        col1, col2, col3 = st.columns(3)

        with col1:
            backtest_type = st.selectbox(
                "Backtest Type",
                ["Standard", "Prompt Hash"],
                key="backtest_type"
            )

        with col2:
            limit_records = st.number_input(
                "Limit Records",
                min_value=10,
                max_value=10000,
                value=1000,
                step=100,
                key="limit_records"
            )

        with col3:
            if st.button("Run Targeted Backtest", key="run_backtest"):
                if backtest_orchestrator:
                    with st.spinner("Running backtest..."):
                        try:
                            if backtest_type == "Prompt Hash":
                                result = backtest_orchestrator.run_backtest_with_prompt_hash(limit=limit_records)
                            else:
                                result = backtest_orchestrator.run_backtest(limit=limit_records)

                            if result['success']:
                                st.success("✅ Backtest completed successfully!")
                                st.json(result)
                            else:
                                st.error(f"❌ Backtest failed: {result.get('error', 'Unknown error')}")

                        except Exception as e:
                            st.error(f"❌ Error running backtest: {e}")
                else:
                    st.error("Backtest orchestrator not available")

        # Backtest results visualization
        st.markdown("### 📈 Backtest Results")

        # Check if results files exist and display them
        results_dir = Path(__file__).parent.parent
        summary_file = results_dir / "summary.csv"
        trades_file = results_dir / "trade_logs.csv"

        if summary_file.exists() and trades_file.exists():
            try:
                # Load the latest results
                latest_summary = pd.read_csv(summary_file)
                latest_trades = pd.read_csv(trades_file)

                if not latest_summary.empty and not latest_trades.empty:
                    st.success("📊 Latest backtest results loaded successfully!")

                    # Overall metrics
                    total_trades = len(latest_trades)
                    wins = len(latest_trades[latest_trades['outcome'] == 'win'])
                    losses = len(latest_trades[latest_trades['outcome'] == 'loss'])
                    expired = len(latest_trades[latest_trades['outcome'] == 'expired'])
                    win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0

                    col1, col2, col3, col4, col5 = st.columns(5)
                    with col1:
                        st.metric("Total Trades", f"{total_trades:,}")
                    with col2:
                        st.metric("Wins", f"{wins:,}")
                    with col3:
                        st.metric("Losses", f"{losses:,}")
                    with col4:
                        st.metric("Expired", f"{expired:,}")
                    with col5:
                        st.metric("Win Rate", f"{win_rate:.1%}")

                    # Performance visualization
                    if not latest_summary.empty:
                        st.markdown("#### 📊 Performance by Combination")

                        # Create a bar chart of win rates
                        fig_win_rate = px.bar(
                            latest_summary,
                            x='symbol',
                            y='win_rate',
                            color='timeframe',
                            title='Win Rate by Symbol and Timeframe',
                            labels={'win_rate': 'Win Rate', 'symbol': 'Symbol'},
                            barmode='group'
                        )
                        fig_win_rate.update_layout(xaxis_tickangle=-45)
                        st.plotly_chart(fig_win_rate, use_container_width=True)

                        # Trade outcomes over time (if timestamp available)
                        if 'timestamp' in latest_trades.columns and not latest_trades['timestamp'].isna().all():
                            st.markdown("#### 📅 Trade Outcomes Over Time")
                            latest_trades['timestamp'] = pd.to_datetime(latest_trades['timestamp'], errors='coerce')
                            trades_over_time = latest_trades.groupby(latest_trades['timestamp'].dt.date)['outcome'].value_counts().unstack().fillna(0)

                            if not trades_over_time.empty:
                                fig_time = px.line(
                                    trades_over_time,
                                    title='Trade Outcomes Over Time',
                                    labels={'value': 'Number of Trades', 'timestamp': 'Date'},
                                    color_discrete_map={'win': 'green', 'loss': 'red', 'expired': 'orange'}
                                )
                                st.plotly_chart(fig_time, use_container_width=True)

                else:
                    st.info("No backtest results available. Run a backtest first.")

            except Exception as e:
                st.error(f"Error loading backtest results: {e}")
        else:
            st.info("No backtest results found. Run a backtest to see results here.")

def create_trade_analysis(trades_df, analysis_type="version"):
    """Create detailed trade analysis section."""
    if trades_df is None:
        return

    st.markdown("---")
    st.markdown("## 🔍 Trade Analysis")

    # Filters
    col1, col2, col3 = st.columns(3)

    with col1:
        # Filter by outcome
        outcomes = ['All'] + list(trades_df['outcome'].unique())
        selected_outcome = st.selectbox("Filter by Outcome", outcomes)

    with col2:
        # Filter by prompt version/hash
        filter_col = 'prompt_hash' if analysis_type == "hash" else 'prompt_version'
        filter_label = 'Prompt Hash' if analysis_type == "hash" else 'Prompt Version'

        if filter_col in trades_df.columns:
            filter_options = ['All'] + list(trades_df[filter_col].unique())
            selected_prompt = st.selectbox(f"Filter by {filter_label}", filter_options)
        else:
            selected_prompt = 'All'

    with col3:
        # Filter by symbol
        symbols = ['All'] + list(trades_df['symbol'].unique())
        selected_symbol = st.selectbox("Filter by Symbol", symbols)

    # Apply filters
    filtered_df = trades_df.copy()

    if selected_outcome != 'All':
        filtered_df = filtered_df[filtered_df['outcome'] == selected_outcome]

    if selected_prompt != 'All':
        filtered_df = filtered_df[filtered_df[filter_col] == selected_prompt]

    if selected_symbol != 'All':
        filtered_df = filtered_df[filtered_df['symbol'] == selected_symbol]

    # Display filtered results
    st.markdown(f"### 📋 Filtered Trades ({len(filtered_df)} results)")

    if not filtered_df.empty:
        # Format for display
        display_df = filtered_df.copy()
        display_df['entry_price'] = display_df['entry_price'].apply(lambda x: f"{x:.4f}")
        display_df['stop_loss'] = display_df['stop_loss'].apply(lambda x: f"{x:.4f}")
        display_df['take_profit'] = display_df['take_profit'].apply(lambda x: f"{x:.4f}")
        display_df['achieved_rr'] = display_df['achieved_rr'].apply(lambda x: f"{x:.2f}")
        display_df['confidence'] = display_df['confidence'].apply(lambda x: f"{x:.2%}")

        st.dataframe(display_df, use_container_width=True)

        # Summary statistics for filtered data
        st.markdown("### 📊 Filtered Data Summary")
        col1, col2, col3, col4 = st.columns(4)

        wins = len(filtered_df[filtered_df['outcome'] == 'win'])
        losses = len(filtered_df[filtered_df['outcome'] == 'loss'])
        win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0

        with col1:
            st.metric("Filtered Trades", len(filtered_df))

        with col2:
            st.metric("Wins", wins)

        with col3:
            st.metric("Losses", losses)

        with col4:
            st.metric("Win Rate", f"{win_rate:.1%}")

def create_symbol_performance(summary_df):
    """Create symbol performance analysis."""
    if summary_df is None:
        return

    st.markdown("---")
    st.markdown("## 🏆 Symbol Performance")

    if not summary_df.empty and 'symbol' in summary_df.columns:
        # Group by symbol
        symbol_performance = summary_df.groupby('symbol').agg({
            'total_trades': 'sum',
            'win_rate': 'mean',
            'avg_rr': 'mean',
            'profit_factor': 'mean'
        }).reset_index()

        # Sort by win rate
        symbol_performance = symbol_performance.sort_values('win_rate', ascending=False)

        # Create performance chart
        fig_symbol = px.bar(
            symbol_performance,
            x='symbol',
            y='win_rate',
            title='Win Rate by Symbol',
            labels={'win_rate': 'Win Rate', 'symbol': 'Symbol'},
            color='win_rate',
            color_continuous_scale='RdYlGn'
        )
        fig_symbol.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig_symbol, use_container_width=True)

        # Symbol performance table
        st.markdown("### 📋 Symbol Performance Table")
        display_df = symbol_performance.copy()
        display_df['win_rate'] = display_df['win_rate'].apply(lambda x: f"{x:.1%}")
        display_df['avg_rr'] = display_df['avg_rr'].apply(lambda x: f"{x:.2f}")
        display_df['profit_factor'] = display_df['profit_factor'].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "∞")

        st.dataframe(display_df, use_container_width=True)

def create_prompt_viewer(analysis_type="version"):
    """Create prompt viewing section for hash analysis."""
    if analysis_type != "hash" or CandleCacheDatabase is None:
        return

    st.markdown("---")
    st.markdown("## 📝 Prompt Viewer")

    try:
        db = CandleCacheDatabase()
        all_mappings = db.get_all_prompt_mappings()

        if not all_mappings:
            st.warning("No prompt mappings found in database.")
            return

        # Create a dataframe for display
        prompt_data = []
        for prompt_hash, prompt_text in all_mappings.items():
            # Get metadata for this hash
            metadata = db.get_prompt_metadata(prompt_hash)
            prompt_data.append({
                'hash': prompt_hash,
                'prompt_length': len(prompt_text),
                'timeframe': metadata.get('timeframe', 'N/A') if metadata else 'N/A',
                'symbol': metadata.get('symbol', 'N/A') if metadata else 'N/A',
                'prompt_preview': prompt_text[:100] + '...' if len(prompt_text) > 100 else prompt_text
            })

        df = pd.DataFrame(prompt_data)

        # Display summary
        st.markdown(f"**Total Prompts:** {len(df)}")

        # Hash selector
        selected_hash = st.selectbox(
            "Select Prompt Hash to View:",
            options=df['hash'].tolist(),
            format_func=lambda x: f"{x} (Length: {df[df['hash']==x]['prompt_length'].iloc[0]})"
        )

        if selected_hash:
            # Get full prompt text
            full_prompt = all_mappings[selected_hash]
            metadata = db.get_prompt_metadata(selected_hash)

            col1, col2 = st.columns([2, 1])

            with col1:
                st.markdown("### 📄 Full Prompt Text")
                st.text_area(
                    "Prompt Content:",
                    value=full_prompt,
                    height=400,
                    disabled=True,
                    key=f"prompt_{selected_hash}"
                )

            with col2:
                st.markdown("### ℹ️ Prompt Metadata")
                if metadata:
                    st.markdown(f"**Timeframe:** {metadata.get('timeframe', 'N/A')}")
                    st.markdown(f"**Symbol:** {metadata.get('symbol', 'N/A')}")
                else:
                    st.markdown("*No metadata available*")

                st.markdown(f"**Hash:** `{selected_hash}`")
                st.markdown(f"**Length:** {len(full_prompt)} characters")

                # Copy button (using text area for easy copying)
                st.markdown("### 📋 Copy Prompt")
                st.text_area(
                    "Copy the prompt below:",
                    value=full_prompt,
                    height=200,
                    key=f"copy_{selected_hash}",
                    help="Select all text (Ctrl+A) and copy (Ctrl+C) to use this prompt elsewhere"
                )

        # Display all prompts table
        st.markdown("### 📋 All Prompts Overview")
        display_df = df.copy()
        display_df['hash'] = display_df['hash'].apply(lambda x: f"`{x}`")
        st.dataframe(display_df, use_container_width=True)

    except Exception as e:
        st.error(f"Error loading prompt data: {e}")
        st.info("Make sure the database is properly set up and contains prompt mappings.")

def create_realtime_backtest_visualization(trades_df, analysis_type="version"):
    """Create real-time backtest visualization showing progress through candles."""
    if trades_df is None or trades_df.empty:
        return

    st.markdown("---")
    st.markdown("## 📊 Real-Time Backtest Visualization")

    # Check for required columns
    required_columns = ['timestamp', 'outcome']
    missing_columns = [col for col in required_columns if col not in trades_df.columns]

    if missing_columns:
        st.error(f"❌ Missing required columns: {missing_columns}")
        st.markdown("**Available columns:**")
        st.code(str(list(trades_df.columns)))
        return

    # Debug: Show available columns
    with st.expander("🔍 Debug Info (Click to expand)"):
        st.markdown(f"**Available columns:** {list(trades_df.columns)}")
        st.markdown(f"**DataFrame shape:** {trades_df.shape}")
        if len(trades_df) > 0:
            st.markdown("**Sample row:**")
            st.json(dict(trades_df.iloc[0]))

    # Convert timestamp to datetime if needed
    if 'timestamp' in trades_df.columns:
        trades_df = trades_df.copy()
        if not pd.api.types.is_datetime64_any_dtype(trades_df['timestamp']):
            trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'], errors='coerce')

        # Filter out invalid timestamps
        trades_df = trades_df.dropna(subset=['timestamp'])

        if trades_df.empty:
            st.warning("No valid timestamp data available for visualization.")
            return

        # Sort by timestamp
        trades_df = trades_df.sort_values('timestamp')

        # Create time series visualization
        st.markdown("### 📈 Backtest Timeline")

        # Create a figure showing trades over time
        fig = go.Figure()

        # Add background for the entire time period
        time_min = trades_df['timestamp'].min()
        time_max = trades_df['timestamp'].max()

        # Add win/loss markers
        wins = trades_df[trades_df['outcome'] == 'win']
        losses = trades_df[trades_df['outcome'] == 'loss']
        expired = trades_df[trades_df['outcome'] == 'expired']

        # Helper function to create hover text safely
        def create_hover_text(row):
            symbol = row.get('symbol', 'N/A')
            timeframe = row.get('timeframe', 'N/A')
            rr = row.get('achieved_rr', 0)
            confidence = row.get('confidence', 0)
            return f"Symbol: {symbol}<br>Timeframe: {timeframe}<br>RR: {rr:.2f}<br>Confidence: {confidence:.1%}"

        # Add win trades
        if not wins.empty:
            fig.add_trace(go.Scatter(
                x=wins['timestamp'],
                y=[1] * len(wins),  # Fixed y position for wins
                mode='markers',
                name='Wins',
                marker=dict(
                    symbol='triangle-up',
                    size=10,
                    color='green',
                    line=dict(width=2, color='darkgreen')
                ),
                text=wins.apply(create_hover_text, axis=1),
                hovertemplate="Win<br>%{text}<extra></extra>"
            ))

        # Add loss trades
        if not losses.empty:
            fig.add_trace(go.Scatter(
                x=losses['timestamp'],
                y=[0] * len(losses),  # Fixed y position for losses
                mode='markers',
                name='Losses',
                marker=dict(
                    symbol='triangle-down',
                    size=10,
                    color='red',
                    line=dict(width=2, color='darkred')
                ),
                text=losses.apply(create_hover_text, axis=1),
                hovertemplate="Loss<br>%{text}<extra></extra>"
            ))

        # Add expired trades
        if not expired.empty:
            fig.add_trace(go.Scatter(
                x=expired['timestamp'],
                y=[-1] * len(expired),  # Fixed y position for expired
                mode='markers',
                name='Expired',
                marker=dict(
                    symbol='circle',
                    size=8,
                    color='orange',
                    line=dict(width=2, color='darkorange')
                ),
                text=expired.apply(lambda row: f"Symbol: {row.get('symbol', 'N/A')}<br>Timeframe: {row.get('timeframe', 'N/A')}<br>Confidence: {row.get('confidence', 0):.1%}", axis=1),
                hovertemplate="Expired<br>%{text}<extra></extra>"
            ))

        # Update layout
        fig.update_layout(
            title="Backtest Progress Timeline",
            xaxis_title="Time",
            yaxis_title="Trade Outcome",
            yaxis=dict(
                tickmode='array',
                tickvals=[1, 0, -1],
                ticktext=['Wins', 'Losses', 'Expired'],
                range=[-1.5, 1.5]
            ),
            height=400,
            showlegend=True
        )

        # Add range slider
        fig.update_xaxes(rangeslider_visible=True)

        st.plotly_chart(fig, use_container_width=True)

        # Statistics over time
        st.markdown("### 📊 Cumulative Performance Over Time")

        # Calculate cumulative metrics
        trades_df['cumulative_trades'] = range(1, len(trades_df) + 1)
        trades_df['cumulative_wins'] = (trades_df['outcome'] == 'win').cumsum()
        trades_df['cumulative_win_rate'] = trades_df['cumulative_wins'] / trades_df['cumulative_trades']

        col1, col2 = st.columns(2)

        with col1:
            # Cumulative win rate over time
            fig_cum_win_rate = px.line(
                trades_df,
                x='timestamp',
                y='cumulative_win_rate',
                title='Cumulative Win Rate Over Time',
                labels={'cumulative_win_rate': 'Win Rate', 'timestamp': 'Time'}
            )
            fig_cum_win_rate.update_layout(height=300)
            st.plotly_chart(fig_cum_win_rate, use_container_width=True)

        with col2:
            # Trade frequency over time
            daily_trades = trades_df.groupby(trades_df['timestamp'].dt.date).size().reset_index(name='trades')

            fig_frequency = px.bar(
                daily_trades,
                x='timestamp',
                y='trades',
                title='Daily Trade Frequency',
                labels={'trades': 'Number of Trades', 'timestamp': 'Date'}
            )
            fig_frequency.update_layout(height=300)
            st.plotly_chart(fig_frequency, use_container_width=True)

        # Progress metrics
        st.markdown("### 📈 Backtest Progress Summary")

        total_days = (time_max - time_min).days if pd.notna(time_max) and pd.notna(time_min) else 0
        total_trades = len(trades_df)
        avg_trades_per_day = total_trades / max(total_days, 1)

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Total Time Period", f"{total_days} days")

        with col2:
            st.metric("Total Trades", f"{total_trades:,}")

        with col3:
            st.metric("Avg Trades/Day", f"{avg_trades_per_day:.1f}")

        with col4:
            latest_date = trades_df['timestamp'].max().strftime('%Y-%m-%d') if pd.notna(trades_df['timestamp'].max()) else 'N/A'
            st.metric("Latest Trade Date", latest_date)

def main():
    """Main Streamlit app."""
    # Analysis type selector
    st.sidebar.markdown("## 🎛️ Dashboard Controls")
    analysis_type = st.sidebar.radio(
        "Analysis Type",
        ["Version Analysis", "Prompt Hash Analysis"],
        help="Choose between prompt version analysis (existing) or prompt hash analysis (new)"
    )

    # Convert to internal type
    internal_type = "hash" if analysis_type == "Prompt Hash Analysis" else "version"

    # Load data based on selection
    summary_df, trades_df = load_data(internal_type)

    if summary_df is not None and trades_df is not None:
        # Refresh button
        if st.sidebar.button("🔄 Refresh Data"):
            st.rerun()

        # About section
        st.sidebar.markdown("---")
        st.sidebar.markdown("## ℹ️ About")
        if internal_type == "hash":
            st.sidebar.markdown("""
            **Prompt Hash Analysis Dashboard**

            This dashboard analyzes trading performance by prompt content using MD5 hashes.

            **Features:**
            - 📊 Performance metrics by prompt hash
            - 📈 Interactive charts and graphs
            - 🔍 Detailed trade analysis
            - 🏆 Symbol performance comparison
            - 📋 Hash-to-prompt mapping
            """)
        else:
            st.sidebar.markdown("""
            **Prompt Version Analysis Dashboard**

            This dashboard visualizes the results of trading prompt performance backtesting.

            **Features:**
            - 📊 Performance metrics overview
            - 📈 Interactive charts and graphs
            - 🔍 Detailed trade analysis
            - 🏆 Symbol performance comparison
            - 📋 Filterable data tables
            """)

        # Main content
        create_overview_metrics(summary_df, trades_df, internal_type)
        create_performance_charts(summary_df, trades_df, internal_type)
        create_trade_analysis(trades_df, internal_type)
        create_symbol_performance(summary_df)

        # Add real-time backtest visualization
        create_realtime_backtest_visualization(trades_df, internal_type)

        # Add prompt viewer for hash analysis
        if internal_type == "hash":
            create_prompt_viewer(internal_type)

        # Footer
        st.markdown("---")
        st.markdown("### 📄 Data Sources")
        if internal_type == "hash":
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("**Summary Data:** `summary_prompt_hash.csv`")
            with col2:
                st.markdown("**Trade Logs:** `trade_logs_prompt_hash.csv`")
        else:
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("**Summary Data:** `summary.csv`")
            with col2:
                st.markdown("**Trade Logs:** `trade_logs.csv`")

        st.markdown(f"**Last Updated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def create_prompt_performance_insights(trades_df):
    """Create focused prompt performance insights."""
    if trades_df is None or trades_df.empty:
        return

    st.markdown("---")
    st.markdown("## 🎯 Prompt Performance Insights")

    # Calculate prompt-level performance
    if 'prompt_hash' in trades_df.columns:
        prompt_performance = trades_df.groupby('prompt_hash').agg({
            'outcome': lambda x: (x == 'win').mean(),
            'entry_price': 'count',
            'achieved_rr': 'mean'
        }).round(4)

        prompt_performance = prompt_performance.rename(columns={
            'outcome': 'win_rate',
            'entry_price': 'total_trades',
            'achieved_rr': 'avg_rr'
        }).reset_index()

        # Sort by win rate
        prompt_performance = prompt_performance.sort_values('win_rate', ascending=False)

        if not prompt_performance.empty:
            st.markdown("### 🏆 Best Performing Prompts")

            # Top 10 prompts
            top_prompts = prompt_performance.head(10).copy()
            top_prompts['rank'] = range(1, len(top_prompts) + 1)

            # Format for display
            display_df = top_prompts[['rank', 'prompt_hash', 'win_rate', 'total_trades', 'avg_rr']].copy()
            display_df['win_rate'] = display_df['win_rate'].apply(lambda x: f"{x:.1%}")
            display_df['avg_rr'] = display_df['avg_rr'].apply(lambda x: f"{x:.2f}")

            st.dataframe(display_df, use_container_width=True)

            # Performance distribution
            st.markdown("### 📊 Prompt Performance Distribution")
            fig_dist = px.histogram(
                prompt_performance,
                x='win_rate',
                title='Distribution of Prompt Win Rates',
                labels={'win_rate': 'Win Rate'},
                color_discrete_sequence=['#1f77b4']
            )
            st.plotly_chart(fig_dist, use_container_width=True)

if __name__ == "__main__":
    main()