import unittest
import tempfile
import os
import sqlite3
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
import sys
sys.path.insert(0, str(project_root))

from prompt_performance.core.database_utils import CandleCacheDatabase
from prompt_performance.core.data_loader import AnalysisDataLoader
from prompt_performance.core.candle_fetcher import CandleFetcher
from prompt_performance.core.trade_simulator import TradeSimulator
from prompt_performance.core.metrics_aggregator import MetricsAggregator
from prompt_performance.core.backtest_orchestrator import BacktestOrchestrator

class TestCandleCacheDatabase(unittest.TestCase):
    """Test CandleCacheDatabase functionality."""

    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False)
        self.temp_db.close()
        self.db_path = self.temp_db.name
        self.db = CandleCacheDatabase(self.db_path)

    def tearDown(self):
        os.unlink(self.db_path)

    def test_insert_and_retrieve_candles(self):
        """Test inserting and retrieving candles."""
        candles = [
            {
                'start_time': 1000000,
                'open_price': 50000.0,
                'high_price': 51000.0,
                'low_price': 49000.0,
                'close_price': 50500.0,
                'volume': 100.0,
                'turnover': 5000000.0
            }
        ]

        self.db.insert_candles(candles, 'BTCUSDT', '1h', 'linear')

        # Test retrieval
        retrieved = self.db.get_candles_after_timestamp('BTCUSDT', '1h', 999000)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0]['close_price'], 50500.0)

    def test_duplicate_prevention(self):
        """Test that duplicate candles are not inserted."""
        candles = [
            {
                'start_time': 1000000,
                'open_price': 50000.0,
                'high_price': 51000.0,
                'low_price': 49000.0,
                'close_price': 50500.0,
                'volume': 100.0,
                'turnover': 5000000.0
            }
        ]

        # Insert twice
        self.db.insert_candles(candles, 'BTCUSDT', '1h', 'linear')
        self.db.insert_candles(candles, 'BTCUSDT', '1h', 'linear')

        count = self.db.get_candle_count('BTCUSDT', '1h')
        self.assertEqual(count, 1)

class TestAnalysisDataLoader(unittest.TestCase):
    """Test AnalysisDataLoader functionality."""

    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False)
        self.temp_db.close()
        self.db_path = self.temp_db.name

        # Create test database with sample data
        self._create_test_analysis_db()

    def tearDown(self):
        os.unlink(self.db_path)

    def _create_test_analysis_db(self):
        """Create a test analysis database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create analysis_results table
        cursor.execute("""
            CREATE TABLE analysis_results (
                id TEXT,
                symbol TEXT,
                timeframe TEXT,
                timestamp TEXT,
                recommendation TEXT,
                entry_price REAL,
                stop_loss REAL,
                take_profit REAL,
                confidence REAL,
                analysis_data TEXT
            )
        """)

        # Insert test data
        test_records = [
            ('1', 'BTCUSDT', '1h', '2024-01-01T00:00:00Z', 'buy', 50000.0, 49000.0, 52000.0, 0.8,
             '{"prompt_version": "v1.0"}'),
            ('2', 'ETHUSDT', '30m', '2024-01-01T00:00:00Z', 'sell', 3000.0, 3100.0, 2900.0, 0.7,
             '{"prompt_version": "v1.0"}'),
            ('3', 'BTCUSDT', '1h', '2024-01-01T00:00:00Z', 'hold', 50000.0, 49000.0, 52000.0, 0.6,
             '{"prompt_version": "v1.0"}'),  # Should be filtered out
        ]

        cursor.executemany("""
            INSERT INTO analysis_results
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, test_records)

        conn.commit()
        conn.close()

    def test_load_filtered_records(self):
        """Test loading and filtering records."""
        loader = AnalysisDataLoader(self.db_path)
        records = loader.load_filtered_records()

        # Should have 2 records (hold filtered out)
        self.assertEqual(len(records), 2)

        # Check first record
        self.assertEqual(records[0]['symbol'], 'BTCUSDT')
        self.assertEqual(records[0]['recommendation'], 'buy')
        self.assertEqual(records[0]['analysis_data']['prompt_version'], 'v1.0')

    def test_group_records_by_symbol_timeframe(self):
        """Test grouping records."""
        loader = AnalysisDataLoader(self.db_path)
        records = loader.load_filtered_records()
        groups = loader.group_records_by_symbol_timeframe(records)

        self.assertIn('BTCUSDT_1h', groups)
        self.assertIn('ETHUSDT_30m', groups)
        self.assertEqual(len(groups['BTCUSDT_1h']), 1)

class TestTradeSimulator(unittest.TestCase):
    """Test TradeSimulator functionality."""

    def setUp(self):
        self.simulator = TradeSimulator()

    def test_simulate_buy_trade_win(self):
        """Test buy trade that wins."""
        record = {
            'recommendation': 'buy',
            'entry_price': 50000.0,
            'stop_loss': 49000.0,
            'take_profit': 52000.0
        }

        candles = [
            {
                'high_price': 52500.0,  # Hits take profit
                'low_price': 49500.0,
                'close_price': 52500.0
            }
        ]

        result = self.simulator.simulate_trade(record, candles)

        self.assertEqual(result['outcome'], 'win')
        self.assertEqual(result['duration_candles'], 1)
        self.assertEqual(result['exit_price'], 52000.0)

    def test_simulate_buy_trade_loss(self):
        """Test buy trade that loses."""
        record = {
            'recommendation': 'buy',
            'entry_price': 50000.0,
            'stop_loss': 49000.0,
            'take_profit': 52000.0
        }

        candles = [
            {
                'high_price': 50500.0,
                'low_price': 48500.0,  # Hits stop loss
                'close_price': 48500.0
            }
        ]

        result = self.simulator.simulate_trade(record, candles)

        self.assertEqual(result['outcome'], 'loss')
        self.assertEqual(result['exit_price'], 49000.0)

    def test_simulate_sell_trade_win(self):
        """Test sell trade that wins."""
        record = {
            'recommendation': 'sell',
            'entry_price': 50000.0,
            'stop_loss': 51000.0,
            'take_profit': 48000.0
        }

        candles = [
            {
                'high_price': 50500.0,
                'low_price': 47500.0,  # Hits take profit
                'close_price': 47500.0
            }
        ]

        result = self.simulator.simulate_trade(record, candles)

        self.assertEqual(result['outcome'], 'win')
        self.assertEqual(result['exit_price'], 48000.0)

    def test_simulate_trade_expired(self):
        """Test trade that expires without hitting SL or TP."""
        record = {
            'recommendation': 'buy',
            'entry_price': 50000.0,
            'stop_loss': 49000.0,
            'take_profit': 52000.0
        }

        candles = [
            {
                'high_price': 50500.0,
                'low_price': 49500.0,  # No hit
                'close_price': 50000.0
            }
        ]

        result = self.simulator.simulate_trade(record, candles)

        self.assertEqual(result['outcome'], 'expired')
        self.assertEqual(result['duration_candles'], 1)

class TestMetricsAggregator(unittest.TestCase):
    """Test MetricsAggregator functionality."""

    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.aggregator = MetricsAggregator(self.temp_dir)

    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_calculate_metrics(self):
        """Test metrics calculation."""
        # Create mock trade results
        results = [
            {
                'outcome': 'win',
                'achieved_rr': 2.0,
                'take_profit': 52000.0,
                'entry_price': 50000.0,
                'stop_loss': 49000.0
            },
            {
                'outcome': 'loss',
                'achieved_rr': 0.0,
                'take_profit': 52000.0,
                'entry_price': 50000.0,
                'stop_loss': 49000.0
            },
            {
                'outcome': 'win',
                'achieved_rr': 1.5,
                'take_profit': 51500.0,
                'entry_price': 50000.0,
                'stop_loss': 49000.0
            }
        ]

        # Test aggregation by prompt version
        aggregated = self.aggregator.aggregate_by_prompt_version([
            {**r, 'prompt_version': 'v1.0'} for r in results
        ])

        self.assertEqual(len(aggregated), 1)
        self.assertEqual(aggregated[0]['total_trades'], 3)
        self.assertEqual(aggregated[0]['win_rate'], 2/3)  # 2 wins out of 3 total

class TestCandleFetcher(unittest.TestCase):
    """Test CandleFetcher functionality."""

    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False)
        self.temp_db.close()
        self.db_path = self.temp_db.name

    def tearDown(self):
        os.unlink(self.db_path)

    @patch('prompt_performance.core.candle_fetcher.BybitAPIManager')
    def test_category_inference(self, mock_api_manager):
        """Test category inference from symbol."""
        fetcher = CandleFetcher()

        # Test linear category
        self.assertEqual(fetcher._infer_category_from_symbol('BTCUSDT'), 'linear')
        self.assertEqual(fetcher._infer_category_from_symbol('ETH.P'), 'linear')

        # Test inverse category
        self.assertEqual(fetcher._infer_category_from_symbol('BTCUSD'), 'inverse')

    @patch('prompt_performance.core.candle_fetcher.BybitAPIManager')
    def test_symbol_normalization(self, mock_api_manager):
        """Test symbol normalization."""
        fetcher = CandleFetcher()

        self.assertEqual(fetcher._normalize_symbol_for_api('BTC.P'), 'BTC')
        self.assertEqual(fetcher._normalize_symbol_for_api('ETH.S'), 'ETH')
        self.assertEqual(fetcher._normalize_symbol_for_api('BTCUSDT'), 'BTCUSDT')

    @patch('prompt_performance.core.candle_fetcher.BybitAPIManager')
    def test_timeframe_mapping(self, mock_api_manager):
        """Test timeframe to interval mapping."""
        fetcher = CandleFetcher()

        self.assertEqual(fetcher._map_timeframe_to_interval('1m'), '1')
        self.assertEqual(fetcher._map_timeframe_to_interval('1h'), '60')
        self.assertEqual(fetcher._map_timeframe_to_interval('1d'), 'D')

if __name__ == '__main__':
    unittest.main()