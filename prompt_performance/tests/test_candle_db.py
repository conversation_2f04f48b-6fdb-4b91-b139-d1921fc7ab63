#!/usr/bin/env python3
"""Test script to reproduce the CandleCacheDatabase error."""

import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from prompt_performance.core.database_utils import CandleCacheDatabase
    print("✅ Successfully imported CandleCacheDatabase")

    # Try to create an instance
    db = CandleCacheDatabase()
    print("✅ Successfully created CandleCacheDatabase instance")

    # Check if methods exist
    if hasattr(db, 'get_available_symbols'):
        print("✅ get_available_symbols method exists")
    else:
        print("❌ get_available_symbols method missing")

    if hasattr(db, 'get_available_timeframes'):
        print("✅ get_available_timeframes method exists")
    else:
        print("❌ get_available_timeframes method missing")

    # Try to call the methods
    try:
        symbols = db.get_available_symbols()
        print(f"✅ get_available_symbols() returned: {symbols}")
    except Exception as e:
        print(f"❌ get_available_symbols() failed: {e}")

    try:
        timeframes = db.get_available_timeframes()
        print(f"✅ get_available_timeframes() returned: {timeframes}")
    except Exception as e:
        print(f"❌ get_available_timeframes() failed: {e}")

except ImportError as e:
    print(f"❌ Import failed: {e}")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
