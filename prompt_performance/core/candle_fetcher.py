import logging
import time
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from trading_bot.core.bybit_api_manager import BybitAPIManager
from trading_bot.config.settings import Config
from .database_utils import CandleCacheDatabase

logger = logging.getLogger(__name__)

class CandleFetcher:
    """Handles fetching and caching candles from Bybit API."""

    def __init__(self, config: Optional[Config] = None, use_testnet: bool = False):
        if config is None:
            # Look for config.yaml in the parent directory of prompt_performance
            import sys
            from pathlib import Path
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config.yaml"
            self.config = Config.from_yaml(str(config_path))
        else:
            self.config = config
        self.api_manager = BybitAPIManager(self.config, use_testnet=use_testnet)
        self.db = CandleCacheDatabase()

    def _infer_category_from_symbol(self, symbol: str) -> str:
        """Infer Bybit category from symbol."""
        symbol_upper = symbol.upper()

        if '.P' in symbol_upper:
            return 'linear'
        elif 'USD' in symbol_upper and not symbol_upper.endswith('USDT'):
            return 'inverse'
        else:
            return 'linear'  # Default

    def _normalize_symbol_for_api(self, symbol: str) -> str:
        """Normalize symbol for Bybit API (remove suffixes if needed)."""
        # Remove common suffixes for API compatibility
        symbol = symbol.upper()
        for suffix in ['.P', '.S']:
            if symbol.endswith(suffix):
                symbol = symbol[:-len(suffix)]
                break
        return symbol

    def _map_timeframe_to_interval(self, timeframe: str) -> str:
        """Map internal timeframe to Bybit API interval."""
        mapping = {
            '1m': '1',
            '5m': '5',
            '15m': '15',
            '30m': '30',
            '1h': '60',
            '4h': '240',
            '1d': 'D',
            '1w': 'W'
        }
        return mapping.get(timeframe, '60')  # Default to 1h

    def _fetch_candles_from_api(self, symbol: str, timeframe: str, start_time: int, limit: int = 1000) -> List[Dict[str, Any]]:
        """Fetch candles from Bybit API with retry logic."""
        category = self._infer_category_from_symbol(symbol)
        api_symbol = self._normalize_symbol_for_api(symbol)
        interval = self._map_timeframe_to_interval(timeframe)

        logger.info(f"Fetching {limit} candles for {symbol} {timeframe} from {start_time} (category: {category}, interval: {interval})")

        # Bybit API parameters
        params = {
            'symbol': api_symbol,
            'interval': interval,
            'start': start_time,
            'limit': limit,
            'category': category
        }

        response = self.api_manager.get_kline(**params)

        if not response or response.get('retCode') != 0:
            logger.error(f"Failed to fetch candles: {response}")
            return []

        candles_data = response.get('result', {}).get('list', [])
        if not candles_data:
            logger.warning("No candles returned from API")
            return []

        # Parse candles from API response
        candles = []
        for candle_data in candles_data:
            try:
                # Bybit returns: [start_time, open, high, low, close, volume, turnover]
                candle = {
                    'start_time': int(candle_data[0]),
                    'open_price': float(candle_data[1]),
                    'high_price': float(candle_data[2]),
                    'low_price': float(candle_data[3]),
                    'close_price': float(candle_data[4]),
                    'volume': float(candle_data[5]),
                    'turnover': float(candle_data[6])
                }
                candles.append(candle)
            except (IndexError, ValueError) as e:
                logger.warning(f"Failed to parse candle data: {candle_data}, error: {e}")
                continue

        logger.info(f"Successfully fetched {len(candles)} candles from API")
        return candles

    def fetch_and_cache_candles(self, symbol: str, timeframe: str, earliest_timestamp: int, max_historical: bool = False) -> bool:
        """Fetch missing candles and cache them. Returns True if successful."""
        # Check current cache state
        latest_cached = self.db.get_latest_candle_timestamp(symbol, timeframe)

        # If max_historical is True, fetch extensive historical data
        if max_historical:
            return self._fetch_maximum_historical_candles(symbol, timeframe)

        # If cache is empty or outdated, fetch new candles
        if latest_cached is None or latest_cached < earliest_timestamp:
            logger.info(f"Cache outdated for {symbol} {timeframe}. Latest cached: {latest_cached}, Need from: {earliest_timestamp}")

            # Fetch up to 1000 candles starting from earliest_timestamp
            candles = self._fetch_candles_from_api(symbol, timeframe, earliest_timestamp, limit=1000)

            if candles:
                category = self._infer_category_from_symbol(symbol)
                self.db.insert_candles(candles, symbol, timeframe, category)
                return True
            else:
                logger.warning(f"No candles fetched for {symbol} {timeframe}")
                return False
        else:
            logger.info(f"Cache is up to date for {symbol} {timeframe}. Latest: {latest_cached}")
            return True

    def _fetch_maximum_historical_candles(self, symbol: str, timeframe: str) -> bool:
        """Fetch maximum historical candles for a symbol/timeframe combination."""
        import time

        logger.info(f"Fetching maximum historical candles for {symbol} {timeframe}")

        # Check current cache state
        latest_cached = self.db.get_latest_candle_timestamp(symbol, timeframe)
        earliest_cached = self.db.get_earliest_candle_timestamp(symbol, timeframe)

        # Get current timestamp in milliseconds
        current_time = int(time.time() * 1000)

        category = self._infer_category_from_symbol(symbol)
        total_fetched = 0

        # Phase 1: Fetch historical data (backwards from earliest point)
        logger.info(f"Phase 1: Fetching historical data for {symbol} {timeframe}")

        # If we have no data, start from 90 days ago
        if latest_cached is None:
            start_time = current_time - (90 * 24 * 60 * 60 * 1000)  # 90 days ago
        else:
            # Start from the earliest cached candle to fill gaps
            start_time = earliest_cached if earliest_cached else current_time - (90 * 24 * 60 * 60 * 1000)

        max_iterations = 100  # Prevent infinite loops
        iteration = 0

        while iteration < max_iterations:
            # Fetch candles in batches
            candles = self._fetch_candles_from_api(symbol, timeframe, start_time, limit=1000)

            if not candles:
                logger.info(f"No more historical candles to fetch for {symbol} {timeframe}")
                break

            # Insert candles into database
            self.db.insert_candles(candles, symbol, timeframe, category)
            total_fetched += len(candles)

            # Get the earliest timestamp from the fetched candles to continue backwards
            earliest_fetched = min(candle['start_time'] for candle in candles)

            # If we're getting the same or newer data, we've reached the limit
            if earliest_fetched >= start_time:
                break

            # Move start_time backwards to fetch older candles
            start_time = earliest_fetched - 1  # Subtract 1ms to avoid duplicates

            iteration += 1

            # Small delay to be respectful to the API
            time.sleep(0.1)

        # Phase 2: Fetch newer data (forwards from latest cached point)
        logger.info(f"Phase 2: Fetching newer data for {symbol} {timeframe}")

        # Get updated latest timestamp after historical fetch
        latest_cached = self.db.get_latest_candle_timestamp(symbol, timeframe)

        if latest_cached is not None:
            # Start fetching from the latest cached timestamp + 1ms
            start_time = latest_cached + 1

            iteration = 0
            while iteration < max_iterations:
                # Fetch candles in batches starting from latest cached + 1ms
                candles = self._fetch_candles_from_api(symbol, timeframe, start_time, limit=1000)

                if not candles:
                    logger.info(f"No more newer candles to fetch for {symbol} {timeframe}")
                    break

                # Insert candles into database
                self.db.insert_candles(candles, symbol, timeframe, category)
                total_fetched += len(candles)

                # Get the latest timestamp from the fetched candles to continue forwards
                latest_fetched = max(candle['start_time'] for candle in candles)

                # If we're getting the same or older data, we've reached the limit
                if latest_fetched <= start_time:
                    break

                # Move start_time forwards to fetch newer candles
                start_time = latest_fetched + 1  # Add 1ms to avoid duplicates

                iteration += 1

                # Small delay to be respectful to the API
                time.sleep(0.1)

        logger.info(f"Successfully fetched {total_fetched} total candles for {symbol} {timeframe}")
        return True

    def get_candles_for_simulation(self, symbol: str, timeframe: str, start_timestamp: int, limit: int = 1000) -> List[Dict[str, Any]]:
        """Get candles from cache for trade simulation."""
        return self.db.get_candles_after_timestamp(symbol, timeframe, start_timestamp, limit)
