cd prompt_performance
python run_backtest_prompt_hash.py


# Prompt Performance Backtest System

A high-performance backtesting system for evaluating trading prompt effectiveness using historical market data from Bybit.

## Overview

This system simulates trades based on analysis recommendations stored in the trading bot database, providing detailed performance metrics for different prompt versions across various symbols and timeframes.

## Features

- **Efficient Caching**: SQLite-based local cache for historical candle data
- **Delta Fetching**: Only fetches missing candles to minimize API calls
- **Trade Simulation**: Realistic simulation of buy/sell trades with proper SL/TP logic
- **Performance Metrics**: Comprehensive metrics including win rate, profit factor, expectancy
- **Dual Analysis Modes**: Support for both prompt version and prompt hash analysis
- **Metadata Extraction**: Automatic extraction of timeframe and symbol from prompts for enhanced analysis
- **Prompt Normalization**: Intelligent removal of dynamic market data for consistent hashing
- **Real-Time Visualization**: Interactive dashboard showing backtest progress over time
- **Scalable Architecture**: Modular design for easy extension and maintenance

## Architecture

```
prompt_performance/
├── core/
│   ├── database_utils.py      # SQLite database operations
│   ├── data_loader.py         # Analysis records loading and filtering
│   ├── candle_fetcher.py      # Bybit API integration and caching
│   ├── trade_simulator.py     # Trade simulation logic
│   ├── metrics_aggregator.py  # Performance calculations and CSV export
│   ├── backtest_orchestrator.py # Main orchestration logic
│   └── utils.py               # Utility functions (hash generation, etc.)
├── tests/
│   └── test_backtest.py       # Comprehensive unit tests
├── docs/
│   └── README.md             # This documentation
├── dashboard/
│   ├── app.py                # Streamlit dashboard (dual analysis modes)
│   └── requirements.txt      # Dashboard dependencies
├── run_backtest.py           # Standard version-based backtest
├── run_backtest_prompt_hash.py # New prompt hash-based backtest
└── candle_cache.db           # SQLite cache database (created automatically)
```

## Metadata Extraction System

The system includes an advanced metadata extraction system that automatically extracts and preserves important information from trading prompts for enhanced analysis.

### Features

- **Automatic Extraction**: Extracts timeframe and symbol information from prompt text
- **Prompt Normalization**: Removes dynamic market data while preserving core trading logic
- **Database Storage**: Stores extracted metadata alongside prompt hashes for analysis
- **Analysis Integration**: Uses metadata for deeper performance comparisons

### How It Works

1. **Prompt Analysis**: Parses prompt text to extract structured metadata
2. **Normalization**: Removes dynamic content (prices, funding rates, etc.) for consistent hashing
3. **Metadata Storage**: Saves extracted timeframe and symbol in database
4. **Analysis Enhancement**: Uses metadata for cross-market performance comparisons

### Extracted Metadata

| Field | Description | Example |
|-------|-------------|---------|
| `timeframe` | Trading timeframe from prompt | `"1h"`, `"30m"`, `"4h"` |
| `symbol` | Trading symbol from prompt | `"BTCUSDT"`, `"ETHUSDT.P"` |

### Benefits

- **Consistent Grouping**: Prompts with same strategy but different market data get same hash
- **Enhanced Analysis**: Compare prompt performance across different timeframes and symbols
- **Data Preservation**: Maintains original prompt context while enabling accurate comparisons
- **Scalable**: Automatically handles new prompts without manual configuration

## Installation

1. Ensure you have the required dependencies:
   - `pybit` for Bybit API
   - `sqlite3` (built-in)
   - `pandas` (optional, for advanced analysis)

2. Configure your Bybit API credentials in the main project's `config.yaml`

## Usage

### Basic Usage

```bash
cd prompt_performance
python run_backtest.py
```

### Prompt Hash Analysis

For analyzing performance by prompt content (using MD5 hashes):

```bash
cd prompt_performance
python run_backtest_prompt_hash.py
```

This mode:
- Uses the dedicated `analysis_prompt` database column
- Groups identical prompts using 5-character MD5 hashes
- Provides deeper analysis of prompt effectiveness
- Maintains full compatibility with existing version-based analysis

### Advanced Options

```bash
# Limit number of records to process
python run_backtest.py --limit 1000
python run_backtest_prompt_hash.py --limit 1000

# Use testnet instead of mainnet
python run_backtest.py --testnet
python run_backtest_prompt_hash.py --testnet

# Enable verbose logging
python run_backtest.py --verbose
python run_backtest_prompt_hash.py --verbose
```

### 📊 Streamlit Dashboard

After running the backtest, visualize your results with the interactive dashboard:

```bash
# Install dashboard dependencies
cd prompt_performance/dashboard
pip install -r requirements.txt

# Run the dashboard
streamlit run app.py
```

The dashboard will be available at `http://localhost:8501` and supports both analysis modes:

#### Version Analysis Mode (Existing)
- **📈 Performance Overview**: Key metrics and win rate analysis by prompt version
- **🎯 Risk-Reward Charts**: Visual breakdown of trading performance
- **📅 Time Series Analysis**: Trade outcomes over time
- **🔍 Interactive Filters**: Filter by outcome, prompt version, and symbol
- **🏆 Symbol Performance**: Compare performance across different symbols
- **📋 Detailed Tables**: Comprehensive data tables with all trade details

#### Prompt Hash Analysis Mode (New)
- **🔢 Hash-based Grouping**: Performance analysis by 5-character MD5 prompt hashes
- **📊 Hash Performance Matrix**: Compare effectiveness of different prompt patterns
- **🔍 Hash-to-Prompt Lookup**: View actual prompt text for any hash
- **📝 Prompt Metadata**: Extracted timeframe and symbol information from prompts
- **🎯 Symbol/Timeframe Analysis**: Performance across different market conditions
- **📈 Confidence Analysis**: How confidence levels affect performance per prompt
- **📋 Interactive Filtering**: Filter by hash, symbol, timeframe, and outcome
- **📊 Real-Time Visualization**: See backtest progress over time with trade markers

### Python API

```python
from prompt_performance.core.backtest_orchestrator import BacktestOrchestrator

orchestrator = BacktestOrchestrator(use_testnet=False)
result = orchestrator.run_backtest(limit=1000)

if result['success']:
    print(f"Processed {result['total_trades']} trades")
    print(f"Win rate: {result['win_rate']:.2%}")
```

## Output Files

The system generates CSV files in the `prompt_performance/` directory:

### Standard Analysis (Version-based)

#### summary.csv
Aggregated performance metrics by prompt version, symbol, and timeframe.

| Column | Description |
|--------|-------------|
| prompt_version | Version identifier of the trading prompt |
| symbol | Trading symbol (e.g., BTCUSDT) |
| timeframe | Timeframe (e.g., 1h, 4h, 1d) |
| total_trades | Total number of simulated trades |
| win_rate | Percentage of winning trades |
| avg_rr | Average risk-reward ratio for winning trades |
| profit_factor | Total profit divided by total loss |
| expectancy | Expected value per trade |

#### trade_logs.csv
Detailed log of each simulated trade.

| Column | Description |
|--------|-------------|
| prompt_version | Version identifier of the trading prompt |
| symbol | Trading symbol |
| timeframe | Timeframe |
| timestamp | Trade entry timestamp |
| direction | Trade direction (buy/sell) |
| entry_price | Entry price |
| stop_loss | Stop loss price |
| take_profit | Take profit price |
| outcome | Trade outcome (win/loss/expired) |
| duration_candles | Number of candles until resolution |
| achieved_rr | Achieved risk-reward ratio |
| confidence | Confidence score from analysis |

### Prompt Hash Analysis (New)

#### summary_prompt_hash.csv
Aggregated performance metrics by prompt hash, symbol, and timeframe.

| Column | Description |
|--------|-------------|
| prompt_hash | 5-character MD5 hash of the prompt content |
| symbol | Trading symbol (e.g., BTCUSDT) |
| timeframe | Timeframe (e.g., 1h, 4h, 1d) |
| total_trades | Total number of simulated trades |
| win_rate | Percentage of winning trades |
| avg_rr | Average risk-reward ratio for winning trades |
| profit_factor | Total profit divided by total loss |
| expectancy | Expected value per trade |

#### trade_logs_prompt_hash.csv
Detailed log of each simulated trade with prompt hash grouping.

| Column | Description |
|--------|-------------|
| prompt_hash | 5-character MD5 hash of the prompt content |
| symbol | Trading symbol |
| timeframe | Timeframe |
| timestamp | Trade entry timestamp |
| direction | Trade direction (buy/sell) |
| entry_price | Entry price |
| stop_loss | Stop loss price |
| take_profit | Take profit price |
| outcome | Trade outcome (win/loss/expired) |
| duration_candles | Number of candles until resolution |
| achieved_rr | Achieved risk-reward ratio |
| confidence | Confidence score from analysis |

### Hash Mapping Database
The system also maintains a `prompt_hash_mappings` table in the backtest database that maps hashes to their original prompt text for reference and analysis.

## Data Flow

1. **Load Analysis Records**: Read filtered records from `trading_bot/data/analysis_results.db`
2. **Group by Symbol/Timeframe**: Organize records for efficient processing
3. **Delta Fetch Candles**: Fetch only missing historical data from Bybit API
4. **Cache Management**: Store candles in local SQLite database
5. **Filter Records**: Remove records without sufficient historical data
6. **Simulate Trades**: Walk through candles to simulate trade outcomes
7. **Aggregate Metrics**: Calculate performance statistics
8. **Generate Reports**: Export results to CSV files

## Key Components

### CandleCacheDatabase
- Manages SQLite database for candle storage
- Prevents duplicate insertions
- Provides efficient querying by symbol/timeframe/timestamp

### AnalysisDataLoader
- Loads and filters analysis records from database
- Supports both prompt version and prompt hash analysis modes
- Validates required fields (prompt_version/analysis_prompt, recommendation, prices)
- Groups records by symbol/timeframe combinations
- Generates MD5 hashes for prompt content analysis

### CandleFetcher
- Integrates with Bybit v5 API
- Handles category inference (linear/inverse)
- Implements retry logic with exponential backoff
- Normalizes symbols for API compatibility

### TradeSimulator
- Simulates buy/sell trades using historical candles
- Properly handles stop loss and take profit logic
- Tracks trade duration and achieved R:R ratios
- Supports both prompt version and prompt hash analysis modes

### MetricsAggregator
- Calculates comprehensive performance metrics
- Supports aggregation by multiple dimensions
- Exports results to CSV format

## Testing

Run the comprehensive test suite:

```bash
cd prompt_performance
python -m pytest tests/test_backtest.py -v
```

Or run specific test classes:

```bash
python -m unittest tests.test_backtest.TestTradeSimulator
```

## Performance Considerations

- **Caching**: Minimizes API calls by caching historical data
- **Batching**: Processes records in groups for efficiency
- **Filtering**: Early filtering reduces processing overhead
- **Indexing**: Database indexes optimize query performance

## Error Handling

The system includes robust error handling for:
- API rate limits and network issues
- Invalid data formats
- Missing required fields
- Database connection problems

## Configuration

The system uses the main project's `config.yaml` for:
- Bybit API settings
- Circuit breaker configuration
- Retry policies
- Rate limiting parameters

## Extending the System

The modular architecture allows easy extension:
- Add new data sources
- Implement additional metrics
- Support different exchanges
- Create custom simulation logic

## Troubleshooting

### Common Issues

1. **API Errors**: Check Bybit credentials and network connectivity
2. **No Data**: Ensure analysis_results.db contains valid records
3. **Performance**: Consider limiting record count for large datasets

### Logs

Check `prompt_performance/backtest.log` for detailed execution information.

## License

This system is part of the trading bot project and follows the same licensing terms.