#!/usr/bin/env python3
"""
Production-style test for position tightening functionality.
Tests the actual tightening logic with realistic scenarios.
"""

import sys
import os
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, AsyncMock
import logging

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/projects/^^Python/Analyse_Chart_Screenshot')

from trading_bot.config.settings import Config
from trading_bot.core.enhanced_position_monitor import EnhancedPositionMonitor
from trading_bot.core.common_types import PositionInfo

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockTrader:
    """Mock trader for testing"""
    def __init__(self):
        self.api_manager = Mock()
        self.position_manager = Mock()
        self.position_manager.data_agent = Mock()

class MockConfig:
    """Mock config for testing"""
    def __init__(self):
        self.trading = self._create_trading_config()
        self.bybit = Mock()
        self.bybit.recv_window = 60000

    def _create_trading_config(self):
        """Create trading config with position tightening enabled"""
        # Create RR tightening steps config
        from trading_bot.config.settings import RRTighteningConfig, RRTighteningStepConfig

        rr_config = RRTighteningConfig(steps={
            '1R': RRTighteningStepConfig(
                profit_threshold=1.0,
                sl_position=0.3,
                description="Partial profit protection"
            ),
            '1.5R': RRTighteningStepConfig(
                profit_threshold=1.5,
                sl_position=1.0,
                description="Breakeven + 1R profit lock"
            ),
            '2R': RRTighteningStepConfig(
                profit_threshold=2.0,
                sl_position=1.5,
                description="Lock 1.5R profit"
            ),
            '2.5R': RRTighteningStepConfig(
                profit_threshold=2.5,
                sl_position=2.0,
                description="Lock 2R profit"
            ),
            '3R': RRTighteningStepConfig(
                profit_threshold=3.0,
                sl_position=2.5,
                description="Lock 2.5R profit"
            )
        })

        # Create age tightening config
        from trading_bot.config.settings import AgeTighteningConfig
        age_config = AgeTighteningConfig(
            enabled=True,
            max_tightening_pct=30.0,
            min_profit_threshold=1.0,
            age_tightening_bars={
                '1m': 720, '5m': 216, '15m': 96, '30m': 96,
                '1h': 48, '4h': 18, '1d': 4, '1w': 1
            }
        )

        config_data = {
            'risk_tolerance': 0.4,
            'min_confidence_threshold': 0.8,
            'paper_trading': False,
            'risk_percentage': 0.01,
            'max_loss_usd': 15.0,
            'min_rr': 1.5,
            'leverage': 2,
            'balance_safety_margin': 0.5,
            'max_concurrent_trades': 3,
            'auto_approve_trades': True,
            'enable_dynamic_risk_allocation': True,
            'enable_position_tightening': True,  # Master switch ON
            'enable_sl_tightening': True,        # SL tightening ON for testing
            'enable_adx_tightening': False,      # Keep ADX off for focused testing
            'adx_period': 14,
            'atr_period': 14,
            'adx_strength_threshold': 25,
            'base_atr_multiplier': 2.0,
            'adx_target_profit_usd': 50.0,
            'enable_tp_proximity_trailing': False,
            'tp_proximity_threshold_pct': 1.0,
            'tp_proximity_trailing_pct': 1.0,
            'rr_tightening_steps': rr_config,
            'age_tightening': age_config
        }
        return Mock(**config_data)

def create_test_position(symbol, side, entry_price, current_price, current_sl, risk_amount, profit_in_r, timeframe='1h', hours_old=1):
    """Create a test position with specific profit level"""
    created_at = datetime.utcnow().replace(tzinfo=timezone.utc) - timedelta(hours=hours_old)

    return PositionInfo(
        symbol=symbol,
        side=side,
        size=100.0,
        entry_price=entry_price,
        current_price=current_price,
        unrealized_pnl=0.0,
        current_stop_loss=current_sl,
        current_take_profit=None,
        position_idx=0,
        risk_amount=risk_amount,
        timeframe=timeframe,
        created_at=created_at,
        last_tightened_milestone=None,
        has_trade_data=True
    )

async def test_rr_tightening_logic():
    """Test the RR tightening logic with various profit levels"""
    print("🧪 Testing RR-based position tightening logic...")

    # Create mock components
    mock_trader = MockTrader()
    mock_config = MockConfig()
    logger = logging.getLogger("test")

    # Create position monitor
    monitor = EnhancedPositionMonitor(mock_trader, mock_config, logger)

    # Test cases: (profit_in_r, expected_milestone, description)
    test_cases = [
        (0.5, None, "Below 1R - no tightening"),
        (1.2, "1R", "Above 1R - should trigger 1R tightening"),
        (1.7, "1.5R", "Above 1.5R - should trigger 1.5R tightening"),
        (2.3, "2R", "Above 2R - should trigger 2R tightening"),
        (2.8, "2.5R", "Above 2.5R - should trigger 2.5R tightening"),
        (3.2, "3R", "Above 3R - should trigger 3R tightening"),
        (4.0, "3R", "Above 3R - should still be 3R (highest configured)"),
    ]

    success_count = 0
    total_tests = len(test_cases)

    for profit_r, expected_milestone, description in test_cases:
        print(f"\n📊 Test: {description}")
        print(f"   Profit: {profit_r}R, Expected milestone: {expected_milestone}")

        # Create test position (LONG)
        entry_price = 100.0
        current_price = entry_price + (profit_r * 10)  # 10 = 1R
        current_sl = entry_price - 10  # Original SL at -1R
        risk_amount = 10.0  # 1R = $10

        position = create_test_position(
            symbol="BTCUSDT",
            side="Buy",
            entry_price=entry_price,
            current_price=current_price,
            current_sl=current_sl,
            risk_amount=risk_amount,
            profit_in_r=profit_r
        )

        try:
            # Test the milestone detection
            actual_milestone = monitor._get_current_milestone(profit_r)
            print(f"   Actual milestone: {actual_milestone}")

            if actual_milestone == expected_milestone:
                print(f"   ✅ Milestone detection: PASS")
                success_count += 1
            else:
                print(f"   ❌ Milestone detection: FAIL (expected {expected_milestone}, got {actual_milestone})")

            # Test the tightening calculation
            proposed_sl, tightening_level, target_milestone = monitor._apply_long_tightening(
                entry_price, current_price, current_sl, risk_amount, profit_r
            )

            print(f"   Original SL: {current_sl}")
            print(f"   Proposed SL: {proposed_sl}")
            print(f"   Tightening level: {tightening_level}")

            if target_milestone == expected_milestone:
                print(f"   ✅ Tightening calculation: PASS")
                success_count += 1
            else:
                print(f"   ❌ Tightening calculation: FAIL (expected {expected_milestone}, got {target_milestone})")

        except Exception as e:
            print(f"   ❌ Error in test: {e}")
            import traceback
            traceback.print_exc()

    print(f"\n📈 RR Tightening Test Results: {success_count}/{total_tests * 2} passed")
    return success_count == (total_tests * 2)

async def test_short_position_tightening():
    """Test tightening logic for short positions"""
    print("\n🔻 Testing SHORT position tightening logic...")

    mock_trader = MockTrader()
    mock_config = MockConfig()
    logger = logging.getLogger("test")
    monitor = EnhancedPositionMonitor(mock_trader, mock_config, logger)

    # Test short position at 1.8R profit
    entry_price = 100.0
    current_price = entry_price - 18.0  # 1.8R profit (1R = $10)
    current_sl = entry_price + 10.0    # Original SL at +1R
    risk_amount = 10.0

    position = create_test_position(
        symbol="BTCUSDT",
        side="Sell",
        entry_price=entry_price,
        current_price=current_price,
        current_sl=current_sl,
        risk_amount=risk_amount,
        profit_in_r=1.8
    )

    try:
        proposed_sl, tightening_level, target_milestone = monitor._apply_short_tightening(
            entry_price, current_price, current_sl, risk_amount, 1.8
        )

        print(f"   Entry: {entry_price}")
        print(f"   Current: {current_price}")
        print(f"   Original SL: {current_sl}")
        print(f"   Proposed SL: {proposed_sl}")
        print(f"   Tightening level: {tightening_level}")

        # For short position at 1.8R, should trigger 1.5R tightening
        # 1.5R step: sl_position = 1.0, so SL should move to entry - 1.0R = 100 - 10 = 90
        expected_sl = entry_price - 10.0  # entry - 1R

        if abs(proposed_sl - expected_sl) < 0.001 and tightening_level == "1.5R":
            print("   ✅ SHORT position tightening: PASS")
            return True
        else:
            print(f"   ❌ SHORT position tightening: FAIL (expected SL {expected_sl}, got {proposed_sl})")
            return False

    except Exception as e:
        print(f"   ❌ Error in SHORT test: {e}")
        return False

async def test_age_based_tightening():
    """Test age-based tightening for unprofitable positions"""
    print("\n⏰ Testing age-based tightening logic...")

    mock_trader = MockTrader()
    mock_config = MockConfig()
    logger = logging.getLogger("test")
    monitor = EnhancedPositionMonitor(mock_trader, mock_config, logger)

    # Test old unprofitable position (should get age-based tightening)
    entry_price = 100.0
    current_price = 95.0  # -0.5R loss (1R = $10)
    current_sl = 90.0     # Original SL at -1R
    risk_amount = 10.0

    # Create position that's 25 hours old (1h threshold is 48 hours, so no tightening expected)
    position = create_test_position(
        symbol="BTCUSDT",
        side="Buy",
        entry_price=entry_price,
        current_price=current_price,
        current_sl=current_sl,
        risk_amount=risk_amount,
        profit_in_r=-0.5,
        hours_old=25
    )

    try:
        # Calculate age factor (should be 0.0 since 25h < 48h threshold)
        age_factor = monitor._calculate_age_factor(position.created_at, position.timeframe)
        age_tightening = monitor._calculate_age_tightening(age_factor, -0.5)

        print(f"   Position age: 25 hours")
        print(f"   Timeframe threshold: 48 hours (2 days)")
        print(f"   Age factor: {age_factor:.2f}")
        print(f"   Age tightening: {age_tightening:.3f}")

        # Since 25h < 48h threshold, no age-based tightening should occur
        if age_factor == 0.0 and age_tightening == 0.0:
            print("   ✅ Age-based tightening: PASS (correctly no tightening for young position)")
            return True
        else:
            print("   ❌ Age-based tightening: FAIL (should be no tightening)")
            return False

    except Exception as e:
        print(f"   ❌ Error in age test: {e}")
        return False

async def test_configurable_steps():
    """Test that configurable steps work correctly"""
    print("\n⚙️ Testing configurable RR steps...")

    mock_trader = MockTrader()
    mock_config = MockConfig()
    logger = logging.getLogger("test")
    monitor = EnhancedPositionMonitor(mock_trader, mock_config, logger)

    # Check that custom steps are loaded
    steps = monitor.rr_tightening_steps
    print(f"   Loaded {len(steps)} RR tightening steps")

    expected_steps = ['1R', '1.5R', '2R', '2.5R', '3R']
    for step_name in expected_steps:
        if step_name in steps:
            step_config = steps[step_name]
            print(f"   ✅ {step_name}: {step_config['profit_threshold']}R -> {step_config['sl_position']}R")
        else:
            print(f"   ❌ Missing step: {step_name}")
            return False

    # Test a specific step configuration
    step_1r = steps.get('1R')
    if step_1r and step_1r['profit_threshold'] == 1.0 and step_1r['sl_position'] == 0.3:
        print("   ✅ Step configuration: PASS")
        return True
    else:
        print("   ❌ Step configuration: FAIL")
        return False

async def test_comprehensive_age_based_tightening():
    """Test age-based tightening for unprofitable positions with various scenarios"""
    print("\n⏰ Testing comprehensive age-based tightening logic...")

    mock_trader = MockTrader()
    mock_config = MockConfig()
    logger = logging.getLogger("test")
    monitor = EnhancedPositionMonitor(mock_trader, mock_config, logger)

    test_scenarios = [
        {
            "name": "Young position (25h < 48h threshold)",
            "hours_old": 25,
            "expected_tightening": 0.0,
            "description": "Should not tighten young positions"
        },
        {
            "name": "Old position (60h > 48h threshold)",
            "hours_old": 60,
            "expected_tightening": 0.25,  # (60-48)/48 = 0.25
            "description": "Should tighten old unprofitable positions"
        },
        {
            "name": "Very old position (96h = 2x threshold)",
            "hours_old": 96,
            "expected_tightening": 1.0,  # (96-48)/48 = 1.0
            "description": "Should apply maximum tightening for very old positions"
        }
    ]

    all_passed = True

    for scenario in test_scenarios:
        print(f"\n   📊 Scenario: {scenario['name']}")
        print(f"   Description: {scenario['description']}")

        # Create position with specific age
        position = create_test_position(
            symbol="BTCUSDT",
            side="Buy",
            entry_price=100.0,
            current_price=95.0,  # -0.5R loss
            current_sl=90.0,
            risk_amount=10.0,
            profit_in_r=-0.5,
            hours_old=scenario["hours_old"]
        )

        try:
            # Calculate age factor and tightening
            age_factor = monitor._calculate_age_factor(position.created_at, position.timeframe)
            age_tightening = monitor._calculate_age_tightening(age_factor, -0.5)

            print(f"   Position age: {scenario['hours_old']} hours")
            print(f"   Timeframe threshold: 48 hours")
            print(f"   Age factor: {age_factor:.3f}")
            print(f"   Age tightening: {age_tightening:.3f}")
            print(f"   Expected tightening: {scenario['expected_tightening']:.3f}")

            # Check if result matches expectation (with small tolerance for floating point)
            tolerance = 0.01
            if abs(age_tightening - scenario["expected_tightening"]) <= tolerance:
                print(f"   ✅ {scenario['name']}: PASS")
            else:
                print(f"   ❌ {scenario['name']}: FAIL")
                all_passed = False

        except Exception as e:
            print(f"   ❌ Error in {scenario['name']}: {e}")
            all_passed = False

    return all_passed

async def test_tp_proximity_trailing():
    """Test TP proximity trailing stop functionality"""
    print("\n🎯 Testing TP proximity trailing logic...")

    mock_trader = MockTrader()
    mock_config = MockConfig()
    logger = logging.getLogger("test")
    monitor = EnhancedPositionMonitor(mock_trader, mock_config, logger)

    test_scenarios = [
        {
            "name": "Far from TP (no proximity)",
            "entry_price": 100.0,
            "current_price": 105.0,
            "tp_price": 120.0,
            "side": "Buy",
            "expected_triggered": False
        },
        {
            "name": "Close to TP (within 1% threshold)",
            "entry_price": 100.0,
            "current_price": 119.0,  # 1% from TP of 120
            "tp_price": 120.0,
            "side": "Buy",
            "expected_triggered": True
        },
        {
            "name": "Short position close to TP",
            "entry_price": 100.0,
            "current_price": 81.0,   # 1% from TP of 80
            "tp_price": 80.0,
            "side": "Sell",
            "expected_triggered": True
        }
    ]

    all_passed = True

    for scenario in test_scenarios:
        print(f"\n   📊 Scenario: {scenario['name']}")

        try:
            result = monitor._check_tp_proximity(
                scenario["entry_price"],
                scenario["current_price"],
                scenario["tp_price"],
                scenario["side"],
                "TESTUSDT"
            )

            triggered = result is not None if scenario["expected_triggered"] else result is None

            print(f"   Current: {scenario['current_price']}, TP: {scenario['tp_price']}")
            print(f"   Distance: {abs(scenario['current_price'] - scenario['tp_price'])}")
            print(f"   Distance %: {abs(scenario['current_price'] - scenario['tp_price']) / scenario['current_price'] * 100:.2f}%")
            print(f"   Expected triggered: {scenario['expected_triggered']}")
            print(f"   Actually triggered: {result is not None}")

            if triggered:
                print(f"   ✅ {scenario['name']}: PASS")
            else:
                print(f"   ❌ {scenario['name']}: FAIL")
                all_passed = False

        except Exception as e:
            print(f"   ❌ Error in {scenario['name']}: {e}")
            all_passed = False

    return all_passed

async def test_milestone_tracking():
    """Test milestone tracking and database updates"""
    print("\n🏆 Testing milestone tracking...")

    mock_trader = MockTrader()
    mock_config = MockConfig()
    logger = logging.getLogger("test")
    monitor = EnhancedPositionMonitor(mock_trader, mock_config, logger)

    # Test milestone detection with various profit levels
    test_profits = [0.5, 1.2, 1.7, 2.3, 2.8, 3.2, 4.0, 5.0, 6.0]
    expected_milestones = [None, "1R", "1.5R", "2R", "2.5R", "3R", "3R", "3R", "3R"]

    all_passed = True

    for profit, expected in zip(test_profits, expected_milestones):
        milestone = monitor._get_current_milestone(profit)
        print(f"   Profit {profit}R -> Milestone: {milestone} (expected: {expected})")

        if milestone == expected:
            print("   ✅ PASS")
        else:
            print("   ❌ FAIL")
            all_passed = False

    return all_passed

async def test_edge_cases():
    """Test edge cases and error conditions"""
    print("\n⚠️ Testing edge cases and error conditions...")

    mock_trader = MockTrader()
    mock_config = MockConfig()
    logger = logging.getLogger("test")
    monitor = EnhancedPositionMonitor(mock_trader, mock_config, logger)

    edge_cases = [
        {
            "name": "Zero risk amount",
            "entry_price": 100.0,
            "current_price": 110.0,
            "current_sl": 90.0,
            "risk_amount": 0.0,
            "should_handle": True
        },
        {
            "name": "Very small profit (0.01R)",
            "entry_price": 100.0,
            "current_price": 100.1,
            "current_sl": 90.0,
            "risk_amount": 10.0,
            "should_handle": True
        },
        {
            "name": "Very large profit (10R)",
            "entry_price": 100.0,
            "current_price": 200.0,
            "current_sl": 90.0,
            "risk_amount": 10.0,
            "should_handle": True
        }
    ]

    all_passed = True

    for case in edge_cases:
        print(f"\n   📊 Edge case: {case['name']}")

        try:
            position = create_test_position(
                symbol="TESTUSDT",
                side="Buy",
                entry_price=case["entry_price"],
                current_price=case["current_price"],
                current_sl=case["current_sl"],
                risk_amount=case["risk_amount"],
                profit_in_r=(case["current_price"] - case["entry_price"]) / case["risk_amount"] if case["risk_amount"] > 0 else 0
            )

            # Test milestone detection
            profit_r = (case["current_price"] - case["entry_price"]) / case["risk_amount"] if case["risk_amount"] > 0 else 0
            milestone = monitor._get_current_milestone(profit_r)

            # Test tightening calculation
            proposed_sl, tightening_level, target_milestone = monitor._apply_long_tightening(
                case["entry_price"], case["current_price"], case["current_sl"],
                case["risk_amount"], profit_r
            )

            print(f"   Profit: {profit_r:.2f}R")
            print(f"   Milestone: {milestone}")
            print(f"   Proposed SL: {proposed_sl}")
            print(f"   Tightening level: {tightening_level}")

            # Should not crash and should return reasonable values
            if proposed_sl >= 0 and isinstance(tightening_level, str):
                print(f"   ✅ {case['name']}: PASS")
            else:
                print(f"   ❌ {case['name']}: FAIL")
                all_passed = False

        except Exception as e:
            if case["should_handle"]:
                print(f"   ❌ {case['name']}: FAIL (unexpected error: {e})")
                all_passed = False
            else:
                print(f"   ✅ {case['name']}: PASS (correctly handled error case)")

    return all_passed

async def main():
    """Run all tests"""
    print("🚀 Starting Position Tightening Production Tests")
    print("=" * 60)

    # Load actual configuration to make sure it works
    try:
        config = Config.from_yaml('config.yaml')
        print("✅ Real configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load real configuration: {e}")
        return False

    # Run tests
    test_results = []

    print("\n" + "=" * 60)
    test_results.append(await test_configurable_steps())

    print("\n" + "=" * 60)
    test_results.append(await test_rr_tightening_logic())

    print("\n" + "=" * 60)
    test_results.append(await test_short_position_tightening())

    print("\n" + "=" * 60)
    test_results.append(await test_age_based_tightening())

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)

    passed = sum(test_results)
    total = len(test_results)

    for i, result in enumerate(test_results, 1):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"Test {i}: {status}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Position tightening is production-ready.")
        return True
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    import asyncio
    success = asyncio.run(main())
    sys.exit(0 if success else 1)