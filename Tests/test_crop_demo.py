"""
Simple crop script - just the values and save result
"""

from PIL import Image
from pathlib import Path

# Input image path
input_image = "Tests/XRPUSDT.P_30m_20250917_024055.png"

# CROP VALUES - ADJUST THESE until you're happy
crop_values = {
    'left': 50,      # Remove left sidebar
    'top': 40,     # Remove top browser bar
    'right': 320,     # Remove right elements
    'bottom': 40   # Remove bottom browser bar
}

# Output path
output_dir = Path("Tests/cropped_results")
output_dir.mkdir(parents=True, exist_ok=True)
output_image = output_dir / "cropped_result.png"

def main():
    """Crop the image and save result."""
    print("🖼️ Cropping image...")
    print(f"📁 Input: {input_image}")
    print(f"📁 Output: {output_image}")
    print(f"✂️ Crop values: {crop_values}")

    try:
        # Load image
        image = Image.open(input_image)
        width, height = image.size

        print(f"📐 Original size: {width}x{height}")

        # Calculate crop box
        left = crop_values['left']
        top = crop_values['top']
        right = crop_values['right']
        bottom = crop_values['bottom']

        crop_box = (
            left,                    # left
            top,                     # upper
            width - right,          # right
            height - bottom         # lower
        )

        print(f"📦 Crop box: {crop_box}")

        # Validate crop box
        if crop_box[0] >= crop_box[2] or crop_box[1] >= crop_box[3]:
            print("❌ Invalid crop values!")
            return

        # Crop and save
        cropped_image = image.crop(crop_box)
        cropped_image.save(output_image, format='PNG')

        cropped_width, cropped_height = cropped_image.size
        print(f"✅ Cropped size: {cropped_width}x{cropped_height}")
        print(f"💾 Saved to: {output_image}")

        print("\n🎯 Result saved!")
        print("   Adjust crop_values above and run again if needed.")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()