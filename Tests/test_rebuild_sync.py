#!/usr/bin/env python3
"""
Test script for RebuildSyncManager functionality.
This script tests the rebuild database functionality to identify issues.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the parent directory to path for imports
sys.path.insert(0, str(Path(__file__).resolve().parent))

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.rebuild_sync_manager import RebuildSyncManager, SyncConfig
from trading_bot.config.settings import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_rebuild_sync():
    """Test the rebuild sync functionality."""
    logger = logging.getLogger(__name__)

    try:
        # Initialize components
        config = Config.from_yaml()
        data_agent = DataAgent()

        # Create sync config
        sync_config = SyncConfig()

        # Mock trader object (minimal implementation for testing)
        class MockTrader:
            pass

        trader = MockTrader()

        # Initialize rebuild sync manager
        sync_manager = RebuildSyncManager(data_agent, trader, sync_config)

        logger.info("Starting rebuild sync test...")

        # Test rebuild mode
        result = await sync_manager.sync_with_exchange(rebuild=True)

        logger.info(f"Rebuild sync completed with result: {result}")

        if result['status'] == 'success':
            logger.info("✅ Rebuild sync test PASSED")
            logger.info(f"Open positions inserted: {result.get('open_positions_inserted', 0)}")
            logger.info(f"Trades created: {result.get('trades_created', 0)}")
            logger.info(f"PnL records processed: {result.get('pnl_records_processed', 0)}")
        else:
            logger.error("❌ Rebuild sync test FAILED")
            logger.error(f"Error: {result.get('message', 'Unknown error')}")
            if result.get('errors'):
                for error in result['errors']:
                    logger.error(f"  - {error}")

        return result

    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return {'status': 'error', 'message': str(e)}

if __name__ == "__main__":
    print("Testing RebuildSyncManager...")
    result = asyncio.run(test_rebuild_sync())
    print(f"\nTest completed. Final status: {result.get('status', 'unknown')}")