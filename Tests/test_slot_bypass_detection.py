#!/usr/bin/env python3
"""
Test script to detect slot checking bypass in the autotrader execution flow.
This script simulates the complete trading flow to identify where slot limits are bypassed.
"""
import sys
import os
import logging
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SlotBypassTest:
    """Test class to detect slot checking bypass in trading flow."""

    def __init__(self):
        self.test_results = []
        self.mock_api_manager = None
        self.mock_risk_manager = None
        self.mock_trader = None

    def setup_mock_environment(self):
        """Set up mock environment with controlled slot limits."""
        print("=" * 60)
        print("🔧 SETTING UP MOCK ENVIRONMENT")
        print("=" * 60)

        # Mock configuration with slot limit of 3
        class MockTradingConfig:
            max_concurrent_trades = 3
            min_rr = 1.5
            risk_tolerance = 0.5
            min_confidence_threshold = 0.6
            paper_trading = True
            risk_percentage = 0.01
            max_loss_usd = 200.0
            leverage = 1
            balance_safety_margin = 0.8
            auto_approve_trades = False
            max_trades_check_mode = "positions"
            age_tightening_bars = {}
            enable_dynamic_risk_allocation = True
            order_replacement = None
            exchange_sync = None

        class MockBybitConfig:
            recv_window = 60000
            use_testnet = True
            max_retries = 3
            circuit_breaker = None

        class MockPathsConfig:
            database = "trading_bot/data/analysis_results.db"
            charts = "trading_bot/data/charts"
            logs = "trading_bot/logs"
            session_file = "trading_bot/data/"

        class MockOpenAIConfig:
            api_key = "test_key"
            model = "gpt-4"
            max_tokens = 1000
            temperature = 0.7
            assistant = None

        class MockAgentConfig:
            model = "gpt-4"
            max_tokens = 1000
            temperature = 0.7

        class MockTradingViewConfig:
            enabled = False
            base_url = "https://www.tradingview.com"
            login_url = "https://www.tradingview.com/accounts/signin/"
            chart_url_template = "https://www.tradingview.com/chart/?symbol={symbol}"
            browser = None
            auth = None
            screenshot = None
            rate_limit = None
            retry = None
            target_chart = None

        class MockFileManagementConfig:
            enable_backup = False

        class MockConfig:
            def __init__(self):
                self.trading = MockTradingConfig()
                self.bybit = MockBybitConfig()
                self.paths = MockPathsConfig()
                self.openai = MockOpenAIConfig()
                self.agents = {'default': MockAgentConfig()}
                self.tradingview = MockTradingViewConfig()
                self.file_management = MockFileManagementConfig()

        self.config = MockConfig()

        # Mock API Manager
        self.mock_api_manager = Mock()
        self.mock_api_manager.get_positions.return_value = {
            'retCode': 0,
            'result': {'list': []}  # No positions initially
        }
        self.mock_api_manager.get_open_orders.return_value = {
            'retCode': 0,
            'result': {'list': []}  # No orders initially
        }
        self.mock_api_manager.get_fee_rates.return_value = {
            'result': {'list': [{'takerFeeRate': '0.001'}]}
        }

        # Mock the BybitAPIManager class to avoid real API calls
        with patch('trading_bot.core.trader.BybitAPIManager') as mock_api_class:
            mock_api_class.return_value = self.mock_api_manager
            self.mock_api_manager_class = mock_api_class

        # Mock Risk Manager
        self.mock_risk_manager = Mock()
        self.mock_risk_manager.can_execute_trade.return_value = {
            'can_execute': True,
            'reason': 'Slots available'
        }

        # Mock Trader
        self.mock_trader = Mock()
        self.mock_trader.config = self.config
        self.mock_trader.api_manager = self.mock_api_manager
        self.mock_trader.risk_manager = self.mock_risk_manager

        print("✅ Mock environment set up successfully")
        print(f"   Max concurrent trades: {self.config.trading.max_concurrent_trades}")

    def test_slot_check_bypass_detection(self):
        """Test to detect if slot checking is bypassed in execute_trade."""
        print("\n" + "=" * 60)
        print("🧪 TESTING SLOT CHECK BYPASS DETECTION")
        print("=" * 60)

        # Import the actual TradeExecutor
        from trading_bot.core.trader import TradeExecutor

        # Create TradeExecutor with mock components
        trader = TradeExecutor(
            config=self.config,
            use_testnet=True,
            risk_manager=self.mock_risk_manager
        )
        trader.api_manager = self.mock_api_manager

        # Test Signal
        test_signal = {
            'recommendation': 'BUY',
            'entry_price': 50000.0,
            'take_profit': 51000.0,
            'stop_loss': 49500.0,
            'symbol': 'BTCUSDT',
            'direction': 'LONG'
        }

        print("\n📊 Test Case 1: Normal slot check behavior")
        print("   Sending trade signal with available slots...")

        # Mock risk manager to return True (slots available)
        self.mock_risk_manager.can_execute_trade.return_value = {
            'can_execute': True,
            'reason': 'Slots available'
        }

        # Mock the place_market_order_with_tp_sl to track if it's called
        with patch.object(trader, 'place_market_order_with_tp_sl') as mock_place_order:
            mock_place_order.return_value = {'success': True}

            # Call execute_trade
            result = trader.execute_trade(test_signal, 'BTCUSDT')

            # Check if slot check was called
            slot_check_called = self.mock_risk_manager.can_execute_trade.called
            trade_executed = mock_place_order.called

            print(f"   Slot check called: {slot_check_called}")
            print(f"   Trade executed: {trade_executed}")

            if slot_check_called and trade_executed:
                print("   ✅ PASS - Slot check performed and trade executed")
                self.test_results.append(("Normal flow", True))
            else:
                print("   ❌ FAIL - Expected both slot check and trade execution")
                self.test_results.append(("Normal flow", False))

        print("\n📊 Test Case 2: Slot limit exceeded")
        print("   Sending trade signal when slots are full...")

        # Reset mock
        self.mock_risk_manager.can_execute_trade.reset_mock()

        # Mock risk manager to return False (no slots available)
        self.mock_risk_manager.can_execute_trade.return_value = {
            'can_execute': False,
            'reason': 'Maximum concurrent trades (3) exceeded'
        }

        # Mock the place_market_order_with_tp_sl to track if it's called
        with patch.object(trader, 'place_market_order_with_tp_sl') as mock_place_order:
            mock_place_order.return_value = {'success': True}

            # Call execute_trade
            result = trader.execute_trade(test_signal, 'BTCUSDT')

            # Check if slot check was called and trade was blocked
            slot_check_called = self.mock_risk_manager.can_execute_trade.called
            trade_executed = mock_place_order.called

            print(f"   Slot check called: {slot_check_called}")
            print(f"   Trade executed: {trade_executed}")

            if slot_check_called and not trade_executed:
                print("   ✅ PASS - Slot check performed and trade blocked")
                self.test_results.append(("Slot limit exceeded", True))
            elif slot_check_called and trade_executed:
                print("   ❌ FAIL - CRITICAL: Slot check bypassed! Trade executed despite slot limit")
                self.test_results.append(("Slot limit exceeded", False))
            else:
                print("   ❌ FAIL - Slot check not called")
                self.test_results.append(("Slot limit exceeded", False))

        print("\n📊 Test Case 3: Risk manager not available")
        print("   Testing behavior when risk_manager is None...")

        # Create trader without risk manager
        trader_no_risk = TradeExecutor(
            config=self.config,
            use_testnet=True,
            risk_manager=None  # No risk manager
        )
        trader_no_risk.api_manager = self.mock_api_manager

        # Mock the place_market_order_with_tp_sl to track if it's called
        with patch.object(trader_no_risk, 'place_market_order_with_tp_sl') as mock_place_order:
            mock_place_order.return_value = {'success': True}

            # Call execute_trade
            result = trader_no_risk.execute_trade(test_signal, 'BTCUSDT')

            # Check if trade was executed without risk manager
            trade_executed = mock_place_order.called

            print(f"   Risk manager available: False")
            print(f"   Trade executed: {trade_executed}")

            if trade_executed:
                print("   ⚠️  WARNING - Trade executed without risk manager")
                self.test_results.append(("No risk manager", True))
            else:
                print("   ✅ PASS - Trade blocked when no risk manager")
                self.test_results.append(("No risk manager", False))

    def test_multiple_trades_simulation(self):
        """Simulate multiple trades to test slot accumulation."""
        print("\n" + "=" * 60)
        print("🔄 TESTING MULTIPLE TRADES SIMULATION")
        print("=" * 60)

        from trading_bot.core.trader import TradeExecutor

        # Track trades executed
        trades_executed = 0
        slot_checks_performed = 0

        # Create trader
        trader = TradeExecutor(
            config=self.config,
            use_testnet=True,
            risk_manager=self.mock_risk_manager
        )
        trader.api_manager = self.mock_api_manager

        # Test Signal
        test_signal = {
            'recommendation': 'BUY',
            'entry_price': 50000.0,
            'take_profit': 51000.0,
            'stop_loss': 49500.0,
            'symbol': 'BTCUSDT',
            'direction': 'LONG'
        }

        print(f"   Max concurrent trades: {self.config.trading.max_concurrent_trades}")
        print("   Simulating 5 trade attempts...")

        for i in range(5):
            print(f"\n   Trade attempt {i+1}:")

            # For first 3 trades, allow execution
            if i < 3:
                self.mock_risk_manager.can_execute_trade.return_value = {
                    'can_execute': True,
                    'reason': f'Slots available ({3-i-1} remaining)'
                }
            else:
                self.mock_risk_manager.can_execute_trade.return_value = {
                    'can_execute': False,
                    'reason': 'Maximum concurrent trades (3) exceeded'
                }

            # Reset mock call count
            self.mock_risk_manager.can_execute_trade.reset_mock()

            with patch.object(trader, 'place_market_order_with_tp_sl') as mock_place_order:
                mock_place_order.return_value = {'success': True}

                # Execute trade
                result = trader.execute_trade(test_signal, f'BTCUSDT_{i}')

                slot_check_called = self.mock_risk_manager.can_execute_trade.called
                trade_executed = mock_place_order.called

                if slot_check_called:
                    slot_checks_performed += 1

                if trade_executed:
                    trades_executed += 1

                print(f"     Slot check: {'✅' if slot_check_called else '❌'}")
                print(f"     Trade executed: {'✅' if trade_executed else '❌'}")

                if i < 3 and trade_executed:
                    print("     Expected: Trade should execute")
                elif i >= 3 and not trade_executed:
                    print("     Expected: Trade should be blocked")
                elif i >= 3 and trade_executed:
                    print("     ❌ UNEXPECTED: Trade executed despite slot limit!")

        print("\n📊 Simulation Results:")
        print(f"   Total trades attempted: 5")
        print(f"   Trades executed: {trades_executed}")
        print(f"   Slot checks performed: {slot_checks_performed}")
        print(f"   Expected executed: 3 (limited by max_concurrent_trades)")

        if trades_executed == 3 and slot_checks_performed == 5:
            print("   ✅ PASS - Slot limits working correctly")
            self.test_results.append(("Multiple trades simulation", True))
        else:
            print("   ❌ FAIL - Slot limits not enforced properly")
            self.test_results.append(("Multiple trades simulation", False))

    def run_all_tests(self):
        """Run all slot bypass detection tests."""
        print("🚀 STARTING SLOT BYPASS DETECTION TESTS")
        print("=" * 60)

        self.setup_mock_environment()
        self.test_slot_check_bypass_detection()
        self.test_multiple_trades_simulation()

        # Summary
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)

        passed_tests = 0
        total_tests = len(self.test_results)

        for test_name, passed in self.test_results:
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {test_name}: {status}")
            if passed:
                passed_tests += 1

        print(f"\n   Results: {passed_tests}/{total_tests} tests passed")

        if passed_tests == total_tests:
            print("   🎉 ALL TESTS PASSED - No slot bypass detected!")
            return True
        else:
            print("   ⚠️  SLOT BYPASS DETECTED - Check the failing tests above")
            return False

def main():
    """Main function to run the slot bypass detection tests."""
    test_suite = SlotBypassTest()
    success = test_suite.run_all_tests()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)