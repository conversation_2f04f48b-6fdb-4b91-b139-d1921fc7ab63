#!/usr/bin/env python3
"""Production test for real-time trade tracking system."""
import sys
import os
import asyncio
import time
from datetime import datetime, timezone
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from trading_bot.config.settings import Config
from run_autotrader import SimpleTradingBot


class ProductionTradeTrackingTest:
    """Production test for real-time trade tracking."""
    
    def __init__(self):
        self.config = Config.from_yaml()
        self.bot = None
        self.test_results = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result."""
        status = "✅ PASS" if passed else "❌ FAIL"
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append((test_name, passed, message))
    
    async def test_bot_initialization(self):
        """Test that the bot initializes with real-time tracking components."""
        print("\n🔍 Testing Bot Initialization...")
        
        try:
            # Initialize bot with testnet for safety
            self.bot = SimpleTradingBot(use_testnet=True)
            
            # Check if real-time components are initialized
            has_trade_tracker = hasattr(self.bot.position_manager, 'trade_tracker') and self.bot.position_manager.trade_tracker is not None
            has_trade_monitor = hasattr(self.bot.position_manager, 'trade_monitor') and self.bot.position_manager.trade_monitor is not None
            has_incremental_sync = hasattr(self.bot, 'incremental_sync_manager') and self.bot.incremental_sync_manager is not None
            
            if has_trade_tracker and has_trade_monitor and has_incremental_sync:
                self.log_test_result("Bot Initialization", True, "All real-time components initialized")
            else:
                missing = []
                if not has_trade_tracker:
                    missing.append("trade_tracker")
                if not has_trade_monitor:
                    missing.append("trade_monitor")
                if not has_incremental_sync:
                    missing.append("incremental_sync_manager")
                self.log_test_result("Bot Initialization", False, f"Missing components: {missing}")
                
        except Exception as e:
            self.log_test_result("Bot Initialization", False, f"Error: {e}")
    
    async def test_realtime_monitoring_lifecycle(self):
        """Test starting and stopping real-time monitoring."""
        print("\n🔍 Testing Real-Time Monitoring Lifecycle...")
        
        try:
            if not self.bot:
                self.log_test_result("Monitoring Lifecycle", False, "Bot not initialized")
                return
            
            # Start monitoring
            await self.bot.start_realtime_monitoring()
            
            # Check if monitoring is active
            if hasattr(self.bot.position_manager, 'trade_monitor'):
                status = self.bot.position_manager.trade_monitor.get_monitoring_status()
                is_monitoring = status.get('is_monitoring', False)
                
                if is_monitoring:
                    self.log_test_result("Start Monitoring", True, "Monitoring started successfully")
                    
                    # Stop monitoring
                    await self.bot.stop_realtime_monitoring()
                    
                    # Check if monitoring stopped
                    status_after = self.bot.position_manager.trade_monitor.get_monitoring_status()
                    is_monitoring_after = status_after.get('is_monitoring', True)
                    
                    if not is_monitoring_after:
                        self.log_test_result("Stop Monitoring", True, "Monitoring stopped successfully")
                    else:
                        self.log_test_result("Stop Monitoring", False, "Monitoring did not stop")
                else:
                    self.log_test_result("Start Monitoring", False, "Monitoring did not start")
            else:
                self.log_test_result("Monitoring Lifecycle", False, "Trade monitor not available")
                
        except Exception as e:
            self.log_test_result("Monitoring Lifecycle", False, f"Error: {e}")
    
    async def test_incremental_sync(self):
        """Test incremental sync functionality."""
        print("\n🔍 Testing Incremental Sync...")
        
        try:
            if not self.bot:
                self.log_test_result("Incremental Sync", False, "Bot not initialized")
                return
            
            # Perform incremental sync
            sync_result = self.bot.perform_incremental_sync()
            
            if sync_result.get('status') == 'success':
                sync_type = sync_result.get('sync_type', 'unknown')
                trades_updated = sync_result.get('trades_updated', 0)
                trades_created = sync_result.get('trades_created', 0)
                
                self.log_test_result("Incremental Sync", True, 
                                   f"Sync completed ({sync_type}): {trades_updated} updated, {trades_created} created")
            else:
                error = sync_result.get('error', 'Unknown error')
                self.log_test_result("Incremental Sync", False, f"Sync failed: {error}")
                
        except Exception as e:
            self.log_test_result("Incremental Sync", False, f"Error: {e}")
    
    def test_database_schema_production(self):
        """Test database schema in production environment."""
        print("\n🔍 Testing Production Database Schema...")
        
        try:
            if not self.bot:
                self.log_test_result("Database Schema", False, "Bot not initialized")
                return
            
            # Apply migrations
            self.bot.data_agent.migrate_database()
            
            # Check for required columns
            conn = self.bot.data_agent.get_connection()
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(trades)")
            columns = [column[1] for column in cursor.fetchall()]
            conn.close()
            
            required_columns = ['prompt_name', 'timeframe', 'confidence', 'risk_reward_ratio']
            missing_columns = [col for col in required_columns if col not in columns]
            
            if missing_columns:
                self.log_test_result("Database Schema", False, f"Missing columns: {missing_columns}")
            else:
                self.log_test_result("Database Schema", True, f"All required columns present")
                
        except Exception as e:
            self.log_test_result("Database Schema", False, f"Error: {e}")
    
    def test_trade_tracker_integration(self):
        """Test trade tracker integration with trader."""
        print("\n🔍 Testing Trade Tracker Integration...")
        
        try:
            if not self.bot:
                self.log_test_result("Trade Tracker Integration", False, "Bot not initialized")
                return
            
            # Check if trader has trade tracker
            has_tracker = hasattr(self.bot.trader, 'trade_tracker') and self.bot.trader.trade_tracker is not None
            has_monitor = hasattr(self.bot.trader, 'trade_monitor') and self.bot.trader.trade_monitor is not None
            
            if has_tracker and has_monitor:
                self.log_test_result("Trade Tracker Integration", True, "Trader properly linked to tracking components")
            else:
                missing = []
                if not has_tracker:
                    missing.append("trade_tracker")
                if not has_monitor:
                    missing.append("trade_monitor")
                self.log_test_result("Trade Tracker Integration", False, f"Missing links: {missing}")
                
        except Exception as e:
            self.log_test_result("Trade Tracker Integration", False, f"Error: {e}")
    
    def test_position_manager_integration(self):
        """Test position manager integration with real-time tracking."""
        print("\n🔍 Testing Position Manager Integration...")
        
        try:
            if not self.bot:
                self.log_test_result("Position Manager Integration", False, "Bot not initialized")
                return
            
            # Check position manager components
            pm = self.bot.position_manager
            has_tracker = hasattr(pm, 'trade_tracker') and pm.trade_tracker is not None
            has_monitor = hasattr(pm, 'trade_monitor') and pm.trade_monitor is not None
            
            # Check if components are properly linked
            tracker_linked = hasattr(self.bot.trader, 'trade_tracker') and self.bot.trader.trade_tracker == pm.trade_tracker
            monitor_linked = hasattr(self.bot.trader, 'trade_monitor') and self.bot.trader.trade_monitor == pm.trade_monitor
            
            if has_tracker and has_monitor and tracker_linked and monitor_linked:
                self.log_test_result("Position Manager Integration", True, "All components properly integrated")
            else:
                issues = []
                if not has_tracker:
                    issues.append("no_tracker")
                if not has_monitor:
                    issues.append("no_monitor")
                if not tracker_linked:
                    issues.append("tracker_not_linked")
                if not monitor_linked:
                    issues.append("monitor_not_linked")
                self.log_test_result("Position Manager Integration", False, f"Issues: {issues}")
                
        except Exception as e:
            self.log_test_result("Position Manager Integration", False, f"Error: {e}")
    
    async def test_mock_trade_execution(self):
        """Test mock trade execution with real-time tracking."""
        print("\n🔍 Testing Mock Trade Execution...")
        
        try:
            if not self.bot:
                self.log_test_result("Mock Trade Execution", False, "Bot not initialized")
                return
            
            # Create a mock trade signal
            mock_signal = {
                "recommendation": "BUY",
                "symbol": "BTCUSDT",
                "quantity": 0.001,
                "entry_price": 50000.0,
                "take_profit": 52000.0,
                "stop_loss": 48000.0,
                "confidence": 0.85,
                "timeframe": "1h",
                "prompt_name": "PRODUCTION_TEST_PROMPT",
                "recommendation_id": "prod_test_rec_001"
            }
            
            # Execute trade in dry run mode
            result = self.bot.trader.execute_trade(
                signal=mock_signal,
                symbol="BTCUSDT",
                order_type="Limit",
                dry_run=True
            )
            
            if result.get("status") == "executed_dry_run":
                self.log_test_result("Mock Trade Execution", True, "Dry run trade executed successfully")
            else:
                error = result.get("error", "Unknown error")
                self.log_test_result("Mock Trade Execution", False, f"Trade execution failed: {error}")
                
        except Exception as e:
            self.log_test_result("Mock Trade Execution", False, f"Error: {e}")
    
    async def run_production_tests(self):
        """Run all production tests."""
        print("🚀 Starting Production Real-Time Trade Tracking Tests")
        print("=" * 70)
        
        # Run tests
        await self.test_bot_initialization()
        self.test_database_schema_production()
        self.test_trade_tracker_integration()
        self.test_position_manager_integration()
        await self.test_realtime_monitoring_lifecycle()
        await self.test_incremental_sync()
        await self.test_mock_trade_execution()
        
        # Summary
        print("\n" + "=" * 70)
        print("📊 PRODUCTION TEST SUMMARY")
        print("=" * 70)
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 ALL PRODUCTION TESTS PASSED! System ready for live trading.")
        else:
            print(f"\n⚠️ {total - passed} tests failed. Please review before production use.")
        
        return passed == total


async def main():
    """Main test function."""
    test_suite = ProductionTradeTrackingTest()
    success = await test_suite.run_production_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
