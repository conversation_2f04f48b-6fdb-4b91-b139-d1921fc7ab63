#!/usr/bin/env python3
"""
Test script for the incremental sync functionality.
This script demonstrates how to use the new rebuild parameter.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.rebuild_sync_manager import RebuildSyncManager, SyncConfig

async def test_incremental_sync():
    """Test the incremental sync functionality."""
    
    # Initialize components
    data_agent = DataAgent()
    config = SyncConfig()
    sync_manager = RebuildSyncManager(data_agent, None, config)
    
    print("=== Testing Incremental Sync Functionality ===\n")
    
    # Test 1: Incremental sync (default behavior)
    print("1. Testing incremental sync (rebuild=False)...")
    result = await sync_manager.sync_with_exchange(rebuild=False)
    print(f"   Result: {result['status']}")
    print(f"   Sync mode: {result.get('sync_mode', 'N/A')}")
    print(f"   Trades processed: {result.get('trades_processed', 0)}")
    print(f"   Transitions processed: {result.get('transitions_processed', 0)}")
    print(f"   Open positions inserted: {result.get('open_positions_inserted', 0)}")
    print(f"   Duration: {result.get('sync_duration', 0):.2f}s\n")
    
    # Test 2: Full rebuild sync
    print("2. Testing full rebuild sync (rebuild=True)...")
    result = await sync_manager.sync_with_exchange(rebuild=True)
    print(f"   Result: {result['status']}")
    print(f"   Sync mode: {result.get('sync_mode', 'N/A')}")
    print(f"   Trades processed: {result.get('trades_processed', 0)}")
    print(f"   Trades created: {result.get('trades_created', 0)}")
    print(f"   Open positions inserted: {result.get('open_positions_inserted', 0)}")
    print(f"   Duration: {result.get('sync_duration', 0):.2f}s\n")
    
    # Test 3: Check sync status
    print("3. Checking sync status...")
    status = sync_manager.get_sync_status()
    print(f"   Is syncing: {status['is_syncing']}")
    print(f"   Total syncs: {status['sync_stats']['total_syncs']}")
    print(f"   Successful syncs: {status['sync_stats']['successful_syncs']}")
    print(f"   Failed syncs: {status['sync_stats']['failed_syncs']}")
    
    print("\n=== Test completed successfully! ===")

if __name__ == "__main__":
    asyncio.run(test_incremental_sync())
