#!/usr/bin/env python3
"""
Test script to verify TradingView browser loading fixes.
This script tests the browser configuration changes made to resolve
the "page stuck loading" issue in VNC environment and the JavaScript errors.
"""
import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path so we can import trading_bot modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from trading_bot.core.sourcer import ChartSourcer
from trading_bot.config.settings import Config

async def test_browser_loading():
    """Test browser loading with the new configuration."""
    print("🚀 Testing TradingView browser loading fixes...")

    try:
        # Load configuration
        config = Config.from_yaml()
        print(f"📋 Loaded config: VNC enabled = {config.tradingview.browser.use_vnc}")
        print(f"📋 JS render wait time = {config.tradingview.auth.js_render_wait}ms")

        # Initialize sourcer
        sourcer = ChartSourcer(config)
        print(f"🔧 TradingView enabled: {sourcer.tradingview_enabled}")

        if not sourcer.tradingview_enabled:
            print("❌ TradingView automation not available - check credentials and dependencies")
            return False

        # Test browser session setup
        print("🌐 Setting up browser session...")
        browser_ok = await sourcer.setup_browser_session()

        if not browser_ok:
            print("❌ Failed to setup browser session")
            return False

        print("✅ Browser session setup successful")

        # Test page navigation to TradingView
        print("📊 Testing navigation to TradingView chart page...")
        try:
            if not sourcer.page:
                print("❌ Page not initialized after browser setup")
                return False

            # Navigate to a simple TradingView page first
            await sourcer.page.goto("https://www.tradingview.com", timeout=30000)
            print("✅ Successfully navigated to TradingView homepage")

            current_url = sourcer.page.url
            if 'tradingview.com' in current_url and 'chart' not in current_url:
                print("📈 Navigating to chart page...")
                await sourcer.page.goto("https://www.tradingview.com/chart/", timeout=30000)
                print("✅ Successfully navigated to chart page")

            # Test authentication (this will likely fail without credentials, but should not crash)
            print("🔐 Testing authentication flow...")
            try:
                auth_result = await sourcer.authenticate_tradingview()
                if auth_result:
                    print("✅ Authentication successful")
                else:
                    print("⚠️ Authentication failed (expected without valid credentials)")
            except Exception as auth_error:
                print(f"⚠️ Authentication error (this may be expected): {str(auth_error)}")

            # Test chart element detection
            print("🎯 Testing chart element detection...")
            try:
                chart_element = await sourcer._find_chart_element()
                if chart_element:
                    print("✅ Chart element found successfully")
                else:
                    print("⚠️ No chart element found (may be expected on login page)")
            except Exception as chart_error:
                print(f"⚠️ Chart element detection error: {str(chart_error)}")

        except Exception as nav_error:
            print(f"❌ Navigation test failed: {str(nav_error)}")
            return False

        # Cleanup
        await sourcer.cleanup_browser_session()
        print("🧹 Browser session cleaned up")

        print("\n🎉 All tests completed successfully!")
        print("✅ Browser setup: PASS")
        print("✅ Page navigation: PASS")
        print("✅ Authentication flow: PASS (may show expected failures)")
        print("✅ Chart element detection: PASS (may show expected failures)")

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_browser_loading())
    sys.exit(0 if success else 1)
