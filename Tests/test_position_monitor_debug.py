#!/usr/bin/env python3
"""
Debug script to test the position monitor and identify the source of "Unknown error"
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from trading_bot.config.settings import load_config
from trading_bot.core.trader import TradeExecutor
from trading_bot.core.data_agent import DataAgent
from trading_bot.core.enhanced_position_monitor import EnhancedPositionMonitor

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def test_position_monitor():
    """Test the position monitor to identify the source of errors"""
    try:
        logger.info("🔍 Starting position monitor debug test")
        
        # Load configuration
        logger.info("🔍 Loading configuration...")
        config = load_config()
        logger.info("✅ Configuration loaded successfully")
        
        # Initialize data agent
        logger.info("🔍 Initializing data agent...")
        data_agent = DataAgent()
        logger.info("✅ Data agent initialized successfully")
        
        # Initialize trader
        logger.info("🔍 Initializing trader...")
        trader = TradeExecutor(config, data_agent)
        logger.info("✅ Trader initialized successfully")
        
        # Initialize position monitor
        logger.info("🔍 Initializing position monitor...")
        position_monitor = EnhancedPositionMonitor(
            trader=trader,
            config=config,
            logger=logger,
            data_agent=data_agent
        )
        logger.info("✅ Position monitor initialized successfully")
        
        # Test position monitoring
        logger.info("🔍 Testing position monitoring...")
        result = await position_monitor.monitor_positions()
        
        logger.info(f"📊 Position monitor result: {result}")
        
        if result.get("status") == "error":
            logger.error(f"❌ Position monitor failed with error: {result.get('error')}")
            logger.error(f"❌ Error type: {result.get('error_type')}")
        else:
            logger.info("✅ Position monitor completed successfully")
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        logger.error(f"❌ Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_position_monitor())
