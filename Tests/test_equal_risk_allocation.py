#!/usr/bin/env python3
"""
Test script for the new True Equal Risk Allocation implementation.

This script tests that:
1. Each position loses exactly the same risk amount when SL is hit
2. max_loss_usd serves as additional safety filter
3. Position values vary appropriately based on symbol volatility
4. Logging shows correct allocation information
"""

import sys
import os
from unittest.mock import Mock

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from trading_bot.core.risk_manager import RiskManager


class MockTrader:
    """Mock trader for testing position sizing."""

    def __init__(self):
        self.api_manager = Mock()
        self.get_wallet_balance = Mock()
        self.get_instruments_info = Mock()
        self.get_positions = Mock()
        self.get_open_orders = Mock()

    def setup_wallet_balance(self, balance_usdt=1000.0):
        """Setup mock wallet balance response."""
        self.get_wallet_balance.return_value = {
            "result": {
                "list": [{
                    "totalAvailableBalance": str(balance_usdt)
                }]
            }
        }

    def setup_instrument_info(self, symbol="BTCUSDT", min_qty=0.001, max_qty=100.0, qty_step=0.001):
        """Setup mock instrument info response."""
        self.get_instruments_info.return_value = {
            "result": {
                "list": [{
                    "lotSizeFilter": {
                        "minOrderQty": str(min_qty),
                        "maxOrderQty": str(max_qty),
                        "qtyStep": str(qty_step)
                    }
                }]
            }
        }

    def setup_positions_and_orders(self, active_positions=0, pending_orders=0):
        """Setup mock positions and orders responses."""
        # Mock positions response
        positions = []
        for i in range(active_positions):
            positions.append({
                "symbol": f"TEST{i}USDT",
                "size": "1.0"
            })

        self.get_positions.return_value = {
            "retCode": 0,
            "result": {
                "list": positions
            }
        }

        # Mock orders response
        orders = []
        for i in range(pending_orders):
            orders.append({
                "symbol": f"TEST{i}USDT",
                "side": "Buy",
                "orderStatus": "New",
                "qty": "1.0",
                "price": "50000.0"
            })

        self.get_open_orders.return_value = {
            "retCode": 0,
            "result": {
                "list": orders
            }
        }


class MockPositionManager:
    """Mock position manager for testing."""
    pass


class MockConfig:
    """Mock config for testing."""

    def __init__(self):
        # Trading config
        self.trading = Mock()
        self.trading.risk_percentage = 0.01  # 1%
        self.trading.max_loss_usd = 10.0     # $10 max loss
        self.trading.max_concurrent_trades = 3
        self.trading.position_sizing = Mock()
        self.trading.position_sizing.min_balance_threshold = 500.0
        self.trading.position_sizing.min_position_value_usd = 50.0


def test_equal_risk_allocation():
    """Test the new equal risk allocation implementation."""

    print("=" * 80)
    print("🧪 TESTING TRUE EQUAL RISK ALLOCATION")
    print("=" * 80)

    # Setup mocks
    mock_trader = MockTrader()
    mock_position_manager = MockPositionManager()
    mock_config = MockConfig()

    # Initialize risk manager
    risk_manager = RiskManager(mock_trader, mock_position_manager, mock_config)

    # Test scenarios with different symbols and price differences
    test_cases = [
        {
            "symbol": "BTCUSDT",
            "entry_price": 50000.0,
            "stop_loss": 49000.0,  # $1000 difference
            "description": "BTC with $1000 SL distance"
        },
        {
            "symbol": "ETHUSDT",
            "entry_price": 3000.0,
            "stop_loss": 2940.0,   # $60 difference
            "description": "ETH with $60 SL distance"
        },
        {
            "symbol": "ADAUSDT",
            "entry_price": 0.50,
            "stop_loss": 0.485,    # $0.015 difference
            "description": "ADA with $0.015 SL distance"
        }
    ]

    # Setup mock responses
    mock_trader.setup_wallet_balance(balance_usdt=1000.0)  # $1000 balance
    mock_trader.setup_instrument_info()
    mock_trader.setup_positions_and_orders(active_positions=0, pending_orders=0)  # No existing positions/orders

    print("\n📊 TEST SETUP:")
    print(f"   Account Balance: $1000.00")
    print(f"   Risk Percentage: {mock_config.trading.risk_percentage * 100}%")
    print(f"   Max Loss USD: ${mock_config.trading.max_loss_usd}")
    print(f"   Max Concurrent Trades: {mock_config.trading.max_concurrent_trades}")
    print(f"   Expected Total Risk: ${1000.0 * mock_config.trading.risk_percentage}")
    print(f"   Expected Risk per Slot: ${(1000.0 * mock_config.trading.risk_percentage) / mock_config.trading.max_concurrent_trades}")

    results = []

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"🧪 TEST CASE {i}: {test_case['description']}")
        print(f"{'='*60}")

        # Mock available slots (simulate 3 available slots)
        risk_manager.get_available_slots = Mock(return_value=3)

        # Calculate position size
        result = risk_manager.calculate_slot_based_position_size(
            entry_price=test_case['entry_price'],
            stop_loss=test_case['stop_loss'],
            symbol=test_case['symbol'],
            signal={"confidence": 0.8}
        )

        if "error" in result:
            print(f"❌ ERROR: {result['error']}")
            continue

        # Extract results
        position_size = result.get("position_size", 0)
        position_value = result.get("position_value", 0)
        price_diff = abs(test_case['entry_price'] - test_case['stop_loss'])
        actual_risk = position_size * price_diff

        print("📈 POSITION CALCULATION RESULTS:")
        print(f"   Symbol: {test_case['symbol']}")
        print(f"   Entry Price: ${test_case['entry_price']}")
        print(f"   Stop Loss: ${test_case['stop_loss']}")
        print(f"   Price Difference: ${price_diff}")
        print(f"   Position Size: {position_size:.6f} {test_case['symbol'].replace('USDT', '')} (${position_value:.2f} USD)")
        print(f"   Position Value: ${position_value:.2f}")
        print(f"   Actual Risk: ${actual_risk:.2f}")

        # Verify equal risk (accounting for exchange rounding)
        expected_risk_per_slot = (1000.0 * 0.01) / 3  # $10 total risk / 3 slots = $3.33
        risk_difference = abs(actual_risk - expected_risk_per_slot)

        # Allow for exchange rounding (qty_step) - risk should be very close to expected
        if risk_difference < 0.5:  # Allow up to $0.50 difference due to rounding
            print(f"✅ EQUAL RISK VERIFIED: ${actual_risk:.2f} (expected: ${expected_risk_per_slot:.2f}, diff: ${risk_difference:.2f})")
        else:
            print(f"❌ RISK MISMATCH: ${actual_risk:.2f} vs expected ${expected_risk_per_slot:.2f}")

        results.append({
            "symbol": test_case['symbol'],
            "position_size": position_size,
            "position_value": position_value,
            "actual_risk": actual_risk,
            "expected_risk": expected_risk_per_slot,
            "risk_match": risk_difference < 0.5  # Same tolerance as verification
        })

    # Summary
    print(f"\n{'='*80}")
    print("📊 TEST SUMMARY")
    print(f"{'='*80}")

    all_passed = True
    for result in results:
        status = "✅ PASS" if result['risk_match'] else "❌ FAIL"
        print(f"{status} {result['symbol']}: Risk=${result['actual_risk']:.2f} "
              f"(Expected=${result['expected_risk']:.2f})")

        if not result['risk_match']:
            all_passed = False

    print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")

    if all_passed:
        print("\n🎉 TRUE EQUAL RISK ALLOCATION IS WORKING CORRECTLY!")
        print("   - Each position has identical risk exposure")
        print("   - max_loss_usd serves as additional safety filter")
        print("   - Position values vary appropriately based on volatility")
    else:
        print("\n⚠️  ISSUES DETECTED - CHECK IMPLEMENTATION")

    return all_passed


def test_dual_filter_safety():
    """Test the dual filter approach with max_loss_usd."""

    print(f"\n{'='*80}")
    print("🛡️  TESTING DUAL FILTER SAFETY SYSTEM")
    print(f"{'='*80}")

    # Setup mocks
    mock_trader = MockTrader()
    mock_position_manager = MockPositionManager()
    mock_config = MockConfig()

    # Test with high risk scenario
    mock_config.trading.risk_percentage = 0.05  # 5% risk (higher than max_loss_usd)
    mock_config.trading.max_loss_usd = 10.0     # $10 cap

    risk_manager = RiskManager(mock_trader, mock_position_manager, mock_config)

    # Setup high balance to trigger max_loss_usd cap
    mock_trader.setup_wallet_balance(balance_usdt=10000.0)  # $10,000 balance
    mock_trader.setup_instrument_info()
    mock_trader.setup_positions_and_orders(active_positions=0, pending_orders=0)  # No existing positions/orders
    risk_manager.get_available_slots = Mock(return_value=1)  # 1 slot available

    print("\n📊 HIGH RISK SCENARIO TEST:")
    print(f"   Account Balance: $10,000.00")
    print(f"   Risk Percentage: {mock_config.trading.risk_percentage * 100}%")
    print(f"   Max Loss USD: ${mock_config.trading.max_loss_usd}")
    print(f"   Calculated Risk: ${10000.0 * mock_config.trading.risk_percentage}")
    print(f"   Expected Capped Risk: ${min(10000.0 * mock_config.trading.risk_percentage, mock_config.trading.max_loss_usd)}")

    result = risk_manager.calculate_slot_based_position_size(
        entry_price=50000.0,
        stop_loss=49000.0,
        symbol="BTCUSDT",
        signal={"confidence": 0.8}
    )

    if "error" in result:
        print(f"❌ ERROR: {result['error']}")
        return False

    position_size = result.get("position_size", 0)
    actual_risk = position_size * 1000  # $1000 price difference

    position_value_usd = position_size * 50000.0  # $50,000 entry price
    print("\n📈 RESULTS:")
    print(f"   Position Size: {position_size:.6f} BTC (${position_value_usd:.2f} USD)")
    print(f"   Actual Risk: ${actual_risk:.2f}")

    # Verify max_loss_usd cap is working
    if actual_risk <= mock_config.trading.max_loss_usd + 0.01:  # Allow small rounding
        print(f"✅ DUAL FILTER WORKING: Risk capped at ${actual_risk:.2f} (≤ ${mock_config.trading.max_loss_usd})")
        return True
    else:
        print(f"❌ FILTER FAILED: Risk ${actual_risk:.2f} exceeds cap ${mock_config.trading.max_loss_usd}")
        return False


if __name__ == "__main__":
    print("🚀 Starting Equal Risk Allocation Tests...")

    # Run tests
    test1_passed = test_equal_risk_allocation()
    test2_passed = test_dual_filter_safety()

    print(f"\n{'='*80}")
    print("🏁 FINAL TEST RESULTS")
    print(f"{'='*80}")
    print(f"Equal Risk Allocation Test: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"Dual Filter Safety Test: {'✅ PASS' if test2_passed else '❌ FAIL'}")

    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! TRUE EQUAL RISK ALLOCATION IS WORKING PERFECTLY!")
        sys.exit(0)
    else:
        print("\n⚠️  SOME TESTS FAILED - CHECK IMPLEMENTATION")
        sys.exit(1)
