#!/usr/bin/env python3
"""
Test script to verify database fixes for UNIQUE constraint and locking issues.
"""
import sys
import os
from pathlib import Path

# Add the project root to the Python path so we can import trading_bot modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.position_manager import PositionManager
from datetime import datetime, timezone
import tempfile
import sqlite3

def test_duplicate_order_id_handling():
    """Test that store_trade handles duplicate order_id gracefully."""
    print("Testing duplicate order_id handling...")

    # Create a temporary database for testing
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as temp_db:
        temp_db_path = temp_db.name

    try:
        # Initialize DataAgent with temp database
        data_agent = DataAgent(temp_db_path)

        # Test data
        order_id = "test_order_123"
        trade_data = {
            'trade_id': 'trade_001',
            'recommendation_id': 'rec_001',
            'symbol': 'BTCUSDT',
            'side': 'Buy',
            'quantity': 1.0,
            'entry_price': 50000.0,
            'take_profit': 51000.0,
            'stop_loss': 49000.0,
            'order_id': order_id,
            'orderLinkId': None,
            'pnl': 0.0,
            'status': 'open',
            'state': 'trade',
            'placed_by': 'BOT'
        }

        # First insertion should succeed
        result1 = data_agent.store_trade(**trade_data)
        print(f"First insertion result: {result1}")
        assert result1 == True, "First insertion should succeed"

        # Second insertion with same order_id should also succeed (no error)
        result2 = data_agent.store_trade(**trade_data)
        print(f"Second insertion result: {result2}")
        assert result2 == True, "Second insertion should succeed (duplicate handled gracefully)"

        # Verify only one record exists
        existing_trade = data_agent.get_trade_by_order_id(order_id)
        print(f"Retrieved trade: {existing_trade is not None}")
        assert existing_trade is not None, "Trade should exist in database"

        print("✅ Duplicate order_id handling test passed!")

    finally:
        # Clean up
        if os.path.exists(temp_db_path):
            os.unlink(temp_db_path)

def test_position_manager_duplicate_handling():
    """Test that PositionManager handles duplicates correctly."""
    print("Testing PositionManager duplicate handling...")

    # Create a temporary database for testing
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as temp_db:
        temp_db_path = temp_db.name

    try:
        # Initialize components
        data_agent = DataAgent(temp_db_path)

        # Create a mock trader object with minimal functionality
        class MockTrader:
            def get_exchange_sync_manager(self):
                return None  # This will test the fallback path

        position_manager = PositionManager(data_agent, MockTrader())

        order_id = "test_order_pm_123"

        # Test with exchange_data but no sync manager
        exchange_data = {
            "symbol": "BTCUSDT",
            "side": "Buy",
            "qty": 1.0,
            "price": 50000.0,
            "orderStatus": "NEW"
        }

        # First call should return error because sync manager is not available
        result1 = position_manager.ensure_trade_record(
            order_id=order_id,
            exchange_data=exchange_data
        )
        print(f"First ensure_trade_record result: {result1}")
        assert result1.get("error") == "Exchange sync manager not available", "Should return error when sync manager not available"

        # Test without exchange_data (should also return sync manager error)
        result2 = position_manager.ensure_trade_record(
            order_id=order_id,
            exchange_data=None
        )
        print(f"Second ensure_trade_record result: {result2}")
        assert result2.get("error") == "Exchange sync manager not available", "Should return same error when no exchange_data"

        print("✅ PositionManager duplicate handling test passed!")

    finally:
        # Clean up
        if os.path.exists(temp_db_path):
            os.unlink(temp_db_path)

def test_database_connection_settings():
    """Test that database connection settings are properly configured."""
    print("Testing database connection settings...")

    # Create a temporary database for testing
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as temp_db:
        temp_db_path = temp_db.name

    try:
        # Test connection creation
        data_agent = DataAgent(temp_db_path)
        conn = data_agent.get_connection()

        # Check PRAGMA settings
        cursor = conn.cursor()

        # Check journal mode
        cursor.execute("PRAGMA journal_mode")
        journal_mode = cursor.fetchone()[0]
        print(f"Journal mode: {journal_mode}")
        assert journal_mode == "wal", f"Expected WAL mode, got {journal_mode}"

        # Check synchronous mode
        cursor.execute("PRAGMA synchronous")
        sync_mode = cursor.fetchone()[0]
        print(f"Synchronous mode: {sync_mode}")
        # SQLite returns 1 for NORMAL mode
        assert sync_mode == 1, f"Expected NORMAL mode (1), got {sync_mode}"

        # Check busy timeout
        cursor.execute("PRAGMA busy_timeout")
        busy_timeout = cursor.fetchone()[0]
        print(f"Busy timeout: {busy_timeout}")
        assert busy_timeout == 10000, f"Expected 10000ms busy timeout, got {busy_timeout}"

        conn.close()
        print("✅ Database connection settings test passed!")

    finally:
        # Clean up
        if os.path.exists(temp_db_path):
            os.unlink(temp_db_path)

def main():
    """Run all tests."""
    print("Running database fixes tests...\n")

    try:
        test_duplicate_order_id_handling()
        print()

        test_position_manager_duplicate_handling()
        print()

        test_database_connection_settings()
        print()

        print("🎉 All tests passed! Database fixes are working correctly.")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0

if __name__ == "__main__":
    exit(main())