#!/usr/bin/env python3
"""Test script for real-time trade tracking system."""
import sys
import asyncio
import time
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from trading_bot.core.data_agent import DataAgent
from trading_bot.core.realtime_trade_tracker import RealTimeTradeTracker
from trading_bot.core.realtime_trade_monitor import RealTimeTradeMonitor
from trading_bot.core.incremental_sync_manager import IncrementalSyncManager
from trading_bot.core.bybit_api_manager import BybitAPIManager
from trading_bot.config.settings import Config


class RealTimeTradeTrackingTest:
    """Comprehensive test suite for real-time trade tracking."""

    def __init__(self):
        self.config = Config.from_yaml()
        self.data_agent = DataAgent()
        self.api_manager = BybitAPIManager(self.config, use_testnet=True)  # Use testnet for safety
        self.trade_tracker = RealTimeTradeTracker(self.data_agent)
        self.trade_monitor = RealTimeTradeMonitor(self.data_agent, self.api_manager, self.trade_tracker)
        self.incremental_sync = IncrementalSyncManager(self.data_agent, self.api_manager, self.trade_tracker)

        self.test_results = []
        self.test_counter = 0

    def cleanup_test_data(self):
        """Clean up test data between tests."""
        try:
            conn = self.data_agent.get_connection()
            cursor = conn.cursor()
            # Delete test trades
            cursor.execute("DELETE FROM trades WHERE id LIKE 'test_trade_%'")
            cursor.execute("DELETE FROM trades WHERE order_id LIKE 'mock_%' OR order_id LIKE 'status_%' OR order_id LIKE 'integrity_%'")
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Warning: Failed to cleanup test data: {e}")

    def get_unique_trade_id(self, prefix: str = "test_trade") -> str:
        """Generate unique trade ID for testing."""
        self.test_counter += 1
        return f"{prefix}_{self.test_counter}_{int(time.time())}"
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result."""
        status = "✅ PASS" if passed else "❌ FAIL"
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append((test_name, passed, message))
    
    def test_database_schema(self):
        """Test that database schema includes all required fields."""
        print("\n🔍 Testing Database Schema...")
        
        try:
            # Apply migrations to ensure all columns exist
            self.data_agent.migrate_database()
            
            # Check table structure
            conn = self.data_agent.get_connection()
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(trades)")
            columns = [column[1] for column in cursor.fetchall()]
            conn.close()
            
            required_columns = [
                'id', 'recommendation_id', 'symbol', 'side', 'quantity',
                'entry_price', 'take_profit', 'stop_loss', 'order_id',
                'orderLinkId', 'pnl', 'status', 'state', 'avg_exit_price',
                'closed_size', 'created_at', 'updated_at', 'placed_by',
                'alteration_details', 'prompt_name', 'timeframe',
                'confidence', 'risk_reward_ratio', 'order_type'
            ]
            
            missing_columns = [col for col in required_columns if col not in columns]
            
            if missing_columns:
                self.log_test_result("Database Schema", False, f"Missing columns: {missing_columns}")
            else:
                self.log_test_result("Database Schema", True, f"All {len(required_columns)} columns present")
                
        except Exception as e:
            self.log_test_result("Database Schema", False, f"Error: {e}")
    
    def test_trade_record_creation(self):
        """Test creating comprehensive trade records."""
        print("\n🔍 Testing Trade Record Creation...")

        try:
            self.cleanup_test_data()  # Clean up before test

            # Create test trade signal
            test_signal = {
                "recommendation": "BUY",
                "direction": "BUY",
                "quantity": 100.0,
                "entry_price": 50000.0,
                "take_profit": 52000.0,
                "stop_loss": 48000.0,
                "confidence": 0.85,
                "timeframe": "1h",
                "risk_reward_ratio": 2.5,
                "prompt_name": "TEST_PROMPT"
            }

            # Record trade placement with unique ID
            trade_id = self.get_unique_trade_id("creation_test")
            rec_id = self.get_unique_trade_id("creation_rec")

            result_trade_id = self.trade_tracker.record_trade_placement(
                trade_signal=test_signal,
                symbol="BTCUSDT",
                trade_id=trade_id,
                recommendation_id=rec_id
            )
            
            if result_trade_id:
                # Check if trade exists in database
                conn = self.data_agent.get_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM trades WHERE id = ?", (trade_id,))
                trade_row = cursor.fetchone()
                conn.close()

                if trade_row:
                    self.log_test_result("Trade Record Creation", True, f"Trade {trade_id} stored successfully")
                else:
                    self.log_test_result("Trade Record Creation", False, "Trade not found in database")
            else:
                self.log_test_result("Trade Record Creation", False, "Failed to create trade record")
                
        except Exception as e:
            self.log_test_result("Trade Record Creation", False, f"Error: {e}")
    
    def test_order_id_update(self):
        """Test updating trade with order ID from broker response."""
        print("\n🔍 Testing Order ID Update...")

        try:
            self.cleanup_test_data()  # Clean up before test

            # Create test trade
            test_signal = {
                "recommendation": "SELL",
                "direction": "SELL",
                "quantity": 50.0,
                "entry_price": 3000.0,
                "take_profit": 2900.0,
                "stop_loss": 3100.0,
                "confidence": 0.75,
                "timeframe": "4h",
                "risk_reward_ratio": 3.0
            }

            trade_id = self.get_unique_trade_id("order_update_test")
            rec_id = self.get_unique_trade_id("order_update_rec")

            result_trade_id = self.trade_tracker.record_trade_placement(
                trade_signal=test_signal,
                symbol="ETHUSDT",
                trade_id=trade_id,
                recommendation_id=rec_id
            )
            
            if result_trade_id:
                # Simulate broker response
                order_id = f"mock_order_{int(time.time())}"
                mock_broker_response = {
                    "result": {
                        "orderId": order_id,
                        "orderLinkId": f"mock_link_{int(time.time())}"
                    }
                }

                # Update with order ID
                update_success = self.trade_tracker.update_trade_with_order_id(trade_id, mock_broker_response)

                if update_success:
                    # Verify update
                    updated_trade = self.data_agent.get_trade_by_order_id(order_id)
                    if updated_trade and updated_trade.get('order_id') == order_id:
                        self.log_test_result("Order ID Update", True, "Order ID updated successfully")
                    else:
                        self.log_test_result("Order ID Update", False, "Order ID not found in updated trade")
                else:
                    self.log_test_result("Order ID Update", False, "Failed to update order ID")
            else:
                self.log_test_result("Order ID Update", False, "Failed to create initial trade")
                
        except Exception as e:
            self.log_test_result("Order ID Update", False, f"Error: {e}")
    
    def test_trade_status_update(self):
        """Test real-time trade status updates."""
        print("\n🔍 Testing Trade Status Update...")
        
        try:
            # Create trade with order ID
            test_signal = {
                "recommendation": "BUY",
                "quantity": 75.0,
                "entry_price": 1500.0,
                "take_profit": 1600.0,
                "stop_loss": 1400.0,
                "confidence": 0.9,
                "timeframe": "15m"
            }
            
            trade_id = self.trade_tracker.record_trade_placement(
                test_signal, "ADAUSDT", "test_trade_003", "test_rec_003"
            )
            
            # Add order ID
            mock_response = {"result": {"orderId": "status_test_order", "orderLinkId": "status_link"}}
            self.trade_tracker.update_trade_with_order_id(trade_id, mock_response)
            
            # Test status update
            exchange_data = {
                "orderStatus": "Filled",
                "avgPrice": "1505.0",
                "cumExecQty": "75.0"
            }
            
            status_update_success = self.trade_tracker.update_trade_status(
                "status_test_order", "filled", exchange_data
            )
            
            if status_update_success:
                # Verify status update
                updated_trade = self.data_agent.get_trade_by_order_id("status_test_order")
                if updated_trade and updated_trade.get('status') == 'filled':
                    self.log_test_result("Trade Status Update", True, "Status updated to filled")
                else:
                    self.log_test_result("Trade Status Update", False, "Status not updated correctly")
            else:
                self.log_test_result("Trade Status Update", False, "Failed to update status")
                
        except Exception as e:
            self.log_test_result("Trade Status Update", False, f"Error: {e}")
    
    def test_incremental_sync(self):
        """Test incremental sync functionality."""
        print("\n🔍 Testing Incremental Sync...")
        
        try:
            # Test incremental sync (this will use mock data since we're on testnet)
            stats = self.incremental_sync.sync_new_and_updated_trades(lookback_hours=1)
            
            if hasattr(stats, 'trades_checked'):
                self.log_test_result("Incremental Sync", True, 
                                   f"Sync completed: {stats.trades_checked} checked, {stats.trades_updated} updated")
            else:
                self.log_test_result("Incremental Sync", False, "Invalid sync stats returned")
                
        except Exception as e:
            self.log_test_result("Incremental Sync", False, f"Error: {e}")
    
    async def test_trade_monitor(self):
        """Test real-time trade monitoring."""
        print("\n🔍 Testing Trade Monitor...")
        
        try:
            # Start monitoring
            await self.trade_monitor.start_monitoring()
            
            # Add a test order to monitoring
            self.trade_monitor.add_order_to_monitoring("test_monitor_order")
            
            # Check monitoring status
            status = self.trade_monitor.get_monitoring_status()
            
            monitored_count = status.get('monitored_orders_count', 0)
            if status.get('is_monitoring') and monitored_count > 0:
                self.log_test_result("Trade Monitor", True,
                                   f"Monitor active with {monitored_count} orders")
            else:
                self.log_test_result("Trade Monitor", False, "Monitor not active or no orders")
            
            # Stop monitoring
            await self.trade_monitor.stop_monitoring()
            
        except Exception as e:
            self.log_test_result("Trade Monitor", False, f"Error: {e}")
    
    def test_data_integrity(self):
        """Test data integrity and no duplicates."""
        print("\n🔍 Testing Data Integrity...")

        try:
            self.cleanup_test_data()  # Clean up before test

            # Create multiple trades with same order ID (should not create duplicates)
            test_signal = {
                "recommendation": "BUY",
                "quantity": 100.0,
                "entry_price": 40000.0,
                "take_profit": 42000.0,
                "stop_loss": 38000.0
            }

            # First trade
            trade_id1 = self.get_unique_trade_id("integrity_test_1")
            rec_id1 = self.get_unique_trade_id("integrity_rec_1")

            result1 = self.trade_tracker.record_trade_placement(
                test_signal, "BTCUSDT", trade_id1, rec_id1
            )

            if result1:
                # Add order ID
                order_id = f"integrity_order_{int(time.time())}"
                mock_response = {"result": {"orderId": order_id, "orderLinkId": "integrity_link"}}
                self.trade_tracker.update_trade_with_order_id(trade_id1, mock_response)

                # Try to create another trade with same order ID (should be prevented by database constraint)
                trade_id2 = self.get_unique_trade_id("integrity_test_2")
                rec_id2 = self.get_unique_trade_id("integrity_rec_2")

                result2 = self.trade_tracker.record_trade_placement(
                    test_signal, "BTCUSDT", trade_id2, rec_id2
                )

                if result2:
                    # Try to update with same order ID - this should fail or be handled gracefully
                    self.trade_tracker.update_trade_with_order_id(trade_id2, mock_response)

                    # Check for duplicates
                    conn = self.data_agent.get_connection()
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM trades WHERE order_id = ?", (order_id,))
                    count = cursor.fetchone()[0]
                    conn.close()

                    if count == 1:
                        self.log_test_result("Data Integrity", True, "No duplicate order IDs created")
                    else:
                        self.log_test_result("Data Integrity", False, f"Found {count} trades with same order ID")
                else:
                    self.log_test_result("Data Integrity", False, "Failed to create second trade")
            else:
                self.log_test_result("Data Integrity", False, "Failed to create first trade")
                
        except Exception as e:
            self.log_test_result("Data Integrity", False, f"Error: {e}")
    
    async def run_all_tests(self):
        """Run all tests."""
        print("🚀 Starting Real-Time Trade Tracking Tests")
        print("=" * 60)
        
        # Run synchronous tests
        self.test_database_schema()
        self.test_trade_record_creation()
        self.test_order_id_update()
        self.test_trade_status_update()
        self.test_incremental_sync()
        self.test_data_integrity()
        
        # Run asynchronous tests
        await self.test_trade_monitor()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 ALL TESTS PASSED! Real-time trade tracking is working correctly.")
        else:
            print(f"\n⚠️ {total - passed} tests failed. Please review the issues above.")
        
        return passed == total


async def main():
    """Main test function."""
    test_suite = RealTimeTradeTrackingTest()
    success = await test_suite.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
