"""
Test file for count_open_positions_and_orders function.
This test verifies the counting logic works correctly in various scenarios.
"""
import unittest
from unittest.mock import Mock, patch
from trading_bot.core.utils import count_open_positions_and_orders

# Add the project root to the Python path so we can import trading_bot modules
sys.path.insert(0, str(Path(__file__).parent.parent))


class TestCountOpenPositionsAndOrders(unittest.TestCase):
    """Test cases for count_open_positions_and_orders function."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock trader instance
        self.mock_trader = Mock()

        # Sample position data
        self.sample_positions = [
            {
                "symbol": "BTCUSDT",
                "size": "0.001",
                "avgPrice": "50000"
            },
            {
                "symbol": "ETHUSDT",
                "size": "0.1",
                "avgPrice": "3000"
            },
            {
                "symbol": "ADAUSDT",
                "size": "0",  # Zero size position
                "avgPrice": "0.5"
            }
        ]

        # Sample order data with various types
        self.sample_orders = [
            # Entry orders (should be counted)
            {
                "symbol": "BTCUSDT",
                "orderStatus": "New",
                "orderType": "limit",
                "stopOrderType": ""  # Not a TP/SL order
            },
            {
                "symbol": "ETHUSDT",
                "orderStatus": "PartiallyFilled",
                "orderType": "market",
                "stopOrderType": None
            },
            # TP/SL orders (should NOT be counted as entry orders)
            {
                "symbol": "BTCUSDT",
                "orderStatus": "New",
                "orderType": "TakeProfit",
                "stopOrderType": "TakeProfit"
            },
            {
                "symbol": "ETHUSDT",
                "orderStatus": "Untriggered",
                "orderType": "StopLoss",
                "stopOrderType": "StopLoss"
            },
            # Cancelled order (should not be counted)
            {
                "symbol": "ADAUSDT",
                "orderStatus": "Cancelled",
                "orderType": "limit",
                "stopOrderType": ""
            }
        ]

    def test_count_with_provided_data(self):
        """Test counting with provided position and order data."""
        result = count_open_positions_and_orders(
            positions=self.sample_positions,
            orders=self.sample_orders
        )

        # Verify results
        self.assertEqual(result["total_positions"], 3)
        self.assertEqual(result["active_positions_count"], 2)  # BTC and ETH (ADA has size 0)
        self.assertEqual(result["open_entry_orders_count"], 2)  # BTC and ETH entry orders
        self.assertEqual(result["take_profit_orders"], 1)
        self.assertEqual(result["stop_loss_orders"], 1)

        # Verify symbol-specific counts
        self.assertEqual(result["active_positions"]["BTCUSDT"], 1)
        self.assertEqual(result["active_positions"]["ETHUSDT"], 1)
        self.assertNotIn("ADAUSDT", result["active_positions"])

        self.assertEqual(result["open_entry_orders"]["BTCUSDT"], 1)
        self.assertEqual(result["open_entry_orders"]["ETHUSDT"], 1)

    def test_count_with_successful_api_responses(self):
        """Test counting with successful API responses."""
        # Mock successful API responses
        self.mock_trader.get_positions.return_value = {
            "retCode": 0,
            "result": {"list": self.sample_positions}
        }
        self.mock_trader.get_realtime_orders.return_value = {
            "retCode": 0,
            "result": {"list": self.sample_orders}
        }

        result = count_open_positions_and_orders(trader=self.mock_trader)

        # Verify results
        self.assertEqual(result["total_positions"], 3)
        self.assertEqual(result["active_positions_count"], 2)
        self.assertEqual(result["open_entry_orders_count"], 2)
        self.assertEqual(result["take_profit_orders"], 1)
        self.assertEqual(result["stop_loss_orders"], 1)

    def test_count_with_api_failure_positions(self):
        """Test counting when positions API fails."""
        # Mock failed positions API
        self.mock_trader.get_positions.return_value = {
            "retCode": 10002,
            "retMsg": "invalid request, please check your server timestamp"
        }
        self.mock_trader.get_realtime_orders.return_value = {
            "retCode": 0,
            "result": {"list": self.sample_orders}
        }

        # Should raise exception due to positions API failure
        with self.assertRaises(Exception) as context:
            count_open_positions_and_orders(trader=self.mock_trader)

        self.assertIn("Failed to fetch positions from API", str(context.exception))

    def test_count_with_api_failure_orders(self):
        """Test counting when orders API fails."""
        # Mock failed orders API
        self.mock_trader.get_positions.return_value = {
            "retCode": 0,
            "result": {"list": self.sample_positions}
        }
        self.mock_trader.get_realtime_orders.return_value = {
            "retCode": 10002,
            "retMsg": "invalid request, please check your server timestamp"
        }

        # Should raise exception due to orders API failure
        with self.assertRaises(Exception) as context:
            count_open_positions_and_orders(trader=self.mock_trader)

        self.assertIn("Failed to fetch orders from API", str(context.exception))

    def test_count_with_empty_data(self):
        """Test counting with empty position and order data."""
        result = count_open_positions_and_orders(
            positions=[],
            orders=[]
        )

        # Verify results
        self.assertEqual(result["total_positions"], 0)
        self.assertEqual(result["active_positions_count"], 0)
        self.assertEqual(result["open_entry_orders_count"], 0)
        self.assertEqual(result["take_profit_orders"], 0)
        self.assertEqual(result["stop_loss_orders"], 0)

    def test_count_with_none_data(self):
        """Test counting with None data."""
        result = count_open_positions_and_orders(
            positions=None,
            orders=None
        )

        # Should handle None gracefully
        self.assertEqual(result["total_positions"], 0)
        self.assertEqual(result["active_positions_count"], 0)
        self.assertEqual(result["open_entry_orders_count"], 0)

    def test_count_with_mixed_order_statuses(self):
        """Test counting with various order statuses."""
        mixed_orders = [
            {"symbol": "BTCUSDT", "orderStatus": "New", "orderType": "limit", "stopOrderType": ""},
            {"symbol": "ETHUSDT", "orderStatus": "PartiallyFilled", "orderType": "market", "stopOrderType": ""},
            {"symbol": "ADAUSDT", "orderStatus": "Filled", "orderType": "limit", "stopOrderType": ""},  # Should not count
            {"symbol": "DOTUSDT", "orderStatus": "Cancelled", "orderType": "limit", "stopOrderType": ""},  # Should not count
            {"symbol": "LINKUSDT", "orderStatus": "Rejected", "orderType": "limit", "stopOrderType": ""},  # Should not count
        ]

        result = count_open_positions_and_orders(
            positions=[],
            orders=mixed_orders
        )

        # Should only count New and PartiallyFilled orders
        self.assertEqual(result["open_entry_orders_count"], 2)
        self.assertEqual(result["open_entry_orders"]["BTCUSDT"], 1)
        self.assertEqual(result["open_entry_orders"]["ETHUSDT"], 1)

    def test_count_with_tp_sl_orders_only(self):
        """Test counting with only TP/SL orders."""
        tp_sl_only_orders = [
            {
                "symbol": "BTCUSDT",
                "orderStatus": "New",
                "orderType": "TakeProfit",
                "stopOrderType": "TakeProfit"
            },
            {
                "symbol": "ETHUSDT",
                "orderStatus": "Untriggered",
                "orderType": "StopLoss",
                "stopOrderType": "StopLoss"
            }
        ]

        result = count_open_positions_and_orders(
            positions=[],
            orders=tp_sl_only_orders
        )

        # Should count TP/SL orders but not as entry orders
        self.assertEqual(result["open_entry_orders_count"], 0)
        self.assertEqual(result["take_profit_orders"], 1)
        self.assertEqual(result["stop_loss_orders"], 1)

    def test_count_with_zero_size_positions(self):
        """Test counting with positions that have zero size."""
        zero_size_positions = [
            {"symbol": "BTCUSDT", "size": "0", "avgPrice": "50000"},
            {"symbol": "ETHUSDT", "size": "0.1", "avgPrice": "3000"},
            {"symbol": "ADAUSDT", "size": "0.0", "avgPrice": "0.5"}
        ]

        result = count_open_positions_and_orders(
            positions=zero_size_positions,
            orders=[]
        )

        # Should only count non-zero positions
        self.assertEqual(result["total_positions"], 3)
        self.assertEqual(result["active_positions_count"], 1)  # Only ETH
        self.assertEqual(result["active_positions"]["ETHUSDT"], 1)

    def test_count_with_raw_data_preservation(self):
        """Test that raw data is preserved in result."""
        result = count_open_positions_and_orders(
            positions=self.sample_positions,
            orders=self.sample_orders
        )

        # Verify raw data is preserved
        self.assertEqual(len(result["raw_positions"]), 3)
        self.assertEqual(len(result["raw_orders"]), 5)
        self.assertEqual(result["raw_positions"], self.sample_positions)
        self.assertEqual(result["raw_orders"], self.sample_orders)


if __name__ == '__main__':
    unittest.main()
