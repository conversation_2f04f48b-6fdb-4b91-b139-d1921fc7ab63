#!/usr/bin/env python3
"""
Real API test for cancel_order_with_verification function.

This test uses real Bybit API calls to test the cancellation utility function
and ensure it only cancels entry orders for BTCUSDT, not TP/SL orders.
"""

import sys
import os
import logging
from datetime import datetime, timezone

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from trading_bot.core.utils import cancel_order_with_verification, _is_tp_sl_order
from trading_bot.core.trader import TradeExecutor
from trading_bot.config.settings import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_real_cancel_order():
    """Test the cancel_order_with_verification function with real API calls."""

    print("🔄 Starting real API test for cancel_order_with_verification")
    print("=" * 60)

    try:
        # Initialize trader with real API
        config = Config.from_yaml()
        trader = TradeExecutor(config, use_testnet=False)  # Use live trading

        print("✅ Trader initialized successfully")

        # Test connection
        connection_test = trader.test_connection()
        if not connection_test.get("connected", False):
            print("❌ Failed to connect to Bybit API")
            return False

        print("✅ Bybit API connection successful")

        # Get open orders for BTCUSDT
        symbol = "BTCUSDT"
        print(f"\n🔍 Getting open orders for {symbol}...")

        orders_response = trader.get_open_orders(symbol=symbol, openOnly=0, limit=50)

        if orders_response.get("retCode") != 0:
            print(f"❌ Failed to get orders: {orders_response.get('retMsg', 'API error')}")
            return False

        orders = orders_response.get("result", {}).get("list", [])
        print(f"✅ Found {len(orders)} open orders for {symbol}")

        if not orders:
            print(f"ℹ️ No open orders found for {symbol}")
            return True

        # Analyze orders
        entry_orders = []
        tp_sl_orders = []

        print(f"\n📊 Analyzing {len(orders)} orders:")
        print("-" * 40)

        for i, order in enumerate(orders, 1):
            order_id = order.get("orderId", "N/A")
            order_link_id = order.get("orderLinkId", "")
            order_type = order.get("orderType", "")
            stop_order_type = order.get("stopOrderType", "")
            side = order.get("side", "")
            qty = order.get("qty", "0")
            price = order.get("price", "0")
            status = order.get("orderStatus", "")

            is_tp_sl = _is_tp_sl_order(order)

            print(f"{i}. Order ID: {order_id}")
            print(f"   Link ID: {order_link_id or 'N/A'}")
            print(f"   Type: {order_type}, Stop Type: {stop_order_type}")
            print(f"   Side: {side}, Qty: {qty}, Price: {price}")
            print(f"   Status: {status}")
            print(f"   Is TP/SL: {is_tp_sl}")

            if is_tp_sl:
                tp_sl_orders.append(order)
                print("   🛡️  TP/SL ORDER - WILL BE PROTECTED")
            else:
                entry_orders.append(order)
                print("   🎯 ENTRY ORDER - CAN BE CANCELLED")
            print()

        print(f"📈 Summary:")
        print(f"   Entry orders: {len(entry_orders)}")
        print(f"   TP/SL orders: {len(tp_sl_orders)}")
        print()

        # Test TP/SL order protection
        if tp_sl_orders:
            print("🛡️  Testing TP/SL order protection...")
            test_order = tp_sl_orders[0]
            order_id = test_order.get("orderId")
            order_link_id = test_order.get("orderLinkId")

            print(f"   Attempting to cancel TP/SL order: {order_id or order_link_id}")

            result = cancel_order_with_verification(
                trader=trader,
                symbol=symbol,
                order_id=order_id,
                order_link_id=order_link_id if not order_id else None
            )

            if not result["success"] and "TP/SL" in result["error"]:
                print("   ✅ TP/SL order correctly blocked from cancellation")
                print(f"   Error: {result['error']}")
            else:
                print("   ❌ TP/SL order was not blocked!")
                print(f"   Result: {result}")
                return False
        else:
            print("ℹ️ No TP/SL orders found to test protection")

        # Test entry order cancellation (if available)
        if entry_orders:
            print("\n🎯 Testing entry order cancellation...")

            # Ask user for confirmation before cancelling real orders
            print("⚠️  WARNING: This will cancel a real order!")
            response = input("   Do you want to proceed with cancelling an entry order? (yes/no): ").lower().strip()

            if response not in ['yes', 'y']:
                print("   ℹ️ Test cancelled by user")
                return True

            test_order = entry_orders[0]
            order_id = test_order.get("orderId")
            order_link_id = test_order.get("orderLinkId")

            print(f"   Attempting to cancel entry order: {order_id or order_link_id}")

            result = cancel_order_with_verification(
                trader=trader,
                symbol=symbol,
                order_id=order_id,
                order_link_id=order_link_id if not order_id else None
            )

            if result["success"]:
                print("   ✅ Entry order successfully cancelled")
                print(f"   Status: {result['status']}")
                print(f"   Attempts: {result['attempts']}")
                if result.get("verification_success"):
                    print("   ✅ Cancellation verified")
                else:
                    print("   ⚠️ Cancellation verification failed")
            else:
                print("   ❌ Entry order cancellation failed")
                print(f"   Error: {result.get('error', 'Unknown error')}")
                return False
        else:
            print("ℹ️ No entry orders found to test cancellation")

        print("\n🎉 Real API test completed successfully!")
        print("✅ TP/SL orders are protected")
        print("✅ Entry orders can be cancelled")
        print("✅ Single source of truth working correctly")

        return True

    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_order_analysis():
    """Show detailed analysis of current orders without cancelling."""

    print("🔍 Order Analysis for BTCUSDT")
    print("=" * 40)

    try:
        config = Config.from_yaml()
        trader = TradeExecutor(config, use_testnet=False)

        # Get open orders
        orders_response = trader.get_open_orders(symbol="BTCUSDT", openOnly=0, limit=50)

        if orders_response.get("retCode") != 0:
            print(f"❌ Failed to get orders: {orders_response.get('retMsg', 'API error')}")
            return

        orders = orders_response.get("result", {}).get("list", [])

        if not orders:
            print("ℹ️ No open orders found for BTCUSDT")
            return

        print(f"Found {len(orders)} open orders:")
        print()

        for i, order in enumerate(orders, 1):
            order_id = order.get("orderId", "N/A")
            order_link_id = order.get("orderLinkId", "")
            order_type = order.get("orderType", "")
            stop_order_type = order.get("stopOrderType", "")
            side = order.get("side", "")
            qty = order.get("qty", "0")
            price = order.get("price", "0")
            status = order.get("orderStatus", "")

            is_tp_sl = _is_tp_sl_order(order)

            print(f"Order {i}:")
            print(f"  ID: {order_id}")
            print(f"  Link ID: {order_link_id or 'N/A'}")
            print(f"  Type: {order_type}")
            print(f"  Stop Type: {stop_order_type}")
            print(f"  Side: {side}")
            print(f"  Quantity: {qty}")
            print(f"  Price: {price}")
            print(f"  Status: {status}")
            print(f"  Is TP/SL: {is_tp_sl}")
            print(f"  Can Cancel: {'❌ No (TP/SL protected)' if is_tp_sl else '✅ Yes (Entry order)'}")
            print()

    except Exception as e:
        print(f"❌ Analysis failed: {e}")


if __name__ == "__main__":
    print("Real API Test for Order Cancellation")
    print("====================================")

    # First show order analysis
    show_order_analysis()

    print("\n" + "="*60)
    choice = input("Do you want to run the full cancellation test? (yes/no): ").lower().strip()

    if choice in ['yes', 'y']:
        success = test_real_cancel_order()
        if success:
            print("\n🎉 All tests passed!")
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
    else:
        print("ℹ️ Test cancelled by user")
