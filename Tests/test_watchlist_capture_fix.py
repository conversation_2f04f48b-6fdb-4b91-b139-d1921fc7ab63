#!/usr/bin/env python3
"""
Test script to verify the updated watchlist capture logic.
This script tests that the capture_all_watchlist_screenshots method:
1. Uses target_chart for authentication when provided
2. Then captures all individual watchlist symbols (not just the target chart)
3. Works correctly in both modes (with and without target_chart)
"""
import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path so we can import trading_bot modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from trading_bot.core.sourcer import ChartSourcer
from trading_bot.config.settings import Config

async def test_watchlist_capture_logic():
    """Test the updated watchlist capture logic."""
    print("🧪 Testing updated watchlist capture logic...")

    try:
        # Load configuration
        config = Config.from_yaml()
        print(f"📋 Loaded config: target_chart = {config.tradingview.target_chart}")

        # Initialize sourcer
        sourcer = ChartSourcer(config)
        print(f"🔧 TradingView enabled: {sourcer.tradingview_enabled}")

        if not sourcer.tradingview_enabled:
            print("❌ TradingView automation not available - check credentials and dependencies")
            return False

        # Test 1: Standard watchlist mode (without target_chart)
        print("\n📊 Test 1: Standard watchlist mode (no target_chart)")
        try:
            # Setup browser session
            browser_ok = await sourcer.setup_browser_session()
            if not browser_ok:
                print("❌ Failed to setup browser session")
                return False

            # Test watchlist capture without target_chart
            print("📸 Testing watchlist capture without target_chart...")
            screenshot_paths = await sourcer.capture_all_watchlist_screenshots(timeframe="1d")

            print(f"📊 Captured {len(screenshot_paths)} symbols in standard mode")
            for symbol, path in screenshot_paths.items():
                print(f"  ✅ {symbol}: {Path(path).name}")

            # Cleanup after test
            await sourcer.cleanup_browser_session()

        except Exception as e:
            print(f"❌ Standard watchlist mode test failed: {str(e)}")
            await sourcer.cleanup_browser_session()
            return False

        # Test 2: Watchlist mode with target_chart authentication
        print("\n🔐 Test 2: Watchlist mode with target_chart authentication")
        try:
            # Setup fresh browser session
            browser_ok = await sourcer.setup_browser_session()
            if not browser_ok:
                print("❌ Failed to setup browser session")
                return False

            # Test watchlist capture with target_chart for authentication
            target_chart_url = config.tradingview.target_chart
            if target_chart_url:
                print(f"🔐 Testing watchlist capture with target_chart authentication: {target_chart_url}")
                screenshot_paths = await sourcer.capture_all_watchlist_screenshots(
                    target_chart=target_chart_url,
                    timeframe="1d"
                )

                print(f"📊 Captured {len(screenshot_paths)} symbols with target_chart auth")
                for symbol, path in screenshot_paths.items():
                    print(f"  ✅ {symbol}: {Path(path).name}")

                # Cleanup after test
                await sourcer.cleanup_browser_session()
            else:
                print("⚠️ No target_chart configured, skipping target_chart authentication test")

        except Exception as e:
            print(f"❌ Target chart authentication test failed: {str(e)}")
            await sourcer.cleanup_browser_session()
            return False

        print("\n🎉 All watchlist capture tests completed successfully!")
        print("✅ Standard watchlist mode: PASS")
        print("✅ Target chart authentication mode: PASS")
        print("✅ Multiple symbol capture: VERIFIED")

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_watchlist_capture_logic())
    sys.exit(0 if success else 1)