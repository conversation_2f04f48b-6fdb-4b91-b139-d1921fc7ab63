#!/usr/bin/env python3
"""
Test script to verify the fix for missing timeframe and created_at warnings.
This tests that positions without trade data don't generate warnings.
"""
import asyncio
import logging
from datetime import datetime, timezone
from trading_bot.core.common_types import PositionInfo
from trading_bot.core.enhanced_position_monitor import EnhancedPositionMonitor

# Add the project root to the Python path so we can import trading_bot modules
sys.path.insert(0, str(Path(__file__).parent.parent))

# Mock configuration class
class MockConfig:
    def __init__(self):
        self.trading = self

    @property
    def enable_sl_tightening(self):
        return True

    @property
    def enable_tp_proximity_trailing(self):
        return True

    @property
    def tp_proximity_threshold_pct(self):
        return 2.0

    @property
    def tp_proximity_trailing_pct(self):
        return 1.0

    @property
    def age_tightening_bars(self):
        return {'1h': 24}

# Mock trader class
class MockTrader:
    def __init__(self):
        self.api_manager = None
        self.position_manager = None

    def get_last_close_price(self, symbol, interval):
        return 100.0

# Mock logger that captures log messages
class MockLogger:
    def __init__(self):
        self.messages = []

    def info(self, msg):
        self.messages.append(('INFO', msg))

    def warning(self, msg):
        self.messages.append(('WARNING', msg))

    def error(self, msg):
        self.messages.append(('ERROR', msg))

    def debug(self, msg):
        self.messages.append(('DEBUG', msg))

async def test_missing_timeframe_fix():
    """Test that positions without trade data don't generate warnings"""
    print("🧪 Testing Missing Timeframe Fix")
    print("=" * 50)

    # Create mock objects
    config = MockConfig()
    trader = MockTrader()
    logger = MockLogger()

    # Create enhanced position monitor
    monitor = EnhancedPositionMonitor(trader, config, logger)

    # Test 1: Position without trade data (has_trade_data=False)
    print("\n📊 Test 1: Position without trade data")
    position_no_trade = PositionInfo(
        symbol="TESTUSDT",
        side="Buy",
        size=1.0,
        entry_price=100.0,
        current_price=105.0,  # 5% profit
        unrealized_pnl=5.0,
        current_stop_loss=98.0,
        current_take_profit=110.0,
        position_idx=0,
        risk_amount=2.0,
        timeframe=None,  # Missing
        created_at=None,  # Missing
        has_trade_data=False  # No trade data
    )

    result = await monitor._check_and_tighten_stop_loss_enhanced(position_no_trade)

    # Check that no warning was logged
    warnings = [msg for level, msg in logger.messages if level == 'WARNING' and 'Missing timeframe or created_at' in msg]
    print(f"Warnings logged: {len(warnings)}")
    if warnings:
        print(f"Warning: {warnings[0]}")
    else:
        print("✅ No warning logged for position without trade data")

    # Test 2: Position with trade data but missing fields (low profit to trigger age-based check)
    print("\n📊 Test 2: Position with trade data but missing fields")
    logger.messages.clear()  # Clear previous messages

    position_with_trade_missing_fields = PositionInfo(
        symbol="TESTUSDT",
        side="Buy",
        size=1.0,
        entry_price=100.0,
        current_price=100.5,  # 0.5% profit = 0.25R (less than 1R)
        unrealized_pnl=0.5,
        current_stop_loss=98.0,
        current_take_profit=110.0,
        position_idx=0,
        risk_amount=2.0,
        timeframe=None,  # Missing
        created_at=None,  # Missing
        has_trade_data=True  # Has trade data but fields missing
    )

    result = await monitor._check_and_tighten_stop_loss_enhanced(position_with_trade_missing_fields)

    # Check that warning was logged
    warnings = [msg for level, msg in logger.messages if level == 'WARNING' and 'Missing timeframe or created_at' in msg]
    print(f"Warnings logged: {len(warnings)}")
    if warnings:
        print(f"✅ Warning logged for position with trade data but missing fields: {warnings[0][1]}")
    else:
        print("❌ No warning logged (expected warning)")
        print("All logged messages:")
        for level, msg in logger.messages:
            print(f"  {level}: {msg}")

    # Test 3: Position with complete trade data
    print("\n📊 Test 3: Position with complete trade data")
    logger.messages.clear()  # Clear previous messages

    position_complete = PositionInfo(
        symbol="TESTUSDT",
        side="Buy",
        size=1.0,
        entry_price=100.0,
        current_price=105.0,  # 5% profit
        unrealized_pnl=5.0,
        current_stop_loss=98.0,
        current_take_profit=110.0,
        position_idx=0,
        risk_amount=2.0,
        timeframe='1h',  # Present
        created_at=datetime.now(timezone.utc),  # Present
        has_trade_data=True
    )

    result = await monitor._check_and_tighten_stop_loss_enhanced(position_complete)

    # Check that no warning was logged
    warnings = [msg for level, msg in logger.messages if level == 'WARNING' and 'Missing timeframe or created_at' in msg]
    print(f"Warnings logged: {len(warnings)}")
    if warnings:
        print(f"❌ Unexpected warning: {warnings[0]}")
    else:
        print("✅ No warning logged for position with complete trade data")

    print("\n✅ All tests completed!")
    print("\nSummary:")
    print("• Positions without trade data: No warning (correct)")
    print("• Positions with trade data but missing fields: Warning logged (correct)")
    print("• Positions with complete trade data: No warning (correct)")

if __name__ == "__main__":
    asyncio.run(test_missing_timeframe_fix())