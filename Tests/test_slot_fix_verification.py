#!/usr/bin/env python3
"""
Test to verify the slot bypass fix in execute_trade method.
This test confirms that the return statement now properly blocks trades when slots are exceeded.
"""
import sys
import os
import logging
from unittest.mock import Mock, patch, MagicMock

# Add the project root to the Python path so we can import trading_bot modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_slot_fix():
    """Test that the slot bypass fix works correctly."""
    print("=" * 60)
    print("🧪 TESTING SLOT BYPASS FIX")
    print("=" * 60)

    # Mock configuration
    class MockConfig:
        class Trading:
            max_concurrent_trades = 2  # Set to 2 for testing
            min_rr = 1.5
            risk_tolerance = 0.5
            min_confidence_threshold = 0.6
            paper_trading = True
            risk_percentage = 0.01
            max_loss_usd = 200.0
            leverage = 1
            balance_safety_margin = 0.8
            auto_approve_trades = False
            max_trades_check_mode = "positions"
            age_tightening_bars = {}
            enable_dynamic_risk_allocation = True
            order_replacement = None
            exchange_sync = None

        class MockBybitConfig:
            recv_window = 60000
            use_testnet = True
            max_retries = 3
            circuit_breaker = None

        class MockPathsConfig:
            database = "trading_bot/data/analysis_results.db"
            charts = "trading_bot/data/charts"
            logs = "trading_bot/logs"
            session_file = "trading_bot/data/"

        class MockOpenAIConfig:
            api_key = "test_key"
            model = "gpt-4"
            max_tokens = 1000
            temperature = 0.7
            assistant = None

        class MockAgentConfig:
            model = "gpt-4"
            max_tokens = 1000
            temperature = 0.7

        class MockTradingViewConfig:
            enabled = False
            base_url = "https://www.tradingview.com"
            login_url = "https://www.tradingview.com/accounts/signin/"
            chart_url_template = "https://www.tradingview.com/chart/?symbol={symbol}"
            browser = None
            auth = None
            screenshot = None
            rate_limit = None
            retry = None
            target_chart = None

        class MockFileManagementConfig:
            enable_backup = False

        def __init__(self):
            self.trading = self.Trading()
            self.bybit = self.MockBybitConfig()
            self.paths = self.MockPathsConfig()
            self.openai = self.MockOpenAIConfig()
            self.agents = {'default': self.MockAgentConfig()}
            self.tradingview = self.MockTradingViewConfig()
            self.file_management = self.MockFileManagementConfig()

    # Mock API Manager
    mock_api_manager = Mock()
    mock_api_manager.get_positions.return_value = {
        'retCode': 0,
        'result': {'list': [
            {'symbol': 'BTCUSDT', 'size': 0.1},  # 1 position
            {'symbol': 'ETHUSDT', 'size': 0.5}   # 2 positions total
        ]}
    }
    mock_api_manager.get_open_orders.return_value = {
        'retCode': 0,
        'result': {'list': []}  # No pending orders
    }
    mock_api_manager.get_fee_rates.return_value = {
        'result': {'list': [{'takerFeeRate': '0.001'}]}
    }

    # Mock Risk Manager that correctly blocks trades
    mock_risk_manager = Mock()
    mock_risk_manager.can_execute_trade.return_value = {
        'can_execute': False,
        'reason': 'Maximum concurrent trades (2) exceeded'
    }

    # Mock the BybitAPIManager class
    with patch('trading_bot.core.trader.BybitAPIManager') as mock_api_class:
        mock_api_class.return_value = mock_api_manager

        # Import and test the TradeExecutor
        from trading_bot.core.trader import TradeExecutor

        config = MockConfig()
        trader = TradeExecutor(
            config=config,
            use_testnet=True,
            risk_manager=mock_risk_manager
        )

        # Test signal with valid RR ratio (> 1.5)
        test_signal = {
            'recommendation': 'BUY',
            'entry_price': 50000.0,
            'take_profit': 52000.0,  # Higher TP for better RR
            'stop_loss': 49500.0,
            'symbol': 'ADAUSDT',
            'direction': 'LONG'
        }

        print("\n📊 Test Case: Slot limit exceeded (should be blocked)")
        print("   Max concurrent trades: 2")
        print("   Current positions: 2 (at limit)")
        print("   Attempting to execute 3rd trade...")

        # Execute trade - this should now be blocked
        result = trader.execute_trade(test_signal, 'ADAUSDT')

        # Check results
        slot_check_called = mock_risk_manager.can_execute_trade.called
        trade_blocked = result.get('error') is not None
        error_contains_slot_limit = 'Slot limit exceeded' in str(result.get('error', ''))

        print(f"   Slot check called: {slot_check_called}")
        print(f"   Trade blocked: {trade_blocked}")
        print(f"   Error contains slot limit: {error_contains_slot_limit}")
        print(f"   Result: {result}")

        if slot_check_called and trade_blocked and error_contains_slot_limit:
            print("   ✅ PASS - Slot bypass fix is working correctly!")
            return True
        else:
            print("   ❌ FAIL - Slot bypass fix is not working")
            return False

def test_slot_fix_with_available_slots():
    """Test that trades still execute when slots are available."""
    print("\n" + "=" * 60)
    print("🧪 TESTING SLOT FIX WITH AVAILABLE SLOTS")
    print("=" * 60)

    # Mock configuration
    class MockConfig:
        class Trading:
            max_concurrent_trades = 3
            min_rr = 1.5
            risk_tolerance = 0.5
            min_confidence_threshold = 0.6
            paper_trading = True
            risk_percentage = 0.01
            max_loss_usd = 200.0
            leverage = 1
            balance_safety_margin = 0.8
            auto_approve_trades = False
            max_trades_check_mode = "positions"
            age_tightening_bars = {}
            enable_dynamic_risk_allocation = True
            order_replacement = None
            exchange_sync = None

        class MockBybitConfig:
            recv_window = 60000
            use_testnet = True
            max_retries = 3
            circuit_breaker = None

        class MockPathsConfig:
            database = "trading_bot/data/analysis_results.db"
            charts = "trading_bot/data/charts"
            logs = "trading_bot/logs"
            session_file = "trading_bot/data/"

        class MockOpenAIConfig:
            api_key = "test_key"
            model = "gpt-4"
            max_tokens = 1000
            temperature = 0.7
            assistant = None

        class MockAgentConfig:
            model = "gpt-4"
            max_tokens = 1000
            temperature = 0.7

        class MockTradingViewConfig:
            enabled = False
            base_url = "https://www.tradingview.com"
            login_url = "https://www.tradingview.com/accounts/signin/"
            chart_url_template = "https://www.tradingview.com/chart/?symbol={symbol}"
            browser = None
            auth = None
            screenshot = None
            rate_limit = None
            retry = None
            target_chart = None

        class MockFileManagementConfig:
            enable_backup = False

        def __init__(self):
            self.trading = self.Trading()
            self.bybit = self.MockBybitConfig()
            self.paths = self.MockPathsConfig()
            self.openai = self.MockOpenAIConfig()
            self.agents = {'default': self.MockAgentConfig()}
            self.tradingview = self.MockTradingViewConfig()
            self.file_management = self.MockFileManagementConfig()

    # Mock API Manager
    mock_api_manager = Mock()
    mock_api_manager.get_positions.return_value = {
        'retCode': 0,
        'result': {'list': []}  # No positions
    }
    mock_api_manager.get_open_orders.return_value = {
        'retCode': 0,
        'result': {'list': []}  # No pending orders
    }
    mock_api_manager.get_fee_rates.return_value = {
        'result': {'list': [{'takerFeeRate': '0.001'}]}
    }

    # Mock Risk Manager that allows trades
    mock_risk_manager = Mock()
    mock_risk_manager.can_execute_trade.return_value = {
        'can_execute': True,
        'reason': 'Slots available'
    }

    # Mock the BybitAPIManager class
    with patch('trading_bot.core.trader.BybitAPIManager') as mock_api_class:
        mock_api_class.return_value = mock_api_manager

        # Import and test the TradeExecutor
        from trading_bot.core.trader import TradeExecutor

        config = MockConfig()
        trader = TradeExecutor(
            config=config,
            use_testnet=True,
            risk_manager=mock_risk_manager
        )

        # Test signal with valid RR ratio (> 1.5)
        test_signal = {
            'recommendation': 'BUY',
            'entry_price': 50000.0,
            'take_profit': 52000.0,  # Higher TP for better RR
            'stop_loss': 49500.0,
            'symbol': 'ADAUSDT',
            'direction': 'LONG'
        }

        print("\n📊 Test Case: Slots available (should execute)")
        print("   Max concurrent trades: 3")
        print("   Current positions: 0")
        print("   Attempting to execute 1st trade...")

        # Mock the place_market_order_with_tp_sl method
        with patch.object(trader, 'place_market_order_with_tp_sl') as mock_place_order:
            mock_place_order.return_value = {'success': True}

            # Execute trade - this should now execute
            result = trader.execute_trade(test_signal, 'ADAUSDT')

            # Check results
            slot_check_called = mock_risk_manager.can_execute_trade.called
            trade_executed = mock_place_order.called
            no_error = result.get('error') is None

            print(f"   Slot check called: {slot_check_called}")
            print(f"   Trade executed: {trade_executed}")
            print(f"   No error returned: {no_error}")

            if slot_check_called and trade_executed and no_error:
                print("   ✅ PASS - Normal trade execution still works!")
                return True
            else:
                print("   ❌ FAIL - Normal trade execution is broken")
                return False

def main():
    """Main function to run the slot fix verification tests."""
    print("🚀 SLOT BYPASS FIX VERIFICATION")
    print("=" * 60)

    test1_passed = test_slot_fix()
    test2_passed = test_slot_fix_with_available_slots()

    print("\n" + "=" * 60)
    print("📋 VERIFICATION RESULTS")
    print("=" * 60)

    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED - Slot bypass fix is working correctly!")
        print("\n🔧 FIX SUMMARY:")
        print("   Added return statement in execute_trade() method")
        print("   Location: trading_bot/core/trader.py, lines 1168-1171")
        print("   Effect: Trades are now properly blocked when slot limits are exceeded")
        return True
    else:
        print("❌ SOME TESTS FAILED - Fix may not be working correctly")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)