#!/usr/bin/env python3
"""
Position Sizing Demonstration Script

This script demonstrates the position sizing calculations used in the trading bot,
including both standard position sizing and slot-based risk management.

It imports the actual position sizing logic from the trading bot codebase and
provides multiple test scenarios with varying inputs.
"""

import sys
import os
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from trading_bot.config.settings import Config
from trading_bot.core.trader import TradeExecutor
from trading_bot.core.risk_manager import RiskManager
from trading_bot.core.position_manager import PositionManager


@dataclass
class TestScenario:
    """Represents a test scenario for position sizing."""
    name: str
    usdt_balance: float
    entry_price: float
    stop_loss: float
    take_profit: float
    symbol: str
    confidence: float
    direction: str
    leverage: int = 2
    active_trades: Optional[List[Dict[str, Any]]] = None
    risk_allocation_multiplier: float = 1.0


class PositionSizingDemo:
    """Demonstrates position sizing calculations with various scenarios."""

    def __init__(self):
        """Initialize the demo with mock components."""
        # Create a minimal config object for demonstration
        class MockConfig:
            def __init__(self):
                self.trading = MockTradingConfig()

        class MockTradingConfig:
            def __init__(self):
                self.risk_percentage = 0.01  # 1% risk per trade
                self.leverage = 2
                self.max_concurrent_trades = 3
                self.max_loss_usd = 35.0
                self.min_rr = 1.9

        self.config = MockConfig()

        # Mock components for demonstration
        self.position_manager = None
        self.risk_manager = None
        self.trader = None

        # Setup logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

    def setup_components(self):
        """Setup mock trading components for demonstration."""
        # Create a mock position manager
        class MockPositionManager:
            def __init__(self):
                self.data_agent = MockDataAgent()

        class MockDataAgent:
            def __init__(self):
                self._active_trades = []

            def get_trades(self, status='open'):
                return self._active_trades

            def set_active_trades(self, trades):
                self._active_trades = trades if trades else []

        # Create a mock trader
        class MockTrader:
            def __init__(self, config):
                self.config = config
                self.api_manager = MockAPIManager()

            def calculate_risk_reward_ratio(self, entry_price, tp_price, sl_price, direction, symbol=None, include_fees=True):
                """Calculate RR ratio using the actual logic."""
                if entry_price <= 0 or tp_price <= 0 or sl_price <= 0:
                    return 0.0

                direction = direction.upper()

                if direction == "LONG":
                    risk = entry_price - sl_price
                    reward = tp_price - entry_price
                elif direction == "SHORT":
                    risk = sl_price - entry_price
                    reward = entry_price - tp_price
                else:
                    risk = abs(entry_price - sl_price)
                    reward = abs(tp_price - entry_price)

                if risk <= 0 or reward <= 0:
                    return 0.0

                return reward / risk

            def get_positions(self):
                """Mock positions response."""
                return {"error": None, "result": {"list": []}}

            def get_open_orders(self):
                """Mock orders response."""
                return {"error": None, "result": {"list": []}}

        class MockAPIManager:
            def get_fee_rates(self, symbol=None):
                """Mock fee rates response."""
                return {"error": None, "result": {"list": [{"takerFeeRate": "0.0006"}]}}

        self.position_manager = MockPositionManager()
        self.trader = MockTrader(self.config)
        self.risk_manager = RiskManager(self.trader, self.position_manager, self.config)

    def calculate_standard_position_size(self, entry_price: float, stop_loss: float,
                                       symbol: str, usdt_balance: float) -> Dict[str, Any]:
        """Calculate position size using standard method (simplified version)."""
        risk_percentage = self.config.trading.risk_percentage
        leverage = self.config.trading.leverage
        max_loss_usd = self.config.trading.max_loss_usd

        # Calculate risk amount
        risk_amount = usdt_balance * risk_percentage
        if risk_amount > max_loss_usd:
            risk_amount = max_loss_usd

        # Calculate price difference
        price_diff = abs(entry_price - stop_loss)

        # Calculate position size
        risk_based_position_size = risk_amount / price_diff

        # Apply leverage
        max_position_value = usdt_balance * leverage
        balance_based_position_size = max_position_value / entry_price

        # Use the smaller of the two
        position_size = min(risk_based_position_size, balance_based_position_size)
        position_value = position_size * entry_price

        return {
            "success": True,
            "position_size": position_size,
            "position_value": position_value,
            "risk_amount": risk_amount,
            "risk_percentage": risk_percentage * 100,
            "price_diff": price_diff,
            "calculation_method": "risk_based" if position_size == risk_based_position_size else "balance_based"
        }

    def calculate_slot_based_position_size(self, scenario: TestScenario) -> Dict[str, Any]:
        """Calculate position size using slot-based risk management."""
        if not self.risk_manager or not self.position_manager:
            return {"error": "Components not initialized"}

        # Create signal data
        signal = {
            "symbol": scenario.symbol,
            "entry_price": scenario.entry_price,
            "take_profit": scenario.take_profit,
            "stop_loss": scenario.stop_loss,
            "direction": scenario.direction,
            "confidence": scenario.confidence,
            "risk_allocation_multiplier": scenario.risk_allocation_multiplier
        }

        # Update config for this scenario
        self.config.trading.leverage = scenario.leverage

        # Mock active trades in position manager
        if scenario.active_trades and hasattr(self.position_manager, 'data_agent'):
            self.position_manager.data_agent.set_active_trades(scenario.active_trades)

        # Calculate slot-based position size
        result = self.risk_manager.calculate_slot_based_position_size(
            entry_price=scenario.entry_price,
            stop_loss=scenario.stop_loss,
            symbol=scenario.symbol,
            signal=signal
        )

        return result

    def run_scenario(self, scenario: TestScenario) -> None:
        """Run a single test scenario and display results."""
        print(f"\n{'='*80}")
        print(f"TEST SCENARIO: {scenario.name}")
        print(f"{'='*80}")

        print(f"📊 INPUT PARAMETERS:")
        print(f"   USDT Balance: ${scenario.usdt_balance:,.2f}")
        print(f"   Entry Price: ${scenario.entry_price:.4f}")
        print(f"   Stop Loss: ${scenario.stop_loss:.4f}")
        print(f"   Take Profit: ${scenario.take_profit:.4f}")
        print(f"   Symbol: {scenario.symbol}")
        print(f"   Direction: {scenario.direction}")
        print(f"   Confidence: {scenario.confidence:.2f}")
        print(f"   Leverage: {scenario.leverage}x")
        print(f"   Risk Allocation Multiplier: {scenario.risk_allocation_multiplier:.2f}")

        if scenario.active_trades:
            print(f"   Active Trades: {len(scenario.active_trades)}")
            for i, trade in enumerate(scenario.active_trades, 1):
                print(f"     Trade {i}: {trade.get('symbol', 'Unknown')} @ ${trade.get('entry_price', 0):.4f}")

        # Calculate RR ratio
        if not self.trader:
            print("❌ Error: Trader not initialized")
            return

        rr_ratio = self.trader.calculate_risk_reward_ratio(
            scenario.entry_price, scenario.take_profit, scenario.stop_loss,
            scenario.direction, scenario.symbol
        )

        print(f"\n🎯 RISK-REWARD ANALYSIS:")
        print(f"   Risk-Reward Ratio: {rr_ratio:.2f}")

        # Standard position sizing
        print(f"\n📈 STANDARD POSITION SIZING:")
        standard_result = self.calculate_standard_position_size(
            scenario.entry_price, scenario.stop_loss, scenario.symbol, scenario.usdt_balance
        )

        if standard_result["success"]:
            print(f"   Position Size: {standard_result['position_size']:.6f} {scenario.symbol.replace('USDT', '')}")
            print(f"   Position Value: ${standard_result['position_value']:.2f}")
            print(f"   Risk Amount: ${standard_result['risk_amount']:.2f}")
            print(f"   Risk Percentage: {standard_result['risk_percentage']:.3f}%")
            print(f"   Calculation Method: {standard_result['calculation_method']}")

        # Slot-based position sizing
        print(f"\n🎲 SLOT-BASED POSITION SIZING:")
        slot_result = self.calculate_slot_based_position_size(scenario)

        if slot_result.get("success"):
            print(f"   Position Size: {slot_result['position_size']:.6f} {scenario.symbol.replace('USDT', '')}")
            print(f"   Position Value: ${slot_result['position_value']:.2f}")

            if "slot_info" in slot_result:
                slot_info = slot_result["slot_info"]
                print(f"   Available Slots: {slot_info['available_slots']}/{slot_info['max_slots']}")
                print(f"   Base Risk per Trade: {slot_info['base_risk_per_slot']*100:.3f}%")
                print(f"   RR Weight Multiplier: {slot_info['rr_weight']:.2f}")
                print(f"   Risk Allocation Multiplier: {slot_info['risk_allocation_multiplier']:.2f}")
                print(f"   Final Adjusted Risk: {slot_info['risk_per_slot']:.3f}%")
                print(f"   Trade RR Ratio: {slot_info['trade_rr_ratio']:.2f}")

        print(f"\n💡 COMPARISON:")
        if standard_result["success"] and slot_result.get("success"):
            size_diff = slot_result['position_size'] - standard_result['position_size']
            value_diff = slot_result['position_value'] - standard_result['position_value']

            print(f"   Size Difference: {size_diff:+.6f} {scenario.symbol.replace('USDT', '')}")
            print(f"   Value Difference: ${value_diff:+.2f}")

            if abs(size_diff) > 0.000001:
                print(f"   Adjustment Reason: Slot-based system applied RR weighting and risk allocation")
            else:
                print(f"   Adjustment Reason: No adjustment needed (same result)")

    def demonstrate_portfolio_rr_weighting(self, positions: List[Dict], total_capital: float, name: str):
        """Demonstrate RR weighting allocation across multiple positions."""
        if not self.trader:
            print("❌ Error: Trader not initialized")
            return

        print(f"\n{'='*80}")
        print(f"PORTFOLIO RR WEIGHTING DEMONSTRATION: {name}")
        print(f"{'='*80}")
        print(f"Total Capital: ${total_capital:,.2f}")

        # Calculate RR for each position
        rr_ratios = []
        for pos in positions:
            rr = self.trader.calculate_risk_reward_ratio(pos['entry'], pos['tp'], pos['sl'], pos['direction'], pos['symbol'])
            rr_ratios.append(rr)
            print(f"\n{pos['symbol']}: Entry ${pos['entry']:.4f}, TP ${pos['tp']:.4f}, SL ${pos['sl']:.4f}")
            print(f"   RR Ratio: {rr:.2f}")
            if 'size' in pos:
                print(f"   Current Size: {pos['size']:.4f} (${pos['value']:.2f})")

        # Calculate weighting
        total_rr = sum(rr_ratios)
        print(f"\n🎯 RR WEIGHTING ANALYSIS:")
        print(f"   Total RR: {total_rr:.2f}")
        allocations = []
        for i, rr in enumerate(rr_ratios):
            weight = rr / total_rr
            allocation = weight * total_capital
            allocations.append(allocation)
            print(f"   {positions[i]['symbol']}: Weight {weight:.3f}, Allocation ${allocation:.2f}")

        # For comparison scenario, calculate position sizes
        if name == "RR Weighting Comparison":
            print(f"\n📊 POSITION SIZE CALCULATION (1% risk per trade on allocated capital):")
            for i, pos in enumerate(positions):
                entry = pos['entry']
                sl = pos['sl']
                price_diff = abs(entry - sl)
                allocation = allocations[i]
                risk_amount = allocation * 0.01
                position_size = risk_amount / price_diff
                position_value = position_size * entry
                print(f"   {pos['symbol']}: Size {position_size:.4f}, Value ${position_value:.2f} (Risk: ${risk_amount:.2f})")

        print(f"\n💡 RR WEIGHTING EFFECT:")
        print("   Higher RR ratios receive larger capital allocation")
        print("   This optimizes capital efficiency by favoring higher-probability trades")

    def run_all_scenarios(self) -> None:
        """Run all predefined test scenarios."""
        print("🚀 POSITION SIZING DEMONSTRATION")
        print("This script demonstrates how the trading bot calculates position sizes")
        print("using both standard and slot-based risk management approaches.\n")

        # Setup components
        self.setup_components()

        # Define test scenarios
        scenarios = [
            TestScenario(
                name="Basic Long Trade - No Active Trades",
                usdt_balance=1000.0,
                entry_price=50000.0,
                stop_loss=49000.0,
                take_profit=52000.0,
                symbol="BTCUSDT",
                confidence=0.85,
                direction="LONG"
            ),

            TestScenario(
                name="High Confidence Trade with Leverage",
                usdt_balance=2000.0,
                entry_price=3000.0,
                stop_loss=2950.0,
                take_profit=3100.0,
                symbol="ETHUSDT",
                confidence=0.92,
                direction="LONG",
                leverage=3
            ),

            TestScenario(
                name="Short Trade with Risk Allocation",
                usdt_balance=1500.0,
                entry_price=100.0,
                stop_loss=102.0,
                take_profit=98.0,
                symbol="ALTUSDT",
                confidence=0.78,
                direction="SHORT",
                risk_allocation_multiplier=1.2
            ),

            TestScenario(
                name="Trade with Active Positions",
                usdt_balance=1200.0,
                entry_price=2500.0,
                stop_loss=2450.0,
                take_profit=2600.0,
                symbol="ETHUSDT",
                confidence=0.88,
                direction="LONG",
                active_trades=[
                    {
                        "symbol": "BTCUSDT",
                        "entry_price": 50000.0,
                        "take_profit": 52000.0,
                        "stop_loss": 49000.0,
                        "side": "Buy",
                        "risk_reward_ratio": 2.0
                    }
                ]
            ),

            TestScenario(
                name="Low Balance High Risk Trade",
                usdt_balance=500.0,
                entry_price=0.5,
                stop_loss=0.49,
                take_profit=0.52,
                symbol="MEMEUSDT",
                confidence=0.75,
                direction="LONG",
                leverage=1
            ),

            TestScenario(
                name="Multiple Active Trades",
                usdt_balance=3000.0,
                entry_price=10000.0,
                stop_loss=9800.0,
                take_profit=10200.0,
                symbol="BTCUSDT",
                confidence=0.90,
                direction="LONG",
                active_trades=[
                    {
                        "symbol": "ETHUSDT",
                        "entry_price": 3000.0,
                        "take_profit": 3100.0,
                        "stop_loss": 2950.0,
                        "side": "Buy",
                        "risk_reward_ratio": 2.0
                    },
                    {
                        "symbol": "ALTUSDT",
                        "entry_price": 100.0,
                        "take_profit": 98.0,
                        "stop_loss": 102.0,
                        "side": "Sell",
                        "risk_reward_ratio": 2.0
                    }
                ]
            )
        ]

        # Demonstrate portfolio RR weighting
        # Real Portfolio Example
        real_positions = [
            {'symbol': 'BCHUSDT', 'entry': 557.40, 'tp': 582.00, 'sl': 548.00, 'direction': 'LONG', 'size': 0.7300, 'value': 407.41},
            {'symbol': 'BNBUSDT', 'entry': 869.50, 'tp': 901.40, 'sl': 858.00, 'direction': 'LONG', 'size': 0.8000, 'value': 696.24},
            {'symbol': 'NEARUSDT', 'entry': 2.55, 'tp': 2.78, 'sl': 2.49, 'direction': 'LONG', 'size': 451.70, 'value': 1135.12}
        ]
        self.demonstrate_portfolio_rr_weighting(real_positions, 2500.0, "Real Portfolio Example")

        # RR Weighting Comparison
        comparison_positions = [
            {'symbol': 'ALT1USDT', 'entry': 100.0, 'tp': 104.0, 'sl': 98.0, 'direction': 'LONG'},
            {'symbol': 'ALT2USDT', 'entry': 100.0, 'tp': 106.0, 'sl': 98.0, 'direction': 'LONG'},
            {'symbol': 'ALT3USDT', 'entry': 100.0, 'tp': 108.0, 'sl': 98.0, 'direction': 'LONG'}
        ]
        self.demonstrate_portfolio_rr_weighting(comparison_positions, 1000.0, "RR Weighting Comparison")

        # Run all scenarios
        for scenario in scenarios:
            self.run_scenario(scenario)

        print(f"\n{'='*80}")
        print("🎉 DEMONSTRATION COMPLETE")
        print(f"{'='*80}")
        print("\nKey Learnings:")
        print("• Standard sizing uses fixed risk percentage per trade")
        print("• Slot-based sizing adjusts risk based on RR ratio and active trades")
        print("• Higher RR ratios get larger position sizes")
        print("• Risk allocation multipliers can increase/decrease position sizes")
        print("• Active trades influence RR weighting calculations")
        print("• Balance and leverage constraints always apply")


def main():
    """Main function to run the position sizing demonstration."""
    try:
        demo = PositionSizingDemo()
        demo.run_all_scenarios()
    except Exception as e:
        print(f"❌ Error running demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()