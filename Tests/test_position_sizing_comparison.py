#!/usr/bin/env python3
"""
Position Sizing Methods Comparison Test

This test compares the three position sizing methods:
1. Enhanced Position Sizing (confidence + volatility weighting)
2. Slot-Based Equal Risk Allocation
3. Basic Position Sizing (simple risk %)

Run this test to see how each method would size positions for the same trade signal.
"""

import sys
import os
from decimal import Decimal
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from trading_bot.config.settings import Config
from trading_bot.core.trader import TradeExecutor
from trading_bot.core.risk_manager import RiskManager


class MockDataAgent:
    """Mock data agent for testing."""
    def get_trades(self, status='open'):
        return []


class MockPositionManager:
    """Mock position manager for testing."""
    def __init__(self):
        self.data_agent = MockDataAgent()

    def ensure_trade_record(self, **kwargs):
        return {"created": True, "trade": {"trade_id": "test_trade"}}


def create_test_signal(symbol: str = "BTCUSDT", confidence: float = 0.85) -> Dict[str, Any]:
    """Create a test trading signal for comparison."""
    return {
        "recommendation": "BUY",
        "entry_price": 45000.0,
        "take_profit": 46500.0,
        "stop_loss": 44250.0,
        "confidence": confidence,
        "symbol": symbol,
        "direction": "LONG"
    }


def test_position_sizing_methods():
    """Test and compare all three position sizing methods."""

    print("🎯 POSITION SIZING METHODS COMPARISON TEST")
    print("=" * 60)

    # Load configuration
    config = Config.from_yaml()
    print(f"📋 Loaded config from: {config}")

    # Create test signal
    test_signal = create_test_signal()
    symbol = test_signal["symbol"]

    print(f"\n📊 Test Signal: {symbol}")
    print(f"   Entry: ${test_signal['entry_price']:,.2f}")
    print(f"   Stop Loss: ${test_signal['stop_loss']:,.2f}")
    print(f"   Take Profit: ${test_signal['take_profit']:,.2f}")
    print(f"   Confidence: {test_signal['confidence']:.1%}")
    print(f"   Risk-Reward Ratio: {(test_signal['take_profit'] - test_signal['entry_price']) / (test_signal['entry_price'] - test_signal['stop_loss']):.2f}")

    # Initialize trader and risk manager
    mock_position_manager = MockPositionManager()
    trader = TradeExecutor(config, position_manager=mock_position_manager)
    risk_manager = RiskManager(trader, mock_position_manager, config)

    # Test data storage
    results = {}

    balance_response = trader.get_wallet_balance()
    balance = balance_response.get('result', {}).get('list', [{}])[0].get('totalAvailableBalance', 'N/A')
    if isinstance(balance, str):
        print(f"\n💰 Account Balance: ${balance}")
    else:
        print(f"\n💰 Account Balance: ${balance:,.2f}")
    print(f"📈 Risk Percentage: {config.trading.risk_percentage:.1%}")
    print(f"⚡ Leverage: {config.trading.leverage}x")

    # Test 1: Basic Position Sizing
    print(f"\n🔹 METHOD 1: BASIC POSITION SIZING")
    print("-" * 40)

    try:
        basic_result = trader.calculate_position_size(
            test_signal["entry_price"],
            test_signal["stop_loss"],
            symbol
        )

        if "error" in basic_result:
            print(f"❌ Error: {basic_result['error']}")
            basic_position_size = 0
            basic_risk_amount = 0
        else:
            basic_position_size = basic_result["position_size"]
            basic_risk_amount = basic_result["actual_risk"]
            print(f"✅ Position Size: {basic_position_size:.6f} {symbol.replace('USDT', '')}")
            print(f"💰 Position Value: ${basic_result['position_value']:,.2f}")
            print(f"⚠️ Risk Amount: ${basic_risk_amount:.2f}")
            print(f"📊 Risk % of Balance: {basic_result['risk_percentage']:.2f}%")

        results["basic"] = {
            "position_size": basic_position_size,
            "risk_amount": basic_risk_amount,
            "position_value": basic_result.get("position_value", 0),
            "method": "Basic"
        }

    except Exception as e:
        print(f"❌ Exception in basic sizing: {e}")
        results["basic"] = {"error": str(e)}

    # Test 2: Slot-Based Position Sizing
    print(f"\n🔹 METHOD 2: SLOT-BASED EQUAL RISK ALLOCATION")
    print("-" * 40)

    try:
        slot_result = risk_manager.calculate_slot_based_position_size(
            test_signal["entry_price"],
            test_signal["stop_loss"],
            symbol,
            test_signal
        )

        if "error" in slot_result:
            print(f"❌ Error: {slot_result['error']}")
            slot_position_size = 0
            slot_risk_amount = 0
        else:
            slot_position_size = slot_result["position_size"]
            slot_risk_amount = slot_result["actual_risk"]
            print(f"✅ Position Size: {slot_position_size:.6f} {symbol.replace('USDT', '')}")
            print(f"💰 Position Value: ${slot_result['position_value']:,.2f}")
            print(f"⚠️ Risk Amount: ${slot_risk_amount:.2f}")
            print(f"🎯 Risk per Slot: ${slot_result.get('slot_info', {}).get('risk_per_slot', 'N/A')}")
            print(f"📊 Available Slots: {slot_result.get('slot_info', {}).get('available_slots', 'N/A')}")

        results["slot_based"] = {
            "position_size": slot_position_size,
            "risk_amount": slot_risk_amount,
            "position_value": slot_result.get("position_value", 0),
            "method": "Slot-Based"
        }

    except Exception as e:
        print(f"❌ Exception in slot-based sizing: {e}")
        results["slot_based"] = {"error": str(e)}

    # Test 3: Enhanced Position Sizing
    print(f"\n🔹 METHOD 3: ENHANCED POSITION SIZING")
    print("-" * 40)

    try:
        enhanced_result = risk_manager.calculate_enhanced_position_size_with_safety(
            test_signal["entry_price"],
            test_signal["stop_loss"],
            symbol,
            test_signal
        )

        if "error" in enhanced_result:
            print(f"❌ Error: {enhanced_result['error']}")
            enhanced_position_size = 0
            enhanced_risk_amount = 0
        else:
            enhanced_position_size = enhanced_result["position_size"]
            enhanced_risk_amount = enhanced_result["actual_risk"]
            print(f"✅ Position Size: {enhanced_position_size:.6f} {symbol.replace('USDT', '')}")
            print(f"💰 Position Value: ${enhanced_result['position_value']:,.2f}")
            print(f"⚠️ Risk Amount: ${enhanced_risk_amount:.2f}")
            print(f"🎯 Confidence Weight: {enhanced_result.get('confidence_weight', 'N/A'):.2f}x")
            print(f"📊 Volatility Weight: {enhanced_result.get('volatility_weight', 'N/A'):.2f}x")
            print(f"🔄 Combined Weight: {enhanced_result.get('combined_weight', 'N/A'):.2f}x")

        results["enhanced"] = {
            "position_size": enhanced_position_size,
            "risk_amount": enhanced_risk_amount,
            "position_value": enhanced_result.get("position_value", 0),
            "method": "Enhanced"
        }

    except Exception as e:
        print(f"❌ Exception in enhanced sizing: {e}")
        results["enhanced"] = {"error": str(e)}

    # Comparison Summary
    print(f"\n📈 COMPARISON SUMMARY")
    print("=" * 60)

    valid_results = {k: v for k, v in results.items() if "error" not in v}

    if len(valid_results) >= 2:
        # Sort by position size for comparison
        sorted_results = sorted(valid_results.items(),
                              key=lambda x: x[1]["position_size"],
                              reverse=True)

        print("Position Size Ranking (largest to smallest):")
        for i, (method, data) in enumerate(sorted_results, 1):
            print(f"{i}. {data['method']}: {data['position_size']:.6f} {symbol.replace('USDT', '')}")

        # Calculate differences
        largest = sorted_results[0][1]
        smallest = sorted_results[-1][1]

        if largest["position_size"] > 0 and smallest["position_size"] > 0:
            size_diff = largest["position_size"] - smallest["position_size"]
            percent_diff = (size_diff / smallest["position_size"]) * 100

            print(f"\n📊 Size Difference:")
            print(f"   Largest: {largest['method']} ({largest['position_size']:.6f})")
            print(f"   Smallest: {smallest['method']} ({smallest['position_size']:.6f})")
            print(f"   Difference: {size_diff:.6f} ({percent_diff:+.1f}%)")

        # Risk comparison
        print(f"\n⚠️ Risk Amount Comparison:")
        for method, data in sorted_results:
            print(f"   {data['method']}: ${data['risk_amount']:.2f}")

    else:
        print("❌ Not enough valid results to compare")

    # Configuration Impact
    print(f"\n⚙️ CONFIGURATION IMPACT")
    print("-" * 40)
    print(f"Current Settings:")
    print(f"   use_enhanced_position_sizing: {getattr(config.trading.position_sizing, 'use_enhanced_position_sizing', False)}")
    print(f"   use_slot_based_position_sizing: {getattr(config.trading.position_sizing, 'use_slot_based_position_sizing', False)}")
    print(f"   risk_percentage: {config.trading.risk_percentage:.1%}")
    print(f"   max_loss_usd: ${config.trading.max_loss_usd:,.0f}")
    print(f"   leverage: {config.trading.leverage}x")
    print(f"   max_concurrent_trades: {config.trading.max_concurrent_trades}")

    print(f"\n✅ Test completed successfully!")
    return results


def test_different_confidence_levels():
    """Test how confidence levels affect enhanced position sizing."""

    print(f"\n🎯 CONFIDENCE LEVEL IMPACT TEST")
    print("=" * 60)

    config = Config.from_yaml()
    mock_position_manager = MockPositionManager()
    mock_trader = TradeExecutor(config, position_manager=mock_position_manager)
    risk_manager = RiskManager(mock_trader, mock_position_manager, config)

    confidence_levels = [0.5, 0.7, 0.8, 0.9, 0.95]
    symbol = "BTCUSDT"

    test_signal_base = {
        "recommendation": "BUY",
        "entry_price": 45000.0,
        "take_profit": 46500.0,
        "stop_loss": 44250.0,
        "symbol": symbol,
        "direction": "LONG"
    }

    print("Confidence → Position Size → Weight")
    print("-" * 40)

    for confidence in confidence_levels:
        test_signal = test_signal_base.copy()
        test_signal["confidence"] = confidence

        try:
            result = risk_manager.calculate_enhanced_position_size_with_safety(
                test_signal["entry_price"],
                test_signal["stop_loss"],
                symbol,
                test_signal
            )

            if "error" not in result:
                position_size = result["position_size"]
                confidence_weight = result.get("confidence_weight", 1.0)
                print(f"{confidence:.1f} → {position_size:.6f} BTC → {confidence_weight:.2f}x")
            else:
                print(f"{confidence:.1f} → ERROR")

        except Exception as e:
            print(".1f")


def test_rr_weighting_scenarios():
    """Test how different RR ratios affect enhanced position sizing."""

    print(f"\n🎯 RR WEIGHTING SCENARIOS TEST")
    print("=" * 60)

    config = Config.from_yaml()
    mock_position_manager = MockPositionManager()
    mock_trader = TradeExecutor(config, position_manager=mock_position_manager)
    risk_manager = RiskManager(mock_trader, mock_position_manager, config)

    # Test different RR ratios
    rr_scenarios = [
        {"tp_price": 45150.0, "expected_rr": 1.2, "description": "Poor RR (1.2:1)"},      # 1.2 RR
        {"tp_price": 45750.0, "expected_rr": 2.0, "description": "Good RR (2.0:1)"},      # 2.0 RR
        {"tp_price": 46500.0, "expected_rr": 3.0, "description": "Excellent RR (3.0:1)"},  # 3.0 RR
    ]

    base_signal = {
        "recommendation": "BUY",
        "entry_price": 45000.0,
        "stop_loss": 44250.0,
        "confidence": 0.85,
        "symbol": "BTCUSDT",
        "direction": "LONG"
    }

    print("RR Ratio → Position Size → RR Weight → Final Weight")
    print("-" * 60)

    for scenario in rr_scenarios:
        test_signal = base_signal.copy()
        test_signal["take_profit"] = scenario["tp_price"]

        try:
            result = risk_manager.calculate_enhanced_position_size_with_safety(
                test_signal["entry_price"],
                test_signal["stop_loss"],
                test_signal["symbol"],
                test_signal
            )

            if "error" not in result:
                position_size = result["position_size"]
                rr_weight = result.get("enhanced_sizing", {}).get("rr_weight", 1.0)
                combined_weight = result.get("enhanced_sizing", {}).get("combined_weight", 1.0)
                print(".1f")
            else:
                print(".1f")

        except Exception as e:
            print(".1f")


def test_volatility_weighting_scenarios():
    """Test how different volatility levels affect enhanced position sizing."""

    print(f"\n🎯 VOLATILITY WEIGHTING SCENARIOS TEST")
    print("=" * 60)

    config = Config.from_yaml()
    mock_position_manager = MockPositionManager()
    mock_trader = TradeExecutor(config, position_manager=mock_position_manager)
    risk_manager = RiskManager(mock_trader, mock_position_manager, config)

    # Mock ATR values for different volatility scenarios
    volatility_scenarios = [
        {"atr_percentage": 0.015, "description": "Low Volatility (ATR: 1.5%)"},     # Below 2% threshold
        {"atr_percentage": 0.035, "description": "Medium Volatility (ATR: 3.5%)"},  # Between thresholds
        {"atr_percentage": 0.06, "description": "High Volatility (ATR: 6.0%)"},     # Above 5% threshold
    ]

    base_signal = {
        "recommendation": "BUY",
        "entry_price": 45000.0,
        "take_profit": 46500.0,
        "stop_loss": 44250.0,
        "confidence": 0.85,
        "symbol": "BTCUSDT",
        "direction": "LONG"
    }

    print("ATR % → Position Size → Volatility Weight → Final Weight")
    print("-" * 60)

    for scenario in volatility_scenarios:
        test_signal = base_signal.copy()

        try:
            # Temporarily mock the ATR calculation method
            original_atr_method = risk_manager._get_atr_for_symbol

            def mock_atr_method(symbol, timeframe="60", period=14):
                return scenario["atr_percentage"]

            risk_manager._get_atr_for_symbol = mock_atr_method

            result = risk_manager.calculate_enhanced_position_size_with_safety(
                test_signal["entry_price"],
                test_signal["stop_loss"],
                test_signal["symbol"],
                test_signal
            )

            # Restore original method
            risk_manager._get_atr_for_symbol = original_atr_method

            if "error" not in result:
                position_size = result["position_size"]
                volatility_weight = result.get("enhanced_sizing", {}).get("volatility_weight", 1.0)
                combined_weight = result.get("enhanced_sizing", {}).get("combined_weight", 1.0)
                print(".1f")
            else:
                print(".1f")

        except Exception as e:
            print(".1f")


def test_combined_weighting_scenarios():
    """Test combined effects of confidence, RR, and volatility weighting."""

    print(f"\n🎯 COMBINED WEIGHTING SCENARIOS TEST")
    print("=" * 60)

    config = Config.from_yaml()
    mock_position_manager = MockPositionManager()
    mock_trader = TradeExecutor(config, position_manager=mock_position_manager)
    risk_manager = RiskManager(mock_trader, mock_position_manager, config)

    # Combined scenarios
    combined_scenarios = [
        {
            "name": "BEST CASE",
            "confidence": 0.95,
            "tp_price": 46500.0,  # 3.0 RR
            "atr_percentage": 0.015,  # Low volatility
            "expected_weights": "~1.0 × 1.1 × 1.1 = 1.21x"
        },
        {
            "name": "WORST CASE",
            "confidence": 0.4,
            "tp_price": 45150.0,  # 1.2 RR
            "atr_percentage": 0.06,   # High volatility
            "expected_weights": "~0.9 × 0.9 × 0.9 = 0.73x"
        },
        {
            "name": "BALANCED CASE",
            "confidence": 0.75,
            "tp_price": 45750.0,  # 2.0 RR
            "atr_percentage": 0.035,  # Medium volatility
            "expected_weights": "~1.0 × 1.0 × 1.0 = 1.00x"
        }
    ]

    base_signal = {
        "recommendation": "BUY",
        "entry_price": 45000.0,
        "stop_loss": 44250.0,
        "symbol": "BTCUSDT",
        "direction": "LONG"
    }

    print("Scenario → Conf → RR → ATR → Position Size → Combined Weight")
    print("-" * 70)

    for scenario in combined_scenarios:
        test_signal = base_signal.copy()
        test_signal["confidence"] = scenario["confidence"]
        test_signal["take_profit"] = scenario["tp_price"]

        try:
            # Mock ATR calculation
            original_atr_method = risk_manager._get_atr_for_symbol

            def mock_atr_method(symbol, timeframe="60", period=14):
                return scenario["atr_percentage"]

            risk_manager._get_atr_for_symbol = mock_atr_method

            result = risk_manager.calculate_enhanced_position_size_with_safety(
                test_signal["entry_price"],
                test_signal["stop_loss"],
                test_signal["symbol"],
                test_signal
            )

            # Restore original method
            risk_manager._get_atr_for_symbol = original_atr_method

            if "error" not in result:
                position_size = result["position_size"]
                confidence_weight = result.get("enhanced_sizing", {}).get("confidence_weight", 1.0)
                rr_weight = result.get("enhanced_sizing", {}).get("rr_weight", 1.0)
                volatility_weight = result.get("enhanced_sizing", {}).get("volatility_weight", 1.0)
                combined_weight = result.get("enhanced_sizing", {}).get("combined_weight", 1.0)

                print("<8")
            else:
                print("<8")

        except Exception as e:
            print("<8")


def test_weighting_configuration_details():
    """Display detailed configuration for all weighting systems."""

    print(f"\n⚙️ WEIGHTING CONFIGURATION DETAILS")
    print("=" * 60)

    config = Config.from_yaml()

    print("CONFIDENCE WEIGHTING:")
    confidence_config = getattr(config.trading.position_sizing, 'confidence_weighting', None)
    if confidence_config:
        print(f"  Enabled: {getattr(confidence_config, 'enabled', False)}")
        print(f"  Low Threshold: {getattr(confidence_config, 'low_confidence_threshold', 0.5)}")
        print(f"  High Threshold: {getattr(confidence_config, 'high_confidence_threshold', 0.9)}")
        print(f"  Low Weight: {getattr(confidence_config, 'low_confidence_weight', 0.9)}x")
        print(f"  Neutral Weight: {getattr(confidence_config, 'neutral_confidence_weight', 1.0)}x")
        print(f"  High Weight: {getattr(confidence_config, 'high_confidence_weight', 1.1)}x")
    else:
        print("  Not configured")

    print(f"\nRR WEIGHTING:")
    rr_config = getattr(config.trading.position_sizing, 'rr_weighting', None)
    if rr_config:
        print(f"  Enabled: {getattr(rr_config, 'enabled', False)}")
        print(f"  Min Weight: {getattr(rr_config, 'min_weight', 0.9)}x")
        print(f"  Max Weight: {getattr(rr_config, 'max_weight', 1.1)}x")
    else:
        print("  Not configured")

    print(f"\nVOLATILITY WEIGHTING:")
    volatility_config = getattr(config.trading.position_sizing, 'volatility_weighting', None)
    if volatility_config:
        print(f"  Enabled: {getattr(volatility_config, 'enabled', False)}")
        print(f"  Use ATR: {getattr(volatility_config, 'use_atr_based', True)}")
        print(f"  Low Volatility Weight: {getattr(volatility_config, 'low_volatility_weight', 1.1)}x")
        print(f"  High Volatility Weight: {getattr(volatility_config, 'high_volatility_weight', 0.9)}x")
        print(f"  ATR Low Threshold: {getattr(volatility_config, 'atr_low_threshold', 0.02)}")
        print(f"  ATR High Threshold: {getattr(volatility_config, 'atr_high_threshold', 0.05)}")
    else:
        print("  Not configured")

    print(f"\nCOMBINED LIMITS:")
    print(f"  Max Combined Weight: {getattr(config.trading.position_sizing, 'max_combined_weight', 1.2)}x")
    print(f"  Min Combined Weight: {getattr(config.trading.position_sizing, 'min_combined_weight', 0.8)}x")


if __name__ == "__main__":
    try:
        # Run main comparison test
        results = test_position_sizing_methods()

        # Run confidence impact test
        test_different_confidence_levels()

        # Run RR weighting scenarios
        test_rr_weighting_scenarios()

        # Run volatility weighting scenarios
        test_volatility_weighting_scenarios()

        # Run combined weighting scenarios
        test_combined_weighting_scenarios()

        # Display configuration details
        test_weighting_configuration_details()

        print(f"\n🎉 All tests completed!")

    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
