#!/usr/bin/env python3
"""
Debug script to test position tightening logic with actual values.
This script simulates the tightening conditions to identify why tightening is not being applied.
"""

def get_current_milestone(profit_in_r: float) -> str | None:
    """Determine the current milestone based on profit level"""
    if profit_in_r >= 6.0:
        return "6R"
    elif profit_in_r >= 5.5:
        return "5.5R"
    elif profit_in_r >= 5.0:
        return "5R"
    elif profit_in_r >= 4.5:
        return "4.5R"
    elif profit_in_r >= 4.0:
        return "4R"
    elif profit_in_r >= 3.5:
        return "3.5R"
    elif profit_in_r >= 3.0:
        return "3R"
    elif profit_in_r >= 2.5:
        return "2.5R"
    elif profit_in_r >= 2.0:
        return "2R"
    elif profit_in_r >= 1.5:
        return "1.5R"
    elif profit_in_r >= 1.0:
        return "1R"
    return None

def apply_long_tightening(entry_price: float, current_price: float,
                         current_sl: float, one_r: float, profit_in_r: float):
    """Apply tightening logic for long positions"""
    proposed_sl = current_sl
    tightening_level = "none"
    target_milestone = None

    print(f"🔍 LONG TIGHTENING: entry_price={entry_price}, current_price={current_price}, "
          f"current_sl={current_sl}, one_r={one_r}, profit_in_r={profit_in_r:.2f}")

    # 1R tightening
    if current_price >= entry_price + 1 * one_r and profit_in_r >= 1.0 and profit_in_r < 1.5:
        new_proposed_sl = entry_price + 0.3 * one_r
        print(f"🔍 1R: new_proposed_sl={new_proposed_sl}, condition_met={new_proposed_sl > proposed_sl and new_proposed_sl < current_price}")
        if new_proposed_sl > proposed_sl and new_proposed_sl < current_price:
            proposed_sl = new_proposed_sl
            tightening_level = "1R"
            target_milestone = "1R"

    # 1.5R tightening
    elif current_price >= entry_price + 1.5 * one_r and profit_in_r >= 1.5 and profit_in_r < 2.0:
        new_proposed_sl = entry_price + 1 * one_r
        print(f"🔍 1.5R: new_proposed_sl={new_proposed_sl}, condition_met={new_proposed_sl > proposed_sl and new_proposed_sl < current_price}")
        if new_proposed_sl > proposed_sl and new_proposed_sl < current_price:
            proposed_sl = new_proposed_sl
            tightening_level = "1.5R"
            target_milestone = "1.5R"

    # 2R tightening
    elif current_price >= entry_price + 2 * one_r and profit_in_r >= 2.0 and profit_in_r < 2.5:
        new_proposed_sl = entry_price + 1.5 * one_r
        price_condition = current_price >= entry_price + 2 * one_r
        profit_condition = profit_in_r >= 2.0 and profit_in_r < 2.5
        sl_valid = new_proposed_sl > proposed_sl and new_proposed_sl < current_price

        print(f"🔍 2R: price_condition={price_condition}, profit_condition={profit_condition}, sl_valid={sl_valid}")
        print(f"🔍 2R: current_price={current_price}, required_price={entry_price + 2 * one_r}")
        print(f"🔍 2R: new_proposed_sl={new_proposed_sl}, current_sl={proposed_sl}")

        if new_proposed_sl > proposed_sl and new_proposed_sl < current_price:
            proposed_sl = new_proposed_sl
            tightening_level = "2R"
            target_milestone = "2R"
            print(f"✅ 2R: Tightening applied! proposed_sl={proposed_sl}")

    # Continue with other levels...
    elif current_price >= entry_price + 2.5 * one_r and profit_in_r >= 2.5 and profit_in_r < 3.0:
        new_proposed_sl = entry_price + 2 * one_r
        if new_proposed_sl > proposed_sl and new_proposed_sl < current_price:
            proposed_sl = new_proposed_sl
            tightening_level = "2.5R"
            target_milestone = "2.5R"

    return proposed_sl, tightening_level, target_milestone

def test_tightening_scenario(symbol: str, entry_price: float, current_price: float,
                           current_sl: float, one_r: float, last_tightened_milestone: str | None = None):
    """Test a specific tightening scenario"""
    print(f"\n{'='*60}")
    print(f"TESTING {symbol} POSITION TIGHTENING")
    print(f"{'='*60}")

    # Calculate profit in R
    profit_distance = current_price - entry_price
    profit_in_r = profit_distance / one_r if one_r > 0 else 0

    print(f"📊 Position Data:")
    print(f"   Entry Price: {entry_price}")
    print(f"   Current Price: {current_price}")
    print(f"   Current SL: {current_sl}")
    print(f"   One R: {one_r}")
    print(f"   Profit Distance: {profit_distance}")
    print(f"   Profit in R: {profit_in_r:.2f}R")
    print(f"   Current Milestone: {get_current_milestone(profit_in_r)}")
    print(f"   Last Tightened Milestone: {last_tightened_milestone}")

    # Check milestone condition
    current_milestone = get_current_milestone(profit_in_r)
    if current_milestone and last_tightened_milestone == current_milestone:
        print(f"⚠️  ALREADY TIGHTENED: Position already tightened at {current_milestone} milestone")
        return

    # Check profit condition
    if profit_in_r < 1.0:
        print(f"⚠️  INSUFFICIENT PROFIT: {profit_in_r:.2f}R < 1.0R minimum")
        return

    # Apply tightening logic
    proposed_sl, tightening_level, target_milestone = apply_long_tightening(
        entry_price, current_price, current_sl, one_r, profit_in_r
    )

    print(f"\n📋 Tightening Result:")
    print(f"   Proposed SL: {proposed_sl}")
    print(f"   Tightening Level: {tightening_level}")
    print(f"   Target Milestone: {target_milestone}")

    # Check if tightening was applied
    if tightening_level == "none":
        print(f"❌ NO TIGHTENING APPLIED")
    else:
        print(f"✅ TIGHTENING APPLIED: {tightening_level}")

def main():
    """Test scenarios based on the user's reported data"""
    print("POSITION TIGHTENING DEBUG TEST")
    print("Based on user's log: BTCUSDT 2.12R, NEARUSDT -0.51R")

    # Test BTCUSDT scenario (2.12R profit)
    # We need to estimate realistic values based on the profit
    # Assuming BTC around $60,000, 2.12R profit suggests:
    # If one_r = $100, then profit_distance = $212
    # If entry_price = $59,000, current_price would be ~$59,212

    print("\n🔍 TESTING BTCUSDT (2.12R profit scenario)")

    # Scenario 1: Conservative estimate
    test_tightening_scenario(
        symbol="BTCUSDT",
        entry_price=59000,  # Conservative entry
        current_price=59212,  # 2.12R profit with one_r = $100
        current_sl=58900,  # Current SL below entry
        one_r=100,  # $100 risk per R
        last_tightened_milestone=None
    )

    # Scenario 2: More aggressive estimate
    test_tightening_scenario(
        symbol="BTCUSDT",
        entry_price=58000,  # More aggressive entry
        current_price=58424,  # 2.12R profit with one_r = $200
        current_sl=57900,  # Current SL below entry
        one_r=200,  # $200 risk per R
        last_tightened_milestone=None
    )

    # Test edge case: already tightened at 2R
    test_tightening_scenario(
        symbol="BTCUSDT",
        entry_price=59000,
        current_price=59212,
        current_sl=59150,  # Already tightened to 1.5R level
        one_r=100,
        last_tightened_milestone="2R"  # Already tightened at 2R
    )

    # Test NEARUSDT scenario (-0.51R profit)
    print("\n🔍 TESTING NEARUSDT (-0.51R profit scenario)")
    test_tightening_scenario(
        symbol="NEARUSDT",
        entry_price=5.0,  # Conservative entry
        current_price=4.745,  # -0.51R profit with one_r = $0.255
        current_sl=4.9,  # Current SL below entry
        one_r=0.255,  # Risk amount
        last_tightened_milestone=None
    )

if __name__ == "__main__":
    main()