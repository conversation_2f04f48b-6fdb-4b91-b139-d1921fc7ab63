#!/usr/bin/env python3
"""
Equal Allocation Test Script

This script tests the new equal allocation functionality in the risk manager,
specifically testing both equal risk allocation and equal position value allocation.
"""

import sys
import os
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from trading_bot.config.settings import Config
from trading_bot.core.trader import TradeExecutor
from trading_bot.core.risk_manager import RiskManager
from trading_bot.core.position_manager import PositionManager


@dataclass
class TestCase:
    """Represents a test case for equal allocation."""
    name: str
    usdt_balance: float
    available_slots: int
    total_risk_percentage: float
    equal_allocation_base: bool
    symbols: List[Dict[str, Any]]


class EqualAllocationTester:
    """Tests the equal allocation functionality."""

    def __init__(self):
        """Initialize the tester with mock components."""
        # Create a minimal config object
        class MockConfig:
            def __init__(self):
                self.trading = MockTradingConfig()

        class MockTradingConfig:
            def __init__(self):
                self.risk_percentage = 0.01  # 1% risk per trade
                self.leverage = 2
                self.max_concurrent_trades = 3
                self.max_loss_usd = 35.0
                self.min_rr = 1.9
                self.position_sizing = MockPositionSizingConfig()

        class MockPositionSizingConfig:
            def __init__(self):
                self.equal_allocation_base = True
                self.min_position_value_usd = 50.0
                self.min_balance_threshold = 500.0

        self.config = MockConfig()
        self.position_manager = None
        self.risk_manager = None
        self.trader = None

        # Setup logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

    def setup_components(self, equal_allocation_base: bool = True):
        """Setup mock trading components."""
        # Update config
        self.config.trading.position_sizing.equal_allocation_base = equal_allocation_base

        # Create a mock position manager
        class MockPositionManager:
            def __init__(self):
                self.data_agent = MockDataAgent()

        class MockDataAgent:
            def __init__(self):
                self._active_trades = []

            def get_trades(self, status='open'):
                return self._active_trades

        # Create a mock trader
        class MockTrader:
            def __init__(self, config):
                self.config = config
                self.api_manager = MockAPIManager()

            def calculate_risk_reward_ratio(self, entry_price, tp_price, sl_price, direction, symbol=None, include_fees=True):
                """Calculate RR ratio using the actual logic."""
                if entry_price <= 0 or tp_price <= 0 or sl_price <= 0:
                    return 0.0

                direction = direction.upper()

                if direction == "LONG":
                    risk = entry_price - sl_price
                    reward = tp_price - entry_price
                elif direction == "SHORT":
                    risk = sl_price - entry_price
                    reward = entry_price - tp_price
                else:
                    risk = abs(entry_price - sl_price)
                    reward = abs(tp_price - entry_price)

                if risk <= 0 or reward <= 0:
                    return 0.0

                return reward / risk

            def get_wallet_balance(self, coin="USDT"):
                """Mock wallet balance response."""
                return {
                    "result": {
                        "list": [
                            {
                                "totalAvailableBalance": "5092.25"
                            }
                        ]
                    }
                }

            def get_positions(self, symbol=None, **kwargs):
                """Mock positions response."""
                return {
                    "retCode": 0,
                    "result": {
                        "list": []  # No open positions
                    }
                }

            def get_open_orders(self, symbol=None, openOnly=1, limit=50, **kwargs):
                """Mock orders response."""
                return {
                    "retCode": 0,
                    "result": {
                        "list": []  # No open orders
                    }
                }

            def get_instruments_info(self, symbol, **kwargs):
                """Mock instruments info response."""
                return {
                    "retCode": 0,
                    "result": {
                        "list": [
                            {
                                "lotSizeFilter": {
                                    "minOrderQty": 0.001,
                                    "maxOrderQty": 1000000.0,
                                    "qtyStep": 0.001
                                }
                            }
                        ]
                    }
                }

        class MockAPIManager:
            def get_fee_rates(self, symbol=None):
                """Mock fee rates response."""
                return {"retCode": 0, "result": {"list": [{"takerFeeRate": "0.0006"}]}}

            def get_instruments_info(self, category="linear", symbol=None):
                """Mock instruments info response."""
                return {
                    "retCode": 0,
                    "result": {
                        "list": [
                            {
                                "lotSizeFilter": {
                                    "minOrderQty": 0.001,
                                    "maxOrderQty": 1000000.0,
                                    "qtyStep": 0.001
                                }
                            }
                        ]
                    }
                }

            def get_positions(self, category="linear", settleCoin="USDT", symbol=None):
                """Mock positions response."""
                return {
                    "retCode": 0,
                    "result": {
                        "list": []  # No open positions
                    }
                }

            def get_open_orders(self, category="linear", openOnly=1, limit=50, settleCoin=None, symbol=None):
                """Mock open orders response."""
                return {
                    "retCode": 0,
                    "result": {
                        "list": []  # No open orders
                    }
                }

        self.position_manager = MockPositionManager()
        self.trader = MockTrader(self.config)
        self.risk_manager = RiskManager(self.trader, self.position_manager, self.config)

    def test_equal_allocation(self, test_case: TestCase) -> Dict[str, Any]:
        """Test equal allocation for a given test case."""
        print(f"\n{'='*80}")
        print(f"TEST CASE: {test_case.name}")
        print(f"{'='*80}")

        # Setup components with the test case configuration
        self.setup_components(test_case.equal_allocation_base)

        # Mock the available slots
        original_get_available_slots = self.risk_manager.get_available_slots
        self.risk_manager.get_available_slots = lambda positions_data=None, orders_data=None: test_case.available_slots

        # Update config for this test
        self.config.trading.risk_percentage = test_case.total_risk_percentage

        print(f"📊 TEST PARAMETERS:")
        print(f"   USDT Balance: ${test_case.usdt_balance:,.2f}")
        print(f"   Available Slots: {test_case.available_slots}")
        print(f"   Total Risk: {test_case.total_risk_percentage*100:.1f}%")
        print(f"   Equal Allocation Base: {test_case.equal_allocation_base}")
        print(f"   Symbols to test: {len(test_case.symbols)}")

        results = []
        total_position_value = 0.0

        for i, symbol_data in enumerate(test_case.symbols):
            print(f"\n🔍 TESTING SYMBOL {i+1}: {symbol_data['symbol']}")

            # Create signal data
            signal = {
                "symbol": symbol_data["symbol"],
                "entry_price": symbol_data["entry_price"],
                "take_profit": symbol_data["take_profit"],
                "stop_loss": symbol_data["stop_loss"],
                "direction": symbol_data["direction"],
                "confidence": symbol_data.get("confidence", 0.8)
            }

            # Calculate position size
            result = self.risk_manager.calculate_slot_based_position_size(
                entry_price=symbol_data["entry_price"],
                stop_loss=symbol_data["stop_loss"],
                symbol=symbol_data["symbol"],
                signal=signal
            )

            if "error" not in result:
                position_value = result.get("position_value", 0)
                total_position_value += position_value
                results.append(result)

                print(f"   ✅ Success: Position Value = ${position_value:.2f}")
                if "slot_info" in result:
                    slot_info = result["slot_info"]
                    if test_case.equal_allocation_base:
                        print(f"   📊 Position Value per Slot: ${slot_info.get('position_value_per_slot', 0):.2f}")
                    else:
                        print(f"   📊 Risk per Slot: ${slot_info.get('risk_per_slot', 0):.2f}")
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                results.append(result)

        # Analyze results
        print(f"\n📈 RESULTS ANALYSIS:")
        print(f"   Total Position Value: ${total_position_value:.2f}")

        if test_case.equal_allocation_base:
            expected_value_per_slot = (test_case.usdt_balance * test_case.total_risk_percentage) / test_case.available_slots
            print(f"   Expected Value per Slot: ${expected_value_per_slot:.2f}")

            # Check if all positions have approximately equal values
            position_values = [r.get("position_value", 0) for r in results if r.get("success")]
            if len(position_values) > 1:
                min_value = min(position_values)
                max_value = max(position_values)
                value_range = max_value - min_value
                avg_value = sum(position_values) / len(position_values)

                print(f"   Position Values: {[f'${v:.2f}' for v in position_values]}")
                print(f"   Value Range: ${value_range:.2f}")
                print(f"   Average Value: ${avg_value:.2f}")

                # Check if values are reasonably equal (within 1% of average)
                tolerance = avg_value * 0.01
                all_equal = all(abs(v - avg_value) <= tolerance for v in position_values)
                print(f"   Equal Allocation: {'✅ ACHIEVED' if all_equal else '❌ NOT ACHIEVED'}")
        else:
            print(f"   Risk-based allocation (position values may vary)")

        # Restore original method
        self.risk_manager.get_available_slots = original_get_available_slots

        return {
            "test_case": test_case.name,
            "results": results,
            "total_position_value": total_position_value,
            "allocation_type": "equal_position_value" if test_case.equal_allocation_base else "equal_risk"
        }

    def run_all_tests(self):
        """Run all test cases."""
        print("🚀 EQUAL ALLOCATION TEST SUITE")
        print("Testing both equal risk and equal position value allocation methods.\n")

        # Define test cases
        test_cases = [
            TestCase(
                name="Equal Position Value Allocation - Similar Symbols",
                usdt_balance=5092.25,
                available_slots=3,
                total_risk_percentage=0.05,  # 5% - higher risk for larger positions
                equal_allocation_base=True,
                symbols=[
                    {
                        "symbol": "BTCUSDT",
                        "entry_price": 50000.0,
                        "stop_loss": 49000.0,
                        "take_profit": 52000.0,
                        "direction": "LONG"
                    },
                    {
                        "symbol": "ETHUSDT",
                        "entry_price": 3000.0,
                        "stop_loss": 2940.0,
                        "take_profit": 3120.0,
                        "direction": "LONG"
                    },
                    {
                        "symbol": "ADAUSDT",
                        "entry_price": 0.5,
                        "stop_loss": 0.49,
                        "take_profit": 0.52,
                        "direction": "LONG"
                    }
                ]
            ),

            TestCase(
                name="Equal Risk Allocation - Similar Symbols",
                usdt_balance=5092.25,
                available_slots=3,
                total_risk_percentage=0.05,  # 5%
                equal_allocation_base=False,
                symbols=[
                    {
                        "symbol": "BTCUSDT",
                        "entry_price": 50000.0,
                        "stop_loss": 49000.0,
                        "take_profit": 52000.0,
                        "direction": "LONG"
                    },
                    {
                        "symbol": "ETHUSDT",
                        "entry_price": 3000.0,
                        "stop_loss": 2940.0,
                        "take_profit": 3120.0,
                        "direction": "LONG"
                    },
                    {
                        "symbol": "ADAUSDT",
                        "entry_price": 0.5,
                        "stop_loss": 0.49,
                        "take_profit": 0.52,
                        "direction": "LONG"
                    }
                ]
            ),

            TestCase(
                name="Equal Position Value Allocation - Different Price Scales",
                usdt_balance=5092.25,
                available_slots=3,
                total_risk_percentage=0.05,  # 5%
                equal_allocation_base=True,
                symbols=[
                    {
                        "symbol": "PIXELUSDT",
                        "entry_price": 0.029,
                        "stop_loss": 0.028,
                        "take_profit": 0.031,
                        "direction": "LONG"
                    },
                    {
                        "symbol": "NEARUSDT",
                        "entry_price": 2.47,
                        "stop_loss": 2.42,
                        "take_profit": 2.57,
                        "direction": "LONG"
                    },
                    {
                        "symbol": "LTCUSDT",
                        "entry_price": 114.0,
                        "stop_loss": 112.0,
                        "take_profit": 118.0,
                        "direction": "LONG"
                    }
                ]
            )
        ]

        all_results = []

        for test_case in test_cases:
            result = self.test_equal_allocation(test_case)
            all_results.append(result)

        # Summary
        print(f"\n{'='*80}")
        print("🎉 TEST SUITE COMPLETE")
        print(f"{'='*80}")

        print(f"\n📊 SUMMARY:")
        for result in all_results:
            print(f"   {result['test_case']}: {result['allocation_type']} - Total Value: ${result['total_position_value']:.2f}")

        print(f"\n💡 KEY FINDINGS:")
        print("   • Equal Position Value: Each position has the same USD value")
        print("   • Equal Risk: Each position has the same risk amount (position values vary)")
        print("   • The equal_allocation_base config controls which method is used")
        print("   • Both methods maintain total risk limits and exchange constraints")


def main():
    """Main function to run the equal allocation tests."""
    try:
        tester = EqualAllocationTester()
        tester.run_all_tests()
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()