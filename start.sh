#!/bin/bash

# Production startup script for Trading Bot Docker container
set -e

echo "🚀 Starting Trading Bot in Docker container..."

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for a service to be ready
wait_for_service() {
    local service=$1
    local host=$2
    local port=$3
    local timeout=${4:-30}

    echo "⏳ Waiting for $service to be ready on $host:$port..."
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port" >/dev/null 2>&1; then
            echo "✅ $service is ready!"
            return 0
        fi
        sleep 1
    done

    echo "❌ $service failed to start within $timeout seconds"
    return 1
}

# Set timezone
if [ -n "$TZ" ]; then
    echo "🌍 Setting timezone to $TZ"
    ln -sf /usr/share/zoneinfo/$TZ /etc/localtime
    echo $TZ > /etc/timezone
fi

# Create necessary directories if they don't exist
echo "📁 Ensuring data directories exist..."
mkdir -p /app/trading_bot/data
mkdir -p /app/trading_bot/logs
mkdir -p /app/logs

# Set proper permissions
echo "🔐 Setting directory permissions..."
chmod -R 755 /app/trading_bot/data
chmod -R 755 /app/trading_bot/logs
chmod -R 755 /app/logs

# Check if .env file exists and has required variables
if [ -f "/app/.env" ]; then
    echo "📋 Found .env file, checking for required variables..."

    # Check for critical environment variables
    if ! grep -q "OPENAI_API_KEY" /app/.env; then
        echo "⚠️  WARNING: OPENAI_API_KEY not found in .env file"
    fi

    if ! grep -q "BYBIT_API_KEY" /app/.env; then
        echo "⚠️  WARNING: BYBIT_API_KEY not found in .env file"
    fi

    if ! grep -q "BYBIT_API_SECRET" /app/.env; then
        echo "⚠️  WARNING: BYBIT_API_SECRET not found in .env file"
    fi
else
    echo "⚠️  WARNING: .env file not found. Make sure to mount it as a volume."
fi

# Check if config.yaml exists
if [ ! -f "/app/config.yaml" ]; then
    echo "❌ ERROR: config.yaml not found. Make sure to mount it as a volume."
    exit 1
fi

# Verify Playwright installation
echo "🎭 Checking Playwright installation..."
if command_exists playwright; then
    echo "✅ Playwright is installed"
else
    echo "❌ Playwright not found, installing..."
    pip install playwright
    playwright install chromium
fi

# Health check before starting
echo "🏥 Running pre-start health checks..."

# Check Python installation
if command_exists python; then
    PYTHON_VERSION=$(python --version)
    echo "✅ Python available: $PYTHON_VERSION"
else
    echo "❌ Python not found"
    exit 1
fi

# Check if required Python packages can be imported
echo "📦 Checking Python dependencies..."
python -c "
try:
    import openai
    import playwright
    import yaml
    import telegram
    print('✅ All critical Python packages imported successfully')
except ImportError as e:
    print(f'❌ Missing dependency: {e}')
    exit(1)
"

# Set environment variables for headless browser
export DISPLAY=:99
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

# Start Xvfb for headless browser support (if needed)
if [ "$TRADINGVIEW_HEADLESS" != "false" ]; then
    echo "🖥️  Starting Xvfb for headless browser support..."
    Xvfb :99 -screen 0 1920x1080x24 > /dev/null 2>&1 &
    XVFB_PID=$!
    echo "✅ Xvfb started with PID: $XVFB_PID"

    # Wait a moment for Xvfb to initialize
    sleep 2
fi

# Print startup information
echo "📊 Container Information:"
echo "   - User: $(whoami)"
echo "   - Working Directory: $(pwd)"
echo "   - Python Path: $(which python)"
echo "   - Config File: $(ls -la config.yaml)"
echo "   - Data Directory: $(ls -la trading_bot/data/)"
echo "   - Logs Directory: $(ls -la logs/)"

# Print environment info (without sensitive data)
echo "🔧 Environment Configuration:"
if [ -n "$TZ" ]; then echo "   - Timezone: $TZ"; fi
if [ -n "$PYTHONPATH" ]; then echo "   - Python Path: $PYTHONPATH"; fi

echo ""
echo "🎯 Starting Trading Bot..."
echo "========================================"

# Execute the main application
exec python run_autotrader.py "$@"

# Cleanup (this will only run if exec fails)
if [ -n "$XVFB_PID" ]; then
    echo "🧹 Cleaning up Xvfb process..."
    kill $XVFB_PID 2>/dev/null || true
fi