1. 
git config --global user.name "macd2"
git config --global credential.helper store
git clone -b main-production https://github.com/macd2/Image_analyser_trading_bot.git

2.
to prepare a 1 cpu and 1gb droplet for the bot run first: 
./HelperScripts/setup_swap_memory.sh

3. 
apt install python3.12-venv
python -m venv venv
source venv/bin/activate

4. 
 pip install -r requirements.txt
 playwright install-deps && playwright install-deps 


 -----

# openVPN
sudo apt update && sudo apt install openvpn -y

sudo mkdir -p /etc/openvpn
sudo cp chart_new.ovpn /etc/openvpn/client.conf
sudo chown root:root /etc/openvpn/client.conf
sudo chmod 600 /etc/openvpn/client.conf

# Start OpenVPN
sudo systemctl start openvpn@client

# Enable on boot
sudo systemctl enable openvpn@client

# Don't enable on boot
sudo systemctl disable openvpn@client

# Check status
sudo systemctl status openvpn@client

# Stop OpenVPN
sudo systemctl stop openvpn@client


openvpn config:
client
proto udp
explicit-exit-notify
remote ************ 1194
dev tun
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
verify-x509-name server_6PT5wbs4pEo2jQHD name
auth SHA256
auth-nocache
cipher AES-128-GCM
tls-client
tls-version-min 1.2
tls-cipher TLS-ECDHE-ECDSA-WITH-AES-128-GCM-SHA256
verb 3

# Only route specific traffic through VPN (if needed)
# route 10.0.0.0 *************  # Add specific routes as needed
<ca>
[YOUR CA CERTIFICATE]
</ca>
<cert>
[YOUR CERTIFICATE]
</cert>
<key>
[YOUR PRIVATE KEY]
</key>
<tls-crypt>
[YOUR TLS-CRYPT KEY]
</tls-crypt>
