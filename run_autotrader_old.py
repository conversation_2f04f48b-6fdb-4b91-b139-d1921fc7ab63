from asyncio.log import logger
from datetime import timezone
from typing import Optional

from trading_bot.core.utils import smart_format_price


def run_trading_cycle(self, symbols: Optional[list] = None, dry_run: bool = False,
                        target_timeframe: Optional[str] = None) -> dict:
    """Run complete trading cycle: get recommendations, validate, and execute.
    
    Args:
        symbols: List of symbols to check (if None, gets from database)
        dry_run: If True, don't execute actual trades
        target_timeframe: If specified, only check this timeframe (for autotrader)
    """
    self._print_workflow_header("TRADING CYCLE STARTED", dry_run)

    # Enhanced timestamp synchronization at the start of each cycle
    logger.info("🕐 Synchronizing with Bybit server time...")
    
    # Get current sync status
    sync_status = self.trader.api_manager.get_timestamp_sync_status()
    if sync_status.get("status") == "success":
        sync_data = sync_status.get("sync_data", {})
        offset_ms = sync_data.get("offset_ms", 0)
        last_sync_ago = sync_data.get("last_sync_ago_seconds", 0)
        recv_window = sync_status.get("current_recv_window", 0)
        
        logger.info(f"✅ Timestamp sync status: Offset={offset_ms}ms, Last sync={last_sync_ago:.1f}s ago")
        logger.info(f"   Current recv_window: {recv_window}ms, Circuit breaker: {sync_status.get('circuit_breaker_state', 'unknown')}")
        
        # Force sync if last sync was more than 30 seconds ago
        if last_sync_ago > 30:
            logger.info(f"🔄 Forcing timestamp sync (last sync was {last_sync_ago:.1f}s ago)...")
            if self.trader.api_manager.force_timestamp_sync():
                logger.info(f"✅ Forced timestamp sync successful")
            else:
                logger.info(f"⚠️ Forced timestamp sync failed")
    else:
        logger.info(f"⚠️ Could not get timestamp sync status: {sync_status.get('error', 'Unknown error')}")
        # Try to force sync anyway
        logger.info(f"🔄 Attempting forced timestamp sync...")
        if self.trader.api_manager.force_timestamp_sync():
            logger.info(f"✅ Forced timestamp sync successful")
        else:
            logger.info(f"⚠️ Forced timestamp sync failed")

    symbol_list = symbols or []
    if not symbol_list:
        # If no symbols are provided, get symbols that have fresh recommendations for current timeframe
        try:
            all_analysis = self.data_agent.get_all_latest_analysis()
            if all_analysis:
                # Filter to only get symbols that have valid recommendations for the current timeframe
                # This should match what the mid-cycle check found
                valid_symbols = {}  # Store symbol -> timestamp mapping
                for analysis in all_analysis:
                    symbol = analysis.get('symbol')
                    timeframe = analysis.get('timeframe')
                    timestamp = analysis.get('timestamp', 'N/A')
                    if symbol and timeframe and self.is_recommendation_valid(analysis):
                        # Store the most recent timestamp for each symbol
                        if symbol not in valid_symbols or timestamp > valid_symbols[symbol]:
                            valid_symbols[symbol] = timestamp

                symbol_list = list(valid_symbols.keys())

                # Enhanced debug output with timestamps
                for symbol in symbol_list:
                    timestamp_str = valid_symbols[symbol]
                    try:
                        # Parse and format timestamp to ensure UTC display
                        from trading_bot.core.timestamp_validator import TimestampValidator
                        temp_validator = TimestampValidator()
                        parsed_time = temp_validator.parse_timestamp(timestamp_str)

                        # Ensure we're working with UTC
                        if parsed_time.tzinfo is None:
                            # If naive datetime, assume it's already UTC
                            utc_time = parsed_time.replace(tzinfo=timezone.utc)
                        else:
                            # Convert to UTC if timezone-aware
                            utc_time = parsed_time.astimezone(timezone.utc)

                        utc_formatted = utc_time.strftime('%Y-%m-%d %H:%M:%S UTC')
                        logger.info(f"   • {symbol} (latest: {utc_formatted})")
                    except Exception as e:
                        # Fallback to original timestamp if parsing fails
                        logger.info(f"   • {symbol} (latest: {timestamp_str}) [Parse error: {e}]")
            else:
                logger.info(f"⚠️ No analysis found in database - no symbols to check")
        except Exception as e:
            logger.info(f"❌ Error getting symbols from database: {e}")

    logger.info(
        f"Checking for valid recommendations for {'all symbols' if not symbols else f'{len(symbol_list)} symbols'}...")

    # Execute valid recommendations
    execution_result = self.execute_valid_recommendations(symbol_list, dry_run=dry_run,
                                                            target_timeframe=target_timeframe)

    # Handle backward compatibility - check if result is dict or list
    if isinstance(execution_result, dict):
        results = execution_result.get("results", [])
        skip_reasons = execution_result.get("skip_reasons", {})
    else:
        # Legacy format - just a list
        results = execution_result
        skip_reasons = {}

    # Summary
    successful_trades = [r for r in results if r['trade_result'].get('status') == 'executed_live']
    failed_trades = [r for r in results if r['trade_result'].get('status') == 'failed']
    passed_trades = [r for r in results if r['trade_result'].get('status') == 'dry_run']

    logger.info(f"\n{'=' * 60}")
    logger.info("TRADING CYCLE COMPLETE")
    logger.info(f"{'=' * 60}")
    logger.info(f"Total recommendations checked: {len(symbol_list)}")
    logger.info(f"Successful trades: {len(successful_trades)}")
    logger.info(f"Failed trades: {len(failed_trades)}")
    logger.info(f"Passed trades: {len(passed_trades)}")

    # Display skip reasons summary if any
    if skip_reasons:
        total_skipped = sum(skip_reasons.values())
        logger.info(f"Skipped trades: {total_skipped}")
        logger.info(f"\nSkip reasons breakdown:")

        # Group skip reasons into specific categories
        # Get dynamic min_rr value from config
        min_rr = getattr(self.config.trading, 'min_rr')

        categories = {
            f"Risk-reward ratio < {min_rr}": 0,
            "Recommendation expired": 0,
            "No recommendation in database": 0,
            "No analysis data in database": 0,
            "HOLD": 0,
            "Invalid recommendation": 0,
            "Stale recommendation": 0,
            "Position already exists": 0,
            "No available slots": 0,
            "Missing symbol or timeframe": 0,
            "Invalid confidence value": 0,
            "API Error": 0,
            "Risk validation failed": 0,
            "Data type error": 0,
            "Invalid price format": 0,
            "Missing price data": 0,
            "Low confidence": 0
        }

        for reason, count in skip_reasons.items():
            if "Risk-reward ratio" in reason and ("below minimum" in reason or f"< {min_rr}" in reason):
                categories[f"Risk-reward ratio < {min_rr}"] += count
            elif "Risk validation:" in reason and ("Risk-reward ratio" in reason or "below minimum" in reason):
                categories[f"Risk-reward ratio < {min_rr}"] += count
            elif "Recommendation expired" in reason:
                categories["Recommendation expired"] += count
            elif "No recommendation in database" in reason:
                categories["No recommendation in database"] += count
            elif "No analysis data in database" in reason:
                categories["No analysis data in database"] += count
            elif "analysis in database" in reason or "analysis for" in reason:
                # Handle specific timeframe/symbol analysis missing
                specific_reason = reason[:50] + "..." if len(reason) > 50 else reason
                if specific_reason not in categories:
                    categories[specific_reason] = 0
                categories[specific_reason] += count
            elif "Filtered by recommender" in reason:
                # This means analysis exists but recommender filtered it out for unknown reason
                specific_reason = reason[:50] + "..." if len(reason) > 50 else reason
                if specific_reason not in categories:
                    categories[specific_reason] = 0
                categories[specific_reason] += count
            elif reason == "HOLD" or "HOLD" in reason:
                categories["HOLD"] += count
            elif "Invalid recommendation" in reason:
                categories["Invalid recommendation"] += count
            elif "Stale recommendation" in reason:
                categories["Stale recommendation"] += count
            elif "Position already exists" in reason or "open position already exists" in reason:
                categories["Position already exists"] += count
            elif "Position sizing failed" in reason or "No available slots" in reason:
                categories["No available slots"] += count
            elif "Missing symbol or timeframe" in reason:
                categories["Missing symbol or timeframe"] += count
            elif "Invalid confidence" in reason:
                categories["Invalid confidence value"] += count
            elif "API Error:" in reason:
                categories["API Error"] += count
            elif "Risk validation failed" in reason or "Risk validation:" in reason:
                categories["Risk validation failed"] += count
            elif "Data type error" in reason or "TypeError" in reason:
                categories["Data type error"] += count
            elif "Invalid price format" in reason or "ValueError" in reason or "could not convert" in reason:
                categories["Invalid price format"] += count
            elif "Missing price data" in reason or "Missing required fields" in reason:
                categories["Missing price data"] += count
            elif "Low confidence" in reason or "confidence" in reason.lower():
                categories["Low confidence"] += count
            else:
                # If we still have unmatched reasons, show them specifically instead of "Other errors"
                specific_reason = reason[:50] + "..." if len(reason) > 50 else reason
                if specific_reason not in categories:
                    categories[specific_reason] = 0
                categories[specific_reason] += count
        # Print only non-zero categories
        for category, count in categories.items():
            if count > 0:
                logger.info(f"  {category}: {count}")
    else:
        logger.info(f"Skipped trades: 0")

    if passed_trades:
        logger.info("\nPassed Trade:")
        for trade in passed_trades:
            last_price = trade['trade_result'].get('last_close_price', 'N/A')
            entry_price = trade['trade_result'].get('entry_price', 'N/A')
            stop_loss = trade['trade_result'].get('stop_loss', 'N/A')
            take_profit = trade['trade_result'].get('take_profit', 'N/A')
            rr = trade['trade_result'].get('risk_reward_ratio', 'N/A')
            qty = trade['trade_result'].get('qty', 'N/A')

            symbol = trade['symbol']
            base_currency = symbol.replace('USDT', '').replace('USD', '') if symbol != 'N/A' else 'N/A'

            timeframe_from_trade = trade.get('timeframe', 'N/A')
            timeframe_from_recommendation = trade['recommendation'].get('timeframe', 'N/A')
            pass

            last_price_formatted = smart_format_price(last_price)
            entry_price_formatted = smart_format_price(entry_price)
            stop_loss_formatted = smart_format_price(stop_loss)
            take_profit_formatted = smart_format_price(take_profit)

            usd_value = 'N/A'
            if qty != 'N/A' and entry_price != 'N/A':
                try:
                    usd_value = f"${float(qty) * float(entry_price):,.2f}"
                except (ValueError, TypeError):
                    usd_value = 'N/A'

            timeframe = trade.get('timeframe', 'N/A')
            logger.info(
                f"  📋 {symbol} - {timeframe}: {trade['recommendation']['recommendation']} (would have been executed)")
            logger.info(
                f"     Last Price: {last_price_formatted}, Entry: {entry_price_formatted}, SL: {stop_loss_formatted}, TP: {take_profit_formatted}")
            if rr != 'N/A':
                logger.info(f"     Position: {qty} {base_currency} ({usd_value}), Risk/Reward: {rr:.2f}")
            else:
                logger.info(f"     Position: {qty} {base_currency} ({usd_value})")

    if successful_trades:
        logger.info("\nSuccessful trades:")
        for trade in successful_trades:
            qty = trade['trade_result'].get('qty', 'N/A')
            entry_price = trade['trade_result'].get('entry_price', 'N/A')
            symbol = trade['symbol']
            base_currency = symbol.replace('USDT', '').replace('USD', '') if symbol != 'N/A' else 'N/A'
            order_id = trade['trade_result'].get('order_id')

            usd_value = 'N/A'
            if qty != 'N/A' and entry_price != 'N/A':
                try:
                    usd_value = f"${float(qty) * float(entry_price):,.2f}"
                except (ValueError, TypeError):
                    usd_value = 'N/A'

            timeframe = trade.get('timeframe', 'N/A')
            logger.info(f"  ✅ {symbol} - {timeframe}: {trade['recommendation']['recommendation']} (Order ID: {order_id})")
            logger.info(f"     Position: {qty} {base_currency} ({usd_value})")

    if failed_trades:
        logger.info("\nFailed trades:")
        for trade in failed_trades:
            logger.info(f"  ❌ {trade['symbol']}: {trade['trade_result'].get('error', 'Unknown error')}")

    return {
        "total_checked": len(symbol_list),
        "successful_trades": len(successful_trades),
        "failed_trades": len(failed_trades),
        "passed_trades": len(passed_trades),
        "skip_reasons": skip_reasons,
        "results": results
    }