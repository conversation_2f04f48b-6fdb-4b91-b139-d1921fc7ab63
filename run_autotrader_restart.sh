#!/bin/bash

# Auto-restart script for run_autotrader.py
# This script will automatically restart the trading bot if it crashes

echo "Starting auto-restart wrapper for trading bot..."
echo "Press Ctrl+C to stop the script and exit screen"

while true; do
    echo "$(date): Starting run_autotrader.py..."
    python run_autotrader.py &
    BOT_PID=$!
    START_TIME=$(date +%s)

    while kill -0 $BOT_PID 2>/dev/null; do
        CURRENT_TIME=$(date +%s)
        ELAPSED=$((CURRENT_TIME - START_TIME))
        if [ $ELAPSED -ge 21600 ]; then
            echo "$(date): 6 hours passed, stopping bot gracefully..."
            while kill -0 $BOT_PID 2>/dev/null; do
                kill -INT $BOT_PID
                sleep 1
            done
            break
        fi
        sleep 10
    done

    wait $BOT_PID 2>/dev/null
    EXIT_CODE=$?
    echo "$(date): <PERSON><PERSON> stopped with exit code $EXIT_CODE"

    if [ $EXIT_CODE -eq 0 ]; then
        echo "Exited normally. Restarting in 5 seconds..."
        sleep 5
    elif [ $EXIT_CODE -eq 130 ]; then
        echo "Stopped by signal (6-hour restart). Restarting immediately..."
    else
        echo "Crashed with exit code $EXIT_CODE. Restarting in 10 seconds..."
        sleep 10
    fi
done
