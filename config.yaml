# =================================================================================
# TRADING BOT CONFIGURATION FILE
# =================================================================================
# This file contains all configuration settings for the automated trading bot.
# Each section is clearly documented with comments explaining what each setting does.
#
# IMPORTANT NOTES:
# - Boolean values: true/false (case sensitive)
# - Numeric values: integers or floats as specified
# - String values: use quotes when containing special characters
# - Comments start with # and are ignored by the system
# - Indentation is critical - use spaces consistently
#
# QUICK REFERENCE:
# - paths: File system paths and directories
# - logging: Log file rotation and management
# - file_management: Chart file cleanup and backup settings
# - openai: OpenAI API and assistant configurations
# - bybit: Bybit exchange API settings
# - trading: Core trading parameters and risk management
# - agents: AI model configurations for different tasks
# - tradingview: Chart screenshot automation settings
# =================================================================================

# File Paths Configuration
paths:
  database: "trading_bot/data/analysis_results.db"
  charts: "trading_bot/data/charts"
  logs: "logs"
  session_file: "trading_bot/data/"

# Logging Configuration
logging:
  # Log rotation settings to prevent log files from growing indefinitely
  rotation:
    max_file_size_mb: 10  # Maximum size per log file in MB
    backup_count: 5       # Number of backup files to keep
    # Files will be rotated as: autotrader.log -> autotrader.log.1 -> autotrader.log.2, etc.
  style: "compact"  # Logging format: "compact" or "detailed"
  show_icons: true  # Show emoji indicators in logs

# File Management Configuration
file_management:
  enable_backup: true  # Enable/disable backup creation for chart file operations (default: false)

  # Chart cleaning configuration
  chart_cleaning:
    enable_age_based_cleaning: true  # Enable cleaning files older than age threshold
    max_file_age_hours: 2  # Maximum age in hours for chart files (default: 24 hours)
    enable_cycle_based_cleaning: true  # Keep existing cycle-based cleaning

# OpenAI Configuration
openai:
  api_key: ""  # Will be loaded from .env
  model: "gpt-4.1-mini"
  max_tokens: 500
  temperature: 0.1
  
  # Assistant API Configuration
  assistant:
    default_timeout: 100  # Default timeout for assistant responses in seconds (reduced from 300)
    poll_interval: .5    # Polling interval for checking run status in seconds (increased from 1.0)
    max_retries: 1        # Maximum retries for failed requests
    
    # Default assistant IDs (to be configured by user)
    # These should be created in OpenAI platform and IDs added here
    assistants:
      analyzer: "asst_m11ds7XhdYfN7voO0pRvgbul"         # Assistant ID for chart analysis if "" use none assitant method
      # trader: ""                                      # Assistant ID for trading decisions (starts with 'asst_')
      # risk_manager: ""                                # Assistant ID for risk management (starts with 'asst_')
      # general: ""                                     # Assistant ID for general queries (starts with 'asst_')

# Bybit API Configuration
bybit:
  use_testnet: false # Set to true for testnet, false for mainnet
  recv_window: 30000  # Receive window in milliseconds (30 seconds) - Reduced from 300000ms to fix timing issues
  max_retries: 5 # Max retries for API calls - Increased for robustness
  circuit_breaker:
    error_threshold: 5
    recovery_timeout: 300
    max_recv_window: 600000 # Max recv_window for circuit breaker (10 minutes)
    backoff_multiplier: 2.0
    jitter_range: 0.1

# Trading Configuration
trading:
  # Core Trading Settings
  paper_trading: false  # Set to true for paper trading (no real money), false for live trading
  auto_approve_trades: true  # Skip Telegram confirmation when enabled (default: false for safety)

  # Risk Management Settings
  risk_tolerance: 0.4  # Risk threshold (0.0 to 1.0)
    # Lower values (0.2-0.4): More aggressive, allows higher-risk trades
    # Current value (0.5): Balanced approach, moderate risk filtering
    # Higher values (0.6-0.8): More conservative, stricter risk filtering
    # Maximum (1.0): Only zero-risk signals proceed unchanged

  min_confidence_threshold: 0.81  # Minimum confidence score required for trade execution (0.0-1.0)
  min_rr: 1.5  # Minimum risk-reward ratio required for trades (e.g., 1.2 = 1:1.2 RR)
  risk_percentage: 0.01  # Percentage of total capital to risk per trade (0.01 = 1%)
  max_loss_usd: 15.0  # Maximum total open_slots / max_loss in USD (hard cap for risk control)
  leverage: 2  # Leverage multiplier for all trades (1-100, 1 = no leverage)
  balance_safety_margin: 0.5  # Safety margin for balance usage (0.5 = use max 50% of balance)

  # Trade Limits and Slot Management
  max_concurrent_trades: 3  # Maximum number of concurrent trades allowed (trading slots)
  enable_dynamic_risk_allocation: true  # Enable/disable dynamic risk allocation based on available slots

  # State Machine Configuration (Parallel Operation)
  state_machine:
    enabled: true  # Master switch for trade state machine
    parallel_mode: true  # Run alongside existing system for comparison
    log_comparison: true  # Log differences between old and new systems
    fail_safely: true  # Continue with old system if state machine fails

  # Enhanced Position Sizing Configuration
  position_sizing:
    # Position sizing method selection (hierarchical priority)
    use_enhanced_position_sizing: true     # Use confidence & volatility weighting (highest priority)
    use_slot_based_position_sizing: false   # Use equal risk allocation across slots (medium priority)
    # Basic position sizing is always available as fallback (lowest priority)

    equal_allocation_base: false  # Use equal risk allocation (each position has same risk amount)

    # Safety thresholds to prevent tiny positions
    min_position_value_usd: 50.0  # Minimum USD value per position (prevents micro-trades)
    min_balance_threshold: 500.0  # Minimum account balance required to start trading

    # Confidence-based position sizing adjustments
    confidence_weighting:
      enabled: true  # Enable position size adjustments based on signal confidence
      low_confidence_threshold: 0.5  # Signals below this confidence = low confidence
      high_confidence_threshold: 0.9  # Signals above this confidence = high confidence
      low_confidence_weight: 0.9  # Reduce position size by 10% for low confidence signals
      neutral_confidence_weight: 1.0  # No change for medium confidence signals
      high_confidence_weight: 1.1  # Increase position size by 10% for high confidence signals

    # Risk-reward ratio based position sizing
    rr_weighting:
      enabled: true  # Enable position size adjustments based on RR ratio
      min_weight: 0.9  # Minimum 90% of base allocation for poor RR ratios
      max_weight: 1.1  # Maximum 110% of base allocation for excellent RR ratios

    # Volatility-based position sizing (ATR = Average True Range)
    volatility_weighting:
      enabled: true  # Enable position size adjustments based on asset volatility
      use_atr_based: true  # Use ATR calculation for volatility measurement
      low_volatility_weight: 1.1  # Increase position size by 10% for stable assets
      high_volatility_weight: 0.9  # Decrease position size by 10% for volatile assets
      atr_low_threshold: 0.02  # ATR below this percentage = low volatility
      atr_high_threshold: 0.05  # ATR above this percentage = high volatility

    # Combined weighting limits
    max_combined_weight: 1.2  # Maximum total position size multiplier (20% increase)
    min_combined_weight: 0.8  # Minimum total position size multiplier (20% decrease)

    # Logging preferences for position sizing
    logging:
      style: "compact"  # Logging format: "compact" or "detailed"
      show_icons: true  # Show emoji indicators in logs

  # Master Position Tightening Control
  enable_position_tightening: true  # Master switch to completely disable ALL position tightening features
    # When set to false, ALL tightening mechanisms will be disabled regardless of individual settings
    # When set to true, individual tightening settings below will be respected

  # Dynamic Stop Loss Tightening Features
  enable_sl_tightening: true  # Enable automatic stop loss tightening as profits grow (requires master switch = true)

  # Configurable RR-based Stop Loss Tightening Steps
  # Each level defines when to tighten SL and by how much relative to entry price
  rr_tightening_steps:
    # At 1R profit: Move SL to entry + 0.3R (partial profit protection)
    '2R':
      profit_threshold: 2.0    # Minimum profit level to trigger this step
      sl_position: 1.2         # Move SL to entry + 1.2R
      description: "Partial profit protection"

    # At 1.5R profit: Move SL to entry + 1R (breakeven + 1R profit)
    '2.5R':
      profit_threshold: 2.5    # Minimum profit level to trigger this step
      sl_position: 2.0         # Move SL to entry + 1R (breakeven + 1R profit)
      description: "Breakeven + 1R profit lock"

    # At 2R profit: Move SL to entry + 1.5R
    '3R':
      profit_threshold: 3.0    # Minimum profit level to trigger this step
      sl_position: 2.5         # Move SL to entry + 1.5R
      description: "Lock 1.5R profit"

    # At 2.5R profit: Move SL to entry + 2R
    '3.5R':
      profit_threshold: 3.5    # Minimum profit level to trigger this step
      sl_position: 3.0         # Move SL to entry + 2R
      description: "Lock 2R profit"

    # At 3R profit: Move SL to entry + 2.5R
    '4R':
      profit_threshold: 4.0    # Minimum profit level to trigger this step
      sl_position: 3.5         # Move SL to entry + 2.5R
      description: "Lock 2.5R profit"

    # # At 3.5R profit: Move SL to entry + 3R
    # '3.5R':
    #   profit_threshold: 4.5    # Minimum profit level to trigger this step
    #   sl_position: 4.0         # Move SL to entry + 3R
    #   description: "Lock 3R profit"

    # # At 4R profit: Move SL to entry + 3.5R
    # '4R':
    #   profit_threshold: 5.0    # Minimum profit level to trigger this step
    #   sl_position: 4.5         # Move SL to entry + 3.5R
    #   description: "Lock 3.5R profit"

    # # At 4.5R profit: Move SL to entry + 4R
    # '4.5R':
    #   profit_threshold: 5.5    # Minimum profit level to trigger this step
    #   sl_position: 4.8         # Move SL to entry + 4R
    #   description: "Lock 4R profit"

    # # At 5R profit: Move SL to entry + 4.5R
    # '5R':
    #   profit_threshold: 6.0    # Minimum profit level to trigger this step
    #   sl_position: 5.5         # Move SL to entry + 4.5R
    #   description: "Lock 4.5R profit"

    # # At 5.5R profit: Move SL to entry + 5R
    # '5.5R':
    #   profit_threshold: 6.5    # Minimum profit level to trigger this step
    #   sl_position: 6.0         # Move SL to entry + 5R
    #   description: "Lock 5R profit"

    # # At 6R profit: Move SL to entry + 5.5R
    # '6R':
    #   profit_threshold: 7.0    # Minimum profit level to trigger this step
    #   sl_position: 5.5         # Move SL to entry + 5.5R
    #   description: "Lock 5.5R profit"


  # ADX-based Stop Loss Tightening (Trend-following)
  enable_adx_tightening: false  # Enable ADX-based tightening when portfolio reaches profit target (requires master switch = true)
  adx_period: 14  # Period for ADX calculation (trend strength indicator)
  atr_period: 14  # Period for ATR calculation (volatility measure)
  adx_strength_threshold: 25  # ADX value above this indicates strong trend (0-100)
  base_atr_multiplier: 2.0  # Base multiplier for ATR-based stop placement
  adx_target_profit_usd: 50.0  # Portfolio profit target in USD to trigger ADX tightening

  # TP Proximity Feature - Convert SL to trailing stop when near TP
  enable_tp_proximity_trailing: false   # Enable/disable TP proximity trailing feature
  tp_proximity_threshold_pct: 1.0      # Activate when within X% of take profit (default: 2%)
  tp_proximity_trailing_pct: 1.0       # Trail X% behind last price when activated (default: 1%)
  
  # Age-based tightening configuration
  age_tightening:
    enabled: false  # Enable age-based tightening for unprofitable positions
    max_tightening_pct: 30.0  # Maximum tightening percentage (30% of original risk)
    min_profit_threshold: 1.0  # Only apply age-based tightening below this profit level (in R)

    # Age-based tightening thresholds (bars per timeframe)
    age_tightening_bars:
      '1m': 720      # 12 hours = 720 bars (12 * 60 / 1)
      '5m': 216      # 18 hours = 216 bars (18 * 60 / 5)
      '15m': 96      # 24 hours = 96 bars (24 * 60 / 15)
      '30m': 96      # 36 hours = 72 bars (36 * 60 / 30)
      '1h': 48       # 48 hours = 48 bars (48 * 60 / 60)
      '4h': 18       # 72 hours = 18 bars (72 * 60 / 240)
      '1d': 4        # 96 hours = 4 bars (96 * 60 / 1440)
      '1w': 0.71     # 120 hours = 0.71 bars (120 * 60 / 10080)

    # Age-based tightening strategy:
    # Positions get tighter stop losses as they age beyond timeframe-specific thresholds
    # When position age exceeds threshold, additional tightening is applied proportionally
  
  
  # Trade Scoring System (for Order Replacement)
  trade_scoring:
    distance_factor_multiplier: 0.1  # How much entry price distance affects trade score (0.1 = minimal impact)
    min_score_improvement: 0.1  # Minimum score improvement required to replace existing trade

  # Intelligent Order Replacement System
  order_replacement:
    enable_intelligent_replacement: true  # Master switch to enable/disable automatic order replacement
    min_score_improvement_threshold: 0.15  # Minimum score improvement needed to justify replacement
    partially_filled_protection_threshold: 0.5  # Protect orders more than 50% filled from replacement
    batch_evaluation_timeout_seconds: 30  # Max time to wait before processing signal batch
    max_replacements_per_batch: 10  # Maximum order replacements allowed per evaluation batch
    replacement_cooldown_minutes: 5  # Minimum time between replacements for same symbol
    enable_cross_symbol_replacement: true  # Allow replacing orders across different symbols/timeframes

    # Age-based penalty for old orders
    max_age_bars: 2  # Orders older than this many bars are considered expired
    age_penalty_factor: 0.2  # Score multiplier for expired orders (0.2 = 20% of original score)

  # Exchange Synchronization (Trade State Sync)
  exchange_sync:
    enabled: true  # Enable synchronization between local database and exchange
    sync_interval_seconds: 30  # How often to check for trade state changes
    max_retries: 3  # Maximum retry attempts for failed sync operations
    rate_limit_requests_per_second: 5  # API rate limit for sync operations
    batch_size: 50  # Number of trades to sync in each batch
    error_recovery_delay_seconds: 60  # Delay before retrying after sync errors
    incremental_sync_enabled: true  # Use incremental sync instead of full sync when possible
    full_sync_interval_minutes: 5  # How often to perform full synchronization

# AI Agent Configurations (OpenAI GPT Models)
agents:
  analyzer:  # Chart analysis and trading signal generation
    model: "gpt-4.1-mini"  # GPT model to use for chart analysis
    max_tokens: 500  # Maximum response length for analysis
    temperature: 0.1  # Creativity level (0.0 = deterministic, 1.0 = creative)
    reasoning_effort: "medium"  # Reasoning depth: low, medium, high

  sourcer:  # Signal source identification and validation
    model: "gpt-4.1-mini"  # GPT model for signal sourcing
    max_tokens: 300  # Maximum response length
    temperature: 0.2  # Slightly more creative for signal discovery

  optimizer:  # Trade optimization and parameter tuning
    model: "gpt-4.1-mini"  # GPT model for optimization tasks
    max_tokens: 1000  # Longer responses for detailed optimization
    temperature: 0.1  # Conservative approach for optimization

  timestamp_extractor:  # Time-based analysis and pattern recognition
    model: "gpt-4.1-mini"  # GPT model for timestamp analysis
    max_tokens: 300  # Standard response length
    temperature: 0.0  # Fully deterministic for timestamp extraction

# TradingView Chart Capture Configuration
tradingview:
  enabled: true  # Enable/disable automated chart screenshot capture
  base_url: "https://www.tradingview.com"  # TradingView base URL
  login_url: "https://www.tradingview.com/accounts/signin/"  # Login page URL
  chart_url_template: "https://www.tradingview.com/chart/?symbol={symbol}&interval={timeframe}"
    # Template for chart URLs with {symbol} and {timeframe} placeholders
  target_chart: "https://www.tradingview.com/chart/iXrxoaRu/"  # Specific chart URL to use

  # Browser Automation Settings
  browser:
    headless: true  # Run browser without GUI (faster, no display needed)
    timeout: 300000  # Maximum time to wait for page loads (30 seconds)
    viewport_width: 1600  # Browser window width in pixels - keep smaller than VNC for proper fit
    viewport_height: 900  # Browser window height in pixels - keep smaller than VNC for proper fit
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      # Browser user agent string for compatibility

    # VNC Integration Settings
    use_vnc: false  # Set to true to enable VNC integration, false for standard mode
    vnc_display: ":99"  # VNC display number (only used when use_vnc=true)
    vnc_port: 5999  # VNC port for connection info (only used when use_vnc=true)
    vnc_window_size: "1920x1080"  # Window size when using VNC (matches VNC screen) - increased to match typical desktop resolution


  # Authentication and Session Management
  auth:
    session_timeout: 604800  # Session validity duration in seconds (7 days - more realistic)
    max_login_attempts: 3  # Maximum login retry attempts
    login_retry_delay: 5  # Delay between login attempts in seconds
    js_render_wait: 3000  # Wait time for JavaScript elements to load (increased from 100ms)

  # Screenshot Capture Settings
  screenshot:
    chart_selector: ".chart-container"  # CSS selector for chart area
    wait_for_load: 5000  # Wait time for chart to fully load (5 seconds)
    hide_elements:  # CSS selectors for elements to hide before screenshot
      - ".header-chart-panel"    # Hide top header
      - ".bottom-widgetbar-content"  # Hide bottom widgets
      - ".layout__area--right"   # Hide right sidebar
      - ".toast-wrapper"         # Hide notification toasts
    quality: 90  # Screenshot quality (0-100, higher = better quality)

  # Rate Limiting and Request Control
  rate_limit:
    requests_per_minute: 12  # Maximum requests per minute to TradingView
    delay_between_requests: 4  # Delay between screenshot requests (seconds)
    respect_rate_limits: true  # Enable rate limiting to avoid being blocked

  # Error Handling and Retry Logic
  retry:
    max_attempts: 3  # Maximum retry attempts for failed operations
    backoff_factor: 2  # Exponential backoff multiplier for retries
    base_delay: 5  # Base delay in seconds before first retry
