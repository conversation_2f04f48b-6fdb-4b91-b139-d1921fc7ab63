"""
Database synchronization script for analyzer results tables.

This script syncs analysis_results tables between two SQLite databases by comparing IDs.
It ensures both databases have the same records based on the sync direction.
"""

import sqlite3
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_table_columns(conn: sqlite3.Connection, table_name: str) -> List[str]:
    """Get column names for a table."""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    return columns


def get_table_schema(conn: sqlite3.Connection, table_name: str) -> List[Tuple]:
    """Get full table schema including column types and constraints."""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name})")
    return cursor.fetchall()


def fix_schema_mismatch(source_conn: sqlite3.Connection, target_conn: sqlite3.Connection, table_name: str) -> List[str]:
    """Fix schema mismatch by adding missing columns to target database."""
    fixes_applied = []

    try:
        source_schema = get_table_schema(source_conn, table_name)
        target_schema = get_table_schema(target_conn, table_name)

        # Create lookup dictionaries for easy comparison
        source_columns = {row[1]: row for row in source_schema}
        target_columns = {row[1]: row for row in target_schema}

        # Find missing columns in target
        missing_columns = []
        for col_name, col_info in source_columns.items():
            if col_name not in target_columns:
                missing_columns.append(col_info)

        # Add missing columns to target
        for col_info in missing_columns:
            col_id, col_name, col_type, not_null, default_value, is_primary = col_info

            # Build ALTER TABLE statement
            alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type}"

            if not_null:
                alter_sql += " NOT NULL"
            if default_value is not None:
                alter_sql += f" DEFAULT {default_value}"

            try:
                cursor = target_conn.cursor()
                cursor.execute(alter_sql)
                target_conn.commit()
                fixes_applied.append(f"Added column '{col_name}' ({col_type}) to {table_name}")
                logger.info(f"✅ Fixed schema: {alter_sql}")
            except Exception as e:
                logger.error(f"❌ Failed to add column '{col_name}': {e}")
                fixes_applied.append(f"Failed to add column '{col_name}': {e}")

    except Exception as e:
        logger.error(f"❌ Schema fix failed for {table_name}: {e}")
        fixes_applied.append(f"Schema fix failed: {e}")

    return fixes_applied


def fix_source_schema_mismatch(source_conn: sqlite3.Connection, target_conn: sqlite3.Connection, table_name: str) -> List[str]:
    """Fix schema mismatch by adding missing columns to source database."""
    fixes_applied = []

    try:
        source_schema = get_table_schema(source_conn, table_name)
        target_schema = get_table_schema(target_conn, table_name)

        # Create lookup dictionaries for easy comparison
        source_columns = {row[1]: row for row in source_schema}
        target_columns = {row[1]: row for row in target_schema}

        # Find missing columns in source
        missing_columns = []
        for col_name, col_info in target_columns.items():
            if col_name not in source_columns:
                missing_columns.append(col_info)

        # Add missing columns to source
        for col_info in missing_columns:
            col_id, col_name, col_type, not_null, default_value, is_primary = col_info

            # Build ALTER TABLE statement
            alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type}"

            if not_null:
                alter_sql += " NOT NULL"
            if default_value is not None:
                alter_sql += f" DEFAULT {default_value}"

            try:
                cursor = source_conn.cursor()
                cursor.execute(alter_sql)
                source_conn.commit()
                fixes_applied.append(f"Added column '{col_name}' ({col_type}) to source {table_name}")
                logger.info(f"✅ Fixed source schema: {alter_sql}")
            except Exception as e:
                logger.error(f"❌ Failed to add column '{col_name}' to source: {e}")
                fixes_applied.append(f"Failed to add column '{col_name}' to source: {e}")

    except Exception as e:
        logger.error(f"❌ Source schema fix failed for {table_name}: {e}")
        fixes_applied.append(f"Source schema fix failed: {e}")

    return fixes_applied


def get_record_count(conn: sqlite3.Connection, table_name: str) -> int:
    """Get total record count for a table."""
    cursor = conn.cursor()
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    return cursor.fetchone()[0]


def get_all_ids(conn: sqlite3.Connection, table_name: str) -> set:
    """Get all IDs from a table."""
    cursor = conn.cursor()
    cursor.execute(f"SELECT id FROM {table_name}")
    return {row[0] for row in cursor.fetchall()}


def get_records_by_ids(conn: sqlite3.Connection, table_name: str, ids: List[str], columns: List[str]) -> List[Tuple]:
    """Get records for specific IDs with explicit column order."""
    if not ids:
        return []

    cursor = conn.cursor()
    placeholders = ','.join('?' * len(ids))
    column_list = ','.join(columns)
    cursor.execute(f"SELECT {column_list} FROM {table_name} WHERE id IN ({placeholders})", ids)
    return cursor.fetchall()


def map_record_to_target_columns(record: Tuple, source_columns: List[str], target_columns: List[str]) -> Tuple:
    """Map a record from source column order to target column order."""
    if not record:
        return record

    # Create a mapping from column name to value
    column_values = dict(zip(source_columns, record))

    # Return values in target column order
    return tuple(column_values[col] for col in target_columns)


def insert_records(conn: sqlite3.Connection, table_name: str, columns: List[str], records: List[Tuple]) -> int:
    """Insert records into table."""
    if not records:
        return 0

    cursor = conn.cursor()
    placeholders = ','.join('?' * len(columns))
    query = f"INSERT OR REPLACE INTO {table_name} ({','.join(columns)}) VALUES ({placeholders})"

    inserted = 0
    for record in records:
        try:
            cursor.execute(query, record)
            inserted += 1
        except Exception as e:
            logger.error(f"Failed to insert record: {e}")

    conn.commit()
    return inserted


def sync_analyzer_results(
    source_db_path: str,
    target_db_path: str,
    target_tables: Optional[List[str]] = None,
    bidirectional: bool = False,
    dry_run: bool = False,
    auto_fix_schema: bool = False,
    fix_source_schema: bool = False
) -> Dict[str, Any]:
    """
    Sync analyzer results between two databases.

    Args:
        source_db_path: Path to source database
        target_db_path: Path to target database
        target_tables: List of table names to sync (defaults to ["trades", "analysis_results"])
        bidirectional: If True, sync both ways; if False, only source -> target
        dry_run: If True, only preview changes without applying them
        auto_fix_schema: If True, automatically add missing columns to target database
        fix_source_schema: If True, also fix missing columns in source database when auto_fix_schema is enabled

    Returns:
        Dictionary with sync statistics and results
    """

    start_time = datetime.now()

    # Set default target tables if not provided
    if target_tables is None:
        target_tables = ["trades", "analysis_results"]

    logger.info(f"Starting database sync: {source_db_path} -> {target_db_path}")
    logger.info(f"Target tables: {target_tables}")
    logger.info(f"Bidirectional: {bidirectional}, Dry run: {dry_run}, Auto-fix schema: {auto_fix_schema}, Fix source schema: {fix_source_schema}")

    # Validate database files exist
    source_path = Path(source_db_path)
    target_path = Path(target_db_path)

    if not source_path.exists():
        raise FileNotFoundError(f"Source database not found: {source_db_path}")
    if not target_path.exists():
        raise FileNotFoundError(f"Target database not found: {target_db_path}")

    results = {
        'source_db': str(source_path),
        'target_db': str(target_path),
        'target_tables': target_tables,
        'bidirectional': bidirectional,
        'dry_run': dry_run,
        'auto_fix_schema': auto_fix_schema,
        'fix_source_schema': fix_source_schema,
        'sync_phases': [],
        'total_inserted': 0,
        'total_processed': 0,
        'schema_fixes': [],
        'errors': [],
        'start_time': start_time.isoformat(),
        'end_time': None,
        'duration_seconds': None
    }

    # Connect to databases
    source_conn = sqlite3.connect(source_db_path)
    target_conn = sqlite3.connect(target_db_path)

    try:
        # Sync each table in the target_tables list
        for table_name in target_tables:
            logger.info(f"\n=== Syncing table: {table_name} ===")

            # Get table info
            source_columns = get_table_columns(source_conn, table_name)
            target_columns = get_table_columns(target_conn, table_name)

            # Compare column names (order doesn't matter for our sync logic)
            source_column_names = set(source_columns)
            target_column_names = set(target_columns)

            if source_column_names != target_column_names:
                missing_in_target = source_column_names - target_column_names
                missing_in_source = target_column_names - source_column_names

                if auto_fix_schema:
                    # Auto-fix missing columns in target
                    if missing_in_target:
                        logger.info(f"🔧 Auto-fixing schema for {table_name}: adding {len(missing_in_target)} missing columns to target")
                        schema_fixes = fix_schema_mismatch(source_conn, target_conn, table_name)
                        results['schema_fixes'].extend(schema_fixes)

                    # Auto-fix missing columns in source (only if explicitly enabled)
                    if missing_in_source and fix_source_schema:
                        logger.info(f"🔧 Auto-fixing schema for {table_name}: adding {len(missing_in_source)} missing columns to source")
                        source_schema_fixes = fix_source_schema_mismatch(source_conn, target_conn, table_name)
                        results['schema_fixes'].extend(source_schema_fixes)

                    # Refresh column information after fixes
                    source_columns = get_table_columns(source_conn, table_name)
                    target_columns = get_table_columns(target_conn, table_name)
                    source_column_names = set(source_columns)
                    target_column_names = set(target_columns)

                    # Check if fix was successful
                    if source_column_names == target_column_names:
                        logger.info(f"✅ Schema fix successful for {table_name}")
                    else:
                        remaining_missing_target = source_column_names - target_column_names
                        remaining_missing_source = target_column_names - source_column_names
                        error_msg = f"Table '{table_name}' schemas still don't match after auto-fix"
                        if remaining_missing_target:
                            error_msg += f". Still missing in target: {remaining_missing_target}"
                        if remaining_missing_source:
                            error_msg += f". Still missing in source: {remaining_missing_source}"
                        raise ValueError(error_msg)
                else:
                    # Original error handling when auto_fix is disabled
                    error_msg = f"Table '{table_name}' schemas don't match between databases"
                    if missing_in_target:
                        error_msg += f". Missing in target: {missing_in_target}"
                    if missing_in_source:
                        error_msg += f". Missing in source: {missing_in_source}"
                    raise ValueError(error_msg)

            logger.info(f"Table '{table_name}' columns: {source_columns}")

            # Phase 1: Source -> Target sync
            logger.info(f"Phase 1: Syncing {table_name} source -> target")
            phase1_result = _sync_direction(
                source_conn, target_conn, table_name, source_columns,
                "source_to_target", dry_run
            )
            phase1_result['table_name'] = table_name
            results['sync_phases'].append(phase1_result)
            results['total_inserted'] += phase1_result['inserted']
            results['total_processed'] += phase1_result['processed']

            # Phase 2: Target -> Source sync (if bidirectional)
            if bidirectional:
                logger.info(f"Phase 2: Syncing {table_name} target -> source")
                phase2_result = _sync_direction(
                    target_conn, source_conn, table_name, target_columns,
                    "target_to_source", dry_run
                )
                phase2_result['table_name'] = table_name
                results['sync_phases'].append(phase2_result)
                results['total_inserted'] += phase2_result['inserted']
                results['total_processed'] += phase2_result['processed']

        logger.info("Database sync completed successfully")

    except Exception as e:
        error_msg = f"Sync failed: {str(e)}"
        logger.error(error_msg)
        results['errors'].append(error_msg)
        raise

    finally:
        # Close database connections to clean up temporary files
        try:
            source_conn.close()
            logger.info("Source database connection closed")
        except Exception as e:
            logger.warning(f"Failed to close source connection: {e}")

        try:
            target_conn.close()
            logger.info("Target database connection closed")
        except Exception as e:
            logger.warning(f"Failed to close target connection: {e}")

        # Calculate duration
        end_time = datetime.now()
        results['end_time'] = end_time.isoformat()
        results['duration_seconds'] = (end_time - start_time).total_seconds()

        # Log summary
        logger.info("Sync Summary:")
        logger.info(f"  Total processed: {results['total_processed']}")
        logger.info(f"  Total inserted: {results['total_inserted']}")
        logger.info(".2f")
        logger.info(f"  Schema fixes applied: {len(results['schema_fixes'])}")
        logger.info(f"  Errors: {len(results['errors'])}")

        if results['schema_fixes']:
            logger.info("Schema fixes:")
            for fix in results['schema_fixes']:
                logger.info(f"  - {fix}")

    return results


def _sync_direction(
    from_conn: sqlite3.Connection,
    to_conn: sqlite3.Connection,
    table_name: str,
    from_columns: List[str],
    direction: str,
    dry_run: bool
) -> Dict[str, Any]:
    """Sync records from one database to another in one direction."""

    # Get target database column order for insertions
    to_columns = get_table_columns(to_conn, table_name)

    # Get all IDs from both databases
    from_ids = get_all_ids(from_conn, table_name)
    to_ids = get_all_ids(to_conn, table_name)

    # Find missing IDs in target
    missing_ids = from_ids - to_ids
    missing_count = len(missing_ids)

    logger.info(f"  Direction: {direction}")
    logger.info(f"  Source records: {len(from_ids)}")
    logger.info(f"  Target records: {len(to_ids)}")
    logger.info(f"  Missing in target: {missing_count}")

    phase_result = {
        'direction': direction,
        'source_count': len(from_ids),
        'target_count': len(to_ids),
        'missing_count': missing_count,
        'processed': missing_count,
        'inserted': 0,
        'errors': 0
    }

    if missing_count == 0:
        logger.info("  No records to sync")
        return phase_result

    if dry_run:
        logger.info(f"  DRY RUN: Would insert {missing_count} records")
        phase_result['inserted'] = missing_count  # For reporting purposes
        return phase_result

    # Get missing records from source (use source column order)
    missing_records = get_records_by_ids(from_conn, table_name, list(missing_ids), from_columns)

    # Map records to target column order
    mapped_records = [
        map_record_to_target_columns(record, from_columns, to_columns)
        for record in missing_records
    ]

    # Insert missing records into target (use target column order)
    inserted = insert_records(to_conn, table_name, to_columns, mapped_records)

    phase_result['inserted'] = inserted
    logger.info(f"  Successfully inserted {inserted} records")

    if inserted != missing_count:
        phase_result['errors'] = missing_count - inserted
        logger.warning(f"  Failed to insert {phase_result['errors']} records")

    return phase_result


# Example usage and test functions
def test_sync():
    """Test the sync function with sample databases."""
    # This would be used for testing the sync functionality
    pass


if __name__ == "__main__":
    # Example usage
    try:
        # One-way sync example with default tables (trades and analysis_results)
        result = sync_analyzer_results(
            source_db_path = "trading_bot/data/analysis_results.db",
            target_db_path="^^Server Backup/analysis_results.db",
            # target_db_path = "trading_bot/data/analysis_results.db",
            # source_db_path="^^Server Backup/analysis_results.db",
            target_tables=["trades", "analysis_results"],  # Sync both tables
            bidirectional=False,
            dry_run=False,
            auto_fix_schema=True,  # Auto-fix schema mismatches
            fix_source_schema=False  # Also fix source schema when auto_fix_schema is enabled
        )
        print("Sync completed successfully!")
        print(json.dumps(result, indent=2, default=str))

    except Exception as e:
        print(f"Sync failed: {e}")
        raise
