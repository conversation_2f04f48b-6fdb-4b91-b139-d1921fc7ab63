
git push origin $(git branch --show-current)


[]make sure we ONLY work in the @/backtest  folder

the bounderies should be ignored in the abcktest

use terminal commands and break this down in to smaller tasks investigate this issue 
---------

25-08-30 08:29:08 | 🟡 ⏭️ Skipped: WAVESUSDT.P_1h_20250808_141528.png.backup - Skipped WAVESUSDT.P: File boundary 2025-08-08 14:00:00+00:00 does not match current boundary 2025-08-30 08:00:00+00:00
2025-08-30 08:29:08 | 🔴 🎉 Backtest completed! Processed: 0, Errors: 1, Total: 1
2025-08-30 08:29:08 | 📊 Success rate: 0.0%
2025-08-30 08:29:08 | 🎉 🎉 Backtest completed successfully! Processed 0 images