#!/usr/bin/env python3
"""
Prompt Tester - Visual Analysis Tool with Real Bybit Data

This tool allows testing different trading prompts with real market data from Bybit.
It imports prompts as the analyzer does and runs only the visual agent with the given prompt,
then visualizes the results with real price data from Bybit.

Usage:
    python prompt_tester.py <symbol> <timeframe> <image_path> <prompt_name> <model>

Example:
    python prompt_tester.py BTCUSDT 1h chart_btc_1h.png get_analyzer_prompt_grok gpt-4o
"""

import json
import logging
import os
import sys
from typing import Dict, Any, Optional
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from PIL import Image

from trading_bot.core.bybit_api_manager import BybitAPIManager
from trading_bot.core.analyzer import ChartAnalyzer
from trading_bot.core.utils import smart_format_price, normalize_symbol_for_bybit
from trading_bot.core.prompts.analyzer_prompt import (
    get_analyzer_prompt_simpleer,
    get_analyzer_prompt,
    get_analyzer_prompt_mistral,
    get_analyzer_prompt_grok,
    get_analyzer_prompt_conservative,
    get_analyzer_prompt_conservative_more_risk,
    get_analyzer_prompt_optimized_v26_grok,
    get_analyzer_prompt_optimized_v26_grok_fineTune,
    get_analyzer_prompt_improved_v28,
    get_analyzer_prompt_improved_v28_short_fix,
    code_nova_improoved_based_on_analyzis,
    orginal_propmpt,
    orginal_propmpt_hybrid
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PromptTester:
    """Test different trading prompts with real Bybit market data."""

    def __init__(self, config=None):
        self.config = config

        # Try to load config from file like autotrader does
        if config is None:
            try:
                from trading_bot.config.settings import Config
                config = Config.from_yaml()
                self.config = config
            except Exception as e:
                logger.warning(f"⚠️ Could not load config: {e}")
                self.config = None

        # Initialize Bybit API manager for real market data
        self.bybit_api_manager = None
        if self.config is not None:
            try:
                self.bybit_api_manager = BybitAPIManager(self.config, use_testnet=False)
                logger.info("✅ Bybit API manager initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Bybit API manager: {e}")
                logger.info("💡 Continuing without real market data")

        # Initialize OpenAI client and ChartAnalyzer
        self.client = None
        self.analyzer = None
        if self.config and hasattr(self.config, 'openai') and self.config.openai.api_key:
            try:
                import openai
                self.client = openai.OpenAI(api_key=self.config.openai.api_key)
                self.analyzer = ChartAnalyzer(self.client, self.config, skip_boundary_validation=True, api_manager=self.bybit_api_manager, logger=logger)
                logger.info("✅ ChartAnalyzer initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize ChartAnalyzer: {e}")


    def get_real_market_data(self, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Get real-time market data from Bybit."""
        if not self.bybit_api_manager:
            logger.warning("⚠️ Bybit API manager not available")
            return {}

        market_data = {}

        try:
            # Get current bid/ask prices
            normalized_symbol = normalize_symbol_for_bybit(symbol)
            tickers_response = self.bybit_api_manager.get_tickers(
                category="linear",
                symbol=normalized_symbol
            )

            if tickers_response and tickers_response.get("retCode") == 0:
                ticker_list = tickers_response.get("result", {}).get("list", [])
                if ticker_list:
                    ticker = ticker_list[0]
                    market_data['bid_price'] = float(ticker.get("bid1Price", 0))
                    market_data['ask_price'] = float(ticker.get("ask1Price", 0))
                    market_data['mid_price'] = (market_data['bid_price'] + market_data['ask_price']) / 2

            # Get funding rate
            funding_response = self.bybit_api_manager.get_funding_rate_history(symbol=normalized_symbol)
            if funding_response and funding_response.get("retCode") == 0:
                funding_list = funding_response.get("result", {}).get("list", [])
                if funding_list:
                    market_data['funding_rate'] = funding_list[0].get("fundingRate", "N/A")

            # Get long/short ratio
            ratio_response = self.bybit_api_manager.get_long_short_ratio(symbol=normalized_symbol, timeframe=timeframe)
            if ratio_response and ratio_response.get("retCode") == 0:
                ratio_list = ratio_response.get("result", {}).get("list", [])
                if ratio_list:
                    latest_ratio = ratio_list[0]
                    market_data['long_short_ratio'] = f"Buy: {latest_ratio.get('buyRatio', 'N/A')}, Sell: {latest_ratio.get('sellRatio', 'N/A')}"

            # Get last close price from klines
            kline_response = self.bybit_api_manager.get_kline(
                category="linear",
                symbol=normalized_symbol,
                interval=self._map_timeframe_to_bybit(timeframe),
                limit=1
            )

            if kline_response and kline_response.get("retCode") == 0:
                kline_list = kline_response.get("result", {}).get("list", [])
                if kline_list:
                    market_data['last_close_price'] = float(kline_list[0][4])

        except Exception as e:
            logger.error(f"❌ Error fetching market data for {symbol}: {e}")

        return market_data

    def get_historical_market_data(self, image_path: str, db_path: Optional[str] = None) -> Dict[str, Any]:
        """Get historical market data from database for the specific image."""
        logger.info(f"DB Path: {db_path}")
        try:
            import sqlite3
            from os.path import basename

            # Connect to the database
            if db_path is None:
                logger.error("❌ Database path is None, cannot connect to database")
                return {}
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Extract just the filename from the input path for matching
            image_filename = basename(image_path)
            logger.info(f"🔍 Looking for image: {image_filename}")

            # Query to find the record where the stored path ends with the target filename
            cursor.execute("""
                SELECT analysis_prompt, timestamp
                FROM analysis_results
                WHERE image_path LIKE '%' || ?
                ORDER BY timestamp DESC
                LIMIT 1
            """, (image_filename,))

            result = cursor.fetchone()

            if result:
                analysis_prompt = result[0]

                # Extract market data from the analysis prompt
                # Look for the CURRENT MARKET DATA section
                market_data = {}

                # Extract various market data fields using regex
                import re

                # Extract bid price
                bid_match = re.search(r'Best Bid:\s*([0-9.]+)', analysis_prompt)
                if bid_match:
                    market_data['bid_price'] = float(bid_match.group(1))

                # Extract ask price
                ask_match = re.search(r'Best Ask:\s*([0-9.]+)', analysis_prompt)
                if ask_match:
                    market_data['ask_price'] = float(ask_match.group(1))

                # Extract funding rate
                funding_match = re.search(r'Funding Rate:\s*([0-9.-]+)', analysis_prompt)
                if funding_match:
                    market_data['funding_rate'] = funding_match.group(1)

                # Extract long/short ratio
                ls_match = re.search(r'Long/Short Ratio:\s*Buy:\s*([0-9.]+),\s*Sell:\s*([0-9.]+)', analysis_prompt)
                if ls_match:
                    market_data['long_short_ratio'] = f"Buy: {ls_match.group(1)}, Sell: {ls_match.group(2)}"

                # Extract symbol - look for simple Symbol: "SYMBOL" pattern
                symbol_match = re.search(r'Symbol:\s*"([A-Z]+)"', analysis_prompt)
                if symbol_match:
                    market_data['symbol'] = symbol_match.group(1)

                # Extract timeframe - look for simple Timeframe: "TIMEFRAME" pattern
                timeframe_match = re.search(r'Timeframe:\s*"([0-9a-zA-Z]+)"', analysis_prompt)
                if timeframe_match:
                    market_data['timeframe'] = timeframe_match.group(1)

                # Calculate mid price if both bid and ask are available
                if 'bid_price' in market_data and 'ask_price' in market_data:
                    market_data['mid_price'] = round((market_data['bid_price'] + market_data['ask_price']) / 2, 8)

                logger.info(f"✅ Found historical market data for {image_path}")
                logger.info(f"   Symbol: {market_data.get('symbol', 'N/A')}")
                logger.info(f"   Mid Price: {market_data.get('mid_price', 'N/A')}")
                logger.info(f"   Funding Rate: {market_data.get('funding_rate', 'N/A')}")

                return market_data
            else:
                logger.warning(f"⚠️ No historical data found for image: {image_path}")
                return {}

        except sqlite3.Error as e:
            logger.error(f"❌ Database error: {e}")
            return {}
        except Exception as e:
            logger.error(f"❌ Error fetching historical market data: {e}")
            return {}

    def _map_timeframe_to_bybit(self, timeframe: str) -> str:
        """Map timeframe to Bybit interval format."""
        interval_map = {
            "1m": "1", "5m": "5", "15m": "15", "30m": "30",
            "1h": "60", "2h": "120", "4h": "240", "6h": "360",
            "12h": "720", "1d": "D", "1w": "W", "1M": "M"
        }
        return interval_map.get(timeframe.lower(), "60")

    def get_available_prompts(self) -> Dict[str, str]:
        """Get list of available prompt functions."""
        return {
            "get_analyzer_prompt_simpleer": "Enhanced prompt with structured analysis framework",
            "get_analyzer_prompt": "Standard analyzer prompt with step-by-step process",
            "get_analyzer_prompt_mistral": "Mistral-optimized prompt",
            "get_analyzer_prompt_grok": "Grok-optimized prompt",
            "get_analyzer_prompt_conservative": "Conservative approach with lower confidence thresholds",
            "get_analyzer_prompt_conservative_more_risk": "Conservative with enhanced risk management",
            "get_analyzer_prompt_optimized_v26_grok": "Optimized v2.6 with simplified confidence calculation",
            "get_analyzer_prompt_optimized_v26_grok_fineTune": "Fine-tuned v2.6 with detailed scoring",
            "get_analyzer_prompt_improved_v28": "Improved v2.8 with market regime awareness",
            "get_analyzer_prompt_improved_v28_short_fix": "v2.8 with enhanced short trade detection",
            "code_nova_improoved_based_on_analyzis": "Code Nova improved based on analysis",
            "orginal_propmpt": "Original prompt",
            "orginal_propmpt_hybrid": "Original hybrid prompt"
        }

    def get_available_models(self) -> Dict[str, str]:
        """Get list of available models."""
        return {
            "gpt-4o": "OpenAI GPT-4o (recommended for vision tasks)",
            "gpt-4o-mini": "OpenAI GPT-4o Mini (faster, cheaper)",
            "gpt-4-turbo": "OpenAI GPT-4 Turbo",
            "gpt-4": "OpenAI GPT-4",
            "gpt-3.5-turbo": "OpenAI GPT-3.5 Turbo (legacy)"
        }

    def get_prompt_function(self, prompt_name: str):
        """Get prompt function by name."""
        prompts = self.get_available_prompts()
        if prompt_name not in prompts:
            raise ValueError(f"Unknown prompt: {prompt_name}. Available: {list(prompts.keys())}")

        prompt_functions = {
            "get_analyzer_prompt_simpleer": get_analyzer_prompt_simpleer,
            "get_analyzer_prompt": get_analyzer_prompt,
            "get_analyzer_prompt_mistral": get_analyzer_prompt_mistral,
            "get_analyzer_prompt_grok": get_analyzer_prompt_grok,
            "get_analyzer_prompt_conservative": get_analyzer_prompt_conservative,
            "get_analyzer_prompt_conservative_more_risk": get_analyzer_prompt_conservative_more_risk,
            "get_analyzer_prompt_optimized_v26_grok": get_analyzer_prompt_optimized_v26_grok,
            "get_analyzer_prompt_optimized_v26_grok_fineTune": get_analyzer_prompt_optimized_v26_grok_fineTune,
            "get_analyzer_prompt_improved_v28": get_analyzer_prompt_improved_v28,
            "get_analyzer_prompt_improved_v28_short_fix": get_analyzer_prompt_improved_v28_short_fix,
            "code_nova_improoved_based_on_analyzis": code_nova_improoved_based_on_analyzis,
            "orginal_propmpt": orginal_propmpt,
            "orginal_propmpt_hybrid": orginal_propmpt_hybrid
        }

        return prompt_functions[prompt_name]

    def analyze_with_prompt(self, symbol: str, timeframe: str, image_path: str,
                           prompt_name: str, db_path) -> Dict[str, Any]:
        """
        Analyze chart using specified prompt with real Bybit data using ChartAnalyzer.

        Args:
            symbol: Trading symbol (e.g., "BTCUSDT")
            timeframe: Timeframe (e.g., "1h", "4h", "1d")
            image_path: Path to chart image
            prompt_name: Name of prompt function to use

        Returns:
            Analysis result with real market data
        """
        logger.info(f"🔍 Starting analysis for {symbol} on {timeframe} timeframe")
        logger.info(f"📋 Using prompt: {prompt_name}")
        logger.info(f"🖼️ Chart image: {image_path}")

        # Validate inputs
        if not Path(image_path).exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")

        # Get the specified prompt function
        prompt_function = self.get_prompt_function(prompt_name)

        # Try to get historical market data first, fall back to current data if not available
        market_data = self.get_historical_market_data(image_path, db_path)

        # If no historical data found, use current market data
        if not market_data:
            logger.info("📊 No historical data found, using current market data")
            market_data = self.get_real_market_data(symbol, timeframe)
            if not market_data:
                logger.warning("⚠️ No market data available, proceeding with empty market data")
                market_data = {}

        # Ensure symbol and timeframe are set (use filename extraction if not in historical data)
        if 'symbol' not in market_data:
            market_data['symbol'] = symbol
        if 'timeframe' not in market_data:
            market_data['timeframe'] = timeframe

        # Generate the custom prompt data using the specified prompt function
        custom_prompt_data = prompt_function(market_data)

        logger.info(f"📝 Using custom prompt: {custom_prompt_data['version']['name']} v{custom_prompt_data['version']['version']}")
        logger.info("🚀 Running analysis using ChartAnalyzer with custom prompt...")

        if not self.analyzer:
            raise ValueError("ChartAnalyzer not available - check OpenAI API key configuration")

        try:
            # Use ChartAnalyzer to analyze the chart with the custom prompt data
            logger.info("🔄 Calling ChartAnalyzer...")
            analysis_result = self.analyzer.analyze_chart(
                image_path=image_path,
                use_assistant=True,  # Use assistant API
                custom_prompt_data=custom_prompt_data  # Pass the custom prompt data
            )

            logger.info(f"📊 Analyzer returned: {type(analysis_result)} - {str(analysis_result)[:200]}...")

            # Get real market data for visualization
            market_data = self.get_real_market_data(symbol, timeframe)
            if market_data:
                # Ensure analysis_result is a dictionary before adding market data
                if isinstance(analysis_result, dict):
                    analysis_result["real_market_data"] = market_data
                    logger.info("📊 Real market data added to results")
                else:
                    logger.warning(f"⚠️ Analysis result is not a dictionary: {type(analysis_result)}")
                    # Convert non-dict result to dict format
                    analysis_result = {
                        "error": f"Analysis returned {type(analysis_result).__name__} instead of dict",
                        "raw_result": str(analysis_result)[:1000],  # Limit size
                        "prompt_used": prompt_name,
                        "image_path": image_path,
                        "real_market_data": market_data
                    }

            # Final check before returning
            if not isinstance(analysis_result, dict):
                logger.error(f"❌ Final result is not a dict: {type(analysis_result)}")
                analysis_result = {
                    "error": "Analysis failed - returned non-dictionary result",
                    "result_type": str(type(analysis_result)),
                    "raw_result": str(analysis_result)[:500]
                }

            return analysis_result

        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            return {
                "error": f"Analysis failed: {str(e)}",
                "prompt_used": prompt_name,
                "image_path": image_path
            }

    def visualize_results(self, result: Dict[str, Any]) -> None:
        """Visualize analysis results in a formatted way."""
        print("\n" + "="*80)
        print("📊 PROMPT TESTER RESULTS")
        print("="*80)

        # Debug: Check result type and content
        print(f"🔍 DEBUG: result type = {type(result)}")
        if isinstance(result, dict):
            print(f"🔍 DEBUG: result keys = {list(result.keys())}")
        else:
            print(f"🔍 DEBUG: result content = {str(result)[:200]}...")
            return

        if "error" in result:
            print(f"❌ ERROR: {result['error']}")
            if "raw_response" in result:
                print(f"📄 Raw Response: {result['raw_response'][:500]}...")
            return

        # Header information
        print(f"📈 Symbol: {result.get('real_market_data', {}).get('symbol', 'N/A')}")
        print(f"⏰ Timeframe: {result.get('real_market_data', {}).get('timeframe', 'N/A')}")
        print(f"🤖 Model: {result.get('model_used', 'N/A')}")
        print(f"📋 Prompt: {result.get('prompt_used', 'N/A')}")
        print(f"📝 Prompt Version: {result.get('prompt_version', {}).get('name', 'N/A')}")

        # Market data used
        market_data = result.get('real_market_data', {})
        print("\n📊 Market Data Used:")
        print(f"   Mid Price: {smart_format_price(market_data.get('mid_price'))}")
        print(f"   Bid/Ask: {smart_format_price(market_data.get('bid_price'))} / {smart_format_price(market_data.get('ask_price'))}")
        print(f"   Funding Rate: {market_data.get('funding_rate', 'N/A')}")
        print(f"   Long/Short Ratio: {market_data.get('long_short_ratio', 'N/A')}")

        # Analysis results
        print("\n🎯 ANALYSIS RESULTS:")
        print(f"   Recommendation: {result.get('recommendation', 'N/A')}")
        print(f"   Confidence: {result.get('confidence', 0):.1f}")
        print(f"   Direction: {result.get('direction', 'N/A')}")

        if result.get('entry_price'):
            print(f"   Entry Price: {smart_format_price(result['entry_price'])}")
        if result.get('stop_loss'):
            print(f"   Stop Loss: {smart_format_price(result['stop_loss'])}")
        if result.get('take_profit'):
            print(f"   Take Profit: {smart_format_price(result['take_profit'])}")
        if result.get('risk_reward_ratio'):
            print(f"   Risk-Reward Ratio: {result['risk_reward_ratio']:.2f}")

        # Summary and evidence
        if result.get('summary'):
            print(f"\n📝 Summary: {result['summary']}")

        if result.get('evidence'):
            print(f"\n🔍 Evidence: {result['evidence']}")

        # Explanations
        if result.get('entry_explanation'):
            print(f"\n🎯 Entry Explanation: {result['entry_explanation']}")
        if result.get('stop_loss_explanation'):
            print(f"🛑 Stop Loss Explanation: {result['stop_loss_explanation']}")
        if result.get('take_profit_explanation'):
            print(f"💰 Take Profit Explanation: {result['take_profit_explanation']}")

        # Key levels
        key_levels = result.get('key_levels', {})
        if key_levels:
            print("\n🎚️ Key Levels:")
            print(f"   Support: {smart_format_price(key_levels.get('support'))}")
            print(f"   Resistance: {smart_format_price(key_levels.get('resistance'))}")

        # Risk factors
        risk_factors = result.get('risk_factors', [])
        if risk_factors:
            print("\n⚠️ Risk Factors:")
            for i, risk in enumerate(risk_factors, 1):
                print(f"   {i}. {risk}")

        # Market condition
        print(f"\n🌍 Market Condition: {result.get('market_condition', 'N/A')}")
        print(f"📊 Market Direction: {result.get('market_direction', 'N/A')}")

        # Token usage
        if result.get('tokens_used'):
            print(f"\n🔢 Tokens Used: {result['tokens_used']}")

        print("\n" + "="*80)

    def create_trading_visualization(self, result: Dict[str, Any], output_path: Optional[str] = None) -> Optional[str]:
        """Create a candlestick chart with trading levels (entry, TP, SL) from analysis results."""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            from datetime import datetime, timedelta
            import numpy as np

            # Extract trading data from results
            logger.info(f"📊 Visualization: Extracting data from result type: {type(result)}")
            entry_price = result.get('entry_price')
            stop_loss = result.get('stop_loss')
            take_profit = result.get('take_profit')
            recommendation = result.get('recommendation', '').upper()

            logger.info(f"📊 Visualization: entry_price = {entry_price}, stop_loss = {stop_loss}, take_profit = {take_profit}")

            if not entry_price:
                logger.warning(f"⚠️ No entry price found in results. Available keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                logger.warning(f"📊 Result type: {type(result)}")
                return None

            # Get market data for context
            market_data = result.get('real_market_data', {})
            current_price = market_data.get('mid_price') or market_data.get('last_close_price')

            logger.info(f"📊 Visualization: current_price = {current_price}")

            if not current_price:
                logger.warning("⚠️ No current price available for visualization")
                return None

            logger.info(f"📊 Visualization: Creating chart with entry={entry_price}, current={current_price}")

            # Extract timestamp from image path for annotation
            image_path = result.get('image_path', '')
            image_timestamp = None
            if image_path:
                # Extract timestamp from filename like "TRXUSDT_1h_20250925_220000.png"
                import re
                timestamp_match = re.search(r'_(\d{8})_(\d{6})\.png$', image_path)
                if timestamp_match:
                    date_str = timestamp_match.group(1)  # "20250925"
                    time_str = timestamp_match.group(2)  # "220000"
                    try:
                        image_timestamp = datetime.strptime(f"{date_str}{time_str}", "%Y%m%d%H%M%S")
                    except ValueError:
                        pass

            # Create sample candlestick data (since we don't have historical data in the tester)
            # For demonstration, create 20 candles around current price
            base_time = datetime.now()
            dates = [base_time - timedelta(hours=i) for i in range(20, 0, -1)]

            # Generate realistic candlestick data around current price
            prices = []
            current = float(current_price)

            for i in range(20):
                # Add some random variation but keep trend realistic
                variation = np.random.normal(0, current * 0.02)  # 2% standard deviation
                open_price = max(0.1, current + variation)

                # High and low within 1% of open
                high_var = abs(np.random.normal(0, open_price * 0.01))
                low_var = abs(np.random.normal(0, open_price * 0.01))

                high_price = open_price + high_var
                low_price = max(0.1, open_price - low_var)

                # Close price (next candle's open)
                close_price = max(0.1, open_price + np.random.normal(0, open_price * 0.005))

                prices.append({
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price
                })

                current = close_price

            # Convert dates to matplotlib format
            date_nums = mdates.date2num(dates)

            # Create the plot
            fig, ax = plt.subplots(figsize=(12, 8))

            # Plot candlesticks
            for i, (date_num, price) in enumerate(zip(date_nums, prices)):
                color = 'green' if price['close'] >= price['open'] else 'red'
                ax.plot([date_num, date_num], [price['low'], price['high']],
                        color='black', linewidth=1)
                ax.plot([date_num, date_num], [price['open'], price['close']],
                        color=color, linewidth=4)

            # Add image timestamp annotation if available
            if image_timestamp:
                # Convert image timestamp to matplotlib date number
                image_date_num = mdates.date2num(image_timestamp)

                # Find the closest candle to the image timestamp
                closest_candle_idx = 0
                min_diff = float('inf')
                for i, date_num in enumerate(date_nums):
                    diff = abs(date_num - image_date_num)
                    if diff < min_diff:
                        min_diff = diff
                        closest_candle_idx = i

                # Add vertical line at the image timestamp
                if 0 <= closest_candle_idx < len(date_nums):
                    image_candle_date = date_nums[closest_candle_idx]
                    ax.axvline(x=image_candle_date, color='purple', linestyle='--', alpha=0.8, linewidth=2,
                              label=f'Chart Time: {image_timestamp.strftime("%m/%d %H:%M")}')

                    # Add annotation text
                    closest_price = prices[closest_candle_idx]
                    annotation_y = closest_price['high'] * 1.002  # Slightly above the high
                    ax.annotate('📷 CHART',
                              xy=(image_candle_date, annotation_y),
                              xytext=(image_candle_date, annotation_y * 1.001),
                              fontsize=10, ha='center', va='bottom',
                              bbox=dict(boxstyle='round,pad=0.3', facecolor='purple', alpha=0.7),
                              color='white')

            # Format x-axis
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
            plt.xticks(rotation=45)

            # Add horizontal lines for trading levels
            if entry_price:
                ax.axhline(y=entry_price, color='blue', linestyle='--', alpha=0.8, linewidth=2, label=f'Entry: {entry_price:.2f}')
            if stop_loss:
                ax.axhline(y=stop_loss, color='red', linestyle='--', alpha=0.8, linewidth=2, label=f'SL: {stop_loss:.2f}')
            if take_profit:
                ax.axhline(y=take_profit, color='green', linestyle='--', alpha=0.8, linewidth=2, label=f'TP: {take_profit:.2f}')

            # Add current price line
            ax.axhline(y=current_price, color='orange', linestyle='-', alpha=0.6, linewidth=1, label=f'Current: {current_price:.2f}')

            # Styling
            ax.set_title(f'Trading Analysis: {recommendation} {result.get("symbol", "N/A")}\nPrompt: {result.get("prompt_version", {}).get("name", "N/A")}', fontsize=14, pad=20)
            ax.set_xlabel('Time')
            ax.set_ylabel('Price')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # Set reasonable y-axis limits
            all_prices = [p for price_dict in prices for p in price_dict.values()]
            if entry_price:
                all_prices.append(entry_price)
            if stop_loss:
                all_prices.append(stop_loss)
            if take_profit:
                all_prices.append(take_profit)
            all_prices.append(current_price)

            y_min = min(all_prices) * 0.995
            y_max = max(all_prices) * 1.005

            ax.set_ylim(y_min, y_max)

            plt.tight_layout()

            # Save or show the plot
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                logger.info(f"💾 Trading visualization saved to: {output_path}")
                return output_path
            else:
                # Show interactively
                plt.show()
                return None

        except ImportError as e:
            logger.warning(f"⚠️ Cannot create visualization - missing required packages: {e}")
            logger.info("💡 Install matplotlib and numpy for visualization: pip install matplotlib numpy")
            return None
        except Exception as e:
            logger.error(f"❌ Error creating trading visualization: {e}")
            return None


def main(symbol: str, timeframe: str, image_path: str, prompt_name: str, db_path: Optional[str] = None) -> None:
    """
    Main function for prompt testing.

    Args:
        symbol: Trading symbol (e.g., "BTCUSDT")
        timeframe: Timeframe (e.g., "1h")
        image_path: Path to chart image
        prompt_name: Name of prompt function to use
    """
    # Create tester instance
    tester = PromptTester()

    # Check if analyzer was initialized successfully
    if not tester.analyzer:
        logger.error("❌ ChartAnalyzer not available - check OpenAI API key configuration")
        print("❌ ChartAnalyzer not available - check OpenAI API key configuration")
        return

    try:
        # Run analysis
        result = tester.analyze_with_prompt(symbol, timeframe, image_path, prompt_name, db_path)

        # Visualize results
        logger.info(f"📋 Calling visualize_results with result type: {type(result)}")
        tester.visualize_results(result)

        # Create trading visualization chart
        chart_output = f"prompt_test_{symbol}_{timeframe}_{prompt_name}_chart.png"
        logger.info(f"📊 Creating trading visualization: {chart_output}")
        logger.info(f"📁 Chart will be saved to: {os.path.abspath(chart_output)}")
        logger.info(f"🔍 Result type before visualization: {type(result)}")
        logger.info(f"🔍 Result keys before visualization: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        visualization_result = tester.create_trading_visualization(result, chart_output)
        logger.info(f"📊 Visualization result: {visualization_result}")

        # Save results to file
        output_file = f"prompt_test_{symbol}_{timeframe}_{prompt_name}.json"
        with open(output_file, 'w') as f:
            json.dump(result, f, indent=2, default=str)

        logger.info(f"💾 Results saved to: {output_file}")
        if chart_output:
            logger.info(f"📊 Trading chart saved to: {chart_output}")

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    # Direct values - no argument parser
    image_path = "trading_bot/data/charts/.backup/TRXUSDT_1h_20250925_220000.png"

    # Extract symbol and timeframe from filename using utility functions
    from trading_bot.core.utils import extract_symbol_from_filename, extract_timeframe_from_filename
    symbol = extract_symbol_from_filename(image_path) or ""
    timeframe = extract_timeframe_from_filename(image_path) or ""

    prompt_name = "get_analyzer_prompt_improved_v28_short_fix"
    db_path ="^^Server Backup/analysis_results.db"

    print(f"🔧 Configuration:")
    print(f"   Path: {image_path}")
    print(f"   Symbol: {symbol}")
    print(f"   Timeframe: {timeframe}")
    print(f"   Prompt: {prompt_name}")

    main(symbol, timeframe, image_path, prompt_name, db_path)

