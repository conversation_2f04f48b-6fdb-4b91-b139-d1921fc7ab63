# Rules
- Always be aware of paylance issues and fix them
- Resuse as much code as possible
- This is a private project dont concider scalabiliy 
- Keep code lightwight robust and maintanable
- if you implente new features see if you can reduce redunatn code afterwards

you can get help and API docs by using:
curl "https://r.jina.ai/https://www.example.com" \
  -H "Authorization: Bearer jina_13fc80d4568d4cb9975d4c4cec86c1a7cxDM709F7z8TyMNuMW8YWEkJ7j0J" \
  -H "X-Target-Selector: body, .class, #id"

replace www.example.com with an url like:Market
https://bybit-exchange.github.io/docs/v5/market/time
https://bybit-exchange.github.io/docs/v5/market/kline
https://bybit-exchange.github.io/docs/v5/market/orderbook
https://bybit-exchange.github.io/docs/v5/market/ticker
https://bybit-exchange.github.io/docs/v5/market/recent-trade
https://bybit-exchange.github.io/docs/v5/market/option-info

Order
https://bybit-exchange.github.io/docs/v5/order/create
https://bybit-exchange.github.io/docs/v5/order/cancel
https://bybit-exchange.github.io/docs/v5/order/realtime
https://bybit-exchange.github.io/docs/v5/order/history

Position
https://bybit-exchange.github.io/docs/v5/position/list
https://bybit-exchange.github.io/docs/v5/position/switch-isolated
https://bybit-exchange.github.io/docs/v5/position/set-leverage

Account
https://bybit-exchange.github.io/docs/v5/account/wallet-balance
https://bybit-exchange.github.io/docs/v5/account/coin-greeks
https://bybit-exchange.github.io/docs/v5/account/transaction-log

Asset
https://bybit-exchange.github.io/docs/v5/asset/transfer/inter-transfer
https://bybit-exchange.github.io/docs/v5/asset/transfer/query-inter-transfer-list

Spot Lever Token
https://bybit-exchange.github.io/docs/v5/spot-lever-token/quote
https://bybit-exchange.github.io/docs/v5/spot-lever-token/purchase
https://bybit-exchange.github.io/docs/v5/spot-lever-token/redeem

Spot Margin Trade
https://bybit-exchange.github.io/docs/v5/spot-margin-trade/open

https://bybit-exchange.github.io/docs/v5/spot-margin-trade/close