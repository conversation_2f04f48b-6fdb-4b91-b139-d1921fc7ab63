#!/usr/bin/env python3
"""
Test runner for UltimateDashboard
Runs comprehensive tests and generates validation report
"""
import sys
import os
import unittest
import json
from datetime import datetime
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging for tests
logging.basicConfig(
    level=logging.WARNING,  # Reduce noise during testing
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def run_comprehensive_tests():
    """Run all tests and generate report"""
    print("=" * 60)
    print("UltimateDashboard - Comprehensive Test Suite")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Discover and run tests
    loader = unittest.TestLoader()
    start_dir = os.path.join(os.path.dirname(__file__), 'tests')
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        buffer=True
    )
    
    print("Running tests...")
    print("-" * 40)
    result = runner.run(suite)
    
    # Generate test report
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    passed = total_tests - failures - errors - skipped
    
    print(f"Total Tests Run: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failures}")
    print(f"Errors: {errors}")
    print(f"Skipped: {skipped}")
    print()
    
    if failures > 0:
        print("FAILURES:")
        print("-" * 20)
        for test, traceback in result.failures:
            print(f"❌ {test}")
            print(f"   {traceback.split('AssertionError:')[-1].strip()}")
        print()
    
    if errors > 0:
        print("ERRORS:")
        print("-" * 20)
        for test, traceback in result.errors:
            print(f"💥 {test}")
            print(f"   {traceback.split('Exception:')[-1].strip()}")
        print()
    
    # Overall result
    success_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("✅ EXCELLENT - Dashboard is ready for deployment!")
    elif success_rate >= 75:
        print("⚠️  GOOD - Minor issues to address")
    else:
        print("❌ NEEDS WORK - Significant issues found")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return result.wasSuccessful()

def validate_installation():
    """Validate that all components are properly installed"""
    print("\n" + "=" * 60)
    print("INSTALLATION VALIDATION")
    print("=" * 60)
    
    validation_results = {}
    
    # Check required modules
    required_modules = [
        'flask', 'flask_cors', 'pandas', 'numpy', 
        'requests', 'pybit', 'plotly', 'python-dotenv'
    ]
    
    print("Checking required modules...")
    for module in required_modules:
        try:
            __import__(module.replace('-', '_'))
            print(f"✅ {module}")
            validation_results[module] = True
        except ImportError:
            print(f"❌ {module} - NOT INSTALLED")
            validation_results[module] = False
    
    # Check project structure
    print("\nChecking project structure...")
    required_dirs = [
        'database', 'analytics', 'api', 'static', 
        'templates', 'realtime', 'tests'
    ]
    
    for dir_name in required_dirs:
        dir_path = os.path.join(os.path.dirname(__file__), dir_name)
        if os.path.exists(dir_path):
            print(f"✅ {dir_name}/")
            validation_results[f"dir_{dir_name}"] = True
        else:
            print(f"❌ {dir_name}/ - MISSING")
            validation_results[f"dir_{dir_name}"] = False
    
    # Check key files
    print("\nChecking key files...")
    required_files = [
        'app.py', 'config.py', 'requirements.txt',
        'database/connector.py', 'analytics/core_engine.py',
        'static/css/dashboard.css', 'static/js/dashboard.js',
        'templates/dashboard.html'
    ]
    
    for file_name in required_files:
        file_path = os.path.join(os.path.dirname(__file__), file_name)
        if os.path.exists(file_path):
            print(f"✅ {file_name}")
            validation_results[f"file_{file_name.replace('/', '_')}"] = True
        else:
            print(f"❌ {file_name} - MISSING")
            validation_results[f"file_{file_name.replace('/', '_')}"] = False
    
    # Overall validation
    total_checks = len(validation_results)
    passed_checks = sum(validation_results.values())
    validation_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0
    
    print(f"\nValidation Rate: {validation_rate:.1f}% ({passed_checks}/{total_checks})")
    
    if validation_rate == 100:
        print("✅ PERFECT - All components installed correctly!")
    elif validation_rate >= 90:
        print("⚠️  MOSTLY COMPLETE - Minor components missing")
    else:
        print("❌ INCOMPLETE - Major components missing")
    
    return validation_rate >= 90

def main():
    """Main test runner"""
    print("UltimateDashboard Test & Validation Suite")
    print("=" * 60)
    
    # Run installation validation
    installation_ok = validate_installation()
    
    if not installation_ok:
        print("\n❌ Installation validation failed. Please fix missing components before running tests.")
        return False
    
    # Run comprehensive tests
    tests_ok = run_comprehensive_tests()
    
    # Final summary
    print("\n" + "=" * 60)
    print("FINAL SUMMARY")
    print("=" * 60)
    
    if installation_ok and tests_ok:
        print("🎉 SUCCESS! UltimateDashboard is ready to use!")
        print("\nTo start the dashboard:")
        print("  python app.py")
        print("\nThen open: http://127.0.0.1:8080")
        return True
    else:
        print("❌ Issues found. Please review the results above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
