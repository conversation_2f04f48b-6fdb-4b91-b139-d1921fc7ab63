"""
Test suite for UltimateDashboard
Tests core functionality, API endpoints, and data validation
"""
import unittest
import json
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# Import the Flask app
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from database.connector import DatabaseConnector
from analytics.core_engine import CoreAnalyticsEngine
from analytics.prompt_analyzer import PromptAnalyzer
from analytics.market_classifier import MarketConditionClassifier
from analytics.filters import AdvancedFilters

class TestUltimateDashboard(unittest.TestCase):
    """Test cases for UltimateDashboard"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = app.test_client()
        self.app.testing = True
        
        # Mock database data
        self.mock_trades = [
            {
                'id': 1,
                'symbol': 'BTCUSDT',
                'side': 'Buy',
                'pnl': 15.50,
                'status': 'closed',
                'confidence': 0.8,
                'created_at': '2024-01-01T10:00:00Z'
            },
            {
                'id': 2,
                'symbol': 'ETHUSDT',
                'side': 'Sell',
                'pnl': -8.25,
                'status': 'closed',
                'confidence': 0.6,
                'created_at': '2024-01-01T11:00:00Z'
            }
        ]
        
        self.mock_analysis = [
            {
                'id': 1,
                'symbol': 'BTCUSDT',
                'recommendation': 'BUY',
                'confidence': 0.8,
                'market_condition': 'TRENDING',
                'analysis_prompt': 'Test prompt for BTC analysis',
                'timestamp': '2024-01-01T09:30:00Z'
            }
        ]
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = self.app.get('/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data.get('status') == 'healthy')
        self.assertIn('timestamp', data)
        self.assertIn('components', data)
    
    @patch('database.connector.DatabaseConnector.get_trades')
    @patch('analytics.core_engine.CoreAnalyticsEngine.calculate_comprehensive_metrics')
    def test_overview_endpoint(self, mock_metrics, mock_trades):
        """Test overview API endpoint"""
        # Mock data
        mock_trades.return_value = self.mock_trades
        mock_metrics.return_value = {
            'total_pnl': 7.25,
            'win_rate': 50.0,
            'total_trades': 2,
            'profit_factor': 1.88
        }
        
        response = self.app.get('/api/overview?days=30')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data.get('success'))
        self.assertIn('metrics', data['data'])
        self.assertEqual(data['data']['period_days'], 30)
    
    @patch('database.connector.DatabaseConnector.get_trades')
    def test_trades_endpoint(self, mock_trades):
        """Test trades API endpoint"""
        mock_trades.return_value = self.mock_trades
        
        response = self.app.get('/api/trades?limit=10&symbol=BTCUSDT')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data.get('success'))
        self.assertIn('trades', data['data'])
        self.assertEqual(data['data']['count'], len(self.mock_trades))
    
    @patch('database.connector.DatabaseConnector.get_unique_symbols')
    def test_symbols_endpoint(self, mock_symbols):
        """Test symbols API endpoint"""
        mock_symbols.return_value = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        
        response = self.app.get('/api/symbols')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data.get('success'))
        self.assertIn('symbols', data['data'])
        self.assertEqual(len(data['data']['symbols']), 3)
    
    def test_filter_options_endpoint(self):
        """Test filter options endpoint"""
        with patch('analytics.filters.AdvancedFilters.get_filter_options') as mock_options:
            mock_options.return_value = {
                'symbols': ['BTCUSDT', 'ETHUSDT'],
                'timeframes': ['1h', '4h', '1d'],
                'market_conditions': ['TRENDING', 'RANGING']
            }
            
            response = self.app.get('/api/filter-options')
            self.assertEqual(response.status_code, 200)
            
            data = json.loads(response.data)
            self.assertTrue(data.get('success'))
            self.assertIn('symbols', data['data'])
            self.assertIn('timeframes', data['data'])
    
    def test_filtered_data_endpoint(self):
        """Test filtered data endpoint"""
        with patch('analytics.filters.AdvancedFilters.apply_advanced_filters') as mock_filter:
            mock_filter.return_value = self.mock_trades
            
            filter_request = {
                'symbols': ['BTCUSDT'],
                'min_confidence': 0.7,
                'data_type': 'trades'
            }
            
            response = self.app.post('/api/filtered-data',
                                   data=json.dumps(filter_request),
                                   content_type='application/json')
            self.assertEqual(response.status_code, 200)
            
            data = json.loads(response.data)
            self.assertTrue(data.get('success'))
            self.assertIn('filtered_data', data['data'])
    
    def test_real_time_status_endpoint(self):
        """Test real-time status endpoint"""
        with patch('realtime.data_monitor.get_monitor') as mock_monitor:
            mock_instance = Mock()
            mock_instance.get_real_time_summary.return_value = {
                'monitoring_status': 'active',
                'exchange_status': 'connected',
                'recent_metrics': {'trades_last_hour': 5}
            }
            mock_monitor.return_value = mock_instance
            
            response = self.app.get('/api/real-time/status')
            self.assertEqual(response.status_code, 200)
            
            data = json.loads(response.data)
            self.assertTrue(data.get('success'))
            self.assertIn('monitoring_status', data['data'])

class TestDatabaseConnector(unittest.TestCase):
    """Test database connector functionality"""
    
    def setUp(self):
        """Set up test database"""
        self.db = DatabaseConnector()
    
    @patch('sqlite3.connect')
    def test_database_connection(self, mock_connect):
        """Test database connection"""
        mock_conn = Mock()
        mock_connect.return_value = mock_conn
        
        # Test connection
        result = self.db._execute_query("SELECT 1", fetch_all=True)
        mock_connect.assert_called()
    
    def test_get_trades_parameters(self):
        """Test get_trades method parameters"""
        with patch.object(self.db, '_execute_query') as mock_query:
            mock_query.return_value = []
            
            # Test with various parameters
            self.db.get_trades(limit=10, symbol='BTCUSDT', status='closed')
            mock_query.assert_called()
            
            # Verify SQL query construction
            args, kwargs = mock_query.call_args
            sql_query = args[0]
            self.assertIn('WHERE', sql_query)
            self.assertIn('LIMIT', sql_query)

class TestAnalyticsEngine(unittest.TestCase):
    """Test analytics engine functionality"""
    
    def setUp(self):
        """Set up analytics engine"""
        self.engine = CoreAnalyticsEngine()
    
    def test_calculate_basic_metrics(self):
        """Test basic metrics calculation"""
        mock_trades = [
            {'pnl': 10.0, 'status': 'closed'},
            {'pnl': -5.0, 'status': 'closed'},
            {'pnl': 15.0, 'status': 'closed'},
            {'pnl': -8.0, 'status': 'closed'}
        ]
        
        with patch.object(self.engine.db, 'get_trades') as mock_get_trades:
            mock_get_trades.return_value = mock_trades
            
            metrics = self.engine.calculate_comprehensive_metrics(30)
            
            self.assertIn('total_pnl', metrics)
            self.assertIn('win_rate', metrics)
            self.assertIn('total_trades', metrics)
            self.assertEqual(metrics['total_pnl'], 12.0)
            self.assertEqual(metrics['win_rate'], 50.0)
    
    def test_risk_metrics_calculation(self):
        """Test risk metrics calculation"""
        pnl_values = [10, -5, 15, -8, 20, -12, 5]
        
        # Test max drawdown calculation
        max_dd = self.engine._calculate_max_drawdown(pnl_values)
        self.assertIsInstance(max_dd, (int, float))
        self.assertLessEqual(max_dd, 0)  # Max drawdown should be negative or zero

class TestPromptAnalyzer(unittest.TestCase):
    """Test prompt analyzer functionality"""
    
    def setUp(self):
        """Set up prompt analyzer"""
        self.analyzer = PromptAnalyzer()
    
    def test_extract_prompt_features(self):
        """Test prompt feature extraction"""
        test_prompt = """
        Based on technical analysis, BTC shows strong bullish momentum.
        Risk management: Set stop loss at $45,000.
        Pattern: Ascending triangle breakout confirmed.
        Market context: High volume, positive sentiment.
        """
        
        features = self.analyzer._extract_prompt_features(test_prompt)
        
        self.assertIn('risk_management', features)
        self.assertIn('pattern_analysis', features)
        self.assertIn('market_context', features)
        self.assertTrue(features['risk_management'])  # Should detect stop loss mention
        self.assertTrue(features['pattern_analysis'])  # Should detect pattern mention

class TestAdvancedFilters(unittest.TestCase):
    """Test advanced filtering functionality"""
    
    def setUp(self):
        """Set up advanced filters"""
        self.filters = AdvancedFilters()
    
    def test_apply_symbol_filter(self):
        """Test symbol filtering"""
        test_data = [
            {'symbol': 'BTCUSDT', 'pnl': 10},
            {'symbol': 'ETHUSDT', 'pnl': -5},
            {'symbol': 'BTCUSDT', 'pnl': 15}
        ]
        
        filters = {'symbols': ['BTCUSDT']}
        filtered = self.filters._apply_filters(test_data, filters)
        
        self.assertEqual(len(filtered), 2)
        for item in filtered:
            self.assertEqual(item['symbol'], 'BTCUSDT')
    
    def test_apply_pnl_range_filter(self):
        """Test P&L range filtering"""
        test_data = [
            {'pnl': 10}, {'pnl': -5}, {'pnl': 15}, {'pnl': -2}
        ]
        
        filters = {'min_pnl': 0}  # Only profitable trades
        filtered = self.filters._apply_filters(test_data, filters)
        
        self.assertEqual(len(filtered), 2)
        for item in filtered:
            self.assertGreaterEqual(item['pnl'], 0)
    
    def test_optimization_insights_generation(self):
        """Test optimization insights generation"""
        with patch.object(self.filters, 'apply_advanced_filters') as mock_filter:
            mock_filter.return_value = [
                {'pnl': 10, 'confidence': 0.8, 'symbol': 'BTCUSDT'},
                {'pnl': -5, 'confidence': 0.4, 'symbol': 'ETHUSDT'}
            ]
            
            insights = self.filters.generate_optimization_insights()
            
            self.assertIn('data_summary', insights)
            self.assertIn('performance_patterns', insights)
            self.assertIn('optimization_opportunities', insights)

class TestDataValidation(unittest.TestCase):
    """Test data validation and integrity"""
    
    def test_trade_data_validation(self):
        """Test trade data validation"""
        valid_trade = {
            'id': 1,
            'symbol': 'BTCUSDT',
            'pnl': 10.50,
            'status': 'closed',
            'created_at': '2024-01-01T10:00:00Z'
        }
        
        # Test required fields
        required_fields = ['id', 'symbol', 'status']
        for field in required_fields:
            self.assertIn(field, valid_trade)
        
        # Test data types
        self.assertIsInstance(valid_trade['id'], int)
        self.assertIsInstance(valid_trade['symbol'], str)
        self.assertIsInstance(valid_trade['pnl'], (int, float))
    
    def test_analysis_data_validation(self):
        """Test analysis data validation"""
        valid_analysis = {
            'id': 1,
            'symbol': 'BTCUSDT',
            'recommendation': 'BUY',
            'confidence': 0.8,
            'timestamp': '2024-01-01T10:00:00Z'
        }
        
        # Test confidence range
        self.assertGreaterEqual(valid_analysis['confidence'], 0)
        self.assertLessEqual(valid_analysis['confidence'], 1)
        
        # Test recommendation values
        valid_recommendations = ['BUY', 'SELL', 'HOLD']
        self.assertIn(valid_analysis['recommendation'], valid_recommendations)

if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
