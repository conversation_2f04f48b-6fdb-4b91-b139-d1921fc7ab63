<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UltimateDashboard - Trading Analytics</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <h1>UltimateDashboard</h1>
                <p>Trading Analytics & Performance Optimization</p>
                <div class="header-controls">
                    <select id="timeRange" class="control-select">
                        <option value="7">Last 7 Days</option>
                        <option value="30" selected>Last 30 Days</option>
                        <option value="60">Last 60 Days</option>
                        <option value="90">Last 90 Days</option>
                    </select>
                    <button id="refreshData" class="btn-primary">Refresh Data</button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="dashboard-nav">
            <ul class="nav-tabs">
                <li><a href="#overview" class="nav-tab active" data-tab="overview">Overview</a></li>
                <li><a href="#prompt-analysis" class="nav-tab" data-tab="prompt-analysis">Prompt Analysis</a></li>
                <li><a href="#market-conditions" class="nav-tab" data-tab="market-conditions">Market Conditions</a></li>
                <li><a href="#symbol-analysis" class="nav-tab" data-tab="symbol-analysis">Symbol Analysis</a></li>
                <li><a href="#chart-analysis" class="nav-tab" data-tab="chart-analysis">Chart Analysis</a></li>
                <li><a href="#optimization" class="nav-tab" data-tab="optimization">Optimization</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <h3>Total P&L</h3>
                        <div class="metric-value" id="totalPnl">Loading...</div>
                        <div class="metric-change" id="pnlChange"></div>
                    </div>
                    <div class="metric-card">
                        <h3>Win Rate</h3>
                        <div class="metric-value" id="winRate">Loading...</div>
                        <div class="metric-change" id="winRateChange"></div>
                    </div>
                    <div class="metric-card">
                        <h3>Profit Factor</h3>
                        <div class="metric-value" id="profitFactor">Loading...</div>
                        <div class="metric-change" id="profitFactorChange"></div>
                    </div>
                    <div class="metric-card">
                        <h3>Total Trades</h3>
                        <div class="metric-value" id="totalTrades">Loading...</div>
                        <div class="metric-change" id="tradesChange"></div>
                    </div>
                    <div class="metric-card">
                        <h3>Expected Value</h3>
                        <div class="metric-value" id="expectedValue">Loading...</div>
                        <div class="metric-change" id="evChange"></div>
                    </div>
                    <div class="metric-card">
                        <h3>Max Drawdown</h3>
                        <div class="metric-value" id="maxDrawdown">Loading...</div>
                        <div class="metric-change" id="drawdownChange"></div>
                    </div>
                </div>

                <div class="charts-grid">
                    <div class="chart-container">
                        <h3>P&L Over Time</h3>
                        <div id="pnlChart" class="chart"></div>
                    </div>
                    <div class="chart-container">
                        <h3>Symbol Performance</h3>
                        <div id="symbolChart" class="chart"></div>
                    </div>
                </div>

                <div class="recommendations-section">
                    <h3>Optimization Recommendations</h3>
                    <div id="recommendations" class="recommendations-list">
                        Loading recommendations...
                    </div>
                </div>
            </div>

            <!-- Prompt Analysis Tab -->
            <div id="prompt-analysis" class="tab-content">
                <div class="analysis-header">
                    <h2>Prompt Performance Analysis</h2>
                    <p>Analyze prompt effectiveness and identify optimization opportunities</p>
                </div>

                <div class="charts-grid">
                    <div class="chart-container">
                        <h3>Prompt Performance Comparison</h3>
                        <div id="promptPerformanceChart" class="chart"></div>
                    </div>
                    <div class="chart-container">
                        <h3>Prompt Characteristics Impact</h3>
                        <div id="promptCharacteristicsChart" class="chart"></div>
                    </div>
                </div>

                <div class="prompt-details">
                    <h3>Detailed Prompt Analysis</h3>
                    <div id="promptDetailsTable" class="data-table">
                        Loading prompt analysis...
                    </div>
                </div>

                <div class="prompt-recommendations">
                    <h3>Prompt Optimization Recommendations</h3>
                    <div id="promptRecommendations" class="recommendations-list">
                        Loading prompt recommendations...
                    </div>
                </div>
            </div>

            <!-- Market Conditions Tab -->
            <div id="market-conditions" class="tab-content">
                <div class="analysis-header">
                    <h2>Market Condition Analysis</h2>
                    <p>Performance breakdown by market regimes and conditions</p>
                </div>

                <div class="charts-grid">
                    <div class="chart-container">
                        <h3>Performance by Market Regime</h3>
                        <div id="marketRegimeChart" class="chart"></div>
                    </div>
                    <div class="chart-container">
                        <h3>Market Condition Efficiency</h3>
                        <div id="marketEfficiencyChart" class="chart"></div>
                    </div>
                </div>

                <div class="market-details">
                    <h3>Market Condition Breakdown</h3>
                    <div id="marketDetailsTable" class="data-table">
                        Loading market analysis...
                    </div>
                </div>

                <div class="market-insights">
                    <h3>Market Insights</h3>
                    <div id="marketInsights" class="insights-list">
                        Loading market insights...
                    </div>
                </div>
            </div>

            <!-- Symbol Analysis Tab -->
            <div id="symbol-analysis" class="tab-content">
                <div class="analysis-header">
                    <h2>Symbol Performance Analysis</h2>
                    <p>Detailed analysis of trading performance by symbol</p>
                </div>

                <div class="symbol-controls">
                    <select id="symbolFilter" class="control-select">
                        <option value="">All Symbols</option>
                    </select>
                    <button id="analyzeSymbol" class="btn-secondary">Analyze Selected Symbol</button>
                </div>

                <div class="charts-grid">
                    <div class="chart-container">
                        <h3>Symbol P&L Comparison</h3>
                        <div id="symbolPnlChart" class="chart"></div>
                    </div>
                    <div class="chart-container">
                        <h3>Symbol Win Rate Analysis</h3>
                        <div id="symbolWinRateChart" class="chart"></div>
                    </div>
                </div>

                <div class="symbol-details">
                    <h3>Symbol Performance Details</h3>
                    <div id="symbolDetailsTable" class="data-table">
                        Loading symbol analysis...
                    </div>
                </div>
            </div>

            <!-- Chart Analysis Tab -->
            <div id="chart-analysis" class="tab-content">
                <div class="analysis-header">
                    <h2>Chart Analysis with Trade Markers</h2>
                    <p>Interactive candlestick charts showing trade entry and exit points</p>
                </div>

                <div class="chart-controls">
                    <div class="control-group">
                        <label for="symbolSelector">Symbol:</label>
                        <select id="symbolSelector" class="form-control">
                            <option value="">Select a symbol...</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="timeframeSelector">Timeframe:</label>
                        <select id="timeframeSelector" class="form-control">
                            <option value="1h">1 Hour</option>
                            <option value="4h">4 Hours</option>
                            <option value="1d">1 Day</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="chartDaysSelector">Days:</label>
                        <select id="chartDaysSelector" class="form-control">
                            <option value="7">7 Days</option>
                            <option value="14">14 Days</option>
                            <option value="30" selected>30 Days</option>
                            <option value="60">60 Days</option>
                        </select>
                    </div>
                    <button id="loadChartBtn" class="btn btn-primary">Load Chart</button>
                </div>

                <div class="chart-container">
                    <div class="chart-main">
                        <div id="candlestickChart" class="chart-area">
                            <div class="chart-placeholder">
                                <p>Select a symbol and click "Load Chart" to view candlestick data with trade markers</p>
                            </div>
                        </div>
                    </div>

                    <div class="chart-sidebar">
                        <div class="trade-statistics">
                            <h3>Trade Statistics</h3>
                            <div id="tradeStats" class="stats-panel">
                                <div class="stat-item">
                                    <span class="stat-label">Total Trades:</span>
                                    <span class="stat-value" id="chartTotalTrades">-</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Win Rate:</span>
                                    <span class="stat-value" id="chartWinRate">-</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Total P&L:</span>
                                    <span class="stat-value" id="chartTotalPnl">-</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Avg P&L:</span>
                                    <span class="stat-value" id="chartAvgPnl">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="trade-legend">
                            <h4>Trade Markers</h4>
                            <div class="legend-item">
                                <span class="marker entry-marker"></span>
                                <span>Entry Points</span>
                            </div>
                            <div class="legend-item">
                                <span class="marker exit-marker"></span>
                                <span>Exit Points</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="trade-details">
                    <h3>Trade Details</h3>
                    <div id="tradeDetailsTable" class="data-table">
                        <p>Select a symbol to view trade details</p>
                    </div>
                </div>
            </div>

            <!-- Optimization Tab -->
            <div id="optimization" class="tab-content">
                <div class="analysis-header">
                    <h2>Trading Optimization</h2>
                    <p>Comprehensive optimization recommendations and insights</p>
                </div>

                <div class="optimization-summary">
                    <h3>Optimization Summary</h3>
                    <div id="optimizationSummary" class="summary-cards">
                        Loading optimization summary...
                    </div>
                </div>

                <div class="optimization-recommendations">
                    <h3>Detailed Recommendations</h3>
                    <div id="detailedRecommendations" class="recommendations-detailed">
                        Loading detailed recommendations...
                    </div>
                </div>

                <div class="optimization-actions">
                    <h3>Actionable Insights</h3>
                    <div id="actionableInsights" class="actions-list">
                        Loading actionable insights...
                    </div>
                </div>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
            <p>Loading data...</p>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <span id="lastUpdated">Last updated: Never</span>
            <span id="connectionStatus" class="status-connected">Connected</span>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
