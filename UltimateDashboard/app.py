"""
Main Flask application for UltimateDashboard
Provides REST API endpoints for trading analytics and dashboard functionality
"""
import logging
import os
import time
from datetime import datetime
from flask import Flask, jsonify, request, render_template
from flask_cors import CORS

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    # Load .env from the project root (parent directory)
    env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    load_dotenv(env_path)
    print(f"Loaded environment variables from: {env_path}")
except ImportError:
    print("python-dotenv not installed. Install with: pip install python-dotenv")
except Exception as e:
    print(f"Error loading .env file: {e}")

# Import our modules
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connector import DatabaseConnector
from analytics.core_engine import CoreAnalyticsEngine
from analytics.prompt_analyzer import PromptAnalyzer
from analytics.market_classifier import MarketConditionClassifier
from analytics.filters import AdvancedFilters
from api.bybit_client import BybitClient
from realtime.data_monitor import get_monitor
from config import (
    DASHBOARD_HOST, 
    DASHBOARD_PORT, 
    DEBUG_MODE,
    LOG_LEVEL,
    LOG_FILE
)

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for frontend integration

# Initialize components
db = DatabaseConnector()
analytics_engine = CoreAnalyticsEngine()
prompt_analyzer = PromptAnalyzer()
market_classifier = MarketConditionClassifier()
advanced_filters = AdvancedFilters()
real_time_monitor = get_monitor()

try:
    api_client = BybitClient()
    logger.info("Bybit API client initialized successfully")
except Exception as e:
    logger.warning(f"Failed to initialize Bybit API client: {e}")
    api_client = None

# === DASHBOARD ROUTES ===

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "database": "connected",
            "analytics_engine": "active",
            "api_client": "connected" if api_client else "unavailable"
        }
    })

# === ANALYTICS API ENDPOINTS ===

@app.route('/api/overview')
def get_overview():
    """Get overall trading performance overview"""
    try:
        days = request.args.get('days', 30, type=int)
        
        # Get comprehensive metrics
        metrics = analytics_engine.calculate_comprehensive_metrics(days)
        
        # Get symbol performance
        symbol_performance = analytics_engine.analyze_symbol_performance(days)
        
        # Get optimization recommendations
        recommendations = analytics_engine.generate_optimization_recommendations(days)

        # Get P&L over time data
        pnl_over_time = analytics_engine.calculate_pnl_over_time(days)

        return jsonify({
            "success": True,
            "data": {
                "metrics": metrics,
                "symbol_performance": symbol_performance[:10],  # Top 10 symbols
                "recommendations": recommendations,
                "pnl_over_time": pnl_over_time,
                "period_days": days
            }
        })
        
    except Exception as e:
        logger.error(f"Error in overview endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/prompt-analysis')
def get_prompt_analysis():
    """Get prompt performance analysis"""
    try:
        days = request.args.get('days', 30, type=int)
        
        # Get prompt performance summary
        prompt_summary = prompt_analyzer.get_prompt_performance_summary(days)
        
        return jsonify({
            "success": True,
            "data": prompt_summary
        })
        
    except Exception as e:
        logger.error(f"Error in prompt analysis endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/market-conditions')
def get_market_conditions():
    """Get market condition analysis"""
    try:
        days = request.args.get('days', 30, type=int)
        
        # Get market condition summary
        market_summary = market_classifier.get_market_condition_summary(days)
        
        return jsonify({
            "success": True,
            "data": market_summary
        })
        
    except Exception as e:
        logger.error(f"Error in market conditions endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/symbol-performance')
def get_symbol_performance():
    """Get detailed symbol performance"""
    try:
        days = request.args.get('days', 30, type=int)
        symbol = request.args.get('symbol')
        
        if symbol:
            # Get specific symbol analysis
            symbol_trades = db.get_trades(symbol=symbol, status='closed')
            symbol_analysis = db.get_analysis_results(symbol=symbol)
            
            return jsonify({
                "success": True,
                "data": {
                    "symbol": symbol,
                    "trades": symbol_trades[:100],  # Limit to 100 recent trades
                    "analysis": symbol_analysis[:50],  # Limit to 50 recent analyses
                    "period_days": days
                }
            })
        else:
            # Get all symbols performance
            symbol_performance = analytics_engine.analyze_symbol_performance(days)
            
            return jsonify({
                "success": True,
                "data": {
                    "symbol_performance": symbol_performance,
                    "period_days": days
                }
            })
        
    except Exception as e:
        logger.error(f"Error in symbol performance endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/trades')
def get_trades():
    """Get trades with filtering"""
    try:
        # Parse query parameters
        limit = request.args.get('limit', 100, type=int)
        symbol = request.args.get('symbol')
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Get trades
        trades = db.get_trades(
            limit=limit,
            symbol=symbol,
            status=status,
            start_date=start_date,
            end_date=end_date
        )
        
        return jsonify({
            "success": True,
            "data": {
                "trades": trades,
                "count": len(trades),
                "filters": {
                    "symbol": symbol,
                    "status": status,
                    "start_date": start_date,
                    "end_date": end_date
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error in trades endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/analysis-results')
def get_analysis_results():
    """Get analysis results with filtering"""
    try:
        # Parse query parameters
        limit = request.args.get('limit', 100, type=int)
        symbol = request.args.get('symbol')
        timeframe = request.args.get('timeframe')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Get analysis results
        analysis_results = db.get_analysis_results(
            limit=limit,
            symbol=symbol,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date
        )
        
        return jsonify({
            "success": True,
            "data": {
                "analysis_results": analysis_results,
                "count": len(analysis_results),
                "filters": {
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "start_date": start_date,
                    "end_date": end_date
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error in analysis results endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/confidence-analysis')
def get_confidence_analysis():
    """Get confidence level analysis"""
    try:
        days = request.args.get('days', 30, type=int)
        
        # Get confidence analysis
        confidence_analysis = db.get_confidence_analysis(days)
        
        return jsonify({
            "success": True,
            "data": {
                "confidence_analysis": confidence_analysis,
                "period_days": days
            }
        })
        
    except Exception as e:
        logger.error(f"Error in confidence analysis endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# === REAL-TIME DATA ENDPOINTS ===

@app.route('/api/real-time/validate')
def validate_trade_data():
    """Validate database trade data against exchange"""
    try:
        if not api_client:
            return jsonify({
                "success": False,
                "error": "Bybit API client not available"
            }), 503
        
        # Get recent trades from database
        recent_trades = db.get_trades(limit=50, status='closed')
        
        # Validate against exchange data
        validation_results = api_client.validate_trade_data(recent_trades)
        
        return jsonify({
            "success": True,
            "data": validation_results
        })
        
    except Exception as e:
        logger.error(f"Error in trade validation endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/real-time/executions')
def get_real_time_executions():
    """Get real-time execution data from exchange"""
    try:
        if not api_client:
            return jsonify({
                "success": False,
                "error": "Bybit API client not available"
            }), 503
        
        days = request.args.get('days', 1, type=int)
        symbol = request.args.get('symbol')
        
        # Get execution history
        if symbol:
            executions = api_client.get_executions(symbol=symbol, limit=100)
        else:
            executions = api_client.get_execution_history(days=days)
        
        return jsonify({
            "success": True,
            "data": {
                "executions": executions,
                "symbol": symbol,
                "days": days
            }
        })
        
    except Exception as e:
        logger.error(f"Error in real-time executions endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# === UTILITY ENDPOINTS ===

@app.route('/api/symbols')
def get_symbols():
    """Get list of available symbols"""
    try:
        symbols = db.get_unique_symbols()
        return jsonify({
            "success": True,
            "data": {"symbols": symbols}
        })
        
    except Exception as e:
        logger.error(f"Error in symbols endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/timeframes')
def get_timeframes():
    """Get list of available timeframes"""
    try:
        timeframes = db.get_unique_timeframes()
        return jsonify({
            "success": True,
            "data": {"timeframes": timeframes}
        })

    except Exception as e:
        logger.error(f"Error in timeframes endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# === FILTERING ENDPOINTS ===

@app.route('/api/filter-options')
def get_filter_options():
    """Get available filter options"""
    try:
        filter_options = advanced_filters.get_filter_options()
        return jsonify({
            "success": True,
            "data": filter_options
        })

    except Exception as e:
        logger.error(f"Error in filter options endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/filtered-data', methods=['POST'])
def get_filtered_data():
    """Get filtered trading data"""
    try:
        filters = request.json or {}
        data_type = filters.get('data_type', 'trades')

        # Apply filters
        filtered_data = advanced_filters.apply_advanced_filters(filters, data_type)

        return jsonify({
            "success": True,
            "data": {
                "filtered_data": filtered_data,
                "count": len(filtered_data),
                "filters_applied": filters
            }
        })

    except Exception as e:
        logger.error(f"Error in filtered data endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/optimization-insights', methods=['POST'])
def get_optimization_insights():
    """Get optimization insights based on filters"""
    try:
        filters = request.json or {}

        # Generate insights
        insights = advanced_filters.generate_optimization_insights(filters)

        return jsonify({
            "success": True,
            "data": insights
        })

    except Exception as e:
        logger.error(f"Error in optimization insights endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# === REAL-TIME ENDPOINTS ===

@app.route('/api/real-time/status')
def get_real_time_status():
    """Get real-time monitoring status"""
    try:
        status = real_time_monitor.get_real_time_summary()
        return jsonify({
            "success": True,
            "data": status
        })

    except Exception as e:
        logger.error(f"Error in real-time status endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/real-time/start', methods=['POST'])
def start_real_time_monitoring():
    """Start real-time monitoring"""
    try:
        real_time_monitor.start_monitoring()
        return jsonify({
            "success": True,
            "message": "Real-time monitoring started"
        })

    except Exception as e:
        logger.error(f"Error starting real-time monitoring: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/real-time/stop', methods=['POST'])
def stop_real_time_monitoring():
    """Stop real-time monitoring"""
    try:
        real_time_monitor.stop_monitoring()
        return jsonify({
            "success": True,
            "message": "Real-time monitoring stopped"
        })

    except Exception as e:
        logger.error(f"Error stopping real-time monitoring: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/real-time/sync', methods=['POST'])
def force_sync():
    """Force synchronization with exchange"""
    try:
        sync_results = real_time_monitor.force_sync()
        return jsonify({
            "success": True,
            "data": sync_results
        })

    except Exception as e:
        logger.error(f"Error in force sync: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# === CHART ANALYSIS ENDPOINTS ===

@app.route('/api/chart-data/<symbol>')
def get_chart_data(symbol):
    """Get candlestick chart data for a symbol"""
    try:
        # Get query parameters
        timeframe = request.args.get('timeframe', '1h')
        days = int(request.args.get('days', 7))

        if not api_client:
            return jsonify({
                "success": False,
                "error": "Exchange API not available"
            })

        # Calculate start time
        end_time = int(time.time() * 1000)  # Current time in milliseconds
        start_time = end_time - (days * 24 * 60 * 60 * 1000)  # Days ago in milliseconds

        # Fetch candlestick data from Bybit
        klines = api_client.get_kline_data(
            symbol=symbol,
            interval=timeframe,
            start=start_time,
            end=end_time,
            limit=1000
        )

        if not klines:
            return jsonify({
                "success": False,
                "error": f"No chart data available for {symbol}"
            })

        # Format data for Plotly candlestick chart
        chart_data = []
        for kline in klines:
            chart_data.append({
                'timestamp': int(kline[0]),  # Timestamp
                'datetime': datetime.fromtimestamp(int(kline[0]) / 1000).isoformat(),
                'open': float(kline[1]),
                'high': float(kline[2]),
                'low': float(kline[3]),
                'close': float(kline[4]),
                'volume': float(kline[5])
            })

        return jsonify({
            "success": True,
            "data": {
                "symbol": symbol,
                "timeframe": timeframe,
                "chart_data": chart_data,
                "data_points": len(chart_data)
            }
        })

    except Exception as e:
        logger.error(f"Error getting chart data for {symbol}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/api/trade-markers/<symbol>')
def get_trade_markers(symbol):
    """Get trade entry/exit markers for a symbol"""
    try:
        days = int(request.args.get('days', 30))

        # Get trades for the symbol within the date range
        trades = db.get_trades_for_symbol(symbol, days)

        if not trades:
            return jsonify({
                "success": True,
                "data": {
                    "symbol": symbol,
                    "trade_markers": [],
                    "trade_count": 0
                }
            })

        trade_markers = []
        for trade in trades:
            # Entry marker
            entry_marker = {
                'id': trade['id'],
                'type': 'entry',
                'timestamp': trade['created_at'],
                'price': trade['entry_price'],
                'side': trade['side'],
                'quantity': trade['quantity'],
                'confidence': trade.get('confidence', 0),
                'recommendation_id': trade.get('recommendation_id'),
                'status': trade['status'],
                'pnl': trade.get('pnl', 0)
            }
            trade_markers.append(entry_marker)

            # Exit marker (if trade is closed)
            if trade['status'] == 'closed' and trade.get('avg_exit_price'):
                exit_marker = {
                    'id': trade['id'] + '_exit',
                    'type': 'exit',
                    'timestamp': trade['updated_at'],
                    'price': trade['avg_exit_price'],
                    'side': 'Sell' if trade['side'] == 'Buy' else 'Buy',
                    'quantity': trade.get('closed_size', trade['quantity']),
                    'pnl': trade.get('pnl', 0),
                    'trade_id': trade['id']
                }
                trade_markers.append(exit_marker)

        return jsonify({
            "success": True,
            "data": {
                "symbol": symbol,
                "trade_markers": trade_markers,
                "trade_count": len(trades)
            }
        })

    except Exception as e:
        logger.error(f"Error getting trade markers for {symbol}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/api/chart-symbols')
def get_chart_symbols():
    """Get list of symbols that have trades for chart analysis"""
    try:
        symbols = db.get_symbols_with_trades()

        return jsonify({
            "success": True,
            "data": {
                "symbols": symbols,
                "count": len(symbols)
            }
        })

    except Exception as e:
        logger.error(f"Error getting chart symbols: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

# === ERROR HANDLERS ===

@app.errorhandler(404)
def not_found(error):
    return jsonify({"success": False, "error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"success": False, "error": "Internal server error"}), 500

if __name__ == '__main__':
    logger.info(f"Starting UltimateDashboard on {DASHBOARD_HOST}:{DASHBOARD_PORT}")
    logger.info(f"Debug mode: {DEBUG_MODE}")
    
    app.run(
        host=DASHBOARD_HOST,
        port=DASHBOARD_PORT,
        debug=DEBUG_MODE
    )
