// UltimateDashboard JavaScript
class UltimateDashboard {
    constructor() {
        this.currentTab = 'overview';
        this.currentTimeRange = 30;
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = tab.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });

        // Time range selector
        document.getElementById('timeRange').addEventListener('change', (e) => {
            this.currentTimeRange = parseInt(e.target.value);
            this.loadCurrentTabData();
        });

        // Refresh button
        document.getElementById('refreshData').addEventListener('click', () => {
            this.loadCurrentTabData();
        });

        // Symbol filter
        const symbolFilter = document.getElementById('symbolFilter');
        if (symbolFilter) {
            symbolFilter.addEventListener('change', () => {
                this.loadSymbolAnalysis();
            });
        }

        // Analyze symbol button
        const analyzeSymbolBtn = document.getElementById('analyzeSymbol');
        if (analyzeSymbolBtn) {
            analyzeSymbolBtn.addEventListener('click', () => {
                this.loadSymbolAnalysis();
            });
        }

        // Chart analysis controls
        const loadChartBtn = document.getElementById('loadChartBtn');
        if (loadChartBtn) {
            loadChartBtn.addEventListener('click', () => {
                this.loadChartData();
            });
        }

        const symbolSelector = document.getElementById('symbolSelector');
        if (symbolSelector) {
            symbolSelector.addEventListener('change', () => {
                // Clear previous data when symbol changes
                this.clearChartData();
            });
        }
    }

    switchTab(tabName) {
        // Update active tab
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update active content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;
        this.loadCurrentTabData();
    }

    loadCurrentTabData() {
        this.showLoading();
        
        switch (this.currentTab) {
            case 'overview':
                this.loadOverview();
                break;
            case 'prompt-analysis':
                this.loadPromptAnalysis();
                break;
            case 'market-conditions':
                this.loadMarketConditions();
                break;
            case 'symbol-analysis':
                this.loadSymbolAnalysis();
                break;
            case 'chart-analysis':
                this.loadChartAnalysis();
                break;
            case 'optimization':
                this.loadOptimization();
                break;
        }
    }

    async loadInitialData() {
        try {
            // Load symbols for filter
            await this.loadSymbols();
            
            // Load overview data
            await this.loadOverview();
            
            this.updateLastUpdated();
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('Failed to load initial data');
        }
    }

    async loadOverview() {
        try {
            const response = await fetch(`/api/overview?days=${this.currentTimeRange}`);
            const data = await response.json();

            if (data.success) {
                this.updateOverviewMetrics(data.data.metrics);
                this.updatePnLOverTimeChart(data.data.pnl_over_time);
                this.updateSymbolPerformanceChart(data.data.symbol_performance);
                this.updateRecommendations(data.data.recommendations);
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            console.error('Error loading overview:', error);
            this.showError('Failed to load overview data');
        } finally {
            this.hideLoading();
        }
    }

    async loadPromptAnalysis() {
        try {
            const response = await fetch(`/api/prompt-analysis?days=${this.currentTimeRange}`);
            const data = await response.json();

            if (data.success) {
                this.updatePromptAnalysis(data.data);
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            console.error('Error loading prompt analysis:', error);
            this.showError('Failed to load prompt analysis');
        } finally {
            this.hideLoading();
        }
    }

    async loadMarketConditions() {
        try {
            const response = await fetch(`/api/market-conditions?days=${this.currentTimeRange}`);
            const data = await response.json();

            if (data.success) {
                this.updateMarketConditions(data.data);
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            console.error('Error loading market conditions:', error);
            this.showError('Failed to load market conditions');
        } finally {
            this.hideLoading();
        }
    }

    async loadSymbolAnalysis() {
        try {
            const symbol = document.getElementById('symbolFilter')?.value || '';
            const url = symbol ? 
                `/api/symbol-performance?days=${this.currentTimeRange}&symbol=${symbol}` :
                `/api/symbol-performance?days=${this.currentTimeRange}`;
            
            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                this.updateSymbolAnalysis(data.data);
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            console.error('Error loading symbol analysis:', error);
            this.showError('Failed to load symbol analysis');
        } finally {
            this.hideLoading();
        }
    }

    async loadOptimization() {
        try {
            // Load multiple optimization endpoints
            const [overviewResponse, promptResponse, marketResponse] = await Promise.all([
                fetch(`/api/overview?days=${this.currentTimeRange}`),
                fetch(`/api/prompt-analysis?days=${this.currentTimeRange}`),
                fetch(`/api/market-conditions?days=${this.currentTimeRange}`)
            ]);

            const overviewData = await overviewResponse.json();
            const promptData = await promptResponse.json();
            const marketData = await marketResponse.json();

            if (overviewData.success && promptData.success && marketData.success) {
                this.updateOptimization({
                    overview: overviewData.data,
                    prompts: promptData.data,
                    markets: marketData.data
                });
            } else {
                throw new Error('Failed to load optimization data');
            }
        } catch (error) {
            console.error('Error loading optimization:', error);
            this.showError('Failed to load optimization data');
        } finally {
            this.hideLoading();
        }
    }

    async loadSymbols() {
        try {
            const response = await fetch('/api/symbols');
            const data = await response.json();

            if (data.success) {
                const symbolFilter = document.getElementById('symbolFilter');
                if (symbolFilter) {
                    symbolFilter.innerHTML = '<option value="">All Symbols</option>';
                    data.data.symbols.forEach(symbol => {
                        const option = document.createElement('option');
                        option.value = symbol;
                        option.textContent = symbol;
                        symbolFilter.appendChild(option);
                    });
                }
            }
        } catch (error) {
            console.error('Error loading symbols:', error);
        }
    }

    updateOverviewMetrics(metrics) {
        // Update metric cards
        document.getElementById('totalPnl').textContent = `$${metrics.total_pnl || 0}`;
        document.getElementById('winRate').textContent = `${metrics.win_rate || 0}%`;
        document.getElementById('profitFactor').textContent = metrics.profit_factor || 0;
        document.getElementById('totalTrades').textContent = metrics.total_trades || 0;
        document.getElementById('expectedValue').textContent = `$${metrics.expected_value || 0}`;
        document.getElementById('maxDrawdown').textContent = `$${metrics.max_drawdown || 0}`;

        // Update metric changes (simplified - would need historical data for real changes)
        this.updateMetricChange('pnlChange', metrics.total_pnl);
        this.updateMetricChange('winRateChange', metrics.win_rate);
    }

    updateMetricChange(elementId, value) {
        const element = document.getElementById(elementId);
        if (element && value !== undefined) {
            const isPositive = value > 0;
            element.className = `metric-change ${isPositive ? 'positive' : 'negative'}`;
            element.textContent = `${isPositive ? '+' : ''}${value > 0 ? value.toFixed(2) : ''}`;
        }
    }

    updatePnLOverTimeChart(pnlData) {
        if (!pnlData || pnlData.length === 0) {
            // Show empty chart message
            const chartContainer = document.getElementById('pnlChart');
            if (chartContainer) {
                chartContainer.innerHTML = '<div class="empty-chart">No P&L data available</div>';
            }
            return;
        }

        // Prepare data for Plotly
        const dates = pnlData.map(d => d.date);
        const cumulativePnL = pnlData.map(d => d.cumulative_pnl);

        const trace = {
            x: dates,
            y: cumulativePnL,
            type: 'scatter',
            mode: 'lines+markers',
            name: 'Cumulative P&L',
            line: {
                color: '#007bff',
                width: 2
            },
            marker: {
                size: 4,
                color: cumulativePnL.map(pnl => pnl >= 0 ? '#28a745' : '#dc3545')
            },
            hovertemplate: 'Date: %{x}<br>Cumulative P&L: $%{y:.2f}<extra></extra>'
        };

        const layout = {
            title: 'P&L Over Time',
            xaxis: {
                title: 'Date',
                type: 'date'
            },
            yaxis: {
                title: 'Cumulative P&L ($)',
                zeroline: true,
                zerolinecolor: '#666',
                zerolinewidth: 1
            },
            showlegend: false,
            height: 300,
            margin: { t: 40, r: 20, b: 40, l: 60 },
            plot_bgcolor: '#f8f9fa',
            paper_bgcolor: '#ffffff'
        };

        const config = { responsive: true, displayModeBar: false };

        Plotly.newPlot('pnlChart', [trace], layout, config);
    }

    updateSymbolPerformanceChart(symbolData) {
        if (!symbolData || symbolData.length === 0) return;

        const symbols = symbolData.map(d => d.symbol);
        const pnls = symbolData.map(d => d.total_pnl);
        const winRates = symbolData.map(d => d.win_rate);

        const trace1 = {
            x: symbols,
            y: pnls,
            type: 'bar',
            name: 'Total P&L',
            marker: {
                color: pnls.map(p => p > 0 ? '#10b981' : '#ef4444')
            }
        };

        const layout = {
            title: 'Symbol Performance',
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: { color: '#f8fafc' },
            xaxis: { color: '#cbd5e1' },
            yaxis: { color: '#cbd5e1' }
        };

        Plotly.newPlot('symbolChart', [trace1], layout, {responsive: true});
    }

    updateRecommendations(recommendations) {
        const container = document.getElementById('recommendations');
        if (!recommendations || recommendations.length === 0) {
            container.innerHTML = '<p>No recommendations available.</p>';
            return;
        }

        container.innerHTML = recommendations.map(rec => `
            <div class="recommendation-item ${rec.priority}-priority">
                <div class="recommendation-title">${rec.type.replace('_', ' ').toUpperCase()}</div>
                <div class="recommendation-description">${rec.message}</div>
                ${rec.suggested_action ? `<div class="recommendation-action"><strong>Action:</strong> ${rec.suggested_action}</div>` : ''}
            </div>
        `).join('');
    }

    updatePromptAnalysis(data) {
        // Update prompt performance chart
        if (data.prompt_performance && data.prompt_performance.length > 0) {
            this.createPromptPerformanceChart(data.prompt_performance);
            this.updatePromptDetails(data.prompt_performance);
        }

        // Update prompt recommendations
        if (data.recommendations) {
            this.updatePromptRecommendations(data.recommendations);
        }

        // Update prompt versions summary
        if (data.prompt_versions) {
            this.updatePromptVersionsSummary(data.prompt_versions, data.summary_stats);
        }
    }

    createPromptPerformanceChart(promptData) {
        // Use prompt_name instead of prompt_preview for cleaner display
        const prompts = promptData.map(d => d.prompt_name || 'Unknown');
        const pnls = promptData.map(d => d.total_pnl);
        const winRates = promptData.map(d => d.win_rate);
        const totalTrades = promptData.map(d => d.total_trades);

        const trace1 = {
            x: prompts,
            y: pnls,
            type: 'bar',
            name: 'Total P&L',
            text: totalTrades.map(t => `${t} trades`),
            textposition: 'auto',
            marker: {
                color: pnls.map(p => p > 0 ? '#10b981' : '#ef4444')
            }
        };

        const layout = {
            title: 'Prompt Performance Comparison',
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: { color: '#f8fafc' },
            xaxis: {
                color: '#cbd5e1',
                tickangle: -45,
                title: 'Prompt Version'
            },
            yaxis: {
                color: '#cbd5e1',
                title: 'Total P&L ($)'
            }
        };

        Plotly.newPlot('promptPerformanceChart', [trace1], layout, {responsive: true});
    }

    updatePromptDetails(promptData) {
        const container = document.getElementById('promptDetailsTable');

        if (!promptData || promptData.length === 0) {
            container.innerHTML = '<p class="text-gray-400">No prompt performance data available.</p>';
            return;
        }

        // Create a detailed table for prompt analysis
        const tableHTML = `
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-600">
                        <th class="text-left px-4 py-2">Prompt Version</th>
                        <th class="text-left px-4 py-2">Total Trades</th>
                        <th class="text-left px-4 py-2">Win/Loss</th>
                        <th class="text-left px-4 py-2">Win Rate</th>
                        <th class="text-left px-4 py-2">Total P&L</th>
                        <th class="text-left px-4 py-2">Avg P&L</th>
                        <th class="text-left px-4 py-2">Confidence</th>
                        <th class="text-left px-4 py-2">Profit Factor</th>
                        <th class="text-left px-4 py-2">Market Condition</th>
                    </tr>
                </thead>
                <tbody>
                    ${promptData.map(prompt => `
                        <tr class="border-b border-gray-700 hover:bg-gray-800">
                            <td class="px-4 py-2 font-medium text-blue-400">${prompt.prompt_name || 'Unknown'}</td>
                            <td class="px-4 py-2">${prompt.total_trades}</td>
                            <td class="px-4 py-2">
                                <span class="text-green-400">${prompt.winning_trades || 0}</span> /
                                <span class="text-red-400">${prompt.losing_trades || 0}</span>
                            </td>
                            <td class="px-4 py-2">${prompt.win_rate}%</td>
                            <td class="px-4 py-2 ${prompt.total_pnl >= 0 ? 'text-green-400' : 'text-red-400'}">
                                $${prompt.total_pnl}
                            </td>
                            <td class="px-4 py-2 ${prompt.avg_pnl >= 0 ? 'text-green-400' : 'text-red-400'}">
                                $${prompt.avg_pnl}
                            </td>
                            <td class="px-4 py-2">${prompt.avg_confidence}</td>
                            <td class="px-4 py-2">${prompt.profit_factor || 'N/A'}</td>
                            <td class="px-4 py-2">${prompt.market_condition || 'N/A'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = tableHTML;
    }

    updatePromptVersionsSummary(promptVersions, summaryStats) {
        // Add a summary section if it doesn't exist
        let summaryContainer = document.getElementById('promptVersionsSummary');
        if (!summaryContainer) {
            const promptAnalysisSection = document.querySelector('.prompt-analysis');
            if (promptAnalysisSection) {
                summaryContainer = document.createElement('div');
                summaryContainer.id = 'promptVersionsSummary';
                summaryContainer.className = 'mb-6 p-4 bg-gray-800 rounded-lg';
                promptAnalysisSection.insertBefore(summaryContainer, promptAnalysisSection.firstChild);
            }
        }

        if (summaryContainer && summaryStats) {
            summaryContainer.innerHTML = `
                <h3 class="text-lg font-semibold mb-3">Prompt Analysis Summary</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-400">${summaryStats.total_prompt_versions || 0}</div>
                        <div class="text-sm text-gray-400">Prompt Versions</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-400">${summaryStats.total_trades_analyzed || 0}</div>
                        <div class="text-sm text-gray-400">Total Trades</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-400">${summaryStats.avg_win_rate?.toFixed(1) || 0}%</div>
                        <div class="text-sm text-gray-400">Avg Win Rate</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-400">${summaryStats.avg_profit_factor?.toFixed(2) || 0}</div>
                        <div class="text-sm text-gray-400">Avg Profit Factor</div>
                    </div>
                </div>
                <div class="mt-3 text-sm text-gray-400">
                    Best: <span class="text-green-400">${summaryStats.best_performing_prompt || 'N/A'}</span> |
                    Worst: <span class="text-red-400">${summaryStats.worst_performing_prompt || 'N/A'}</span>
                </div>
            `;
        }
    }

    updatePromptRecommendations(recommendations) {
        const container = document.getElementById('promptRecommendations');
        if (!recommendations || recommendations.length === 0) {
            container.innerHTML = '<p>No prompt recommendations available.</p>';
            return;
        }

        container.innerHTML = recommendations.map(rec => `
            <div class="recommendation-item ${rec.priority}-priority">
                <div class="recommendation-title">${rec.title || rec.type}</div>
                <div class="recommendation-description">${rec.description || rec.message}</div>
                ${rec.implementation ? `<div class="recommendation-action"><strong>Implementation:</strong> ${rec.implementation}</div>` : ''}
            </div>
        `).join('');
    }

    updateMarketConditions(data) {
        if (data.overall_market_analysis && data.overall_market_analysis.market_performance) {
            this.createMarketRegimeChart(data.overall_market_analysis.market_performance);
        }

        if (data.overall_market_analysis && data.overall_market_analysis.market_insights) {
            this.updateMarketInsights(data.overall_market_analysis.market_insights);
        }
    }

    updateSymbolAnalysis(data) {
        if (data.symbol_performance) {
            this.createSymbolPnlChart(data.symbol_performance);
            this.createSymbolWinRateChart(data.symbol_performance);
            this.updateSymbolDetailsTable(data.symbol_performance);
        }
    }

    updateOptimization(data) {
        const allRecommendations = [
            ...(data.overview.recommendations || []),
            ...(data.prompts.recommendations || []),
            ...(data.markets.market_insights || [])
        ];

        this.updateOptimizationSummary(data);
        this.updateDetailedRecommendations(allRecommendations);
    }

    createMarketRegimeChart(marketPerformance) {
        const regimes = Object.keys(marketPerformance);
        const pnls = regimes.map(regime => marketPerformance[regime].total_pnl);
        const winRates = regimes.map(regime => marketPerformance[regime].win_rate);

        const trace1 = {
            x: regimes,
            y: pnls,
            type: 'bar',
            name: 'Total P&L',
            marker: {
                color: pnls.map(p => p > 0 ? '#10b981' : '#ef4444')
            }
        };

        const layout = {
            title: 'Performance by Market Regime',
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: { color: '#f8fafc' },
            xaxis: { color: '#cbd5e1', tickangle: -45 },
            yaxis: { color: '#cbd5e1' }
        };

        Plotly.newPlot('marketRegimeChart', [trace1], layout, {responsive: true});
    }

    createSymbolPnlChart(symbolPerformance) {
        const symbols = symbolPerformance.map(d => d.symbol);
        const pnls = symbolPerformance.map(d => d.total_pnl);

        const trace1 = {
            x: symbols,
            y: pnls,
            type: 'bar',
            name: 'Total P&L',
            marker: {
                color: pnls.map(p => p > 0 ? '#10b981' : '#ef4444')
            }
        };

        const layout = {
            title: 'Symbol P&L Comparison',
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: { color: '#f8fafc' },
            xaxis: { color: '#cbd5e1' },
            yaxis: { color: '#cbd5e1' }
        };

        Plotly.newPlot('symbolPnlChart', [trace1], layout, {responsive: true});
    }

    createSymbolWinRateChart(symbolPerformance) {
        const symbols = symbolPerformance.map(d => d.symbol);
        const winRates = symbolPerformance.map(d => d.win_rate);

        const trace1 = {
            x: symbols,
            y: winRates,
            type: 'bar',
            name: 'Win Rate %',
            marker: {
                color: winRates.map(wr => wr > 50 ? '#10b981' : '#ef4444')
            }
        };

        const layout = {
            title: 'Symbol Win Rate Analysis',
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: { color: '#f8fafc' },
            xaxis: { color: '#cbd5e1' },
            yaxis: { color: '#cbd5e1', title: 'Win Rate (%)' }
        };

        Plotly.newPlot('symbolWinRateChart', [trace1], layout, {responsive: true});
    }

    updateSymbolDetailsTable(symbolPerformance) {
        const container = document.getElementById('symbolDetailsTable');

        const tableHTML = `
            <table class="table">
                <thead>
                    <tr>
                        <th>Symbol</th>
                        <th>Total Trades</th>
                        <th>Total P&L</th>
                        <th>Win Rate</th>
                        <th>Avg P&L</th>
                        <th>Max Drawdown</th>
                    </tr>
                </thead>
                <tbody>
                    ${symbolPerformance.map(symbol => `
                        <tr>
                            <td>${symbol.symbol}</td>
                            <td>${symbol.total_trades}</td>
                            <td class="${symbol.total_pnl > 0 ? 'positive' : 'negative'}">$${symbol.total_pnl}</td>
                            <td class="${symbol.win_rate > 50 ? 'positive' : 'negative'}">${symbol.win_rate}%</td>
                            <td class="${symbol.avg_pnl > 0 ? 'positive' : 'negative'}">$${symbol.avg_pnl}</td>
                            <td>$${symbol.max_drawdown || 0}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = tableHTML;
    }

    updateMarketInsights(insights) {
        const container = document.getElementById('marketInsights');
        if (!insights || insights.length === 0) {
            container.innerHTML = '<p>No market insights available.</p>';
            return;
        }

        container.innerHTML = insights.map(insight => `
            <div class="insight-item ${insight.priority}-priority">
                <div class="insight-title">${insight.title}</div>
                <div class="insight-description">${insight.description}</div>
                <div class="insight-action"><strong>Action:</strong> ${insight.action}</div>
            </div>
        `).join('');
    }

    updateOptimizationSummary(data) {
        const container = document.getElementById('optimizationSummary');

        const overviewMetrics = data.overview.metrics || {};
        const promptCount = data.prompts.prompt_performance ? data.prompts.prompt_performance.length : 0;
        const marketRegimes = data.markets.overall_market_analysis ?
            Object.keys(data.markets.overall_market_analysis.market_performance || {}).length : 0;

        container.innerHTML = `
            <div class="summary-card">
                <h4>Overall Performance</h4>
                <p>Total P&L: $${overviewMetrics.total_pnl || 0}</p>
                <p>Win Rate: ${overviewMetrics.win_rate || 0}%</p>
                <p>Profit Factor: ${overviewMetrics.profit_factor || 0}</p>
            </div>
            <div class="summary-card">
                <h4>Prompt Analysis</h4>
                <p>Analyzed Prompts: ${promptCount}</p>
                <p>Optimization Opportunities: ${data.prompts.recommendations ? data.prompts.recommendations.length : 0}</p>
            </div>
            <div class="summary-card">
                <h4>Market Conditions</h4>
                <p>Market Regimes: ${marketRegimes}</p>
                <p>Market Insights: ${data.markets.overall_market_analysis ?
                    (data.markets.overall_market_analysis.market_insights || []).length : 0}</p>
            </div>
        `;
    }

    updateDetailedRecommendations(recommendations) {
        const container = document.getElementById('detailedRecommendations');
        if (!recommendations || recommendations.length === 0) {
            container.innerHTML = '<p>No detailed recommendations available.</p>';
            return;
        }

        container.innerHTML = recommendations.map(rec => `
            <div class="recommendation-detailed ${rec.priority}-priority">
                <div class="recommendation-header">
                    <span class="recommendation-type">${rec.type || 'General'}</span>
                    <span class="recommendation-priority">${rec.priority || 'medium'} priority</span>
                </div>
                <div class="recommendation-title">${rec.title || rec.message}</div>
                <div class="recommendation-description">${rec.description || rec.suggested_action}</div>
                ${rec.implementation ? `<div class="recommendation-implementation"><strong>Implementation:</strong> ${rec.implementation}</div>` : ''}
            </div>
        `).join('');
    }

    showLoading() {
        document.getElementById('loadingOverlay').classList.remove('hidden');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.add('hidden');
    }

    showError(message) {
        console.error(message);
        // Could implement a toast notification system here
    }

    updateLastUpdated() {
        document.getElementById('lastUpdated').textContent = 
            `Last updated: ${new Date().toLocaleTimeString()}`;
    }

    startAutoRefresh() {
        // Refresh data every 5 minutes
        this.refreshInterval = setInterval(() => {
            this.loadCurrentTabData();
        }, 5 * 60 * 1000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // === CHART ANALYSIS METHODS ===

    async loadChartAnalysis() {
        try {
            // Load symbols for chart selector
            await this.loadChartSymbols();
            this.hideLoading();
        } catch (error) {
            this.showError('Error loading chart analysis: ' + error.message);
            this.hideLoading();
        }
    }

    async loadChartSymbols() {
        try {
            const response = await fetch('/api/chart-symbols');
            const data = await response.json();

            if (data.success) {
                const symbolSelector = document.getElementById('symbolSelector');
                symbolSelector.innerHTML = '<option value="">Select a symbol...</option>';

                data.data.symbols.forEach(symbol => {
                    const option = document.createElement('option');
                    option.value = symbol;
                    option.textContent = symbol;
                    symbolSelector.appendChild(option);
                });
            }
        } catch (error) {
            this.showError('Error loading chart symbols: ' + error.message);
        }
    }

    async loadChartData() {
        const symbol = document.getElementById('symbolSelector').value;
        const timeframe = document.getElementById('timeframeSelector').value;
        const days = document.getElementById('chartDaysSelector').value;

        if (!symbol) {
            alert('Please select a symbol first');
            return;
        }

        this.showLoading();

        try {
            // Load trade markers first (this always works)
            const markersResponse = await fetch(`/api/trade-markers/${symbol}?days=${days}`);
            const markersData = await markersResponse.json();

            if (markersData.success) {
                // Try to load candlestick data
                try {
                    const chartResponse = await fetch(`/api/chart-data/${symbol}?timeframe=${timeframe}&days=${days}`);
                    const chartData = await chartResponse.json();

                    if (chartData.success) {
                        // Render full chart with candlesticks and markers
                        this.renderCandlestickChart(chartData.data, markersData.data);
                    } else {
                        // Fallback: render just trade markers without candlesticks
                        this.renderTradeMarkersOnly(markersData.data, symbol);
                    }
                } catch (chartError) {
                    console.warn('Chart data unavailable, showing trade markers only:', chartError);
                    this.renderTradeMarkersOnly(markersData.data, symbol);
                }

                this.updateTradeStatistics(markersData.data);
                this.updateTradeDetailsTable(markersData.data);
            } else {
                this.showError('Error loading trade data');
            }
        } catch (error) {
            this.showError('Error loading chart data: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderCandlestickChart(chartData, markersData) {
        const candlestickTrace = {
            x: chartData.chart_data.map(d => d.datetime),
            open: chartData.chart_data.map(d => d.open),
            high: chartData.chart_data.map(d => d.high),
            low: chartData.chart_data.map(d => d.low),
            close: chartData.chart_data.map(d => d.close),
            type: 'candlestick',
            name: chartData.symbol,
            increasing: { line: { color: '#26a69a' } },
            decreasing: { line: { color: '#ef5350' } }
        };

        // Helper function to convert timestamp format
        const formatTimestamp = (timestamp) => {
            // Convert "2025-10-05 03:12:53" to "2025-10-05T03:12:53"
            return timestamp.replace(' ', 'T');
        };

        // Create entry markers
        const entryMarkers = markersData.trade_markers.filter(m => m.type === 'entry');
        const entryTrace = {
            x: entryMarkers.map(m => formatTimestamp(m.timestamp)),
            y: entryMarkers.map(m => m.price),
            mode: 'markers',
            type: 'scatter',
            name: 'Entry Points',
            marker: {
                color: '#28a745',
                size: 10,
                symbol: 'triangle-up'
            },
            text: entryMarkers.map(m => `${m.side} ${m.quantity} @ $${m.price}<br>Confidence: ${m.confidence}`),
            hovertemplate: '%{text}<extra></extra>'
        };

        // Create exit markers
        const exitMarkers = markersData.trade_markers.filter(m => m.type === 'exit');
        const exitTrace = {
            x: exitMarkers.map(m => formatTimestamp(m.timestamp)),
            y: exitMarkers.map(m => m.price),
            mode: 'markers',
            type: 'scatter',
            name: 'Exit Points',
            marker: {
                color: '#dc3545',
                size: 10,
                symbol: 'triangle-down'
            },
            text: exitMarkers.map(m => `${m.side} ${m.quantity} @ $${m.price}<br>P&L: $${m.pnl}`),
            hovertemplate: '%{text}<extra></extra>'
        };

        const layout = {
            title: `${chartData.symbol} - ${chartData.timeframe} Chart with Trade Markers`,
            xaxis: { title: 'Time', rangeslider: { visible: false } },
            yaxis: { title: 'Price' },
            showlegend: true,
            height: 500
        };

        const traces = [candlestickTrace, entryTrace];
        if (exitMarkers.length > 0) {
            traces.push(exitTrace);
        }

        Plotly.newPlot('candlestickChart', traces, layout, { responsive: true });
    }

    renderTradeMarkersOnly(markersData, symbol) {
        // Helper function to convert timestamp format
        const formatTimestamp = (timestamp) => {
            // Convert "2025-10-05 03:12:53" to "2025-10-05T03:12:53"
            return timestamp.replace(' ', 'T');
        };

        // Create entry markers
        const entryMarkers = markersData.trade_markers.filter(m => m.type === 'entry');
        const entryTrace = {
            x: entryMarkers.map(m => formatTimestamp(m.timestamp)),
            y: entryMarkers.map(m => m.price),
            mode: 'markers',
            type: 'scatter',
            name: 'Entry Points',
            marker: {
                color: '#28a745',
                size: 12,
                symbol: 'triangle-up'
            },
            text: entryMarkers.map(m => `${m.side} ${m.quantity} @ $${m.price}<br>Confidence: ${m.confidence}<br>P&L: $${m.pnl || 0}`),
            hovertemplate: '%{text}<extra></extra>'
        };

        // Create exit markers
        const exitMarkers = markersData.trade_markers.filter(m => m.type === 'exit');
        const exitTrace = {
            x: exitMarkers.map(m => formatTimestamp(m.timestamp)),
            y: exitMarkers.map(m => m.price),
            mode: 'markers',
            type: 'scatter',
            name: 'Exit Points',
            marker: {
                color: '#dc3545',
                size: 12,
                symbol: 'triangle-down'
            },
            text: exitMarkers.map(m => `${m.side} ${m.quantity} @ $${m.price}<br>P&L: $${m.pnl}`),
            hovertemplate: '%{text}<extra></extra>'
        };

        const layout = {
            title: `${symbol} - Trade Entry/Exit Points (Chart data unavailable)`,
            xaxis: { title: 'Time' },
            yaxis: { title: 'Price' },
            showlegend: true,
            height: 500,
            plot_bgcolor: '#f8f9fa',
            paper_bgcolor: '#ffffff'
        };

        const traces = [entryTrace];
        if (exitMarkers.length > 0) {
            traces.push(exitTrace);
        }

        Plotly.newPlot('candlestickChart', traces, layout, { responsive: true });
    }

    updateTradeStatistics(markersData) {
        console.log('updateTradeStatistics called with:', markersData);

        const trades = markersData.trade_markers.filter(m => m.type === 'entry');
        const closedTrades = trades.filter(t => t.status === 'closed');
        const winningTrades = closedTrades.filter(t => t.pnl > 0);

        const totalTrades = trades.length;
        const winRate = totalTrades > 0 ? (winningTrades.length / closedTrades.length * 100).toFixed(1) : 0;
        const totalPnl = closedTrades.reduce((sum, t) => sum + (t.pnl || 0), 0);
        const avgPnl = closedTrades.length > 0 ? (totalPnl / closedTrades.length).toFixed(2) : 0;

        console.log('Calculated statistics:', {
            totalTrades,
            winRate: `${winRate}%`,
            totalPnl: `$${totalPnl.toFixed(2)}`,
            avgPnl: `$${avgPnl}`
        });

        // Get elements and verify they exist
        const totalTradesEl = document.getElementById('chartTotalTrades');
        const winRateEl = document.getElementById('chartWinRate');
        const totalPnlEl = document.getElementById('chartTotalPnl');
        const avgPnlEl = document.getElementById('chartAvgPnl');

        console.log('Elements found:', {
            totalTrades: !!totalTradesEl,
            winRate: !!winRateEl,
            totalPnl: !!totalPnlEl,
            avgPnl: !!avgPnlEl
        });

        // Update elements with force refresh
        if (totalTradesEl) {
            totalTradesEl.textContent = totalTrades;
            totalTradesEl.style.display = 'block'; // Force display
        }
        if (winRateEl) {
            winRateEl.textContent = `${winRate}%`;
            winRateEl.style.display = 'block'; // Force display
        }
        if (totalPnlEl) {
            totalPnlEl.textContent = `$${totalPnl.toFixed(2)}`;
            totalPnlEl.style.display = 'block'; // Force display
        }
        if (avgPnlEl) {
            avgPnlEl.textContent = `$${avgPnl}`;
            avgPnlEl.style.display = 'block'; // Force display
        }

        console.log('Updated element values:', {
            totalTrades: totalTradesEl?.textContent,
            winRate: winRateEl?.textContent,
            totalPnl: totalPnlEl?.textContent,
            avgPnl: avgPnlEl?.textContent
        });
    }

    updateTradeDetailsTable(markersData) {
        const trades = markersData.trade_markers.filter(m => m.type === 'entry');

        let tableHTML = `
            <table class="table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Side</th>
                        <th>Entry Price</th>
                        <th>Quantity</th>
                        <th>Status</th>
                        <th>P&L</th>
                        <th>Confidence</th>
                    </tr>
                </thead>
                <tbody>
        `;

        trades.forEach(trade => {
            const pnlClass = trade.pnl > 0 ? 'positive' : trade.pnl < 0 ? 'negative' : '';
            tableHTML += `
                <tr>
                    <td>${new Date(trade.timestamp).toLocaleDateString()}</td>
                    <td>${trade.side}</td>
                    <td>$${trade.price}</td>
                    <td>${trade.quantity}</td>
                    <td>${trade.status}</td>
                    <td class="${pnlClass}">$${(trade.pnl || 0).toFixed(2)}</td>
                    <td>${(trade.confidence || 0).toFixed(2)}</td>
                </tr>
            `;
        });

        tableHTML += '</tbody></table>';
        document.getElementById('tradeDetailsTable').innerHTML = tableHTML;
    }

    clearChartData() {
        // Clear chart
        const chartDiv = document.getElementById('candlestickChart');
        chartDiv.innerHTML = '<div class="chart-placeholder"><p>Select a symbol and click "Load Chart" to view candlestick data with trade markers</p></div>';

        // Clear statistics
        document.getElementById('chartTotalTrades').textContent = '-';
        document.getElementById('chartWinRate').textContent = '-';
        document.getElementById('chartTotalPnl').textContent = '-';
        document.getElementById('chartAvgPnl').textContent = '-';

        // Clear trade details
        document.getElementById('tradeDetailsTable').innerHTML = '<p>Select a symbol to view trade details</p>';
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new UltimateDashboard();
});
