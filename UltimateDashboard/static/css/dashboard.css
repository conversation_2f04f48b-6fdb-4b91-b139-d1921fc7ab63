/* UltimateDashboard CSS */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --background-color: #0f172a;
    --surface-color: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --border-color: #334155;
    --hover-color: #475569;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    padding: 1.5rem 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header-content h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.header-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.header-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Navigation */
.dashboard-nav {
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 0 2rem;
}

.nav-tabs {
    display: flex;
    list-style: none;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-tab {
    display: block;
    padding: 1rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    color: var(--text-primary);
    background-color: var(--hover-color);
}

.nav-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.1);
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background-color: var(--surface-color);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    text-align: center;
}

.metric-card h3 {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.metric-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.metric-change.positive {
    color: var(--success-color);
}

.metric-change.negative {
    color: var(--danger-color);
}

/* Charts Grid */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background-color: var(--surface-color);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.chart-container h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.125rem;
}

.chart {
    height: 300px;
    width: 100%;
}

/* Controls */
.control-select {
    background-color: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #1d4ed8;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background-color: #475569;
}

/* Recommendations */
.recommendations-section {
    background-color: var(--surface-color);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recommendation-item {
    background-color: rgba(37, 99, 235, 0.1);
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
}

.recommendation-item.high-priority {
    border-left-color: var(--danger-color);
    background-color: rgba(239, 68, 68, 0.1);
}

.recommendation-item.medium-priority {
    border-left-color: var(--warning-color);
    background-color: rgba(245, 158, 11, 0.1);
}

.recommendation-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.recommendation-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Data Tables */
.data-table {
    background-color: var(--surface-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: rgba(37, 99, 235, 0.1);
    font-weight: 600;
    color: var(--text-primary);
}

.table td {
    color: var(--text-secondary);
}

/* Analysis Headers */
.analysis-header {
    margin-bottom: 2rem;
    text-align: center;
}

.analysis-header h2 {
    font-size: 1.875rem;
    margin-bottom: 0.5rem;
}

.analysis-header p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Symbol Controls */
.symbol-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    align-items: center;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 23, 42, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Bar */
.status-bar {
    background-color: var(--surface-color);
    padding: 0.5rem 2rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-connected {
    color: var(--success-color);
}

.status-disconnected {
    color: var(--danger-color);
}

/* Additional Styles for Enhanced Components */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background-color: var(--surface-color);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.summary-card h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.125rem;
}

.summary-card p {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.recommendations-detailed {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.recommendation-detailed {
    background-color: var(--surface-color);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    border-left: 4px solid var(--primary-color);
}

.recommendation-detailed.high-priority {
    border-left-color: var(--danger-color);
}

.recommendation-detailed.medium-priority {
    border-left-color: var(--warning-color);
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.recommendation-type {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
}

.recommendation-priority {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
}

.recommendation-implementation {
    margin-top: 1rem;
    padding: 1rem;
    background-color: rgba(37, 99, 235, 0.1);
    border-radius: 6px;
    font-size: 0.875rem;
}

.insights-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.insight-item {
    background-color: var(--surface-color);
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
}

.insight-item.high-priority {
    border-left-color: var(--danger-color);
    background-color: rgba(239, 68, 68, 0.1);
}

.insight-item.medium-priority {
    border-left-color: var(--warning-color);
    background-color: rgba(245, 158, 11, 0.1);
}

.insight-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.insight-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.insight-action {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-style: italic;
}

.actions-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-item {
    background-color: var(--surface-color);
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.action-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--success-color);
}

.action-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Table Enhancements */
.table .positive {
    color: var(--success-color);
    font-weight: 600;
}

.table .negative {
    color: var(--danger-color);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .dashboard-nav {
        padding: 0 1rem;
    }

    .nav-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }

    .dashboard-main {
        padding: 1rem;
    }

    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .chart {
        height: 250px;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .recommendation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    /* Chart Analysis Mobile Styles */
    .chart-container {
        flex-direction: column;
    }

    .chart-sidebar {
        width: 100%;
    }

    .chart-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        width: 100%;
    }
}

/* Chart Analysis Styles */
.chart-controls {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--surface-color);
    border-radius: 8px;
    align-items: end;
    border: 1px solid var(--border-color);
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.form-control {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
    background: var(--background-color);
    color: var(--text-primary);
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.chart-container {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-main {
    flex: 1;
    min-height: 500px;
}

.chart-area {
    width: 100%;
    height: 500px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--surface-color);
}

.chart-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-secondary);
    font-style: italic;
}

.chart-sidebar {
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.trade-statistics {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
}

.stats-panel {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 600;
    color: var(--text-primary);
}

.stat-value {
    font-weight: 700;
    color: var(--primary-color);
}

.trade-legend {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.marker {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.entry-marker {
    background-color: var(--success-color);
}

.exit-marker {
    background-color: var(--danger-color);
}

.trade-details {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
}
