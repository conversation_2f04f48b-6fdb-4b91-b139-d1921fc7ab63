"""
Database connector for UltimateDashboard
Integrates with existing SQLite databases to extract comprehensive trade data
"""
import sqlite3
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import (
    ANALYSIS_DB_PATH,
    TRADE_STATES_DB_PATH,
    PROMPT_PERFORMANCE_DB_PATH,
    CANDLE_CACHE_DB_PATH
)

logger = logging.getLogger(__name__)

class DatabaseConnector:
    """Main database connector for UltimateDashboard"""
    
    def __init__(self):
        self.analysis_db_path = ANALYSIS_DB_PATH
        self.trade_states_db_path = TRADE_STATES_DB_PATH
        self.prompt_db_path = PROMPT_PERFORMANCE_DB_PATH
        self.candle_db_path = CANDLE_CACHE_DB_PATH
        
    def get_connection(self, db_path: Path) -> sqlite3.Connection:
        """Get database connection with row factory"""
        conn = sqlite3.connect(str(db_path))
        conn.row_factory = sqlite3.Row
        return conn
    
    def row_to_dict(self, cursor: sqlite3.Cursor, row: sqlite3.Row) -> Dict[str, Any]:
        """Convert SQLite row to dictionary"""
        return {col[0]: row[idx] for idx, col in enumerate(cursor.description)}

    def _execute_query(self, query: str, params: tuple = (), fetch_all: bool = True):
        """Execute a query and return results"""
        try:
            conn = self.get_connection(self.analysis_db_path)
            cursor = conn.cursor()
            cursor.execute(query, params)

            if fetch_all:
                results = cursor.fetchall()
            else:
                results = cursor.fetchone()

            conn.close()
            return results

        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return [] if fetch_all else None
    
    # === ANALYSIS RESULTS QUERIES ===
    
    def get_analysis_results(self, 
                           limit: int = 1000, 
                           symbol: str = None, 
                           timeframe: str = None,
                           start_date: str = None,
                           end_date: str = None) -> List[Dict[str, Any]]:
        """Get analysis results with optional filtering"""
        conn = self.get_connection(self.analysis_db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM analysis_results WHERE 1=1"
        params = []
        
        if symbol:
            query += " AND symbol = ?"
            params.append(symbol)
        
        if timeframe:
            query += " AND timeframe = ?"
            params.append(timeframe)
            
        if start_date:
            query += " AND timestamp >= ?"
            params.append(start_date)
            
        if end_date:
            query += " AND timestamp <= ?"
            params.append(end_date)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        conn.close()
        
        return [self.row_to_dict(cursor, row) for row in rows]
    
    def get_trades(self, 
                   limit: int = 1000, 
                   symbol: str = None, 
                   status: str = None,
                   start_date: str = None,
                   end_date: str = None) -> List[Dict[str, Any]]:
        """Get trades with optional filtering"""
        conn = self.get_connection(self.analysis_db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM trades WHERE 1=1"
        params = []
        
        if symbol:
            query += " AND symbol = ?"
            params.append(symbol)
        
        if status:
            query += " AND status = ?"
            params.append(status)
            
        if start_date:
            query += " AND created_at >= ?"
            params.append(start_date)
            
        if end_date:
            query += " AND created_at <= ?"
            params.append(end_date)
        
        query += " ORDER BY created_at DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        conn.close()
        
        return [self.row_to_dict(cursor, row) for row in rows]
    
    def get_trades_with_analysis(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """Get trades joined with their analysis results"""
        conn = self.get_connection(self.analysis_db_path)
        cursor = conn.cursor()
        
        query = """
        SELECT 
            t.*,
            ar.recommendation,
            ar.confidence,
            ar.analysis_prompt,
            ar.market_condition,
            ar.market_direction,
            ar.rr as analysis_rr,
            ar.timestamp as analysis_timestamp
        FROM trades t
        LEFT JOIN analysis_results ar ON t.recommendation_id = ar.id
        ORDER BY t.created_at DESC
        LIMIT ?
        """
        
        cursor.execute(query, (limit,))
        rows = cursor.fetchall()
        conn.close()
        
        return [self.row_to_dict(cursor, row) for row in rows]
    
    # === TRADING STATISTICS ===
    
    def get_trading_stats(self, symbol: str = None, timeframe: str = None) -> List[Dict[str, Any]]:
        """Get trading statistics"""
        conn = self.get_connection(self.analysis_db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM trading_stats WHERE 1=1"
        params = []
        
        if symbol:
            query += " AND symbol = ?"
            params.append(symbol)
            
        if timeframe:
            query += " AND timeframe = ?"
            params.append(timeframe)
        
        query += " ORDER BY last_updated DESC"
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        conn.close()
        
        return [self.row_to_dict(cursor, row) for row in rows]
    
    # === TRADE STATES ===
    
    def get_trade_states(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """Get trade states from trade_states.db"""
        conn = self.get_connection(self.trade_states_db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM trade_states ORDER BY updated_at DESC LIMIT ?"
        cursor.execute(query, (limit,))
        rows = cursor.fetchall()
        conn.close()
        
        return [self.row_to_dict(cursor, row) for row in rows]
    
    # === PROMPT ANALYSIS ===
    
    def get_prompt_hash_mappings(self) -> List[Dict[str, Any]]:
        """Get prompt hash mappings from prompt performance database"""
        if not self.prompt_db_path.exists():
            logger.warning(f"Prompt database not found: {self.prompt_db_path}")
            return []
            
        conn = self.get_connection(self.prompt_db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM prompt_hash_mappings ORDER BY created_at DESC"
        cursor.execute(query)
        rows = cursor.fetchall()
        conn.close()
        
        return [self.row_to_dict(cursor, row) for row in rows]
    
    # === AGGREGATED ANALYTICS ===
    
    def get_overall_performance(self, days: int = 30) -> Dict[str, Any]:
        """Get overall trading performance metrics"""
        start_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        trades = self.get_trades(start_date=start_date, status='closed')
        
        if not trades:
            return {
                'total_trades': 0,
                'total_pnl': 0.0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'expected_value': 0.0
            }
        
        total_trades = len(trades)
        total_pnl = sum(trade['pnl'] or 0 for trade in trades)
        winning_trades = [t for t in trades if (t['pnl'] or 0) > 0]
        losing_trades = [t for t in trades if (t['pnl'] or 0) < 0]
        
        win_count = len(winning_trades)
        loss_count = len(losing_trades)
        win_rate = win_count / total_trades if total_trades > 0 else 0
        
        total_wins = sum(t['pnl'] for t in winning_trades)
        total_losses = abs(sum(t['pnl'] for t in losing_trades))
        
        avg_win = total_wins / win_count if win_count > 0 else 0
        avg_loss = total_losses / loss_count if loss_count > 0 else 0
        
        profit_factor = total_wins / total_losses if total_losses > 0 else (999.99 if total_wins > 0 else 0)
        expected_value = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
        
        return {
            'total_trades': total_trades,
            'winning_trades': win_count,
            'losing_trades': loss_count,
            'total_pnl': round(total_pnl, 2),
            'win_rate': round(win_rate * 100, 2),
            'profit_factor': round(profit_factor, 2),
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'expected_value': round(expected_value, 2),
            'max_win': max((t['pnl'] or 0 for t in winning_trades), default=0),
            'max_loss': min((t['pnl'] or 0 for t in losing_trades), default=0)
        }
    
    def get_symbol_performance(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get performance metrics by symbol"""
        start_date = (datetime.now() - timedelta(days=days)).isoformat()
        trades = self.get_trades(start_date=start_date, status='closed')
        
        symbol_stats = {}
        
        for trade in trades:
            symbol = trade['symbol']
            pnl = trade['pnl'] or 0
            
            if symbol not in symbol_stats:
                symbol_stats[symbol] = {
                    'symbol': symbol,
                    'trades': [],
                    'total_pnl': 0,
                    'trade_count': 0
                }
            
            symbol_stats[symbol]['trades'].append(pnl)
            symbol_stats[symbol]['total_pnl'] += pnl
            symbol_stats[symbol]['trade_count'] += 1
        
        result = []
        for symbol, stats in symbol_stats.items():
            trades_list = stats['trades']
            winning_trades = [t for t in trades_list if t > 0]
            losing_trades = [t for t in trades_list if t < 0]
            
            win_rate = len(winning_trades) / len(trades_list) if trades_list else 0
            
            result.append({
                'symbol': symbol,
                'total_trades': stats['trade_count'],
                'total_pnl': round(stats['total_pnl'], 2),
                'win_rate': round(win_rate * 100, 2),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'avg_pnl': round(stats['total_pnl'] / stats['trade_count'], 2) if stats['trade_count'] > 0 else 0
            })
        
        return sorted(result, key=lambda x: x['total_pnl'], reverse=True)
    
    def get_unique_symbols(self) -> List[str]:
        """Get list of unique symbols from trades"""
        conn = self.get_connection(self.analysis_db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT DISTINCT symbol FROM trades ORDER BY symbol")
        rows = cursor.fetchall()
        conn.close()
        
        return [row[0] for row in rows]
    
    def get_unique_timeframes(self) -> List[str]:
        """Get list of unique timeframes from analysis results"""
        conn = self.get_connection(self.analysis_db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT DISTINCT timeframe FROM analysis_results ORDER BY timeframe")
        rows = cursor.fetchall()
        conn.close()

        return [row[0] for row in rows]

    def get_unique_prompt_versions(self) -> List[str]:
        """Get list of unique prompt versions from analysis_data JSON"""
        # NOTE: prompt_version was introduced later, so not all records have it
        # TODO: Handle records without prompt_version (older data) - could use timestamp ranges or other identifiers
        try:
            query = """
            SELECT DISTINCT json_extract(analysis_data, '$.analysis.prompt_version') as prompt_version
            FROM analysis_results
            WHERE json_extract(analysis_data, '$.analysis.prompt_version') IS NOT NULL
                AND json_extract(analysis_data, '$.analysis.prompt_version') != ''
            ORDER BY prompt_version
            """

            results = self._execute_query(query, fetch_all=True)
            return [row[0] for row in results if row[0]]

        except Exception as e:
            logger.error(f"Error getting unique prompt versions: {e}")
            return []

    # === PROMPT PERFORMANCE ANALYSIS ===

    def get_prompt_performance(self, days: int = 30) -> List[Dict[str, Any]]:
        """Analyze prompt performance based on trade outcomes using prompt_version from analysis_data"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()

            # Query to get prompt performance using prompt_version from analysis_data JSON
            # NOTE: prompt_version was introduced later, so not all records have it
            # TODO: Handle records without prompt_version (older data) - could use timestamp ranges or other identifiers
            query = """
            SELECT
                json_extract(ar.analysis_data, '$.analysis.prompt_version') as prompt_name,
                COUNT(t.id) as total_trades,
                SUM(CASE WHEN t.pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                SUM(t.pnl) as total_pnl,
                AVG(t.pnl) as avg_pnl,
                AVG(ar.confidence) as avg_confidence,
                ar.market_condition,
                ar.market_direction,
                GROUP_CONCAT(DISTINCT ar.symbol) as symbols
            FROM analysis_results ar
            LEFT JOIN trades t ON ar.id = t.recommendation_id
            WHERE ar.timestamp >= ?
                AND t.status = 'closed'
                AND json_extract(ar.analysis_data, '$.analysis.prompt_version') IS NOT NULL
                AND json_extract(ar.analysis_data, '$.analysis.prompt_version') != ''
            GROUP BY json_extract(ar.analysis_data, '$.analysis.prompt_version')
            HAVING COUNT(t.id) > 0
            ORDER BY total_pnl DESC
            """

            results = self._execute_query(query, (cutoff_date,), fetch_all=True)

            # Process results
            prompt_performance = []
            for row in results:
                win_rate = (row[2] / row[1] * 100) if row[1] > 0 else 0

                # Calculate profit factor safely (avoid division by zero and infinity)
                winning_pnl = row[3] if row[3] > 0 else 0
                losing_pnl = abs(row[3] - winning_pnl) if row[3] < winning_pnl else 1  # Avoid division by zero
                profit_factor = winning_pnl / losing_pnl if losing_pnl > 0 else 0

                # Ensure no infinity or NaN values
                if not isinstance(profit_factor, (int, float)) or profit_factor == float('inf') or profit_factor != profit_factor:
                    profit_factor = 0

                prompt_performance.append({
                    'prompt_name': row[0] or 'Unknown',
                    'prompt_preview': f"Prompt: {row[0]}" if row[0] else 'Unknown Prompt',
                    'total_trades': row[1],
                    'winning_trades': row[2],
                    'losing_trades': row[1] - row[2],
                    'total_pnl': round(row[3], 2),
                    'avg_pnl': round(row[4], 2),
                    'win_rate': round(win_rate, 2),
                    'avg_confidence': round(row[5], 2),
                    'market_condition': row[6],
                    'market_direction': row[7],
                    'symbols': row[8].split(',') if row[8] else [],
                    'profit_factor': round(profit_factor, 2)
                })

            return prompt_performance

        except Exception as e:
            logger.error(f"Error getting prompt performance: {e}")
            return []

        result = []
        for prompt_key, stats in prompt_stats.items():
            if stats['trade_count'] < 3:  # Skip prompts with too few trades
                continue

            trades_list = stats['trades']
            winning_trades = [t for t in trades_list if t > 0]
            losing_trades = [t for t in trades_list if t < 0]

            win_rate = len(winning_trades) / len(trades_list) if trades_list else 0
            avg_confidence = stats['confidence_sum'] / stats['trade_count'] if stats['trade_count'] > 0 else 0

            result.append({
                'prompt_preview': prompt_key,
                'total_trades': stats['trade_count'],
                'total_pnl': round(stats['total_pnl'], 2),
                'win_rate': round(win_rate * 100, 2),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'avg_pnl_per_trade': round(stats['total_pnl'] / stats['trade_count'], 2),
                'avg_confidence': round(avg_confidence, 2),
                'market_conditions': stats['market_conditions']
            })

        return sorted(result, key=lambda x: x['total_pnl'], reverse=True)

    def get_market_condition_performance(self, days: int = 30) -> Dict[str, Any]:
        """Analyze performance by market conditions"""
        start_date = (datetime.now() - timedelta(days=days)).isoformat()
        trades_with_analysis = self.get_trades_with_analysis(limit=10000)

        # Filter by date and closed status
        filtered_trades = [
            t for t in trades_with_analysis
            if t.get('created_at') and t['created_at'] >= start_date and t.get('status') == 'closed'
        ]

        condition_stats = {}

        for trade in filtered_trades:
            market_condition = trade.get('market_condition', 'UNKNOWN')
            market_direction = trade.get('market_direction', 'UNKNOWN')
            pnl = trade.get('pnl', 0) or 0

            # Combine condition and direction for more granular analysis
            condition_key = f"{market_condition}_{market_direction}"

            if condition_key not in condition_stats:
                condition_stats[condition_key] = {
                    'condition': market_condition,
                    'direction': market_direction,
                    'trades': [],
                    'total_pnl': 0,
                    'trade_count': 0
                }

            condition_stats[condition_key]['trades'].append(pnl)
            condition_stats[condition_key]['total_pnl'] += pnl
            condition_stats[condition_key]['trade_count'] += 1

        result = {}
        for condition_key, stats in condition_stats.items():
            trades_list = stats['trades']
            winning_trades = [t for t in trades_list if t > 0]

            win_rate = len(winning_trades) / len(trades_list) if trades_list else 0

            result[condition_key] = {
                'condition': stats['condition'],
                'direction': stats['direction'],
                'total_trades': stats['trade_count'],
                'total_pnl': round(stats['total_pnl'], 2),
                'win_rate': round(win_rate * 100, 2),
                'winning_trades': len(winning_trades),
                'losing_trades': len(trades_list) - len(winning_trades),
                'avg_pnl_per_trade': round(stats['total_pnl'] / stats['trade_count'], 2) if stats['trade_count'] > 0 else 0
            }

        return result

    def get_confidence_analysis(self, days: int = 30) -> List[Dict[str, Any]]:
        """Analyze performance by confidence levels"""
        start_date = (datetime.now() - timedelta(days=days)).isoformat()
        trades_with_analysis = self.get_trades_with_analysis(limit=10000)

        # Filter by date and closed status
        filtered_trades = [
            t for t in trades_with_analysis
            if t.get('created_at') and t['created_at'] >= start_date and t.get('status') == 'closed'
            and t.get('confidence') is not None
        ]

        # Define confidence buckets
        confidence_buckets = [
            (0.0, 0.3, 'Low'),
            (0.3, 0.5, 'Medium-Low'),
            (0.5, 0.7, 'Medium'),
            (0.7, 0.9, 'High'),
            (0.9, 1.0, 'Very High')
        ]

        bucket_stats = {}

        for trade in filtered_trades:
            confidence = trade.get('confidence', 0)
            pnl = trade.get('pnl', 0) or 0

            # Find appropriate bucket
            bucket_key = None
            for min_conf, max_conf, label in confidence_buckets:
                if min_conf <= confidence <= max_conf:
                    bucket_key = label
                    break

            if not bucket_key:
                continue

            if bucket_key not in bucket_stats:
                bucket_stats[bucket_key] = {
                    'confidence_range': bucket_key,
                    'trades': [],
                    'total_pnl': 0,
                    'trade_count': 0
                }

            bucket_stats[bucket_key]['trades'].append(pnl)
            bucket_stats[bucket_key]['total_pnl'] += pnl
            bucket_stats[bucket_key]['trade_count'] += 1

        result = []
        for bucket_key, stats in bucket_stats.items():
            trades_list = stats['trades']
            winning_trades = [t for t in trades_list if t > 0]

            win_rate = len(winning_trades) / len(trades_list) if trades_list else 0

            result.append({
                'confidence_range': bucket_key,
                'total_trades': stats['trade_count'],
                'total_pnl': round(stats['total_pnl'], 2),
                'win_rate': round(win_rate * 100, 2),
                'winning_trades': len(winning_trades),
                'losing_trades': len(trades_list) - len(winning_trades),
                'avg_pnl_per_trade': round(stats['total_pnl'] / stats['trade_count'], 2) if stats['trade_count'] > 0 else 0
            })

        return result

    # === CHART ANALYSIS METHODS ===

    def get_trades_for_symbol(self, symbol: str, days: int = 30) -> List[Dict]:
        """Get all trades for a specific symbol within date range"""
        try:
            query = """
            SELECT
                id, recommendation_id, symbol, side, quantity, entry_price,
                take_profit, stop_loss, order_id, pnl, status, avg_exit_price,
                closed_size, created_at, updated_at, confidence, timeframe
            FROM trades
            WHERE symbol = ?
                AND created_at >= datetime('now', '-{} days')
            ORDER BY created_at DESC
            """.format(days)

            results = self._execute_query(query, (symbol,))
            # Convert rows to dictionaries
            trades = []
            for row in results:
                trade_dict = {
                    'id': row[0],
                    'recommendation_id': row[1],
                    'symbol': row[2],
                    'side': row[3],
                    'quantity': row[4],
                    'entry_price': row[5],
                    'take_profit': row[6],
                    'stop_loss': row[7],
                    'order_id': row[8],
                    'pnl': row[9],
                    'status': row[10],
                    'avg_exit_price': row[11],
                    'closed_size': row[12],
                    'created_at': row[13],
                    'updated_at': row[14],
                    'confidence': row[15],
                    'timeframe': row[16]
                }
                trades.append(trade_dict)
            return trades

        except Exception as e:
            logger.error(f"Error getting trades for symbol {symbol}: {e}")
            return []

    def get_symbols_with_trades(self) -> List[str]:
        """Get list of symbols that have trades"""
        try:
            query = """
            SELECT DISTINCT symbol
            FROM trades
            ORDER BY symbol
            """

            results = self._execute_query(query)
            return [row[0] for row in results]

        except Exception as e:
            logger.error(f"Error getting symbols with trades: {e}")
            return []
