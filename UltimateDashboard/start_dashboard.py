#!/usr/bin/env python3
"""
UltimateDashboard Startup Script
Comprehensive startup with validation, testing, and monitoring
"""
import sys
import os
import subprocess
import time
import webbrowser
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """Print startup banner"""
    print("=" * 70)
    print("🚀 UltimateDashboard - Trading Analytics Platform")
    print("=" * 70)
    print("📊 Comprehensive trading performance analysis")
    print("🤖 AI-powered prompt optimization")
    print("📈 Real-time market condition monitoring")
    print("🎯 Advanced filtering and insights")
    print("=" * 70)
    print(f"Starting at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def check_dependencies():
    """Check if all dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'flask', 'flask-cors', 'pandas', 'numpy', 
        'requests', 'pybit', 'plotly', 'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ Dependencies installed successfully!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies. Please install manually:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    print("✅ All dependencies satisfied!")
    return True

def validate_environment():
    """Validate environment and configuration"""
    print("\n🔧 Validating environment...")
    
    # Check .env file
    env_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
    if os.path.exists(env_file):
        print("  ✅ .env file found")
    else:
        print("  ⚠️  .env file not found - some features may not work")
    
    # Check database files
    db_files = [
        'trading_bot/data/analysis_results.db',
        'trading_bot/data/trade_states.db'
    ]
    
    for db_file in db_files:
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), db_file)
        if os.path.exists(db_path):
            print(f"  ✅ Database found: {db_file}")
        else:
            print(f"  ⚠️  Database not found: {db_file}")
    
    # Check project structure
    required_dirs = ['database', 'analytics', 'api', 'static', 'templates']
    for dir_name in required_dirs:
        dir_path = os.path.join(os.path.dirname(__file__), dir_name)
        if os.path.exists(dir_path):
            print(f"  ✅ {dir_name}/ directory")
        else:
            print(f"  ❌ {dir_name}/ directory missing")
            return False
    
    print("✅ Environment validation complete!")
    return True

def run_quick_tests():
    """Run quick validation tests"""
    print("\n🧪 Running quick validation tests...")
    
    try:
        # Test database connection
        from database.connector import DatabaseConnector
        db = DatabaseConnector()
        print("  ✅ Database connector")
        
        # Test analytics engine
        from analytics.core_engine import CoreAnalyticsEngine
        analytics = CoreAnalyticsEngine()
        print("  ✅ Analytics engine")
        
        # Test API client (may fail if no credentials)
        try:
            from api.bybit_client import BybitClient
            api_client = BybitClient()
            print("  ✅ API client")
        except Exception:
            print("  ⚠️  API client (credentials may be missing)")
        
        print("✅ Quick tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

def start_dashboard():
    """Start the dashboard application"""
    print("\n🚀 Starting UltimateDashboard...")
    
    try:
        # Import and configure the app
        from app import app, real_time_monitor
        from config import DASHBOARD_HOST, DASHBOARD_PORT, DEBUG_MODE
        
        # Start real-time monitoring
        print("  📡 Starting real-time monitoring...")
        real_time_monitor.start_monitoring()
        
        # Print startup info
        dashboard_url = f"http://{DASHBOARD_HOST}:{DASHBOARD_PORT}"
        print(f"  🌐 Dashboard URL: {dashboard_url}")
        print(f"  🔧 Debug mode: {DEBUG_MODE}")
        print()
        
        print("=" * 70)
        print("🎉 UltimateDashboard is starting!")
        print("=" * 70)
        print(f"📱 Open your browser to: {dashboard_url}")
        print("🛑 Press Ctrl+C to stop the dashboard")
        print("=" * 70)
        print()
        
        # Auto-open browser after a short delay
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open(dashboard_url)
                print(f"🌐 Opened browser to {dashboard_url}")
            except Exception:
                pass
        
        import threading
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # Start the Flask app
        app.run(
            host=DASHBOARD_HOST,
            port=DASHBOARD_PORT,
            debug=DEBUG_MODE,
            use_reloader=False  # Disable reloader to prevent double startup
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 Dashboard stopped by user")
        return True
    except Exception as e:
        print(f"\n❌ Failed to start dashboard: {e}")
        return False

def main():
    """Main startup function"""
    print_banner()
    
    # Step 1: Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return False
    
    # Step 2: Validate environment
    if not validate_environment():
        print("\n❌ Environment validation failed. Please check project structure.")
        return False
    
    # Step 3: Run quick tests
    if not run_quick_tests():
        print("\n⚠️  Some tests failed, but continuing anyway...")
    
    # Step 4: Start dashboard
    print("\n" + "=" * 70)
    print("🚀 LAUNCHING ULTIMATEDASHBOARD")
    print("=" * 70)
    
    success = start_dashboard()
    
    if success:
        print("\n✅ Dashboard shutdown complete")
    else:
        print("\n❌ Dashboard encountered errors")
    
    return success

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Startup interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
