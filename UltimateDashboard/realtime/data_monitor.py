"""
Real-time Data Monitor for UltimateDashboard
Monitors database changes and provides real-time updates
"""
import logging
import time
import threading
from typing import Dict, Any, List, Callable, Optional
from datetime import datetime, timedelta
import sqlite3
from collections import defaultdict

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connector import DatabaseConnector
from api.bybit_client import BybitClient

logger = logging.getLogger(__name__)

class RealTimeDataMonitor:
    """Monitor for real-time data updates and synchronization"""
    
    def __init__(self, update_interval: int = 30):
        self.db = DatabaseConnector()
        self.api_client = None
        self.update_interval = update_interval
        self.is_monitoring = False
        self.monitor_thread = None
        self.subscribers = defaultdict(list)
        self.last_update_times = {}
        
        # Initialize API client if available
        try:
            self.api_client = BybitClient()
            logger.info("Real-time monitor initialized with API client")
        except Exception as e:
            logger.warning(f"API client not available for real-time monitoring: {e}")
    
    def start_monitoring(self):
        """Start real-time monitoring"""
        if self.is_monitoring:
            logger.warning("Monitoring already started")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("Real-time monitoring started")
    
    def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Real-time monitoring stopped")
    
    def subscribe(self, event_type: str, callback: Callable):
        """Subscribe to real-time updates"""
        self.subscribers[event_type].append(callback)
        logger.info(f"Subscribed to {event_type} events")
    
    def unsubscribe(self, event_type: str, callback: Callable):
        """Unsubscribe from real-time updates"""
        if callback in self.subscribers[event_type]:
            self.subscribers[event_type].remove(callback)
            logger.info(f"Unsubscribed from {event_type} events")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Check for database updates
                self._check_database_updates()
                
                # Check for new exchange data
                if self.api_client:
                    self._check_exchange_updates()
                
                # Sleep until next check
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.update_interval)
    
    def _check_database_updates(self):
        """Check for new database entries"""
        try:
            current_time = datetime.now()
            
            # Check for new trades
            new_trades = self._get_new_trades()
            if new_trades:
                self._notify_subscribers('new_trades', {
                    'trades': new_trades,
                    'count': len(new_trades),
                    'timestamp': current_time.isoformat()
                })
            
            # Check for new analysis results
            new_analysis = self._get_new_analysis()
            if new_analysis:
                self._notify_subscribers('new_analysis', {
                    'analysis': new_analysis,
                    'count': len(new_analysis),
                    'timestamp': current_time.isoformat()
                })
            
            # Update last check time
            self.last_update_times['database'] = current_time
            
        except Exception as e:
            logger.error(f"Error checking database updates: {e}")
    
    def _check_exchange_updates(self):
        """Check for new exchange data"""
        try:
            current_time = datetime.now()
            
            # Get recent executions from exchange
            recent_executions = self.api_client.get_executions(limit=50)
            
            if recent_executions:
                # Filter for truly new executions
                last_check = self.last_update_times.get('exchange', current_time - timedelta(minutes=5))
                new_executions = []
                
                for execution in recent_executions:
                    exec_time_str = execution.get('execTime')
                    if exec_time_str:
                        try:
                            exec_time = datetime.fromtimestamp(int(exec_time_str) / 1000)
                            if exec_time > last_check:
                                new_executions.append(execution)
                        except Exception:
                            continue
                
                if new_executions:
                    self._notify_subscribers('new_executions', {
                        'executions': new_executions,
                        'count': len(new_executions),
                        'timestamp': current_time.isoformat()
                    })
            
            # Update last check time
            self.last_update_times['exchange'] = current_time
            
        except Exception as e:
            logger.error(f"Error checking exchange updates: {e}")
    
    def _get_new_trades(self) -> List[Dict[str, Any]]:
        """Get new trades since last check"""
        try:
            last_check = self.last_update_times.get('trades', datetime.now() - timedelta(minutes=5))
            
            # Get trades created after last check
            trades = self.db.get_trades(limit=100)
            new_trades = []
            
            for trade in trades:
                created_at_str = trade.get('created_at')
                if created_at_str:
                    try:
                        created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
                        if created_at.replace(tzinfo=None) > last_check:
                            new_trades.append(trade)
                    except Exception:
                        continue
            
            self.last_update_times['trades'] = datetime.now()
            return new_trades
            
        except Exception as e:
            logger.error(f"Error getting new trades: {e}")
            return []
    
    def _get_new_analysis(self) -> List[Dict[str, Any]]:
        """Get new analysis results since last check"""
        try:
            last_check = self.last_update_times.get('analysis', datetime.now() - timedelta(minutes=5))
            
            # Get analysis results created after last check
            analysis_results = self.db.get_analysis_results(limit=100)
            new_analysis = []
            
            for analysis in analysis_results:
                timestamp_str = analysis.get('timestamp')
                if timestamp_str:
                    try:
                        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        if timestamp.replace(tzinfo=None) > last_check:
                            new_analysis.append(analysis)
                    except Exception:
                        continue
            
            self.last_update_times['analysis'] = datetime.now()
            return new_analysis
            
        except Exception as e:
            logger.error(f"Error getting new analysis: {e}")
            return []
    
    def _notify_subscribers(self, event_type: str, data: Dict[str, Any]):
        """Notify all subscribers of an event"""
        try:
            for callback in self.subscribers[event_type]:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"Error in subscriber callback for {event_type}: {e}")
        except Exception as e:
            logger.error(f"Error notifying subscribers for {event_type}: {e}")
    
    def get_real_time_summary(self) -> Dict[str, Any]:
        """Get real-time summary of current status"""
        try:
            current_time = datetime.now()
            
            # Get recent trades (last hour)
            recent_trades = self.db.get_trades(
                start_date=(current_time - timedelta(hours=1)).isoformat(),
                limit=100
            )
            
            # Get recent analysis (last hour)
            recent_analysis = self.db.get_analysis_results(
                start_date=(current_time - timedelta(hours=1)).isoformat(),
                limit=100
            )
            
            # Calculate real-time metrics
            recent_pnl = sum(trade.get('pnl', 0) or 0 for trade in recent_trades)
            recent_trade_count = len(recent_trades)
            recent_analysis_count = len(recent_analysis)
            
            # Get exchange status
            exchange_status = "connected" if self.api_client else "disconnected"
            
            return {
                "timestamp": current_time.isoformat(),
                "monitoring_status": "active" if self.is_monitoring else "inactive",
                "exchange_status": exchange_status,
                "recent_metrics": {
                    "trades_last_hour": recent_trade_count,
                    "analysis_last_hour": recent_analysis_count,
                    "pnl_last_hour": round(recent_pnl, 2)
                },
                "last_updates": {
                    "database": self.last_update_times.get('database', 'Never').isoformat() if isinstance(self.last_update_times.get('database'), datetime) else 'Never',
                    "exchange": self.last_update_times.get('exchange', 'Never').isoformat() if isinstance(self.last_update_times.get('exchange'), datetime) else 'Never'
                },
                "subscriber_counts": {event_type: len(callbacks) for event_type, callbacks in self.subscribers.items()}
            }
            
        except Exception as e:
            logger.error(f"Error getting real-time summary: {e}")
            return {"error": str(e)}
    
    def force_sync(self) -> Dict[str, Any]:
        """Force synchronization with exchange data"""
        try:
            if not self.api_client:
                return {"error": "API client not available"}
            
            # Get recent database trades
            recent_trades = self.db.get_trades(status='closed', limit=100)
            
            # Validate against exchange
            validation_results = self.api_client.validate_trade_data(recent_trades)
            
            # Notify subscribers of sync results
            self._notify_subscribers('sync_complete', {
                'validation_results': validation_results,
                'timestamp': datetime.now().isoformat()
            })
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error in force sync: {e}")
            return {"error": str(e)}

# Global monitor instance
_monitor_instance = None

def get_monitor() -> RealTimeDataMonitor:
    """Get the global monitor instance"""
    global _monitor_instance
    if _monitor_instance is None:
        _monitor_instance = RealTimeDataMonitor()
    return _monitor_instance
