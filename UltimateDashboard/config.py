"""
Configuration file for UltimateDashboard
"""
import os
from pathlib import Path

# Base paths
PROJECT_ROOT = Path(__file__).parent.parent
DASHBOARD_ROOT = Path(__file__).parent

# Database paths
ANALYSIS_DB_PATH = PROJECT_ROOT / "trading_bot" / "data" / "analysis_results.db"
TRADE_STATES_DB_PATH = PROJECT_ROOT / "trading_bot" / "data" / "trade_states.db"
PROMPT_PERFORMANCE_DB_PATH = PROJECT_ROOT / "prompt_performance" / "ab_tests.db"
CANDLE_CACHE_DB_PATH = PROJECT_ROOT / "prompt_performance" / "candle_cache.db"

# API Configuration
BYBIT_API_KEY = os.getenv('BYBIT_API_KEY')
BYBIT_API_SECRET = os.getenv('BYBIT_API_SECRET')
BYBIT_TESTNET = False  # Use live data for analysis

# Dashboard Configuration
DASHBOARD_HOST = "127.0.0.1"
DASHBOARD_PORT = 8080
DEBUG_MODE = True

# Data refresh intervals (in seconds)
REAL_TIME_REFRESH_INTERVAL = 30
HISTORICAL_DATA_REFRESH_INTERVAL = 300

# Analytics Configuration
DEFAULT_LOOKBACK_DAYS = 30
MAX_TRADES_PER_QUERY = 1000
MIN_CONFIDENCE_THRESHOLD = 0.5
MIN_RISK_REWARD_RATIO = 1.5

# Market Condition Classifications
MARKET_CONDITIONS = {
    'TRENDING': ['trending_strong', 'trending_moderate'],
    'RANGING': ['ranging', 'volatile'],
    'BULLISH': ['UP'],
    'BEARISH': ['DOWN'],
    'SIDEWAYS': ['SIDEWAYS']
}

# Prompt Performance Analysis
PROMPT_ANALYSIS_CONFIG = {
    'min_trades_for_analysis': 5,
    'confidence_buckets': [(0.0, 0.3), (0.3, 0.5), (0.5, 0.7), (0.7, 0.9), (0.9, 1.0)],
    'market_condition_buckets': ['TRENDING', 'RANGING'],
    'timeframe_analysis': ['1h', '4h', '1d']
}

# Chart Configuration
CHART_COLORS = {
    'profit': '#00C851',
    'loss': '#FF4444',
    'neutral': '#33B5E5',
    'background': '#1a1a1a',
    'text': '#ffffff',
    'grid': '#333333'
}

# Performance Metrics
PERFORMANCE_METRICS = [
    'total_pnl',
    'win_rate',
    'profit_factor',
    'expected_value',
    'avg_win',
    'avg_loss',
    'max_win',
    'max_loss',
    'total_trades',
    'winning_trades',
    'losing_trades'
]

# Risk Management Thresholds
RISK_THRESHOLDS = {
    'max_drawdown_warning': 0.15,  # 15%
    'max_drawdown_critical': 0.25,  # 25%
    'min_win_rate_warning': 0.40,   # 40%
    'min_win_rate_critical': 0.30,  # 30%
    'min_profit_factor_warning': 1.2,
    'min_profit_factor_critical': 1.0
}

# Export Configuration
EXPORT_FORMATS = ['csv', 'json', 'xlsx']
EXPORT_PATH = DASHBOARD_ROOT / "exports"

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FILE = DASHBOARD_ROOT / "logs" / "dashboard.log"

# Ensure required directories exist
os.makedirs(DASHBOARD_ROOT / "logs", exist_ok=True)
os.makedirs(EXPORT_PATH, exist_ok=True)
