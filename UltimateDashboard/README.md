# UltimateDashboard

A comprehensive trading analytics dashboard for analyzing runn_autotrader bot performance, identifying profitable patterns, and optimizing trading prompts based on market conditions and symbols.

## Features

### Core Analytics
- **Profitability Analysis**: Overall P&L, win rates, risk/reward ratios, expected value
- **Prompt Performance**: Compare different prompts and their effectiveness
- **Market Condition Insights**: Performance breakdown by market conditions
- **Symbol-Specific Metrics**: Detailed analysis per trading pair

### Data Sources
- **Database Integration**: SQLite databases (analysis_results.db, trade_states.db)
- **Real Exchange Data**: Live trade history from Bybit API
- **Prompt Analysis**: Historical prompt performance data

### Key Metrics
- Overall bot P&L (profit/loss)
- Win rate percentage
- Average profit per winning trade
- Average loss per losing trade
- Risk/reward ratios
- Expected value (EV)
- Prompt effectiveness by market condition
- Symbol-specific performance

### Dashboard Features
- Interactive charts and visualizations
- Real-time data updates
- Advanced filtering capabilities
- Export functionality
- Optimization recommendations

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. **Quick Start Options**:

### Option A: Automated Startup (Recommended)
```bash
python UltimateDashboard/start_dashboard.py
```
This script will:
- Check and install dependencies
- Validate environment
- Run quick tests
- Start the dashboard
- Auto-open browser

### Option B: Manual Startup
```bash
# Install dependencies
pip install -r UltimateDashboard/requirements.txt

# Run tests (optional)
python UltimateDashboard/run_tests.py

# Start dashboard
python UltimateDashboard/app.py
```

3. **Environment Variables** (optional - already configured in project .env):
```
BYBIT_API_KEY=your_api_key
BYBIT_API_SECRET=your_api_secret
```

4. **Access Dashboard**: http://127.0.0.1:8080

## Project Structure

```
UltimateDashboard/
├── app.py                 # Main Flask application
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── database/              # Database integration layer
├── analytics/             # Core analytics engine
├── api/                   # Exchange API integration
├── static/                # CSS, JavaScript, images
├── templates/             # HTML templates
├── exports/               # Data export files
└── logs/                  # Application logs
```

## Usage

### Dashboard Navigation
- **Overview**: High-level performance metrics
- **Prompt Analysis**: Detailed prompt performance comparison
- **Market Conditions**: Performance by market regime
- **Symbol Analysis**: Per-symbol trading metrics
- **Optimization**: Recommendations for improvement

### Filtering Options
- Date ranges
- Symbols/trading pairs
- Prompt types
- Market conditions
- Confidence levels
- Risk/reward ratios

### Export Features
- CSV, JSON, Excel formats
- Filtered data export
- Performance reports
- Optimization recommendations

## Technical Details

### Database Schema
- `analysis_results`: AI analysis and recommendations
- `trades`: Trade execution and outcomes
- `trade_states`: Trade state management
- `trading_stats`: Aggregated performance metrics

### API Integration
- Bybit `get_executions` endpoint for trade history
- Real-time data validation against database
- Market data for context analysis

### Analytics Engine
- P&L calculations
- Statistical analysis
- Prompt correlation analysis
- Market condition classification
- Performance optimization algorithms
