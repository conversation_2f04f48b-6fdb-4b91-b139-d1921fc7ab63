"""
Bybit API client for UltimateDashboard
Fetches real-time trade history and market data for validation and analysis
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import time

try:
    from pybit.unified_trading import HTTP
except ImportError:
    HTTP = None

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import BYBIT_API_KEY, BYBIT_API_SECRET, BYBIT_TESTNET

logger = logging.getLogger(__name__)

class BybitClient:
    """Bybit API client for fetching trade history and market data"""
    
    def __init__(self):
        if HTTP is None:
            raise ImportError("pybit library not installed. Run: pip install pybit")
            
        self.api_key = BYBIT_API_KEY
        self.api_secret = BYBIT_API_SECRET
        self.testnet = BYBIT_TESTNET
        
        if not self.api_key or not self.api_secret:
            raise ValueError("Bybit API credentials not found in environment variables")
        
        self.session = HTTP(
            testnet=self.testnet,
            api_key=self.api_key,
            api_secret=self.api_secret,
            recv_window=60000,
            timeout=30
        )
        
        logger.info(f"Bybit client initialized (testnet: {self.testnet})")
    
    def test_connection(self) -> Dict[str, Any]:
        """Test API connection"""
        try:
            response = self.session.get_server_time()
            if response.get("retCode") == 0:
                return {"connected": True, "server_time": response.get("result", {})}
            else:
                return {"connected": False, "error": response.get("retMsg", "Unknown error")}
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return {"connected": False, "error": str(e)}
    
    def get_executions(self, 
                      symbol: str = None,
                      start_time: int = None,
                      end_time: int = None,
                      limit: int = 100) -> Dict[str, Any]:
        """Get execution/trade history"""
        try:
            params = {
                "category": "linear",
                "limit": min(limit, 100)  # Bybit max limit is 100
            }
            
            if symbol:
                params["symbol"] = symbol
            if start_time:
                params["startTime"] = start_time
            if end_time:
                params["endTime"] = end_time
            
            response = self.session.get_executions(**params)
            
            if response.get("retCode") == 0:
                return response
            else:
                logger.error(f"Failed to get executions: {response.get('retMsg', 'Unknown error')}")
                return {"error": response.get("retMsg", "Unknown error")}
                
        except Exception as e:
            logger.error(f"Error getting executions: {e}")
            return {"error": str(e)}
    
    def get_execution_history(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get execution history for specified number of days (max 7 days per request)"""
        try:
            all_executions = []
            
            # Bybit API limit is 7 days, so fetch in chunks
            chunk_size = min(days, 7)
            for i in range(0, days, chunk_size):
                current_chunk = min(chunk_size, days - i)
                end_time = datetime.now() - timedelta(days=i)
                start_time = end_time - timedelta(days=current_chunk)
                
                start_time_ms = int(start_time.timestamp() * 1000)
                end_time_ms = int(end_time.timestamp() * 1000)
                
                logger.info(f"Fetching executions chunk: {start_time} to {end_time}")
                
                cursor = None
                chunk_executions = []
                
                while True:
                    params = {
                        "startTime": start_time_ms,
                        "endTime": end_time_ms,
                        "limit": 100
                    }
                    
                    if cursor:
                        params["cursor"] = cursor
                    
                    response = self.get_executions(**params)
                    
                    if "error" in response:
                        logger.error(f"Failed to get executions for chunk: {response['error']}")
                        break
                    
                    executions = response.get("result", {}).get("list", [])
                    if not executions:
                        break
                    
                    chunk_executions.extend(executions)
                    logger.debug(f"Fetched {len(executions)} executions for chunk (total: {len(chunk_executions)})")
                    
                    next_cursor = response.get("result", {}).get("nextPageCursor")
                    if not next_cursor or cursor == next_cursor:
                        break
                    
                    cursor = next_cursor
                    time.sleep(0.1)  # Rate limiting
                
                all_executions.extend(chunk_executions)
                logger.info(f"Chunk complete: {len(chunk_executions)} executions")
                
                if i + chunk_size < days:
                    time.sleep(1)  # Rate limiting between chunks
            
            logger.info(f"Total executions fetched: {len(all_executions)}")
            return all_executions
            
        except Exception as e:
            logger.error(f"Error getting execution history: {e}")
            return []
    
    def get_order_history(self, 
                         symbol: str = None,
                         limit: int = 50) -> Dict[str, Any]:
        """Get order history"""
        try:
            params = {
                "category": "linear",
                "limit": min(limit, 50)
            }
            
            if symbol:
                params["symbol"] = symbol
            
            response = self.session.get_order_history(**params)
            
            if response.get("retCode") == 0:
                return response
            else:
                logger.error(f"Failed to get order history: {response.get('retMsg', 'Unknown error')}")
                return {"error": response.get("retMsg", "Unknown error")}
                
        except Exception as e:
            logger.error(f"Error getting order history: {e}")
            return {"error": str(e)}
    
    def get_closed_pnl(self, 
                      symbol: str = None,
                      limit: int = 100) -> Dict[str, Any]:
        """Get closed P&L records"""
        try:
            params = {
                "category": "linear",
                "limit": min(limit, 100)
            }
            
            if symbol:
                params["symbol"] = symbol
            
            response = self.session.get_closed_pnl(**params)
            
            if response.get("retCode") == 0:
                return response
            else:
                logger.error(f"Failed to get closed PnL: {response.get('retMsg', 'Unknown error')}")
                return {"error": response.get("retMsg", "Unknown error")}
                
        except Exception as e:
            logger.error(f"Error getting closed PnL: {e}")
            return {"error": str(e)}
    
    def get_positions(self, symbol: str = None) -> Dict[str, Any]:
        """Get current positions"""
        try:
            params = {
                "category": "linear",
                "settleCoin": "USDT"
            }
            
            if symbol:
                params["symbol"] = symbol
            
            response = self.session.get_positions(**params)
            
            if response.get("retCode") == 0:
                return response
            else:
                logger.error(f"Failed to get positions: {response.get('retMsg', 'Unknown error')}")
                return {"error": response.get("retMsg", "Unknown error")}
                
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return {"error": str(e)}
    
    def validate_trade_data(self, db_trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate database trade data against exchange records"""
        try:
            # Get recent executions from exchange
            exchange_executions = self.get_execution_history(days=7)
            
            if not exchange_executions:
                return {"error": "No exchange data available for validation"}
            
            # Create lookup by order ID
            exchange_lookup = {}
            for execution in exchange_executions:
                order_id = execution.get("orderId")
                if order_id:
                    if order_id not in exchange_lookup:
                        exchange_lookup[order_id] = []
                    exchange_lookup[order_id].append(execution)
            
            validation_results = {
                "total_db_trades": len(db_trades),
                "total_exchange_executions": len(exchange_executions),
                "matched_trades": 0,
                "unmatched_trades": 0,
                "discrepancies": []
            }
            
            for trade in db_trades:
                order_id = trade.get("order_id")
                if not order_id:
                    validation_results["unmatched_trades"] += 1
                    continue
                
                if order_id in exchange_lookup:
                    validation_results["matched_trades"] += 1
                    
                    # Check for discrepancies
                    exchange_data = exchange_lookup[order_id][0]  # Take first execution
                    
                    db_symbol = trade.get("symbol")
                    exchange_symbol = exchange_data.get("symbol")
                    
                    if db_symbol != exchange_symbol:
                        validation_results["discrepancies"].append({
                            "order_id": order_id,
                            "type": "symbol_mismatch",
                            "db_value": db_symbol,
                            "exchange_value": exchange_symbol
                        })
                else:
                    validation_results["unmatched_trades"] += 1
            
            validation_results["match_rate"] = (
                validation_results["matched_trades"] / validation_results["total_db_trades"] * 100
                if validation_results["total_db_trades"] > 0 else 0
            )
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating trade data: {e}")
            return {"error": str(e)}

    def get_kline_data(self,
                      symbol: str,
                      interval: str = "60",
                      start: int = None,
                      end: int = None,
                      limit: int = 200) -> List[List]:
        """Get candlestick/kline data for a symbol

        Returns data in format: [timestamp, open, high, low, close, volume]
        """
        try:
            # Convert interval format: 1h -> 60, 4h -> 240, 1d -> D
            interval_map = {
                "1h": "60",
                "4h": "240",
                "1d": "D"
            }
            api_interval = interval_map.get(interval, interval)

            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": api_interval,
                "limit": limit
            }

            if start:
                params["start"] = start
            if end:
                params["end"] = end

            logger.info(f"Requesting kline data with params: {params}")
            response = self.session.get_kline(**params)
            logger.info(f"Kline response retCode: {response.get('retCode')}, retMsg: {response.get('retMsg')}")

            if response.get("retCode") == 0:
                # Bybit returns: [startTime, openPrice, highPrice, lowPrice, closePrice, volume, turnover]
                # We need: [timestamp, open, high, low, close, volume]
                raw_klines = response.get("result", {}).get("list", [])

                # Convert to the format expected by Plotly
                formatted_klines = []
                for kline in raw_klines:
                    if len(kline) >= 6:
                        formatted_klines.append([
                            int(kline[0]),      # timestamp (convert to int)
                            float(kline[1]),    # open
                            float(kline[2]),    # high
                            float(kline[3]),    # low
                            float(kline[4]),    # close
                            float(kline[5])     # volume
                        ])

                logger.info(f"Retrieved {len(formatted_klines)} klines, formatted for chart")
                return formatted_klines
            else:
                logger.error(f"Error getting kline data: {response.get('retMsg', 'Unknown error')}")
                return []

        except Exception as e:
            logger.error(f"Error getting kline data: {e}")
            return []
