"""
Market Condition Classifier for UltimateDashboard
Classifies market conditions and correlates with trading performance
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import statistics

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connector import DatabaseConnector

logger = logging.getLogger(__name__)

class MarketConditionClassifier:
    """Classifies market conditions and analyzes performance correlation"""
    
    def __init__(self):
        self.db = DatabaseConnector()
        
        # Market condition mappings
        self.condition_mappings = {
            'TRENDING': ['trending_strong', 'trending_moderate'],
            'RANGING': ['ranging', 'volatile'],
            'BULLISH': ['UP'],
            'BEARISH': ['DOWN'],
            'SIDEWAYS': ['SIDEWAYS']
        }
    
    def analyze_market_condition_performance(self, days: int = 30) -> Dict[str, Any]:
        """Analyze trading performance by market conditions"""
        try:
            # Get market condition performance from database
            market_performance = self.db.get_market_condition_performance(days)
            
            if not market_performance:
                return {"error": "No market condition data found"}
            
            # Classify and aggregate performance
            classified_performance = self._classify_market_performance(market_performance)
            
            # Calculate market regime statistics
            regime_stats = self._calculate_regime_statistics(classified_performance)
            
            # Identify optimal market conditions
            optimal_conditions = self._identify_optimal_conditions(classified_performance)
            
            # Generate market-based insights
            insights = self._generate_market_insights(classified_performance, regime_stats)
            
            return {
                "market_performance": classified_performance,
                "regime_statistics": regime_stats,
                "optimal_conditions": optimal_conditions,
                "market_insights": insights,
                "analysis_period_days": days,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing market condition performance: {e}")
            return {"error": str(e)}
    
    def _classify_market_performance(self, market_performance: Dict[str, Any]) -> Dict[str, Any]:
        """Classify market performance into broader categories"""
        classified = defaultdict(lambda: {
            'total_trades': 0,
            'total_pnl': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'subconditions': {}
        })
        
        for condition_key, performance in market_performance.items():
            condition = performance.get('condition', 'UNKNOWN')
            direction = performance.get('direction', 'UNKNOWN')
            
            # Map to broader categories
            broader_category = self._map_to_broader_category(condition, direction)
            
            # Aggregate performance
            classified[broader_category]['total_trades'] += performance['total_trades']
            classified[broader_category]['total_pnl'] += performance['total_pnl']
            classified[broader_category]['winning_trades'] += performance['winning_trades']
            classified[broader_category]['losing_trades'] += performance['losing_trades']
            
            # Store subcondition details
            classified[broader_category]['subconditions'][condition_key] = performance
        
        # Calculate derived metrics
        for category, data in classified.items():
            total_trades = data['total_trades']
            if total_trades > 0:
                data['win_rate'] = round((data['winning_trades'] / total_trades) * 100, 2)
                data['avg_pnl_per_trade'] = round(data['total_pnl'] / total_trades, 2)
            else:
                data['win_rate'] = 0
                data['avg_pnl_per_trade'] = 0
        
        return dict(classified)
    
    def _map_to_broader_category(self, condition: str, direction: str) -> str:
        """Map specific conditions to broader categories"""
        # Primary classification by condition
        if condition in ['TRENDING']:
            if direction in ['UP']:
                return 'BULLISH_TRENDING'
            elif direction in ['DOWN']:
                return 'BEARISH_TRENDING'
            else:
                return 'TRENDING'
        elif condition in ['RANGING']:
            return 'RANGING'
        else:
            # Secondary classification by direction
            if direction in ['UP']:
                return 'BULLISH'
            elif direction in ['DOWN']:
                return 'BEARISH'
            elif direction in ['SIDEWAYS']:
                return 'SIDEWAYS'
            else:
                return 'UNKNOWN'
    
    def _calculate_regime_statistics(self, classified_performance: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate statistics for different market regimes"""
        regime_stats = {}
        
        # Overall statistics
        total_trades_all = sum(data['total_trades'] for data in classified_performance.values())
        total_pnl_all = sum(data['total_pnl'] for data in classified_performance.values())
        
        for regime, data in classified_performance.items():
            if data['total_trades'] == 0:
                continue
            
            # Calculate regime-specific statistics
            trade_frequency = (data['total_trades'] / total_trades_all * 100) if total_trades_all > 0 else 0
            pnl_contribution = (data['total_pnl'] / total_pnl_all * 100) if total_pnl_all != 0 else 0
            
            # Calculate profit factor
            winning_pnl = sum(
                subdata['total_pnl'] for subdata in data['subconditions'].values() 
                if subdata['total_pnl'] > 0
            )
            losing_pnl = abs(sum(
                subdata['total_pnl'] for subdata in data['subconditions'].values() 
                if subdata['total_pnl'] < 0
            ))
            
            profit_factor = winning_pnl / losing_pnl if losing_pnl > 0 else (999.99 if winning_pnl > 0 else 0)
            
            regime_stats[regime] = {
                'trade_frequency_percent': round(trade_frequency, 2),
                'pnl_contribution_percent': round(pnl_contribution, 2),
                'profit_factor': round(profit_factor, 2),
                'efficiency_ratio': round(pnl_contribution / trade_frequency, 2) if trade_frequency > 0 else 0
            }
        
        return regime_stats
    
    def _identify_optimal_conditions(self, classified_performance: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify optimal market conditions for trading"""
        optimal_conditions = []
        
        for regime, data in classified_performance.items():
            if data['total_trades'] < 5:  # Skip regimes with too few trades
                continue
            
            # Score based on multiple factors
            win_rate_score = data['win_rate'] / 100  # Normalize to 0-1
            pnl_score = max(0, min(1, (data['total_pnl'] + 100) / 200))  # Normalize around 0, cap at 1
            trade_count_score = min(1, data['total_trades'] / 50)  # Normalize, cap at 50 trades
            
            composite_score = (win_rate_score * 0.4 + pnl_score * 0.4 + trade_count_score * 0.2)
            
            optimal_conditions.append({
                'regime': regime,
                'composite_score': round(composite_score, 3),
                'win_rate': data['win_rate'],
                'total_pnl': data['total_pnl'],
                'total_trades': data['total_trades'],
                'avg_pnl_per_trade': data['avg_pnl_per_trade'],
                'recommendation': self._generate_regime_recommendation(regime, data)
            })
        
        # Sort by composite score
        optimal_conditions.sort(key=lambda x: x['composite_score'], reverse=True)
        
        return optimal_conditions
    
    def _generate_regime_recommendation(self, regime: str, data: Dict[str, Any]) -> str:
        """Generate trading recommendation for specific market regime"""
        win_rate = data['win_rate']
        total_pnl = data['total_pnl']
        avg_pnl = data['avg_pnl_per_trade']
        
        if total_pnl > 50 and win_rate > 60:
            return f"Excellent performance in {regime} conditions. Increase position sizing."
        elif total_pnl > 0 and win_rate > 50:
            return f"Good performance in {regime} conditions. Maintain current strategy."
        elif total_pnl > 0 and win_rate < 50:
            return f"Profitable but low win rate in {regime}. Review risk/reward ratios."
        elif total_pnl < 0 and win_rate > 50:
            return f"High win rate but unprofitable in {regime}. Increase profit targets."
        else:
            return f"Poor performance in {regime} conditions. Consider avoiding or revising strategy."
    
    def _generate_market_insights(self, classified_performance: Dict[str, Any], regime_stats: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate actionable insights about market conditions"""
        insights = []
        
        # Find best and worst performing regimes
        regimes_by_pnl = sorted(
            [(regime, data['total_pnl']) for regime, data in classified_performance.items()],
            key=lambda x: x[1], reverse=True
        )
        
        if len(regimes_by_pnl) >= 2:
            best_regime, best_pnl = regimes_by_pnl[0]
            worst_regime, worst_pnl = regimes_by_pnl[-1]
            
            if best_pnl > 0 and worst_pnl < 0:
                insights.append({
                    "type": "regime_performance",
                    "priority": "high",
                    "title": f"Focus on {best_regime} conditions",
                    "description": f"Best performance: {best_regime} (+${best_pnl}), Worst: {worst_regime} (${worst_pnl})",
                    "action": f"Increase trading frequency in {best_regime} conditions and reduce exposure in {worst_regime}"
                })
        
        # Analyze efficiency ratios
        most_efficient = max(regime_stats.items(), key=lambda x: x[1].get('efficiency_ratio', 0))
        if most_efficient[1]['efficiency_ratio'] > 1.5:
            insights.append({
                "type": "efficiency",
                "priority": "medium",
                "title": f"{most_efficient[0]} shows high efficiency",
                "description": f"Efficiency ratio: {most_efficient[1]['efficiency_ratio']} (PnL contribution vs trade frequency)",
                "action": f"Prioritize {most_efficient[0]} conditions for better capital efficiency"
            })
        
        # Check for overtraded conditions
        overtraded = [
            (regime, stats) for regime, stats in regime_stats.items()
            if stats['trade_frequency_percent'] > 30 and stats['pnl_contribution_percent'] < 10
        ]
        
        for regime, stats in overtraded:
            insights.append({
                "type": "overtrading",
                "priority": "medium",
                "title": f"Overtrading in {regime} conditions",
                "description": f"{stats['trade_frequency_percent']}% of trades but only {stats['pnl_contribution_percent']}% of profits",
                "action": f"Reduce trading frequency in {regime} or improve strategy for these conditions"
            })
        
        return insights
    
    def get_market_condition_summary(self, days: int = 30) -> Dict[str, Any]:
        """Get comprehensive market condition analysis summary"""
        try:
            # Get main analysis
            market_analysis = self.analyze_market_condition_performance(days)
            
            if "error" in market_analysis:
                return market_analysis
            
            # Get symbol-specific market performance
            symbol_market_performance = self._analyze_symbol_market_performance(days)
            
            # Get timeframe-specific market performance
            timeframe_market_performance = self._analyze_timeframe_market_performance(days)
            
            return {
                "overall_market_analysis": market_analysis,
                "symbol_market_performance": symbol_market_performance,
                "timeframe_market_performance": timeframe_market_performance,
                "analysis_period_days": days,
                "summary_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating market condition summary: {e}")
            return {"error": str(e)}
    
    def _analyze_symbol_market_performance(self, days: int = 30) -> Dict[str, Any]:
        """Analyze market condition performance by symbol"""
        try:
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            trades_with_analysis = self.db.get_trades_with_analysis(limit=10000)
            
            # Filter by date and closed status
            filtered_trades = [
                t for t in trades_with_analysis 
                if t.get('created_at') and t['created_at'] >= start_date 
                and t.get('status') == 'closed'
            ]
            
            symbol_market_stats = defaultdict(lambda: defaultdict(lambda: {
                'trades': [],
                'total_pnl': 0,
                'trade_count': 0
            }))
            
            for trade in filtered_trades:
                symbol = trade.get('symbol', 'UNKNOWN')
                market_condition = trade.get('market_condition', 'UNKNOWN')
                market_direction = trade.get('market_direction', 'UNKNOWN')
                market_key = f"{market_condition}_{market_direction}"
                pnl = trade.get('pnl', 0) or 0
                
                symbol_market_stats[symbol][market_key]['trades'].append(pnl)
                symbol_market_stats[symbol][market_key]['total_pnl'] += pnl
                symbol_market_stats[symbol][market_key]['trade_count'] += 1
            
            # Calculate performance metrics
            symbol_results = {}
            
            for symbol, market_data in symbol_market_stats.items():
                symbol_results[symbol] = {}
                
                for market_key, stats in market_data.items():
                    if stats['trade_count'] < 2:
                        continue
                    
                    trades_list = stats['trades']
                    winning_trades = [t for t in trades_list if t > 0]
                    win_rate = len(winning_trades) / len(trades_list) if trades_list else 0
                    
                    symbol_results[symbol][market_key] = {
                        'total_trades': stats['trade_count'],
                        'total_pnl': round(stats['total_pnl'], 2),
                        'win_rate': round(win_rate * 100, 2),
                        'avg_pnl_per_trade': round(stats['total_pnl'] / stats['trade_count'], 2)
                    }
            
            return symbol_results
            
        except Exception as e:
            logger.error(f"Error analyzing symbol market performance: {e}")
            return {}
    
    def _analyze_timeframe_market_performance(self, days: int = 30) -> Dict[str, Any]:
        """Analyze market condition performance by timeframe"""
        try:
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            trades_with_analysis = self.db.get_trades_with_analysis(limit=10000)
            
            # Filter by date and closed status
            filtered_trades = [
                t for t in trades_with_analysis 
                if t.get('created_at') and t['created_at'] >= start_date 
                and t.get('status') == 'closed'
            ]
            
            timeframe_market_stats = defaultdict(lambda: defaultdict(lambda: {
                'trades': [],
                'total_pnl': 0,
                'trade_count': 0
            }))
            
            for trade in filtered_trades:
                timeframe = trade.get('timeframe', 'UNKNOWN')
                market_condition = trade.get('market_condition', 'UNKNOWN')
                market_direction = trade.get('market_direction', 'UNKNOWN')
                market_key = f"{market_condition}_{market_direction}"
                pnl = trade.get('pnl', 0) or 0
                
                timeframe_market_stats[timeframe][market_key]['trades'].append(pnl)
                timeframe_market_stats[timeframe][market_key]['total_pnl'] += pnl
                timeframe_market_stats[timeframe][market_key]['trade_count'] += 1
            
            # Calculate performance metrics
            timeframe_results = {}
            
            for timeframe, market_data in timeframe_market_stats.items():
                timeframe_results[timeframe] = {}
                
                for market_key, stats in market_data.items():
                    if stats['trade_count'] < 2:
                        continue
                    
                    trades_list = stats['trades']
                    winning_trades = [t for t in trades_list if t > 0]
                    win_rate = len(winning_trades) / len(trades_list) if trades_list else 0
                    
                    timeframe_results[timeframe][market_key] = {
                        'total_trades': stats['trade_count'],
                        'total_pnl': round(stats['total_pnl'], 2),
                        'win_rate': round(win_rate * 100, 2),
                        'avg_pnl_per_trade': round(stats['total_pnl'] / stats['trade_count'], 2)
                    }
            
            return timeframe_results
            
        except Exception as e:
            logger.error(f"Error analyzing timeframe market performance: {e}")
            return {}
