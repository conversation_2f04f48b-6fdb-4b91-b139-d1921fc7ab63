"""
Prompt Performance Analyzer for UltimateDashboard
Analyzes prompt effectiveness, correlates with market conditions, and identifies optimization opportunities
"""
import logging
import re
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import statistics

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connector import DatabaseConnector

logger = logging.getLogger(__name__)

class PromptAnalyzer:
    """Analyzes prompt performance and effectiveness"""
    
    def __init__(self):
        self.db = DatabaseConnector()
    
    def analyze_prompt_characteristics(self, days: int = 30) -> Dict[str, Any]:
        """Analyze characteristics of prompts and their performance"""
        try:
            # Get trades with analysis data
            trades_with_analysis = self.db.get_trades_with_analysis(limit=10000)
            
            # Filter by date and closed status
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            filtered_trades = [
                t for t in trades_with_analysis 
                if t.get('created_at') and t['created_at'] >= start_date 
                and t.get('status') == 'closed'
                and t.get('analysis_prompt')
            ]
            
            if not filtered_trades:
                return {"error": "No trades with prompts found for analysis"}
            
            # Extract prompt characteristics
            prompt_characteristics = self._extract_prompt_features(filtered_trades)
            
            # Analyze performance by characteristics
            characteristic_performance = self._analyze_characteristic_performance(prompt_characteristics)
            
            # Find optimal prompt patterns
            optimal_patterns = self._identify_optimal_patterns(characteristic_performance)
            
            return {
                "total_analyzed_trades": len(filtered_trades),
                "prompt_characteristics": characteristic_performance,
                "optimal_patterns": optimal_patterns,
                "analysis_period_days": days,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing prompt characteristics: {e}")
            return {"error": str(e)}
    
    def _extract_prompt_features(self, trades: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract features from prompts for analysis"""
        prompt_features = []
        
        for trade in trades:
            prompt_text = trade.get('analysis_prompt', '')
            if not prompt_text:
                continue
            
            features = {
                'trade_id': trade.get('id'),
                'pnl': trade.get('pnl', 0) or 0,
                'confidence': trade.get('confidence', 0) or 0,
                'market_condition': trade.get('market_condition', 'UNKNOWN'),
                'market_direction': trade.get('market_direction', 'UNKNOWN'),
                'symbol': trade.get('symbol', ''),
                'timeframe': trade.get('timeframe', ''),
                'recommendation': trade.get('recommendation', ''),
                
                # Extract prompt characteristics
                'prompt_length': len(prompt_text),
                'has_risk_management': 'risk' in prompt_text.lower(),
                'has_pattern_analysis': any(pattern in prompt_text.lower() for pattern in [
                    'pattern', 'breakout', 'support', 'resistance', 'trend'
                ]),
                'has_technical_indicators': any(indicator in prompt_text.lower() for indicator in [
                    'rsi', 'ema', 'macd', 'volume', 'moving average'
                ]),
                'has_market_context': any(context in prompt_text.lower() for context in [
                    'funding', 'long/short ratio', 'market condition', 'sentiment'
                ]),
                'has_timeframe_specific': any(tf in prompt_text.lower() for tf in [
                    '1h', '4h', '1d', 'hourly', 'daily', 'timeframe'
                ]),
                'mentions_confidence': 'confidence' in prompt_text.lower(),
                'mentions_probability': any(prob in prompt_text.lower() for prob in [
                    'probability', 'likely', 'chance', 'success rate'
                ]),
                'has_entry_criteria': any(entry in prompt_text.lower() for entry in [
                    'entry', 'enter', 'buy', 'sell', 'long', 'short'
                ]),
                'has_exit_criteria': any(exit_term in prompt_text.lower() for exit_term in [
                    'exit', 'stop loss', 'take profit', 'target'
                ]),
                'prompt_hash': hashlib.md5(prompt_text.encode()).hexdigest()[:8]
            }
            
            prompt_features.append(features)
        
        return prompt_features
    
    def _analyze_characteristic_performance(self, prompt_features: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance by prompt characteristics"""
        characteristic_stats = defaultdict(lambda: {
            'trades': [],
            'total_pnl': 0,
            'trade_count': 0,
            'avg_confidence': 0,
            'confidence_sum': 0
        })
        
        # Analyze each characteristic
        characteristics_to_analyze = [
            'has_risk_management',
            'has_pattern_analysis', 
            'has_technical_indicators',
            'has_market_context',
            'has_timeframe_specific',
            'mentions_confidence',
            'mentions_probability',
            'has_entry_criteria',
            'has_exit_criteria'
        ]
        
        for feature in prompt_features:
            pnl = feature['pnl']
            confidence = feature['confidence']
            
            for characteristic in characteristics_to_analyze:
                has_characteristic = feature.get(characteristic, False)
                key = f"{characteristic}_{has_characteristic}"
                
                characteristic_stats[key]['trades'].append(pnl)
                characteristic_stats[key]['total_pnl'] += pnl
                characteristic_stats[key]['trade_count'] += 1
                characteristic_stats[key]['confidence_sum'] += confidence
        
        # Calculate performance metrics for each characteristic
        results = {}
        
        for key, stats in characteristic_stats.items():
            if stats['trade_count'] < 3:  # Skip characteristics with too few trades
                continue
            
            trades_list = stats['trades']
            winning_trades = [t for t in trades_list if t > 0]
            
            win_rate = len(winning_trades) / len(trades_list) if trades_list else 0
            avg_confidence = stats['confidence_sum'] / stats['trade_count'] if stats['trade_count'] > 0 else 0
            
            results[key] = {
                'total_trades': stats['trade_count'],
                'total_pnl': round(stats['total_pnl'], 2),
                'win_rate': round(win_rate * 100, 2),
                'avg_pnl_per_trade': round(stats['total_pnl'] / stats['trade_count'], 2),
                'avg_confidence': round(avg_confidence, 2),
                'winning_trades': len(winning_trades),
                'losing_trades': len(trades_list) - len(winning_trades)
            }
        
        return results
    
    def _identify_optimal_patterns(self, characteristic_performance: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify optimal prompt patterns based on performance"""
        patterns = []
        
        # Find characteristics that improve performance
        for characteristic, performance in characteristic_performance.items():
            if '_True' in characteristic and performance['total_pnl'] > 0:
                characteristic_name = characteristic.replace('_True', '').replace('has_', '').replace('mentions_', '')
                
                patterns.append({
                    'pattern': characteristic_name,
                    'performance_impact': 'positive',
                    'total_pnl': performance['total_pnl'],
                    'win_rate': performance['win_rate'],
                    'trade_count': performance['total_trades'],
                    'recommendation': f"Include {characteristic_name.replace('_', ' ')} in prompts"
                })
        
        # Sort by total PnL impact
        patterns.sort(key=lambda x: x['total_pnl'], reverse=True)
        
        return patterns[:10]  # Return top 10 patterns
    
    def analyze_prompt_market_correlation(self, days: int = 30) -> Dict[str, Any]:
        """Analyze how prompt performance correlates with market conditions"""
        try:
            # Get trades with analysis data
            trades_with_analysis = self.db.get_trades_with_analysis(limit=10000)
            
            # Filter by date and closed status
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            filtered_trades = [
                t for t in trades_with_analysis 
                if t.get('created_at') and t['created_at'] >= start_date 
                and t.get('status') == 'closed'
                and t.get('analysis_prompt')
            ]
            
            if not filtered_trades:
                return {"error": "No trades with prompts found for analysis"}
            
            # Group by market conditions and prompt characteristics
            correlation_data = defaultdict(lambda: defaultdict(lambda: {
                'trades': [],
                'total_pnl': 0,
                'trade_count': 0
            }))
            
            for trade in filtered_trades:
                market_condition = trade.get('market_condition', 'UNKNOWN')
                market_direction = trade.get('market_direction', 'UNKNOWN')
                market_key = f"{market_condition}_{market_direction}"
                
                prompt_text = trade.get('analysis_prompt', '')
                pnl = trade.get('pnl', 0) or 0
                
                # Analyze prompt characteristics for this market condition
                has_pattern_analysis = any(pattern in prompt_text.lower() for pattern in [
                    'pattern', 'breakout', 'support', 'resistance', 'trend'
                ])
                
                has_risk_management = 'risk' in prompt_text.lower()
                
                prompt_type = 'pattern_focused' if has_pattern_analysis else 'basic'
                if has_risk_management:
                    prompt_type += '_with_risk'
                
                correlation_data[market_key][prompt_type]['trades'].append(pnl)
                correlation_data[market_key][prompt_type]['total_pnl'] += pnl
                correlation_data[market_key][prompt_type]['trade_count'] += 1
            
            # Calculate correlation metrics
            correlation_results = {}
            
            for market_key, prompt_data in correlation_data.items():
                market_results = {}
                
                for prompt_type, stats in prompt_data.items():
                    if stats['trade_count'] < 2:
                        continue
                    
                    trades_list = stats['trades']
                    winning_trades = [t for t in trades_list if t > 0]
                    win_rate = len(winning_trades) / len(trades_list) if trades_list else 0
                    
                    market_results[prompt_type] = {
                        'total_trades': stats['trade_count'],
                        'total_pnl': round(stats['total_pnl'], 2),
                        'win_rate': round(win_rate * 100, 2),
                        'avg_pnl_per_trade': round(stats['total_pnl'] / stats['trade_count'], 2)
                    }
                
                if market_results:
                    correlation_results[market_key] = market_results
            
            return {
                "market_prompt_correlation": correlation_results,
                "analysis_period_days": days,
                "total_analyzed_trades": len(filtered_trades)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing prompt-market correlation: {e}")
            return {"error": str(e)}
    
    def generate_prompt_recommendations(self, days: int = 30) -> List[Dict[str, Any]]:
        """Generate specific prompt optimization recommendations"""
        try:
            recommendations = []
            
            # Analyze prompt characteristics
            characteristics_analysis = self.analyze_prompt_characteristics(days)
            
            if "error" in characteristics_analysis:
                return [{"error": characteristics_analysis["error"]}]
            
            # Analyze market correlation
            market_correlation = self.analyze_prompt_market_correlation(days)
            
            # Generate recommendations based on analysis
            optimal_patterns = characteristics_analysis.get('optimal_patterns', [])
            
            if optimal_patterns:
                top_pattern = optimal_patterns[0]
                recommendations.append({
                    "type": "prompt_enhancement",
                    "priority": "high",
                    "title": f"Enhance prompts with {top_pattern['pattern']}",
                    "description": f"Adding {top_pattern['pattern']} to prompts shows {top_pattern['total_pnl']}$ improvement with {top_pattern['win_rate']}% win rate",
                    "implementation": top_pattern['recommendation']
                })
            
            # Analyze prompt performance by market conditions
            if "market_prompt_correlation" in market_correlation:
                best_market_performance = None
                best_pnl = float('-inf')
                
                for market_key, prompt_data in market_correlation["market_prompt_correlation"].items():
                    for prompt_type, performance in prompt_data.items():
                        if performance['total_pnl'] > best_pnl:
                            best_pnl = performance['total_pnl']
                            best_market_performance = {
                                'market': market_key,
                                'prompt_type': prompt_type,
                                'performance': performance
                            }
                
                if best_market_performance:
                    recommendations.append({
                        "type": "market_specific_prompts",
                        "priority": "medium",
                        "title": f"Optimize prompts for {best_market_performance['market']} conditions",
                        "description": f"Use {best_market_performance['prompt_type']} prompts in {best_market_performance['market']} markets for best results",
                        "implementation": f"Implement conditional prompt selection based on market conditions"
                    })
            
            # Check for underperforming patterns
            characteristic_performance = characteristics_analysis.get('prompt_characteristics', {})
            
            for characteristic, performance in characteristic_performance.items():
                if '_False' in characteristic and performance.get('total_pnl', 0) > 0:
                    # This suggests that NOT having this characteristic is better
                    characteristic_name = characteristic.replace('_False', '').replace('has_', '').replace('mentions_', '')
                    
                    recommendations.append({
                        "type": "prompt_simplification",
                        "priority": "low",
                        "title": f"Consider removing {characteristic_name} from prompts",
                        "description": f"Prompts without {characteristic_name} show better performance",
                        "implementation": f"Simplify prompts by reducing {characteristic_name.replace('_', ' ')} content"
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating prompt recommendations: {e}")
            return [{"error": str(e)}]
    
    def get_prompt_performance_summary(self, days: int = 30) -> Dict[str, Any]:
        """Get a comprehensive summary of prompt performance using dynamic prompt versions"""
        try:
            # Get basic prompt performance (now uses prompt_version from analysis_data)
            prompt_performance = self.db.get_prompt_performance(days)

            # Get unique prompt versions dynamically
            prompt_versions = self.db.get_unique_prompt_versions()

            # Get prompt characteristics analysis
            characteristics_analysis = self.analyze_prompt_characteristics(days)

            # Get market correlation analysis
            market_correlation = self.analyze_prompt_market_correlation(days)

            # Generate recommendations
            recommendations = self.generate_prompt_recommendations(days)

            # Calculate summary statistics
            summary_stats = {
                "total_prompts_analyzed": len(prompt_performance),
                "total_prompt_versions": len(prompt_versions),
                "best_performing_prompt": prompt_performance[0]['prompt_name'] if prompt_performance else None,
                "worst_performing_prompt": prompt_performance[-1]['prompt_name'] if prompt_performance else None,
                "avg_win_rate": sum(p['win_rate'] for p in prompt_performance) / len(prompt_performance) if prompt_performance else 0,
                "avg_profit_factor": sum(p['profit_factor'] for p in prompt_performance) / len(prompt_performance) if prompt_performance else 0,
                "total_trades_analyzed": sum(p['total_trades'] for p in prompt_performance)
            }

            return {
                "prompt_performance": prompt_performance,
                "prompt_versions": prompt_versions,
                "characteristics_analysis": characteristics_analysis,
                "market_correlation": market_correlation,
                "recommendations": recommendations,
                "summary_stats": summary_stats,
                "analysis_period_days": days,
                "summary_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating prompt performance summary: {e}")
            return {"error": str(e)}
