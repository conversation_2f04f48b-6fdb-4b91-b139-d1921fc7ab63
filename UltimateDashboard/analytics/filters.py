"""
Advanced Filtering and Optimization Features for UltimateDashboard
Provides sophisticated filtering capabilities and optimization recommendations
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import statistics

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connector import DatabaseConnector

logger = logging.getLogger(__name__)

class AdvancedFilters:
    """Advanced filtering and optimization features"""
    
    def __init__(self):
        self.db = DatabaseConnector()
    
    def apply_advanced_filters(self, 
                             filters: Dict[str, Any],
                             data_type: str = 'trades') -> List[Dict[str, Any]]:
        """Apply advanced filtering to trading data"""
        try:
            # Base query parameters
            query_params = {
                'limit': filters.get('limit', 1000),
                'start_date': filters.get('start_date'),
                'end_date': filters.get('end_date')
            }
            
            # Get base data
            if data_type == 'trades':
                data = self.db.get_trades_with_analysis(**query_params)
            elif data_type == 'analysis':
                data = self.db.get_analysis_results(**query_params)
            else:
                raise ValueError(f"Unsupported data_type: {data_type}")
            
            # Apply filters
            filtered_data = self._apply_filters(data, filters)
            
            return filtered_data
            
        except Exception as e:
            logger.error(f"Error applying advanced filters: {e}")
            return []
    
    def _apply_filters(self, data: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Apply individual filters to data"""
        filtered_data = data
        
        # Symbol filter
        if filters.get('symbols'):
            symbols = filters['symbols'] if isinstance(filters['symbols'], list) else [filters['symbols']]
            filtered_data = [d for d in filtered_data if d.get('symbol') in symbols]
        
        # Timeframe filter
        if filters.get('timeframes'):
            timeframes = filters['timeframes'] if isinstance(filters['timeframes'], list) else [filters['timeframes']]
            filtered_data = [d for d in filtered_data if d.get('timeframe') in timeframes]
        
        # Status filter
        if filters.get('status'):
            filtered_data = [d for d in filtered_data if d.get('status') == filters['status']]
        
        # Confidence range filter
        if filters.get('min_confidence') is not None:
            filtered_data = [d for d in filtered_data if (d.get('confidence') or 0) >= filters['min_confidence']]
        
        if filters.get('max_confidence') is not None:
            filtered_data = [d for d in filtered_data if (d.get('confidence') or 0) <= filters['max_confidence']]
        
        # P&L range filter
        if filters.get('min_pnl') is not None:
            filtered_data = [d for d in filtered_data if (d.get('pnl') or 0) >= filters['min_pnl']]
        
        if filters.get('max_pnl') is not None:
            filtered_data = [d for d in filtered_data if (d.get('pnl') or 0) <= filters['max_pnl']]
        
        # Market condition filter
        if filters.get('market_conditions'):
            conditions = filters['market_conditions'] if isinstance(filters['market_conditions'], list) else [filters['market_conditions']]
            filtered_data = [d for d in filtered_data if d.get('market_condition') in conditions]
        
        # Market direction filter
        if filters.get('market_directions'):
            directions = filters['market_directions'] if isinstance(filters['market_directions'], list) else [filters['market_directions']]
            filtered_data = [d for d in filtered_data if d.get('market_direction') in directions]
        
        # Recommendation filter
        if filters.get('recommendations'):
            recommendations = filters['recommendations'] if isinstance(filters['recommendations'], list) else [filters['recommendations']]
            filtered_data = [d for d in filtered_data if d.get('recommendation') in recommendations]
        
        # Risk/Reward ratio filter
        if filters.get('min_rr') is not None:
            filtered_data = [d for d in filtered_data if (d.get('rr') or 0) >= filters['min_rr']]
        
        if filters.get('max_rr') is not None:
            filtered_data = [d for d in filtered_data if (d.get('rr') or 0) <= filters['max_rr']]
        
        # Winning/Losing trades filter
        if filters.get('trade_outcome'):
            if filters['trade_outcome'] == 'winning':
                filtered_data = [d for d in filtered_data if (d.get('pnl') or 0) > 0]
            elif filters['trade_outcome'] == 'losing':
                filtered_data = [d for d in filtered_data if (d.get('pnl') or 0) < 0]
        
        # Prompt version filter (using analysis_data JSON)
        if filters.get('prompt_versions'):
            prompt_versions = filters['prompt_versions'] if isinstance(filters['prompt_versions'], list) else [filters['prompt_versions']]
            filtered_data = [d for d in filtered_data
                           if d.get('analysis_data') and self._extract_prompt_version(d['analysis_data']) in prompt_versions]

        # Prompt content filter (basic text search)
        if filters.get('prompt_contains'):
            search_term = filters['prompt_contains'].lower()
            filtered_data = [d for d in filtered_data
                           if d.get('analysis_prompt') and search_term in d['analysis_prompt'].lower()]

        return filtered_data

    def _extract_prompt_version(self, analysis_data: str) -> str:
        """Extract prompt_version from analysis_data JSON string"""
        # NOTE: prompt_version is nested in analysis.prompt_version, introduced later
        try:
            import json
            data = json.loads(analysis_data) if isinstance(analysis_data, str) else analysis_data
            return data.get('analysis', {}).get('prompt_version', 'Unknown')
        except Exception:
            return 'Unknown'
    
    def get_filter_options(self) -> Dict[str, List[str]]:
        """Get available filter options from the database including dynamic prompt versions"""
        try:
            # Get unique values for filter dropdowns
            symbols = self.db.get_unique_symbols()
            timeframes = self.db.get_unique_timeframes()
            prompt_versions = self.db.get_unique_prompt_versions()

            # Get unique market conditions and directions
            analysis_results = self.db.get_analysis_results(limit=5000)
            market_conditions = list(set(r.get('market_condition') for r in analysis_results if r.get('market_condition')))
            market_directions = list(set(r.get('market_direction') for r in analysis_results if r.get('market_direction')))
            recommendations = list(set(r.get('recommendation') for r in analysis_results if r.get('recommendation')))

            return {
                'symbols': sorted(symbols),
                'timeframes': sorted(timeframes),
                'prompt_versions': sorted(prompt_versions),
                'market_conditions': sorted(market_conditions),
                'market_directions': sorted(market_directions),
                'recommendations': sorted(recommendations),
                'trade_outcomes': ['winning', 'losing'],
                'status_options': ['open', 'closed', 'cancelled']
            }

        except Exception as e:
            logger.error(f"Error getting filter options: {e}")
            return {}
    
    def generate_optimization_insights(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate optimization insights based on filtered data"""
        try:
            # Apply filters if provided
            if filters:
                trades_data = self.apply_advanced_filters(filters, 'trades')
            else:
                trades_data = self.db.get_trades(status='closed', limit=5000)
            
            if not trades_data:
                return {"error": "No data available for optimization analysis"}
            
            insights = {
                "data_summary": self._analyze_data_summary(trades_data),
                "performance_patterns": self._identify_performance_patterns(trades_data),
                "optimization_opportunities": self._find_optimization_opportunities(trades_data),
                "risk_analysis": self._analyze_risk_patterns(trades_data),
                "timing_analysis": self._analyze_timing_patterns(trades_data)
            }
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating optimization insights: {e}")
            return {"error": str(e)}
    
    def _analyze_data_summary(self, trades_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze summary statistics of filtered data"""
        total_trades = len(trades_data)
        pnl_values = [trade.get('pnl', 0) or 0 for trade in trades_data]
        
        winning_trades = [p for p in pnl_values if p > 0]
        losing_trades = [p for p in pnl_values if p < 0]
        
        return {
            "total_trades": total_trades,
            "total_pnl": round(sum(pnl_values), 2),
            "win_rate": round(len(winning_trades) / total_trades * 100, 2) if total_trades > 0 else 0,
            "avg_win": round(statistics.mean(winning_trades), 2) if winning_trades else 0,
            "avg_loss": round(statistics.mean(losing_trades), 2) if losing_trades else 0,
            "profit_factor": round(sum(winning_trades) / abs(sum(losing_trades)), 2) if losing_trades else (999.99 if winning_trades else 0),
            "largest_win": round(max(pnl_values), 2) if pnl_values else 0,
            "largest_loss": round(min(pnl_values), 2) if pnl_values else 0
        }
    
    def _identify_performance_patterns(self, trades_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify patterns in trading performance"""
        patterns = []
        
        # Group by symbol
        symbol_performance = defaultdict(list)
        for trade in trades_data:
            symbol = trade.get('symbol', 'UNKNOWN')
            pnl = trade.get('pnl', 0) or 0
            symbol_performance[symbol].append(pnl)
        
        # Find best and worst performing symbols
        symbol_totals = {symbol: sum(pnls) for symbol, pnls in symbol_performance.items()}
        best_symbol = max(symbol_totals, key=symbol_totals.get) if symbol_totals else None
        worst_symbol = min(symbol_totals, key=symbol_totals.get) if symbol_totals else None
        
        if best_symbol and worst_symbol and symbol_totals[best_symbol] > 0:
            patterns.append({
                "type": "symbol_performance",
                "description": f"Best performing symbol: {best_symbol} (+${symbol_totals[best_symbol]:.2f})",
                "recommendation": f"Consider increasing allocation to {best_symbol}"
            })
        
        if worst_symbol and symbol_totals[worst_symbol] < 0:
            patterns.append({
                "type": "symbol_performance",
                "description": f"Worst performing symbol: {worst_symbol} (${symbol_totals[worst_symbol]:.2f})",
                "recommendation": f"Review strategy for {worst_symbol} or reduce allocation"
            })
        
        # Group by market condition
        condition_performance = defaultdict(list)
        for trade in trades_data:
            condition = trade.get('market_condition', 'UNKNOWN')
            pnl = trade.get('pnl', 0) or 0
            condition_performance[condition].append(pnl)
        
        # Find best market conditions
        condition_totals = {condition: sum(pnls) for condition, pnls in condition_performance.items()}
        best_condition = max(condition_totals, key=condition_totals.get) if condition_totals else None
        
        if best_condition and condition_totals[best_condition] > 0:
            patterns.append({
                "type": "market_condition",
                "description": f"Best market condition: {best_condition} (+${condition_totals[best_condition]:.2f})",
                "recommendation": f"Focus trading on {best_condition} market conditions"
            })
        
        return patterns
    
    def _find_optimization_opportunities(self, trades_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find specific optimization opportunities"""
        opportunities = []
        
        # Analyze confidence vs performance
        confidence_performance = defaultdict(list)
        for trade in trades_data:
            confidence = trade.get('confidence', 0) or 0
            pnl = trade.get('pnl', 0) or 0
            
            if confidence > 0:
                confidence_bucket = "high" if confidence > 0.7 else "medium" if confidence > 0.4 else "low"
                confidence_performance[confidence_bucket].append(pnl)
        
        # Check if high confidence trades are actually better
        if 'high' in confidence_performance and 'low' in confidence_performance:
            high_conf_avg = statistics.mean(confidence_performance['high'])
            low_conf_avg = statistics.mean(confidence_performance['low'])
            
            if high_conf_avg > low_conf_avg:
                opportunities.append({
                    "type": "confidence_filtering",
                    "description": f"High confidence trades perform better (avg: ${high_conf_avg:.2f} vs ${low_conf_avg:.2f})",
                    "recommendation": "Consider filtering out trades with confidence < 0.7"
                })
        
        # Analyze risk/reward ratios
        rr_performance = defaultdict(list)
        for trade in trades_data:
            rr = trade.get('rr', 0) or 0
            pnl = trade.get('pnl', 0) or 0
            
            if rr > 0:
                rr_bucket = "high" if rr > 3 else "medium" if rr > 2 else "low"
                rr_performance[rr_bucket].append(pnl)
        
        # Check if higher R/R trades are better
        if 'high' in rr_performance and 'low' in rr_performance:
            high_rr_avg = statistics.mean(rr_performance['high'])
            low_rr_avg = statistics.mean(rr_performance['low'])
            
            if high_rr_avg > low_rr_avg:
                opportunities.append({
                    "type": "risk_reward_filtering",
                    "description": f"High R/R trades perform better (avg: ${high_rr_avg:.2f} vs ${low_rr_avg:.2f})",
                    "recommendation": "Consider only taking trades with R/R > 3.0"
                })
        
        return opportunities
    
    def _analyze_risk_patterns(self, trades_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze risk patterns in the data"""
        pnl_values = [trade.get('pnl', 0) or 0 for trade in trades_data]
        
        if not pnl_values:
            return {}
        
        # Calculate risk metrics
        losing_trades = [p for p in pnl_values if p < 0]
        max_loss = min(pnl_values) if pnl_values else 0
        avg_loss = statistics.mean(losing_trades) if losing_trades else 0
        
        # Calculate consecutive losses
        consecutive_losses = 0
        max_consecutive_losses = 0
        
        for pnl in pnl_values:
            if pnl < 0:
                consecutive_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
            else:
                consecutive_losses = 0
        
        return {
            "max_loss": round(max_loss, 2),
            "avg_loss": round(avg_loss, 2),
            "max_consecutive_losses": max_consecutive_losses,
            "loss_frequency": round(len(losing_trades) / len(pnl_values) * 100, 2) if pnl_values else 0,
            "risk_assessment": "High" if max_consecutive_losses > 5 or max_loss < -100 else "Medium" if max_consecutive_losses > 3 else "Low"
        }
    
    def _analyze_timing_patterns(self, trades_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze timing patterns in trading data"""
        try:
            # Group trades by hour of day
            hourly_performance = defaultdict(list)
            
            for trade in trades_data:
                created_at = trade.get('created_at')
                pnl = trade.get('pnl', 0) or 0
                
                if created_at:
                    try:
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        hour = dt.hour
                        hourly_performance[hour].append(pnl)
                    except Exception:
                        continue
            
            # Find best performing hours
            hourly_totals = {hour: sum(pnls) for hour, pnls in hourly_performance.items()}
            best_hour = max(hourly_totals, key=hourly_totals.get) if hourly_totals else None
            
            return {
                "hourly_performance": dict(hourly_totals),
                "best_trading_hour": best_hour,
                "best_hour_pnl": round(hourly_totals.get(best_hour, 0), 2) if best_hour else 0,
                "recommendation": f"Consider focusing trading around hour {best_hour}" if best_hour and hourly_totals.get(best_hour, 0) > 0 else "No clear timing pattern identified"
            }
            
        except Exception as e:
            logger.error(f"Error analyzing timing patterns: {e}")
            return {}
