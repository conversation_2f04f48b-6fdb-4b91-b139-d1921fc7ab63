"""
Core Analytics Engine for UltimateDashboard
Calculates key trading metrics, performance analysis, and optimization insights
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import statistics
import math

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connector import DatabaseConnector
from api.bybit_client import BybitClient

logger = logging.getLogger(__name__)

class CoreAnalyticsEngine:
    """Core analytics engine for trading performance analysis"""
    
    def __init__(self):
        self.db = DatabaseConnector()
        try:
            self.api_client = BybitClient()
        except Exception as e:
            logger.warning(f"Failed to initialize Bybit client: {e}")
            self.api_client = None
    
    def calculate_comprehensive_metrics(self, days: int = 30) -> Dict[str, Any]:
        """Calculate comprehensive trading performance metrics"""
        try:
            # Get basic performance metrics
            overall_performance = self.db.get_overall_performance(days)
            
            # Get detailed trade data
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            trades = self.db.get_trades(start_date=start_date, status='closed')
            
            if not trades:
                return {
                    "error": "No closed trades found for the specified period",
                    "period_days": days
                }
            
            # Calculate advanced metrics
            advanced_metrics = self._calculate_advanced_metrics(trades)
            
            # Calculate risk metrics
            risk_metrics = self._calculate_risk_metrics(trades)
            
            # Calculate time-based metrics
            time_metrics = self._calculate_time_metrics(trades)
            
            # Combine all metrics
            comprehensive_metrics = {
                **overall_performance,
                **advanced_metrics,
                **risk_metrics,
                **time_metrics,
                "period_days": days,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            return comprehensive_metrics
            
        except Exception as e:
            logger.error(f"Error calculating comprehensive metrics: {e}")
            return {"error": str(e)}
    
    def _calculate_advanced_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate advanced trading metrics"""
        pnl_values = [trade.get('pnl', 0) or 0 for trade in trades]
        
        if not pnl_values:
            return {}
        
        # Sharpe ratio calculation (simplified)
        avg_return = statistics.mean(pnl_values)
        std_return = statistics.stdev(pnl_values) if len(pnl_values) > 1 else 0
        sharpe_ratio = avg_return / std_return if std_return > 0 else 0
        
        # Sortino ratio (downside deviation)
        negative_returns = [p for p in pnl_values if p < 0]
        downside_deviation = statistics.stdev(negative_returns) if len(negative_returns) > 1 else 0
        sortino_ratio = avg_return / downside_deviation if downside_deviation > 0 else 0
        
        # Calmar ratio (return/max drawdown)
        max_drawdown = self._calculate_max_drawdown(pnl_values)
        calmar_ratio = avg_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Recovery factor
        total_return = sum(pnl_values)
        recovery_factor = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        return {
            "sharpe_ratio": round(sharpe_ratio, 3),
            "sortino_ratio": round(sortino_ratio, 3),
            "calmar_ratio": round(calmar_ratio, 3),
            "recovery_factor": round(recovery_factor, 3),
            "max_drawdown": round(max_drawdown, 2),
            "volatility": round(std_return, 2),
            "skewness": round(self._calculate_skewness(pnl_values), 3),
            "kurtosis": round(self._calculate_kurtosis(pnl_values), 3)
        }
    
    def _calculate_risk_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate risk-related metrics"""
        pnl_values = [trade.get('pnl', 0) or 0 for trade in trades]
        
        if not pnl_values:
            return {}
        
        # Value at Risk (VaR) - 95% confidence
        sorted_pnl = sorted(pnl_values)
        var_95_index = int(len(sorted_pnl) * 0.05)
        var_95 = sorted_pnl[var_95_index] if var_95_index < len(sorted_pnl) else sorted_pnl[0]
        
        # Conditional Value at Risk (CVaR)
        cvar_95 = statistics.mean(sorted_pnl[:var_95_index + 1]) if var_95_index >= 0 else var_95
        
        # Maximum consecutive losses
        max_consecutive_losses = self._calculate_max_consecutive_losses(pnl_values)
        
        # Risk of ruin (simplified)
        win_rate = len([p for p in pnl_values if p > 0]) / len(pnl_values)
        avg_win = statistics.mean([p for p in pnl_values if p > 0]) if any(p > 0 for p in pnl_values) else 0
        avg_loss = abs(statistics.mean([p for p in pnl_values if p < 0])) if any(p < 0 for p in pnl_values) else 0
        
        if avg_loss > 0:
            risk_of_ruin = ((1 - win_rate) / win_rate) ** (avg_win / avg_loss) if win_rate > 0 else 1
        else:
            risk_of_ruin = 0
        
        return {
            "var_95": round(var_95, 2),
            "cvar_95": round(cvar_95, 2),
            "max_consecutive_losses": max_consecutive_losses,
            "risk_of_ruin": round(min(risk_of_ruin, 1.0), 4)
        }
    
    def _calculate_time_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate time-based metrics"""
        try:
            # Calculate holding periods
            holding_periods = []
            
            for trade in trades:
                created_at = trade.get('created_at')
                updated_at = trade.get('updated_at')
                
                if created_at and updated_at:
                    try:
                        start_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        end_time = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        holding_period = (end_time - start_time).total_seconds() / 3600  # hours
                        holding_periods.append(holding_period)
                    except Exception:
                        continue
            
            if not holding_periods:
                return {"avg_holding_period_hours": 0, "median_holding_period_hours": 0}
            
            avg_holding_period = statistics.mean(holding_periods)
            median_holding_period = statistics.median(holding_periods)
            
            return {
                "avg_holding_period_hours": round(avg_holding_period, 2),
                "median_holding_period_hours": round(median_holding_period, 2),
                "min_holding_period_hours": round(min(holding_periods), 2),
                "max_holding_period_hours": round(max(holding_periods), 2)
            }
            
        except Exception as e:
            logger.error(f"Error calculating time metrics: {e}")
            return {"avg_holding_period_hours": 0, "median_holding_period_hours": 0}
    
    def _calculate_max_drawdown(self, pnl_values: List[float]) -> float:
        """Calculate maximum drawdown"""
        if not pnl_values:
            return 0
        
        cumulative = 0
        peak = 0
        max_drawdown = 0
        
        for pnl in pnl_values:
            cumulative += pnl
            if cumulative > peak:
                peak = cumulative
            drawdown = peak - cumulative
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        return max_drawdown
    
    def _calculate_max_consecutive_losses(self, pnl_values: List[float]) -> int:
        """Calculate maximum consecutive losses"""
        max_consecutive = 0
        current_consecutive = 0
        
        for pnl in pnl_values:
            if pnl < 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def _calculate_skewness(self, values: List[float]) -> float:
        """Calculate skewness of returns"""
        if len(values) < 3:
            return 0
        
        mean_val = statistics.mean(values)
        std_val = statistics.stdev(values)
        
        if std_val == 0:
            return 0
        
        skewness = sum(((x - mean_val) / std_val) ** 3 for x in values) / len(values)
        return skewness
    
    def _calculate_kurtosis(self, values: List[float]) -> float:
        """Calculate kurtosis of returns"""
        if len(values) < 4:
            return 0
        
        mean_val = statistics.mean(values)
        std_val = statistics.stdev(values)
        
        if std_val == 0:
            return 0
        
        kurtosis = sum(((x - mean_val) / std_val) ** 4 for x in values) / len(values) - 3
        return kurtosis
    
    def analyze_symbol_performance(self, days: int = 30) -> List[Dict[str, Any]]:
        """Analyze performance by symbol with detailed metrics"""
        try:
            symbol_performance = self.db.get_symbol_performance(days)
            
            # Enhance with additional metrics
            for symbol_data in symbol_performance:
                symbol = symbol_data['symbol']
                
                # Get detailed trades for this symbol
                start_date = (datetime.now() - timedelta(days=days)).isoformat()
                symbol_trades = self.db.get_trades(
                    symbol=symbol, 
                    status='closed', 
                    start_date=start_date
                )
                
                if symbol_trades:
                    pnl_values = [trade.get('pnl', 0) or 0 for trade in symbol_trades]
                    
                    # Add advanced metrics
                    symbol_data.update({
                        "max_drawdown": round(self._calculate_max_drawdown(pnl_values), 2),
                        "volatility": round(statistics.stdev(pnl_values), 2) if len(pnl_values) > 1 else 0,
                        "max_win": round(max(pnl_values), 2) if pnl_values else 0,
                        "max_loss": round(min(pnl_values), 2) if pnl_values else 0,
                        "profit_factor": self._calculate_profit_factor(pnl_values)
                    })
            
            return symbol_performance
            
        except Exception as e:
            logger.error(f"Error analyzing symbol performance: {e}")
            return []
    
    def _calculate_profit_factor(self, pnl_values: List[float]) -> float:
        """Calculate profit factor (avoiding infinity values for JSON compatibility)"""
        winning_trades = [p for p in pnl_values if p > 0]
        losing_trades = [p for p in pnl_values if p < 0]

        total_wins = sum(winning_trades) if winning_trades else 0
        total_losses = abs(sum(losing_trades)) if losing_trades else 0

        if total_losses == 0:
            # Return a high but finite number instead of infinity for JSON compatibility
            return 999.99 if total_wins > 0 else 0

        return round(total_wins / total_losses, 2)
    
    def generate_optimization_recommendations(self, days: int = 30) -> List[Dict[str, Any]]:
        """Generate optimization recommendations based on performance analysis"""
        try:
            recommendations = []
            
            # Analyze overall performance
            metrics = self.calculate_comprehensive_metrics(days)
            
            # Check win rate
            win_rate = metrics.get('win_rate', 0)
            if win_rate < 40:
                recommendations.append({
                    "type": "win_rate",
                    "priority": "high",
                    "message": f"Win rate is low at {win_rate}%. Consider reviewing entry criteria and market condition filters.",
                    "suggested_action": "Analyze prompt performance by market conditions to identify optimal entry patterns."
                })
            
            # Check profit factor
            profit_factor = metrics.get('profit_factor', 0)
            if profit_factor < 1.2:
                recommendations.append({
                    "type": "profit_factor",
                    "priority": "high",
                    "message": f"Profit factor is low at {profit_factor}. Risk/reward ratio needs improvement.",
                    "suggested_action": "Review stop loss and take profit levels. Consider tighter stops or wider targets."
                })
            
            # Check max drawdown
            max_drawdown = metrics.get('max_drawdown', 0)
            if max_drawdown > 100:  # Assuming USDT values
                recommendations.append({
                    "type": "risk_management",
                    "priority": "medium",
                    "message": f"Maximum drawdown is high at ${max_drawdown}. Consider position sizing adjustments.",
                    "suggested_action": "Implement dynamic position sizing based on recent performance."
                })
            
            # Analyze prompt performance
            prompt_performance = self.db.get_prompt_performance(days)
            if prompt_performance:
                best_prompt = max(prompt_performance, key=lambda x: x['total_pnl'])
                worst_prompt = min(prompt_performance, key=lambda x: x['total_pnl'])
                
                if best_prompt['total_pnl'] > 0 and worst_prompt['total_pnl'] < 0:
                    recommendations.append({
                        "type": "prompt_optimization",
                        "priority": "medium",
                        "message": f"Significant performance difference between prompts. Best: ${best_prompt['total_pnl']}, Worst: ${worst_prompt['total_pnl']}",
                        "suggested_action": f"Focus on patterns similar to best performing prompt and avoid worst performing patterns."
                    })
            
            # Analyze market condition performance
            market_performance = self.db.get_market_condition_performance(days)
            if market_performance:
                best_condition = max(market_performance.values(), key=lambda x: x['total_pnl'])
                worst_condition = min(market_performance.values(), key=lambda x: x['total_pnl'])
                
                if best_condition['total_pnl'] > 0 and worst_condition['total_pnl'] < 0:
                    recommendations.append({
                        "type": "market_condition",
                        "priority": "medium",
                        "message": f"Performance varies significantly by market conditions. Best: {best_condition['condition']}_{best_condition['direction']}, Worst: {worst_condition['condition']}_{worst_condition['direction']}",
                        "suggested_action": "Consider filtering trades to focus on favorable market conditions."
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating optimization recommendations: {e}")
            return []

    def calculate_pnl_over_time(self, days: int = 30) -> List[Dict[str, Any]]:
        """Calculate cumulative P&L over time for charting"""
        try:
            # Get all closed trades within the date range
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            trades = self.db.get_trades(start_date=start_date, status='closed')

            if not trades:
                return []

            # Sort trades by updated_at (when they were closed)
            trades.sort(key=lambda x: x.get('updated_at', x.get('created_at', '')))

            # Calculate cumulative P&L
            cumulative_pnl = 0
            pnl_data = []

            for trade in trades:
                pnl = trade.get('pnl', 0) or 0
                cumulative_pnl += pnl

                # Use updated_at for closed trades, fallback to created_at
                trade_date = trade.get('updated_at') or trade.get('created_at', '')

                pnl_data.append({
                    'date': trade_date,
                    'cumulative_pnl': round(cumulative_pnl, 2),
                    'trade_pnl': round(pnl, 2),
                    'symbol': trade.get('symbol', ''),
                    'side': trade.get('side', ''),
                    'trade_id': trade.get('id', '')
                })

            return pnl_data

        except Exception as e:
            logger.error(f"Error calculating P&L over time: {e}")
            return []
