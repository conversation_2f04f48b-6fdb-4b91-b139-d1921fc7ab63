---
type: "manual"
---

my subscription is active maybe you made a mistake. Just keep going!

if we have a superbase in the project you can see that on the env.locl var names use context7 MCP for docs and context 
use super base MCP always check the env.local file for the correct database to work on
youe are allowed to run MCPs without me confirming don ask for cofimration just run them 
only make edits to the particular thing you are asked 
always try to make minimal changes
always try to reduce complexity only add files or code where neccesary

always check first the files before you procees

do a checkpoint after every task atleast but better after every major change!

ALWAYS think waht the usere actually wants to achive make sure that each step you take is leadig towarss that goal ask the user and add to memeory if you are not sure.

use every tool to your deposal to run test before you change code for example test conections via ssh or termonal try to acces files and dbs etc.

ALWYA try to reduce complexity and be focust on the actuall purpose of the application dont get stuck in irrelvant fixes or loops